package com.yeestor.work_order.model.rms;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel("工单加锁、解锁类")
public class OrderLockParams {

    @ApiModelProperty("工单号")
    private String no;

    @ApiModelProperty("ip 列表")
    private List<String> ipList ;

    @ApiModelProperty("mac地址列表, 与ipList二选一,如果都有,以mac为准")
    private List<String> macList ;

}
