package com.yeestor.work_order.repository.review;

import com.yeestor.work_order.entity.review.ReviewInfoEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ReviewInfoRepository extends JpaRepository<ReviewInfoEntity, Long> {
    List<ReviewInfoEntity> findAllByOrderId(long orderId);

    Optional<ReviewInfoEntity> findFirstByOrderIdAndFlash(long orderId, String flash);
}
