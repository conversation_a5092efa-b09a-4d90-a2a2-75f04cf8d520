package com.yeestor.work_order.model.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel(value = "UserPermissionVO", description = "用户权限")
public class UserPermissionVO {

    @Data
    @ApiModel(value = "ProductPermission", description = "与产品线相关的权限")
    @Builder
    public static class ProductPermission {

        @ApiModelProperty(value = "产品线")
        private String product ;
        @ApiModelProperty(value = "权限列表")
        private List<String> permission ;
    }

    @ApiModelProperty("与产品线相关的权限列表")
    private List<ProductPermission> productPermissions ;

    @ApiModelProperty("与产品线无关的权限")
    private List<String> permissions ;


}
