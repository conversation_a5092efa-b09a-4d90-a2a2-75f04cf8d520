package com.yeestor.work_order.service.order.change;

import lombok.Builder;
import lombok.Data;


@Data
@Builder
public class DataChangeEvent {
    /**
     * 数据变更类型
     */
    public enum Type {

        /**
         * 工单导入 ,或者说是工单创建
         */
        ORDER_IMPORT,

        /**
         * 工单基础信息变更
         */
        ORDER_UPDATE,

        /**
         * 更新Mars信息
         */
        ORDER_UPDATE_MARS,
        /**
         * 工单重测,等同于工单导入
         */
        ORDER_RETEST,

        /**
         * 工单确认Flash
         */
        ORDER_CONFIRMED_FLASH,

        /**
         * 工单优先级变更
         */
        ORDER_PRIORITY_CHANGED,

        /**
         * 工单被撤销
         */
        ORDER_REVOKED,


        /**
         * plan 确认环境
         */
        PLAN_READY,


        /**
         * plan 确认环境
         */
        PLAN_CONFIRM_DEVICES,

        /**
         * 释放Plan下的所有设备
         */
        PLAN_RELEASE_ALL,

        /**
         * Plan的状态发生改变,
         */
        PLAN_STARTED,

        /**
         * 添加Plan, 手动添加
         */
        PLAN_ADDED,
        /**
         * Plan 优先级被临时调整.
         */
        PLAN_PRIORITY_CHANGED,

        /**
         * Plan 暂缓分配
         */
        PLAN_PAUSE,
        /**
         * Plan 恢复分配
         */
        PLAN_RESUME,

        /**
         * 手动停止Plan.
         */
        PLAN_STOPPED,

        /**
         * plan的报告文件上传完成.
         */
        PLAN_UPLOADED_REPORT,

        /**
         * 手动plan完成.
         */
        PLAN_MANUAL_DONE,
        /**
         * Plan 已完成. 自动完成
         */
        PLAN_COMPLETED,

        /**
         * Plan的状态发生改变, 一些不需要通知的状态,或者是目前没注意的状态
         */
        PLAN_STATUS_CHANGED,

        /**
         * Plan 由取消重新加入测试
         */
        PLAN_RETEST,


        /**
         * 添加设备, 手动添加
         */
        DEVICE_ADDED,

        /**
         * Plan 中的设备样片信息变更
         */
        DEVICE_SAMPLE_CHANGED,

        /**
         * PLAN 中的设备状态变更,测试完成,或者测试失败
         */
        DEVICE_STATUS_CHANGED,

        /**
         * 当且仅当设备被手动释放的时候, 触发此事件
         */
        DEVICE_RELEASED,

        /**
         * 暂停Flash批次的分配
         */
        FLASH_PAUSE,
        /**
         * 恢复Flash 批次的分配
         */
        FLASH_RESUME,

        /**
         * Flash 批次等待合并报告
         */
        FLASH_WAIT_MERGE_REPORT,

        /**
         * FLash 等待上传报告
         */
        FLASH_WAIT_REPORT,

        /**
         * Flash 等待fail 分析
         */
        FLASH_WAIT_FAIL_ANALYSIS,

        /**
         * Flash fail 分析开始
         */
        FLASH_ANALYSIS_STARTED,

        /**
         * Flash 等待review
         */
        FLASH_WAIT_REVIEW,

        /**
         * Flash review 开始
         */
        FLASH_REVIEW_STARTED,

        /**
         * Flash 已经完成
         */
        FLASH_COMPLETED,

        /**
         * Flash 已经取消 ,单独取消 某个批次, 目前不支持
         */
        FLASH_CANCELLED,

        /**
         * 工单已全部完成.
         */
        ORDER_COMPLETED

    }



    private long orderId ;
    private String flash ;
    private Long planId ;

    private String ip ;

    private Type type ;


    public String groupField(){
        return orderId + "_" + flash + "_" + planId ;
    }



}
