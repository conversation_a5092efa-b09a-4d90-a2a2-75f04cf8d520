.order-info {
    font-weight: 500;
    font-size: 15px;
    font-family: PingFangSC, 'helvetica neue', 'hiragino sans gb', arial, 'microsoft yahei ui', 'microsoft yahei', simsun, sans-serif;

    .order-no {
        color: #8C1B1A;
        font-weight: 600;
        font-size: 14px;
    }

    .order-status {
        font-weight: 600;
    }

    .relate {
        display: flex;

        .relate-item {
            width: 75px;
        }

        .relate-scroll {
            padding: 0 16px;
            max-height: 124px;
            border-radius: 4px;
            overflow-y: scroll;
            box-shadow: inset rgba(100, 116, 139, 16%) 0 2px 6px, inset rgba(100, 116, 139, 20%) 0 2px 8px;
        }

        .order-relate {
            color: #1677ff;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
        }
    }

    .order-flash {
        color: #8C1B1A;
        font-weight: 600;
        font-size: 14px;
    }

    .order-chip {
        color: #8C1B1A;
        font-weight: 600;
        font-size: 14px;
    }

    .order-version {
        word-break: break-all;
        padding-right: 30px;
        margin: 0;
        font-weight: 600;
        font-size: 14px;
        color: #8C1B1A;
    }

    .bug-link-body {
        max-width: 380px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        display: inline-flex;

        .bug-link {
            font-weight: 600;
            margin: 0 8px 6px 0;
            border: 2px solid #1890ff;
            padding: 0 6px;
            border-radius: 4px;
            height: 20px;
            line-height: 16px;
            font-size: 14px;
        }

    }

    .test-task {
        font-weight: 600;
        border: 2px solid #1890ff;
        padding: 0 6px;
        border-radius: 4px;
        font-size: 14px;
    }
}