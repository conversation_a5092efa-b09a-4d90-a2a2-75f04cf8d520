package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel("开始终端平台测试")
public class TerminalPlanTestParams {

    @ApiModelProperty("plan名称")
    private String plan;

    @ApiModelProperty("工单号")
    private String no;

    @ApiModelProperty("plan的路径")
    private String planPath;

    @ApiModelProperty("工具路径")
    private String toolPath;

    @ApiModelProperty("发起测试的用户")
    private String username;

    @ApiModelProperty(name = "脚本合集")
    private List<String> caseList;

    @ApiModelProperty("终端列表")
    private List<TerminalModel> terminalList;
}
