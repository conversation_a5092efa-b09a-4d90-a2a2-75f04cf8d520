package com.yeestor.work_order.service.device;

import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.model.rms.*;
import com.yeestor.work_order.utils.RMSApis;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScrcpyService {
    private final RMSApis rmsApis;

    public void holdLockPlatform(
            String orderFlashNo,
            List<TerminalModel> terminalList,
            OAuthUserDetail userDetail
    ) {
        List<String> platformList = terminalList.stream()
                .map(info -> "[" + info.getMac() + ":" + info.getNumberList() + "]")
                .collect(Collectors.toList());
        log.info("orderFlashNo: {} locked platform: {}", orderFlashNo, platformList);
        rmsApis.lockTerminalPlatform(terminalList, userDetail.getUsername(), orderFlashNo);
    }

    /***
     * 解锁设备
     * @param orderFlashNo 工单号
     * @param terminalList 测试终端信息
     * @param userName 操作人
     */
    public void holdUnLockPlatform(
            String orderFlashNo,
            List<TerminalModel> terminalList,
            String userName
    ) {
        List<String> platformList = terminalList.stream()
                .map(info -> "[" + info.getMac() + ":" + info.getNumberList() + "]")
                .collect(Collectors.toList());
        log.info("orderFlashNo: {} unlocked platform: {}", orderFlashNo, platformList);
        rmsApis.unlockTerminalPlatform(terminalList, userName, orderFlashNo);
    }

    /**
     * 停止平台测试
     * @param params 需要停止的平台信息和工单号信息
     * @return 执行结果
     */
    public HandleResp<TerminalPlatformStatusResp> stopTestPlatforms(TerminalPlanStopParams params){
        log.info("stopTestPlatforms stopParams: {}", params);
        HandleResp<TerminalPlatformStatusResp> testResultResp = rmsApis.stopTestTerminalPlan(params);
        log.info("stop test flashNo: {} plan: {} resultResp code: {} msg: {}", params.getNo(), params.getPlan(), testResultResp.getCode(), testResultResp.getMsg());
        return testResultResp;
    }

    /**
     * 启动测试平台测试
     * @param params plan信息、工单信息和平台信息
     * @return 执行结果
     */
    public HandleResp<TerminalPlatformStatusResp> startTestPlatforms(TerminalPlanTestParams params){
        log.info("startTestPlatforms startParams: {}", params);
        HandleResp<TerminalPlatformStatusResp> testResultResp = rmsApis.startTestTerminalPlan(params);
        log.info("start test flashNo: {} plan: {} resultResp code: {} msg: {}", params.getNo(), params.getPlan(), testResultResp.getCode(), testResultResp.getMsg());
        return testResultResp;
    }
}
