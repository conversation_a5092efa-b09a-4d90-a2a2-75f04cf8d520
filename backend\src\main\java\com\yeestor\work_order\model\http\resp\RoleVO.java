package com.yeestor.work_order.model.http.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "RoleVO", description = "角色信息")
public class RoleVO {

    @ApiModelProperty(value = "角色ID")
    private long id ;

    @ApiModelProperty(value = "角色名称")
    private String name ;

    @ApiModelProperty(value = "角色描述")
    private String desc;

    private String createdBy ;

    private String updatedBy;

    @ApiModelProperty(value = "产品列表")
    private List<String> products ;

    @ApiModelProperty(value = "权限列表")
    private List<String> permissions ;

    @ApiModelProperty(value = "用户列表")
    private List<String> users ;


}
