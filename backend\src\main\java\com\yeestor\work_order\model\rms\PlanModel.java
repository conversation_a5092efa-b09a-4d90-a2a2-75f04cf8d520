package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 */
@Data
@Builder
@JsonAutoDetect
@ApiModel(value = "PlanModel",description = "Plan 的信息")
@NoArgsConstructor
@AllArgsConstructor
public class PlanModel {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "plan的标题")
    private String title ;

    @ApiModelProperty("plan的编号")
    private String name ;

    @ApiModelProperty("plan 测试的功能")
    private String feature ;

    @ApiModelProperty("plan的类型，0 为自动， 1 为手动")
    private int type;

    @ApiModelProperty("plan所具备的属性")
    private List<String> attrs ;

    @ApiModelProperty("测试样片的数量")
    private int testNum ;

    @ApiModelProperty("所属产品线")
    private String product ;

    @ApiModelProperty("所属产品")
    private String subProduct ;

    @ApiModelProperty("优先级")
    private int priority ;


    /**
     * 只有当手动Plan，这个字段才有意义。
     * nReport = 1 表示需要上传报告。
     * nReport = 0 表示不需要上传报告。
     */
    @ApiModelProperty("是否需要上传报告")
    @JsonAlias("nReport")
    private String nReport ;

    /**
     * 版本类型 。
     * <ul>
     *     <li>alpha</li>
     *     <li>release</li>
     *     <li>alphaToRelease</li>
     * </ul>
     * <ul>
     *     <li>4：release &  alpha转release测试</li>
     *     <li>5：release & alpha</li>
     * </ul>
     */
    @ApiModelProperty(value = "版本类型",allowableValues = "Alpha,Release,alphaToRelease")
    private String versionType;

    @ApiModelProperty(value = "父Plan的名称,可以为多个,如果在测试的时候对应的父plan 不存在的时候，则表示忽略.",example = "[\"Plan22\",\"Plan23\"]")
    private List<String> parent ;

    @ApiModelProperty(value = "plan的阶段",hidden = true)
    private OrderPlanEntity.Phase phase ;

    @ApiModelProperty(value = "临时Plan的ID",hidden = true)
    private Long tempPlanId ;

    @ApiModelProperty(value = "操作人的钉钉ID",hidden = true)
    private String belongTo;

    @ApiModelProperty(value = "操作人的钉钉ID",hidden = true)
    private String belongToPerson;

    private OrderPlanEntity toEntity(){
        OrderPlanEntity entity = new OrderPlanEntity();
        entity.setCreatedAt(System.currentTimeMillis());
        entity.setName(name);
        entity.setFeature(feature);
        entity.setPhase(phase);
        entity.setType(type);
        entity.setAttrs(String.join(";", Optional.ofNullable(attrs).orElse(new ArrayList<>())));
        entity.setTestNum(testNum);
        entity.setPriority(priority);
        entity.setNeedReport("1".equals(this.nReport));
        entity.setBelongTo(belongTo);
        entity.setBelongToPerson(belongToPerson);
        entity.setTestAll(testNum == 0);

        String parentPlan = parent.stream().filter(s -> s!= null && s.trim().length() > 0).collect(Collectors.joining(","));
        entity.setParentPlan(parentPlan);
        entity.setPlanType("general");

        return entity;
    }


    public OrderPlanEntity toEntity(
            long orderId,
            OrderFlashEntity flashEntity,
            OrderPlanEntity.Status status
    ){
        OrderPlanEntity plan = toEntity();
        plan.setOrderId(orderId);
        if(flashEntity != null){
            plan.setFlash(flashEntity.getFlash());
            int flashSampleNum = flashEntity.getNum();
            plan.setTestNum(Math.min(plan.isTestAll() ?  flashSampleNum: plan.getTestNum(), flashSampleNum) );
            if (belongTo == null) {
                plan.setBelongTo(flashEntity.getTestBy());
            }
            if (belongToPerson == null) {
                plan.setBelongToPerson(flashEntity.getTestPerson());
            }
        }

        if(plan.isManualPlan()){
            plan.setReadyAt(System.currentTimeMillis());
            plan.setConfirmedAt(System.currentTimeMillis());
            plan.setConfirmedPerson("System");
            plan.setStatus(OrderPlanEntity.Status.CONFIRMED);
            plan.setTempPlanId(tempPlanId);
        }
        else {
            plan.setStatus(status);
        }
        return plan;
    }



}
