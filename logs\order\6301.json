{"@timestamp":"2025-07-23T15:09:03.149+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:09:03.16+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.169+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.169+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.17+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.673+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:12:02.675+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.678+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.678+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.671+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:15:02.672+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.675+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.675+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.675+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.705+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.706+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.71+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.71+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.71+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.69+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:24:02.691+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.695+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.695+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.695+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.916+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:51:02.939+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.95+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.95+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.951+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.674+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:57:02.676+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.681+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.681+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.681+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.099+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:00:05.102+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.107+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.107+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.107+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.653+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:03:02.654+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.659+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.659+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.659+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.652+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:09:02.653+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.656+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.656+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.656+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:12:02.636+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.639+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.639+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.692+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6301","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:24:02.7+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6301","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.707+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6301","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.708+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6301","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.708+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6301","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:30:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.628+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.632+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.633+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.669+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:33:02.674+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.677+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.678+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.678+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.623+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:36:02.624+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.626+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.626+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.626+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:39:02.628+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.631+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.631+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.639+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:48:02.64+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.644+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.644+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:51:02.622+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.624+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.624+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.319+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b1447a024600af2","spanId":"2b1447a024600af2","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:00:03.354+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b1447a024600af2","spanId":"2b1447a024600af2","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.372+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b1447a024600af2","spanId":"2b1447a024600af2","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.373+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b1447a024600af2","spanId":"2b1447a024600af2","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.373+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b1447a024600af2","spanId":"2b1447a024600af2","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.659+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c246a3affdd7ea94","spanId":"c246a3affdd7ea94","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:03:02.661+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c246a3affdd7ea94","spanId":"c246a3affdd7ea94","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c246a3affdd7ea94","spanId":"c246a3affdd7ea94","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.665+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c246a3affdd7ea94","spanId":"c246a3affdd7ea94","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.666+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c246a3affdd7ea94","spanId":"c246a3affdd7ea94","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.659+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc4fa335254a0453","spanId":"fc4fa335254a0453","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.661+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc4fa335254a0453","spanId":"fc4fa335254a0453","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.666+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc4fa335254a0453","spanId":"fc4fa335254a0453","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.666+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc4fa335254a0453","spanId":"fc4fa335254a0453","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.667+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc4fa335254a0453","spanId":"fc4fa335254a0453","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.655+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d73911d0b47c163","spanId":"1d73911d0b47c163","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:15:02.661+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d73911d0b47c163","spanId":"1d73911d0b47c163","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.667+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d73911d0b47c163","spanId":"1d73911d0b47c163","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.667+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d73911d0b47c163","spanId":"1d73911d0b47c163","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.667+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d73911d0b47c163","spanId":"1d73911d0b47c163","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.644+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f5b5b588ca94f3ad","spanId":"f5b5b588ca94f3ad","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:21:02.647+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f5b5b588ca94f3ad","spanId":"f5b5b588ca94f3ad","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.651+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f5b5b588ca94f3ad","spanId":"f5b5b588ca94f3ad","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.651+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f5b5b588ca94f3ad","spanId":"f5b5b588ca94f3ad","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.651+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f5b5b588ca94f3ad","spanId":"f5b5b588ca94f3ad","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.654+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"41d4baa0ecd20b7b","spanId":"41d4baa0ecd20b7b","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:24:02.657+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"41d4baa0ecd20b7b","spanId":"41d4baa0ecd20b7b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.661+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"41d4baa0ecd20b7b","spanId":"41d4baa0ecd20b7b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.661+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"41d4baa0ecd20b7b","spanId":"41d4baa0ecd20b7b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.661+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"41d4baa0ecd20b7b","spanId":"41d4baa0ecd20b7b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"27248bbb816563e7","spanId":"27248bbb816563e7","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:27:02.638+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"27248bbb816563e7","spanId":"27248bbb816563e7","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.642+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"27248bbb816563e7","spanId":"27248bbb816563e7","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.642+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"27248bbb816563e7","spanId":"27248bbb816563e7","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.642+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"27248bbb816563e7","spanId":"27248bbb816563e7","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.758+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.76+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.766+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.766+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.766+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.707+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:33:02.709+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.713+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.713+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.713+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.705+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:36:02.707+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.71+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.71+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.71+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:05.584+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:51:05.62+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:05.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:05.63+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:05.63+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.009+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:03.059+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.154+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.155+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.156+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:04.677+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:12:04.902+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:04.906+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:04.906+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:04.907+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.865+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:15:02.867+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.871+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.871+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.871+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.989+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.992+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.996+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.996+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.996+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.853+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:21:02.854+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.856+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.856+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.857+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.875+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:27:02.877+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.882+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.882+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.882+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.675+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.689+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.699+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.707+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.707+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6301","traceType":"分配设备"}
