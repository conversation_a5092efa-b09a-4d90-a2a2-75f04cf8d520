package com.yeestor.work_order.service.plan;


import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.model.rms.PlanModel;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.utils.RMSApis;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Collectors;

import static com.yeestor.work_order.entity.OrderPlanEntity.Status.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(MockitoExtension.class)
class FetchNeedPlanGETest {

    PlanService planService;

    @Mock
    FlashService flashService ;

    @Mock
    OrderPlanRepository planRepository ;

    List<OrderPlanEntity> planEntityList ;
    OrderFlashEntity flashEntity ;

    // given info -- global -- start
    String product = "GE" ;
    String subProduct = "SD" ;
    long orderId = 1L ;
    String flash = "6285EN-TAS-D_64GB" ;

    int maxSampleCount = 100 ;

    // given info -- global -- end

    List<OrderPlanEntity> generateDefaultReleasePlanList(
            String product,
            String subProduct ,
            long orderId,
            String flash,
            int flashSampleNum
    ){

        // generate Plan Entity List
        List<PlanModel> defaultReleasePlanList = planService.getReleaseDefaultPlanList(product,subProduct) ;
        assertThat(defaultReleasePlanList).hasSizeGreaterThan(1);
        OrderFlashEntity flashEntity = new OrderFlashEntity() ;
        flashEntity.setFlash(flash);
        flashEntity.setNum(flashSampleNum);

        List<OrderPlanEntity> planEntityList = defaultReleasePlanList.stream().map(p-> p.toEntity(orderId,
                flashEntity,
                OrderPlanEntity.Status.QUEUE
        )).collect(Collectors.toList());
        assertThat(planEntityList).hasSize(defaultReleasePlanList.size());
        return planEntityList ;
    }

    @BeforeEach
    void setUp() {
//        planService = new PlanService(
//                new RMSApis("prod"),planRepository,null,null,null,null,
//                null,null,null,null,null, null,null,null
//        );
//        planService.setFlashService(flashService) ;
//
//
//        planEntityList =  generateDefaultReleasePlanList(product,subProduct,orderId,flash,maxSampleCount) ;
//
//        flashEntity = new OrderFlashEntity();
//        flashEntity.setOrderId(orderId);
//        flashEntity.setFlash(flash);
//        flashEntity.setNum(maxSampleCount);
    }

    @Test
    @DisplayName("Flash批次样片为空时，不分配Plan。")
    void fetchNeedTestPlan_given_ge_with_no_samples_shouldReturn_empty_planList(){

        // generate Flash Entity
        flashEntity.setLeftNum(0);

        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;

        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        assertThat(resultPlans).isEmpty();
    }

    @Test
    @DisplayName("待确认中的Plan有已经5个了，则不分配")
    void fetchNeedTestPlan_given_ge_with_no_leftPlanCount_shouldReturn_empty_planList(){


        // generate Flash Entity
        flashEntity.setLeftNum(100);

        // 将待确认额度填满 5个
        for (int i = 0; i < 5; i++) {
            planEntityList.get(i).setStatus(READY);
        }

        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);


        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        assertThat(resultPlans).isEmpty();
    }

    @Test
    @DisplayName("全部样片的手动Plan的优先级最高，且未测试完成")
    void fetchNeedTestPlan_given_ge_with_topPriorityManualAllPlan_and_notCompleted_shouldReturn_empty(){
        // 设置剩余样片数量为全部
        flashEntity.setLeftNum(maxSampleCount);

        // 将全部样片的手动Plan设置为优先级最高
        planEntityList.stream()
                .filter(OrderPlanEntity::isManualPlan)
                .filter(OrderPlanEntity::isTestAll)
                .findFirst()
                .ifPresent(planEntity -> {
                    planEntity.setPriority(Integer.MAX_VALUE);
                    planEntity.setStatus(RUNNING);
                });


        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);

        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        // then
        // 全部样片的手动Plan的优先级最高，且未测试完成, 此时不自动分配Plan。
        assertThat(resultPlans).isEmpty();
    }



    @Test
    @DisplayName("测试全部样片的自动Plan 优先级最高，且依赖的手动Plan未完成")
    void fetchNeedTestPlan_given_ge_and_haveLeftPlanCount_and_haveParentPlan_shouldReturn_empty_planList(){

        // 设置剩余样片数量为全部
        flashEntity.setLeftNum(maxSampleCount);

        List<String> planNames = planEntityList.stream().filter(OrderPlanEntity::isTestAll).filter(OrderPlanEntity::isManualPlan).map(OrderPlanEntity::getName).collect(Collectors.toList()) ;
        // 将全部样片的自动Plan设置为优先级最高，且设置其依赖于手动Plan
        planEntityList.stream()
                .filter(OrderPlanEntity::isTestAll)
                .filter(OrderPlanEntity::isAutoPlan)
                .findFirst()
                .ifPresent(planEntity -> {
                    planEntity.setPriority(Integer.MAX_VALUE);
                    planEntity.setParentPlan(String.join(",",planNames));
                });

        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);

        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        // then
        assertThat(resultPlans)
                .isEmpty();

    }



    @Test
    @DisplayName("测试全部样片的自动Plan 优先级最高，且无前置条件，但是样片不足")
    void fetchNeedTestPlan_given_ge_with_haveLittleSamples_shouldReturn_empty_planList(){
        // 样片不足
        flashEntity.setLeftNum(10);

        // all 的自动Plan 清除依赖
        planEntityList.stream()
                .filter(OrderPlanEntity::isTestAll)
                .filter(OrderPlanEntity::isAutoPlan)
                .findFirst()
                .ifPresent(p->{
                    p.setPriority(Integer.MAX_VALUE);
                    p.setParentPlan("");
                });
        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);

        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        // then
        assertThat(resultPlans).isEmpty();

    }

    @Test
    @DisplayName("测试全部样片的自动Plan 优先级最高，且无前置条件")
    void fetchNeedTestPlan_given_ge_with_haveLeftPlanCount_shouldReturn_not_empty_planList(){
        // 剩余全部样片
        flashEntity.setLeftNum(100);
        // all 的自动Plan 清除依赖
        OrderPlanEntity planEntity = planEntityList.stream()
                .filter(OrderPlanEntity::isTestAll)
                .filter(OrderPlanEntity::isAutoPlan)
                .findFirst().
                orElse(null);
        assertThat(planEntity).isNotNull();

        planEntity.setPriority(Integer.MAX_VALUE);
        planEntity.setParentPlan(null);

        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);

        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        // then
        assertThat(resultPlans)
                .hasSize(1)
                .first()
                .isEqualTo(planEntity);

    }


    @Test
    @DisplayName("剩余的样片不足以让任何Plan测试起来")
    void fetchNeedTestPlan_given_ge_with_haveLittleSamples_and_lastAllPlan_is_running_shouldReturn_empty_planList(){
        // given
        // 只剩比较少的样片
        flashEntity.setLeftNum(1);
        // 将一个all的自动Plan设置为正在测试中，其他的all的plan 都设置为已完成
        boolean flag = false ;
        for (OrderPlanEntity planEntity : planEntityList) {
            if (planEntity.isTestAll()) {
                if (planEntity.isAutoPlan() && !flag) {
                    planEntity.setStatus(RUNNING);
                    flag = true;
                } else {
                    planEntity.setStatus(COMPLETED);
                }
            }
        }


        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);

        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        // then
        assertThat(resultPlans)
                .isEmpty();

    }


    @Test
    @DisplayName("剩余部分样片，足够优先级低的Plan测试, 但是不够优先级高的Plan测试")
    void fetchNeedTestPlan_given_ge_with_haveLittleSamples_that_lowPriorityPlanCanTest_shouldReturn_empty_planList(){
        // 将所有样片的Plan 都设置为测试完成、
        planEntityList.stream().filter(OrderPlanEntity::isTestAll).forEach(planEntity -> planEntity.setStatus(COMPLETED));

        // 将样片数量比较少的Plan设置为 优先级最低
        planEntityList.stream()
                .min(Comparator.comparingInt(OrderPlanEntity::getTestNum))
                .ifPresent(planEntity -> {
                    planEntity.setPriority(1);
                    planEntity.setTestNum(1);
                    // 只剩比较少的样片
                    flashEntity.setLeftNum(1);
                });


        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);

        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        // then
        // 这种情况下,不分配,严格按照优先级分配
        assertThat(resultPlans)
                .isEmpty();

    }


    @Test
    @DisplayName("剩余部分样片，足够优先级低的Plan测试, 且优先级高的Plan依赖此低优先级的Plan")
    void fetchNeedTestPlan_given_ge_with_haveLittleSamples_that_lowPriorityPlanCanTest_and_highPriorityPlanDependOnLowPriorityPlan_shouldReturn_lowPriorityPlan() {
        // given
        // 将一个all的自动Plan设置为正在测试中，其他的all的plan 都设置为已完成
        boolean flag = false;
        for (OrderPlanEntity planEntity : planEntityList) {
            if (planEntity.isTestAll()) {
                if (planEntity.isAutoPlan() && !flag) {
                    planEntity.setStatus(RUNNING);
                    flag = true;
                } else {
                    planEntity.setStatus(COMPLETED);
                }
            }
        }
        // Flash 批次只剩余最少的一个Plan的样片
        OrderPlanEntity minTestNumPlan = planEntityList.stream().min(Comparator.comparingInt(OrderPlanEntity::getTestNum)).orElse(null);
        assertThat(minTestNumPlan).isNotNull();

        // 将所需最少样片的Plan的优先级设置为比较低
        minTestNumPlan.setPriority(10);

        // 并将高优先级的Plan设置为依赖此低优先级的Plan
        planEntityList.stream()
                .filter(p -> !p.isTestAll())
                .max(Comparator.comparingInt(OrderPlanEntity::getPriority))
                .ifPresent(highPriorityPlan -> highPriorityPlan.setParentPlan(minTestNumPlan.getName()));
        flashEntity.setLeftNum(minTestNumPlan.getTestNum());


        planEntityList.forEach(planEntity -> log.info("-- {} -- isManual:{} -- testNum:{}  -- priority:{} -- parent:{}",
                planEntity.getName(),
                planEntity.isManualPlan(),
                planEntity.getTestNum(),
                planEntity.getPriority(),
                planEntity.getParentPlan()
        ));

        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);

        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        // 这种情况下返回优先级低的Plan
        assertThat(resultPlans)
                .isNotEmpty()
                .hasSize(1)
                .first()
                .isEqualTo(minTestNumPlan);

    }

    @Test
    @DisplayName("可测试Plan数超过待确认额度时，只分配额度数量的Plan")
    void fetchNeedTestPlan_given_ge_with_enoughPlan_shouldReturn_5_plans(){
        //给与足够的样片
        flashEntity.setLeftNum(100) ;

        // 将所有样片的Plan 都设置为测试完成、
        planEntityList.stream().filter(OrderPlanEntity::isTestAll).forEach(planEntity -> planEntity.setStatus(COMPLETED));

        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);

        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        // 这种情况下返回的Plan列表应该是5个
        assertThat(resultPlans)
                .isNotEmpty()
                .hasSize(5);
    }

    @Test
    @DisplayName("可测试Plan数超过待确认额度，且存在前置的Plan")
    void fetchNeedTest_given_ge_with_enoughPlan_and_havePrePlan_shouldReturn_3_plans(){
        //给与足够的样片
        flashEntity.setLeftNum(100) ;

        // 将所有样片的Plan 都设置为测试完成、
        planEntityList.stream().filter(OrderPlanEntity::isTestAll).forEach(planEntity -> planEntity.setStatus(COMPLETED));

        // 将前两个优先级最高的非all Plan设置为Ready 状态
        planEntityList.stream()
                .filter(p -> !p.isTestAll())
                .sorted(Comparator.comparingInt(OrderPlanEntity::getPriority).reversed())
                .limit(2)
                .forEach(planEntity -> planEntity.setStatus(READY));

        List<OrderPlanEntity> limit4PlanList = planEntityList.stream()
                .filter(p -> !p.isTestAll())
                .filter(p -> p.getStatus() != READY)
                .sorted(
                        Comparator.comparing(OrderPlanEntity::getPriority)
                        .reversed()
                        .thenComparing(Comparator.comparing(OrderPlanEntity::isTestAll).reversed())
                        .thenComparing(OrderPlanEntity::getName)
                )
                .limit(4)
                .collect(Collectors.toList());

        OrderPlanEntity lastPlan = limit4PlanList.get(3);
        OrderPlanEntity randomPlan = limit4PlanList.get(2);
        randomPlan.setParentPlan(lastPlan.getName());

        planEntityList.stream()
                .filter(p -> !p.isTestAll())
                .filter(p -> p.getStatus() != READY)
                .sorted(Comparator.comparingInt(OrderPlanEntity::getPriority).reversed())
                .forEachOrdered(planEntity -> log.info("-- {} -- isManual:{} -- testNum:{}  -- priority:{} -- parent:{}",
                        planEntity.getName(),
                        planEntity.isManualPlan(),
                        planEntity.getTestNum(),
                        planEntity.getPriority(),
                        planEntity.getParentPlan()
                ));



        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);

        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;

        // 这种情况下返回的Plan列表应该是5个
        assertThat(resultPlans)
                .isNotEmpty()
                .hasSize(3)
                .contains(lastPlan)
                .doesNotContain(randomPlan)
        ;
    }


    @Test
    @DisplayName("剩余优先级高的非全部样片的Plan以及优先级低的全部样片的自动Plan")
    void fetchNeedTestPlan_give_ge_with_lowPriorityAllPlan_and_highPriorityNotAllPlan_shouldReturn_highPriorityNotAllPlan(){
        //给与足够的样片
        flashEntity.setLeftNum(100) ;

        // 将一个全部样片的Plan 设置为优先级最低，其他的全部样片设置为测试完成
        planEntityList.stream().filter(OrderPlanEntity::isTestAll).forEach(planEntity -> planEntity.setStatus(COMPLETED));
        OrderPlanEntity lowPriorityPlan = planEntityList.stream().filter(OrderPlanEntity::isTestAll).filter(OrderPlanEntity::isAutoPlan).findFirst().orElse(null);
        assertThat(lowPriorityPlan).isNotNull();
        lowPriorityPlan.setPriority(1);
        lowPriorityPlan.setStatus(QUEUE);


        // 将一个非全部样片的Plan 设置为优先级最高
        OrderPlanEntity highPriorityPlan = planEntityList.stream().filter(p -> !p.isTestAll()).filter(OrderPlanEntity::isAutoPlan).findFirst().orElse(null);
        assertThat(highPriorityPlan).isNotNull();
        highPriorityPlan.setPriority(75);
        highPriorityPlan.setStatus(QUEUE);


        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);
        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;
        assertThat(resultPlans)
                .isNotEmpty()
                .hasSizeGreaterThan(1)
                .first()
                .isEqualTo(highPriorityPlan);
    }


    @Test
    @DisplayName("全部样片的Plan 优先级处于队列中第二个")
    void fetchNeedTestPlan_given_ge_with_firstNotAllPlan_and_secondAllPlan_shouldReturn_firstNotAllPlan(){
        //给与足够的样片
        flashEntity.setLeftNum(100) ;
        // 将一个全部样片的手动 Plan 设置为优先级第二，其他的全部样片设置为测试完成
        planEntityList.stream().filter(OrderPlanEntity::isTestAll).forEach(planEntity -> planEntity.setStatus(COMPLETED));
        OrderPlanEntity secondAllPlan = planEntityList.stream().filter(OrderPlanEntity::isTestAll).filter(OrderPlanEntity::isManualPlan).findFirst().orElse(null);
        assertThat(secondAllPlan).isNotNull();
        secondAllPlan.setPriority(Integer.MAX_VALUE - 100);
        secondAllPlan.setStatus(QUEUE);


        // 将一个非全部样片的Plan 设置为优先级最高
        OrderPlanEntity firstNotAllPlan = planEntityList.stream().filter(p -> !p.isTestAll()).filter(OrderPlanEntity::isAutoPlan).findFirst().orElse(null);
        assertThat(firstNotAllPlan).isNotNull();
        firstNotAllPlan.setPriority(Integer.MAX_VALUE);
        firstNotAllPlan.setStatus(QUEUE);


        // mock
        when(flashService.findFlashOrElseThrow(orderId,flash)).thenReturn(flashEntity) ;
        given(planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash)).willReturn(planEntityList);
        // when
        List<OrderPlanEntity> resultPlans = planService.fetchNeedTestPlanList(subProduct, orderId,flash) ;
        assertThat(resultPlans)
                .isNotEmpty()
                .hasSize(1)
                .first()
                .isEqualTo(firstNotAllPlan);
    }


}