.order-info {
  font-weight: 500;
  font-size: 15px;
  font-family: PingFangSC, 'helvetica neue', 'hiragino sans gb', arial, 'microsoft yahei ui',
    'microsoft yahei', simsun, sans-serif;

  .order-no {
    color: #8c1b1a;
    font-weight: 600;
    font-size: 14px;
  }

  .order-status {
    font-weight: 600;
  }

  .relate {
    display: flex;

    .relate-item {
      width: 75px;
    }

    .relate-scroll {
      max-height: 124px;
      padding: 0 16px;
      overflow-y: scroll;
      border-radius: 4px;
      box-shadow: inset rgba(100, 116, 139, 16%) 0 2px 6px, inset rgba(100, 116, 139, 20%) 0 2px 8px;
    }

    .order-relate {
      color: #1677ff;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .order-flash {
    color: #8c1b1a;
    font-weight: 600;
    font-size: 14px;
  }

  .order-chip {
    color: #8c1b1a;
    font-weight: 600;
    font-size: 14px;
  }

  .order-version {
    margin: 0;
    padding-right: 30px;
    color: #8c1b1a;
    font-weight: 600;
    font-size: 14px;
    word-break: break-all;
  }

  .bug-link-body {
    display: flex;
    display: inline-flex;
    flex-direction: row;
    flex-wrap: wrap;
    max-width: 380px;

    .bug-link {
      height: 20px;
      margin: 0 8px 6px 0;
      padding: 0 6px;
      font-weight: 600;
      font-size: 14px;
      line-height: 16px;
      border: 2px solid #1890ff;
      border-radius: 4px;
    }
  }

  .test-task {
    padding: 0 6px;
    font-weight: 600;
    font-size: 14px;
    border: 2px solid #1890ff;
    border-radius: 4px;
  }
}
