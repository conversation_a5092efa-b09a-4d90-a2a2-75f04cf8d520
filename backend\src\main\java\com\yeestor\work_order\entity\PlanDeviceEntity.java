package com.yeestor.work_order.entity;

import com.yeestor.entity.BaseEntity;
import com.yeestor.work_order.entity.device.DeviceControlEntity;
import com.yeestor.work_order.entity.device.DeviceDiskEntity;
import com.yeestor.work_order.entity.device.DeviceLockInfoEntity;
import com.yeestor.work_order.model.base.DeviceBaseInfo;
import com.yeestor.work_order.utils.DingTalkUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.util.List;

/**
 * 工单 - plan
 */
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity(name = "PlanDevice")
@Table(name="wo_plan_device")
public class PlanDeviceEntity extends BaseEntity implements DeviceBaseInfo {

    public enum Status {
        /**
         * 已占用
         */
        OCCUPIED,

        /**
         * 环境已确认.
         */
        CONFIRMED,

        /**
         * 正在运行中。
         */
        RUNNING,

        /**
         * 已结束 , 成功
         */
        FINISHED_SUCCESS,
        /**
         * 已结束 ， 失败
         */
        FINISHED_FAILED,

        /**
         * 已取消
         */
        CANCELED
    }

    /**
     * 只有这些状态才算是正常结束
     */
    public static final List<Status> FINISHED_STATUS_LIST = List.of(Status.FINISHED_SUCCESS, Status.FINISHED_FAILED);
    public static final List<Status> OCCUPY_STATUS_LIST = List.of(Status.OCCUPIED, Status.CONFIRMED);
    public static final List<Status> COMPLETED_STATUS_LIST = List.of(Status.FINISHED_SUCCESS, Status.FINISHED_FAILED, Status.CANCELED);
    public static final List<Status> FIXED_STATUS_LIST = List.of(
            Status.CONFIRMED,
            Status.RUNNING
    );


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "order_id")
    private long orderId ;


    @Column(name = "plan_id")
    private Long planId ;

    /**
     * plan 的名称。主要用于快速查询。
     */
    @Column(name = "plan_name")
    private String planName ;

    @Column(name = "ip")
    private String ip ;

    /**
     * mac 地址
     */
    @Column(name = "mac")
    private String mac ;

    /**
     * pc 编号。
     */
    @Column(name = "no")
    private String no ;

    /**
     * pc 的位置
     */
    @Column(name = "position")
    private String position ;

    /**
     * pc 的分数
     */
    @Column(name = "score")
    private int score  ;

    @Column(name = "owner",length = 64)
    private String owner ;


    /**
     * pc 测试的数量 ， 通常情况是与pc能测试的数量一致，但是有时候会有不同的情况。
     */
    @Column(name = "test_num")
    private int testNum ;

    /**
     * 设备实际测试的数量。
     */
    @Column(name = "actual_num")
    private Integer actualNum ;

    /**
     * plan 设备数据的版本。旧版本的数据, 只是用于追溯，并不做任何处理。
     */
    @Column(name = "version")
    private int version ;

    /**
     * 设备的状态 , 可以是已经占用，已经结束，已经取消。
     */
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private Status status ;

    /**
     * 测试失败的原因。当测试失败时，这个字段会有值。
     */
    @Column(name = "fail_reason")
    private String failReason ;

    /**
     * 设备是否已经释放， 如果没有释放，这个值为空， 如果释放了，就是 释放的时间。
     */
    @Column(name = "release_at")
    private Long releaseAt ;

    /**
     * 设备由谁来释放的，如果是人为操作的话，这个值为其 钉钉ID
     */
    @Column(name = "release_by")
    private String releaseBy ;

    /**
     * 释放人的名称。
     */
    @Column(name = "release_p")
    private String releasePerson ;

    /**
     * 测试的开始时间
     */
    @Column(name = "start_at")
    private Long startAt ;

    /**
     * 测试的结束时间
     */
    @Column(name = "end_at")
    private Long endAt ;

    /**
     * 机器的确认时间
     */
    @Column(name = "confirm_at")
    private Long confirmAt ;


    /**
     * 手动结束测试的时间。
     */
    @Column(name = "terminate_at")
    private Long terminateAt ;

    /**
     * 终止测试的人员，钉钉ID
     */
    @Column(name = "terminate_by")
    private String terminateBy ;

    /**
     * 终止测试的人员 名称
     */
    @Column(name = "terminate_p")
    private String terminatePerson ;


    @Column(name = "added_by")
    private String addedBy;

    @Column(name = "added_p")
    private String addedPerson ;

    /**
     * 设备最后一次的操作人。
     */
    private String operator ;

    public PlanDeviceEntity duplicate(){
        PlanDeviceEntity deviceEntity = new PlanDeviceEntity();
        deviceEntity.orderId = orderId ;
        deviceEntity.planId = planId ;
        deviceEntity.planName = planName ;
        deviceEntity.ip = ip ;
        deviceEntity.mac = mac ;
        deviceEntity.no = no ;
        deviceEntity.position = position ;
        deviceEntity.score = score ;
        deviceEntity.owner = owner ;
        deviceEntity.testNum = testNum ;
        deviceEntity.actualNum = actualNum ;
        deviceEntity.version = version ;
        deviceEntity.status = status ;
        deviceEntity.failReason = failReason ;
        deviceEntity.releaseAt = releaseAt ;
        deviceEntity.releaseBy = releaseBy ;
        deviceEntity.releasePerson = releasePerson ;
        deviceEntity.startAt = startAt ;
        deviceEntity.endAt = endAt ;
        deviceEntity.confirmAt = confirmAt ;
        deviceEntity.terminateAt = terminateAt ;
        deviceEntity.terminateBy = terminateBy ;
        deviceEntity.terminatePerson = terminatePerson ;
        deviceEntity.operator = operator ;
        deviceEntity.addedBy = addedBy ;
        deviceEntity.addedPerson = addedPerson ;
        deviceEntity.setCreatedAt(getCreatedAt());
        deviceEntity.setUpdatedAt(getUpdatedAt());
        return deviceEntity ;
    }

    public PlanDeviceEntity duplicateEmpty(){
        PlanDeviceEntity newDeviceEntity = duplicate() ;
        newDeviceEntity.setEndAt(null);

        newDeviceEntity.setTerminateAt(null);
        newDeviceEntity.setTerminateBy(null);
        newDeviceEntity.setTerminatePerson(null);

        newDeviceEntity.setReleaseAt(null);
        newDeviceEntity.setReleaseBy(null);
        newDeviceEntity.setReleasePerson(null);

        newDeviceEntity.setUpdatedAt(System.currentTimeMillis());
        return newDeviceEntity ;

    }


    @Transient
    public boolean isSameDevice(PlanDeviceEntity deviceEntity){
        return this.ip.equals(deviceEntity.ip) && this.no.equals(deviceEntity.no) ;
    }

    @Transient
    public boolean isSameDevice(DeviceBaseInfo deviceInfo) {
        // 如果都有mac地址，那么就用mac地址来判断。
        if (StringUtils.hasText(this.mac) && StringUtils.hasText(deviceInfo.getMac())) {
            return this.mac.equalsIgnoreCase(deviceInfo.getMac());
        }
        return this.ip.equalsIgnoreCase(deviceInfo.getIp()) ;
    }


    @Transient
    public boolean isFailed() {
        return this.status == Status.FINISHED_FAILED ;
    }

    @Transient
    public boolean isOccupied(){
        return this.status == Status.OCCUPIED || this.status == Status.CONFIRMED ;
    }

    @Transient
    public boolean isNotFinished(){
        return !FINISHED_STATUS_LIST.contains(this.status) ;
    }


    public void updateInfo(int testNum, String planName){
        this.testNum = testNum ;
        this.planName = planName ;
        this.setUpdatedAt(System.currentTimeMillis());
    }

    public DeviceDiskEntity convert2DiskEntity(
            String flash ,
            String requestUUID,
            String traceId
    ){
        DeviceDiskEntity diskEntity = new DeviceDiskEntity();
        diskEntity.setOrderId(orderId);
        diskEntity.setFlash(flash);
        diskEntity.setPlanId(planId);
        diskEntity.setRequestId(requestUUID);
        diskEntity.setTraceId(traceId);
        diskEntity.setDeviceId(id);
        diskEntity.setMac(mac);
        diskEntity.setIp(ip);
        diskEntity.setNo(no);
        diskEntity.setTestNum(testNum);
        return diskEntity ;
    }


    public DeviceLockInfoEntity convert2LockInfoEntity(){
        DeviceLockInfoEntity lockInfoEntity = new DeviceLockInfoEntity();
        lockInfoEntity.setCreatedAt(System.currentTimeMillis());
        lockInfoEntity.setUpdatedAt(System.currentTimeMillis());
        lockInfoEntity.setOrderId(orderId);
        lockInfoEntity.setPlanId(planId);
        // device info
        lockInfoEntity.setDeviceId(id);
        lockInfoEntity.setMac(mac);
        lockInfoEntity.setIp(ip);
        lockInfoEntity.setPosition(position);
        lockInfoEntity.setNo(no);
        lockInfoEntity.setLocked(true);

        return lockInfoEntity ;
    }

    public DeviceControlEntity convert2ControlEntity(String title, String flash, String userName){
        DeviceControlEntity entity = new DeviceControlEntity();
        entity.setTitle(title);
        entity.setCreatedAt(System.currentTimeMillis());
        entity.setOrderId(orderId);
        entity.setFlash(flash);
        entity.setPlanId(planId);
        entity.setDeviceId(id);
        entity.setMac(mac);
        entity.setIp(ip);
        entity.setNo(no);
        entity.setOperator(userName);
        return entity;
    }

}
