package com.yeestor.work_order.model.http.req;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateFlashInfoReq", description = "修改Flash信息的时候传递的参数")
public class UpdateFlashInfoReq {


    @Min(value = 0,message = "添加的数量不能小于0")
    @ApiModelProperty(value = "添加样片的数量", required = true)
    private int addCount ;

    @ApiModelProperty(value = "添加测试干系人列表")
    private String addTestPerson ;
}
