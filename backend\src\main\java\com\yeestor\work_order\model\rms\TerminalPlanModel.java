package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
@Builder
@JsonAutoDetect
@ApiModel(value = "TerminalPlanModel",description = "终端测试Plan 的信息")
@NoArgsConstructor
@AllArgsConstructor
public class TerminalPlanModel {
    @ApiModelProperty("plan的编号")
    private String name;

    @ApiModelProperty("plan 测试的功能")
    private String feature;

    @ApiModelProperty("plan所具备的属性，可由case的属性直接组合而成")
    private List<String> attrs;

    @ApiModelProperty("plan包含的脚本合集，有优先级，依次执行")
    private List<String> caseList;

    @ApiModelProperty("所属产品线，预留字段，可不传")
    private String product;

    @ApiModelProperty("所属产品，预留字段，可不传")
    private String subProduct;

    @ApiModelProperty("工具名称，预留字段，可不传")
    private String toolName;

    @ApiModelProperty(value = "Plan类型", hidden = true)
    private String type;

    @ApiModelProperty(value = "plan的阶段", hidden = true)
    private OrderPlanEntity.Phase phase;

    @ApiModelProperty(value = "操作人的钉钉ID", hidden = true)
    private String belongTo;

    @ApiModelProperty(value = "操作人的钉钉ID", hidden = true)
    private String belongToPerson;

    public OrderPlanEntity toEntity(long orderId, OrderFlashEntity flashEntity) {
        OrderPlanEntity entity = new OrderPlanEntity();
        entity.setCreatedAt(System.currentTimeMillis());
        entity.setName(name);
        entity.setFeature(feature);
        entity.setPhase(phase);
        entity.setType(1);
        entity.setAttrs(String.join(";", Optional.ofNullable(attrs).orElse(new ArrayList<>())));
        entity.setTestNum(0);
        entity.setPriority(0);
        entity.setNeedReport(false);
        entity.setBelongTo(belongTo);
        entity.setBelongToPerson(belongToPerson);
        entity.setTestAll(false);
        entity.setOrderId(orderId);
        entity.setFlash(flashEntity.getFlash());
        entity.setReadyAt(System.currentTimeMillis());
        entity.setConfirmedAt(System.currentTimeMillis());
        entity.setConfirmedPerson("System");
        entity.setStatus(OrderPlanEntity.Status.CONFIRMED);
        entity.setPlanType(type);
        entity.setScripts(String.join(";", Optional.ofNullable(caseList).orElse(new ArrayList<>())));
        return entity;
    }

}
