///<reference types="cypress" />

// 操作工单号 WO_YS6285##MPTOOL##2170##YMTC-TAS####220210
// 需要操作的工单id
const opeateOrderId = 363;
const tabList = ['flashname01_32GB', 'flashname02_4GB', 'overview'];
const tabFlash = ['flashname01_32GB'];
const planList = [];
context('with flash operate', () => {
  describe('flash-operate', () => {
    beforeEach(() => {
      cy.viewport(1920, 960);
      cy.visit(`http://172.18.11.245:8512/work_order/GE/SD/${opeateOrderId}`).wait(5000);
    });

    // it('flash tab', () => {
    //     tabList.forEach((item: string) => {
    //         cy.get('#flash-tab-' + item).click().wait(1000);
    //     });
    // });

    it('flash 暂缓分配与开始分配', () => {
      tabFlash.forEach((item: string) => {
        const addList = ['Plan7', 'Plan8', 'Plan44'];
        const changePlanList = ['Plan4', 'Plan8'];
        cy.get('#flash-tab-' + item)
          .click()
          .wait(1000);
        // addPlan(addList);
        // flashPause();
        // resumePause();
        changePlanPriority(changePlanList);
      });
    });
  });
});

// 增加plan
const addPlan = function (list: string[]) {
  cy.get('#flash-operate-btn').click().get('#flash-add-plan-btn').click();

  // 类型选择与plan 名称选择
  cy.get('#id-plan-accept').click();
  list.forEach((name: string) => {
    cy.get(`#id-plan-name-${name}`).click();
  });
  // cy.get('#flash-cancle-submit-btn').wait(1000).click()  //取消
  cy.get('#flash-operate-submit-btn').wait(1000).click(); //  确认
};

// 暂缓分配
const flashPause = function () {
  cy.get('#flash-operate-btn').click().get('#flash-pause-plan-btn').click().wait(3000);

  cy.get('span#flash-top-status').contains('暂缓分配');
};

// 开始分配
const resumePause = function () {
  cy.get('#flash-operate-btn').click().get('#flash-resume-plan-btn').click().wait(3000);

  cy.get('span#flash-top-status').contains('正在分配');
};

// 修改优先级
const changePlanPriority = function (list: string[]) {
  cy.get('#flash-operate-btn').click().get('#flash-change-plan-btn').click().wait(1000);
  list.forEach((name: string) => {
    cy.get(`#change-priority-name-${name}`).clear().type('80');
  });
  cy.get('#priority-change-btn').click().wait(2000); // 确认修改

  list.forEach((name: string) => {
    cy.get(`.${name}`).contains('80');
  });
};
