package com.yeestor.work_order.entity.device;

import com.yeestor.entity.BaseEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.model.rms.AttrModel;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Data
@RequiredArgsConstructor
@Entity(name = "OrderDeviceTestHistory")
@Table(name="wo_device_test_history")
public class DeviceTestHistoryEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "ip")
    private String ip;

    /**
     * mac 地址
     */
    @Column(name = "mac")
    private String mac;

    /**
     * pc 编号。
     */
    @Column(name = "no")
    private String no;

    /**
     * pc 的位置
     */
    @Column(name = "position")
    private String position;

    @Column(name = "plan_name")
    private String planName;

    @Column(name = "plan_feature")
    private String planFeature;

    /**
     * 设备测试使用次数
     */
    @Column(name = "use_Num")
    private int useNum;

    /**
     * 设备测试使用者
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 设备测试使用者钉钉id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 工单所属产品线
     */
    @Column(name = "product")
    private String product;

    /**
     * 工单所属产品
     */
    @Column(name = "sub_product")
    private String subProduct;

    @Transient
    public List<AttrModel> attrModelList = new ArrayList<>();

    public DeviceTestHistoryEntity toEntity(OrderPlanEntity planEntity, PlanDeviceEntity deviceEntity, String product, String subProduct){
        DeviceTestHistoryEntity testInfo = new DeviceTestHistoryEntity();
        testInfo.ip = deviceEntity.getIp();
        testInfo.mac = deviceEntity.getMac();
        testInfo.no = deviceEntity.getNo();
        testInfo.position = deviceEntity.getPosition();
        testInfo.product = product;
        testInfo.subProduct = subProduct;
        testInfo.useNum = 1;
        testInfo.userName = planEntity.getBelongToPerson();
        testInfo.userId = planEntity.getBelongTo();
        testInfo.planName = planEntity.getName();
        testInfo.planFeature = planEntity.getFeature();
        return testInfo;
    }

    public int getNumber() {
        if(!position.contains("_")){
            return Integer.MAX_VALUE;
        }
        // Extract the number part of the string, e.g., "SSD5_5_23" -> 23
        int lastIndex = position.lastIndexOf("_");
        return Integer.parseInt(position.substring(lastIndex + 1));
    }
}
