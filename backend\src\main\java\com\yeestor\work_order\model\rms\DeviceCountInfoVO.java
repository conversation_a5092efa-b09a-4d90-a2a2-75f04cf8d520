package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 设备数量分布
 */
@Data
@Builder
@JsonAutoDetect
@ApiModel(value= "DeviceCountInfoVO", description= "设备数量分布")
public class DeviceCountInfoVO {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SimpleDeviceInfo {

        /**
         * 产品线 ,诸如,SD,U2,U3等等, 对应着系统中的subProduct
         */
        @ApiModelProperty("产品线")
        private String product ;

        @ApiModelProperty("PC 的编号")
        private String pcNo ;

        /**
         * 测试机的ip 地址
         */
        @ApiModelProperty("测试机的IP地址")
        private String ip ;

        /**
         * 测试机的mac 地址
         */
        @ApiModelProperty("测试机的MAC地址")
        private String mac ;

        @ApiModelProperty("测试机的位置")
        private String position ;

        /**
         * 机器的属性列表
         */
        @ApiModelProperty("测试机所包含的属性列表")
        private List<AttrModel> attrModelList ;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        @ApiModelProperty("机器所属的Plan。")
        private String plan ;


        @ApiModelProperty("工单ID")
        private long orderId ;

        @ApiModelProperty("plan ID")
        private long planId ;

        @ApiModelProperty("Flash 批次")
        private String flash ;

        @ApiModelProperty("设备当前数据中的状态")
        private String status ;

    }

    @ApiModelProperty(value = "对应的产品线")
    private String product ;
    @ApiModelProperty(value = "空闲数量")
    private int idle ;
    @ApiModelProperty(value = "使用中数量")
    private int busy ;

    @ApiModelProperty(value = "空闲中的设备列表")
    private List<SimpleDeviceInfo>  idleDeviceList ;

    @ApiModelProperty(value = "使用中的设备列表")
    private List<SimpleDeviceInfo>  busyDeviceList ;

    @ApiModelProperty(value = "未在工单中使用,但是在测试中的设备.")
    private List<SimpleDeviceInfo> unkownDeviceList ;


    public static SimpleDeviceInfo of(DeviceModel deviceModel, String orderFlashNo , String plan) {
        return SimpleDeviceInfo.builder()
                .product(deviceModel.getProduct())
                .pcNo(deviceModel.getPcNo())
                .ip(deviceModel.getIp())
                .mac(deviceModel.getMac())
                .position(deviceModel.getPosition())
                .attrModelList(deviceModel.getAttrModelList())
                .plan(plan)
                .build();
    }

}
