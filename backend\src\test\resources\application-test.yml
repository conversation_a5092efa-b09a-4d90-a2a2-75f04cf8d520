server:
  port: 8793

management:
  endpoints:
    web:
      exposure:
        include: '*'

spring:
  application:
    name: system-workorder-test
    title: 工单系统-测试
  security:
    oauth2:
      client:
        client-id: system-workorder
        client-secret: 123456
        access-token-uri: http://***********:8789/auth/oauth/token
      resource:
        token-info-uri: http://***********:8789/auth/oauth/check_token
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  main:
    allow-bean-definition-overriding: true
  boot:
    admin:
      client:
        url: http://***********:8788/admin
  datasource:
    url: jdbc:h2:mem:testdb

  h2:
    console:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: 'update'
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  redis:
    database: 1
    host: ***********
    port: 6379
    pool:
      max-active: -1
      max-idle: 10
      max-wait: 20
      min-idle: 0
    timeout: 1000

  data:
    redis:
      repositories:
        enabled: false

  quartz:
    enable: false
    auto-startup: false
    job-store-type: memory

eureka:
  client:
    serviceUrl:
      defaultZone: http://***********:8788/eureka/

  instance:
    preferIpAddress: true
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}

logging:
  level:
    com.yeestor.work_order: TRACE
    org.hibernate.SQL: TRACE
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.jdbc.core.JdbcTemplate: DEBUG
    org.springframework.jdbc.core.StatementCreatorUtils: TRACE
    org.springframework.web: INFO
    org.springframework.web.HttpLogging: DEBUG
    org.springframework.security: DEBUG
    org.springframework.security.oauth2: DEBUG
