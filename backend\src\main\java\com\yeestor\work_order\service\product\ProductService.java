package com.yeestor.work_order.service.product;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.rms.DeviceModel;
import com.yeestor.work_order.model.rms.PlanStatusChangeParams;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 和产品相关的服务，用于在实际中的各个产线功能差异的实现。
 */

public interface ProductService {

    /**
     *  RMS 中的Plan状态回调接口
     * @param orderEntity 工单实体
     * @param flashEntity Flash批次实体
     * @param planEntity Plan实体
     * @param params 回调参数
     */
    default void onPlanStatusChange(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            PlanStatusChangeParams params
    ) {
        LoggerHolder.error("DefaultProductService.onPlanStatusChange is not implemented -- {} ",flashEntity.getOrderFlashNo());
    }

    /**
     * Plan 状态回调接口中的自动完成Plan 处理
     * @param flashEntity
     * @param planEntity
     */
    default void autoCompletePlan(WorkOrderEntity orderEntity,OrderFlashEntity flashEntity, OrderPlanEntity planEntity){
        LoggerHolder.error("DefaultProductService.autoCompletePlan not implemented -- {}",flashEntity.getOrderFlashNo());
    }

    default void peekDeviceOnPlanStatusChange(OrderFlashEntity flashEntity, OrderPlanEntity planEntity, PlanDeviceEntity deviceEntity) {}

    /**
     * Plan状态回调时，各个产品线可能会存在的自定义行为。
     * @param subProduct 产品
     * @param orderFlashNo rms 工单号
     * @param planEntity Plan实体
     * @param devices 设备列表
     */
    default int deviceHandlerOnPlanSuccess(String subProduct, String orderFlashNo , OrderPlanEntity planEntity, List<PlanDeviceEntity> devices){
        return 0;
    }

    /**
     * 各个产品线的设备的排序规则
     * @return 排序器
     */
    default Comparator<DeviceModel> deviceComparator() {
        return Comparator.comparing(DeviceModel::getScore);
    }


    default void assignDevices(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity){

    }

    List<OrderPlanEntity> fetchNeedTestPlanList(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity);




    /**
     * 处理测试所有样片的Plan的设备分配.
     * @param orderId 工单ID
     * @param planEntity Plan 实体
     * @param devices 可用设备列表
     *
     * @return 需要分配的设备列表,如果返回空列表, 则表示没有可用设备,可以不用处理.
     */
    default List<DeviceModel> handleTestAllPlan(long orderId, OrderPlanEntity planEntity, List<DeviceModel> devices) {
        return new ArrayList<>();
    }
}
@Slf4j
final class LoggerHolder{
    private LoggerHolder(){}
    public static void error(String format, Object... arguments){
        log.error(format,arguments);
    }

}