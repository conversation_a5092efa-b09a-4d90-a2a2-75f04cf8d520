///<reference types="cypress" />

// context('with plan operate', () => {
//     describe('plan-operate', () => {
//         // 需要操作的工单id
//         const orderId = 363;
//         const flashName = 'flashname01_32GB';
//         const planName = 'Plan2';
//         beforeEach(() => {
//           cy.viewport(1920, 960);
//           cy.visit(`http://172.18.11.245:8512/work_order/GE/SD/${orderId}`).wait(5000);
//         });

//         it('取消Plan测试', () => {
//           cy.get('#flash-tab-' + flashName)
//             .click()
//             .wait(1000);
//           cancelPlan(planName);
//         });
//     });
// });

const cancelPlan = function (name: string) {
  cy.get(`a#plan-operate-menu-${name}`).click().get(`button#plan-stop-${name}`).click().wait(2000);

  cy.get(`span#plan-stop-submit-confirm`).click().wait(6000);
  //  检验是否取消测试成功
  cy.get(`img#plan-view-${name}`).click().get(`div#plan-view-status-${name}`).contains('停止');
  cy.get(`span#plan-view-title-${name}`).click();
};
export { cancelPlan };
