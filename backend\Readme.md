# 工单系统

## Tag 规则
创建Tag 需要满足一下规则:
1. Tag 分为4~5个部分, 分别代表前缀, Major(主版本号), <PERSON><PERSON> (日期),<PERSON>(子版本号), 预发布标识符(可选)
2. 前缀：每个标签都应以字符 "v" 开始。
3. 主版本号："v" 之后应跟着一个或多个数字，这是主版本号。例如，在 "v1.0525.01" 中，主版本号是 1。
4. 日期：主版本号后面应该有一个四位数字的日期，这个日期由一个点 "." 分隔。例如，在 "v1.0525.01" 中，日期是 0525。
5. 子版本号：日期之后应该有一个两位数的子版本号，这个子版本号由另一个点 "." 分隔。例如，在 "v1.0525.01" 中，子版本号是 01。
6. 预发布标识符（可选）：标签可以选择性地包含一个预发布标识符，这个标识符是 "-alpha"、"-beta" 或 "-rc"。这些标识符分别代表内部测试版本（alpha），公测版本（beta），以及发布候选版本（release candidate）。

其中, 当`alpha`版本只会在`alpha`环境下自动部署,其他的都会在`alpha` 以及`prod`环境下部署.
