package com.yeestor.work_order.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.service.NotificationService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.text.SimpleDateFormat;
import java.util.*;

class OrderInteractiveModelTest {

    NotificationService.OrderInteractiveModel model ;
    ObjectMapper mapper = new ObjectMapper();

    @BeforeEach
    void initModel(){

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<NotificationService.OrderFlashInteractiveModel> flashModelList = new ArrayList<>();
        NotificationService.OrderFlashInteractiveModel flashModel = new NotificationService.OrderFlashInteractiveModel();
        flashModel.setFlash("3DV4_B27A(U2)_89C41832A2");
        flashModel.setNum(20);
        flashModel.setPlanNum(10);
        flashModel.setStartAt(System.currentTimeMillis());
        flashModel.setStatus("running");
        flashModel.setTestProgress("1/10");
        flashModelList.add(flashModel);


        model = NotificationService.OrderInteractiveModel.builder()
                .title("title")
                .version(TextUtils.parseMpToolVersion("(I)MPTools V1.8.9.2.311_220225(1583)"))
                .versionType("Alpha")
                .builder("Bugs.Wan")
                .buildAt(dateFormat.format(new Date()))
                .flash("3DV4_B27A(U2)_89C41832A2")
                .product("GE - SD")
                .detailUrl("https://www.baidu.com")
                .flashModelList(flashModelList)
                .build();
    }


    @Test
    @DisplayName("需要包含对应的字段")
    void should_contains_all_fields() {
        // given
        model.setTitle("Default Title");

        // when
        Map<String, String> paramMap = model.buildCardDataParamMap(mapper);

        // then
        Assertions.assertThat(paramMap)
                .containsKeys("title", "version", "versionType", "builder", "buildAt", "flash", "product", "flashConfirmBy", "flashConfirmAt", "detailUrl", "sys_full_json_obj");


    }


    @Test
    @DisplayName("sys_full_json_obj 中的包含flashBatch 字段")
    void should_contains_flashBatch_field() {
        // given

        // when
        Map<String, String> paramMap = model.buildCardDataParamMap(mapper);

        // then
        Assertions.assertThat(paramMap.get("sys_full_json_obj"))
                .contains("flashBatch");
    }


    @Test
    @DisplayName("flashBatch 中spendTime 不为空")
    void should_contains_spendTime_field() {
        // given
        List<NotificationService.OrderFlashInteractiveModel> flashModelList = new ArrayList<>();
        NotificationService.OrderFlashInteractiveModel flashModel = new NotificationService.OrderFlashInteractiveModel();
        flashModel.setFlash("3DV4_B27A(U2)_89C41832A2");
        flashModel.setNum(20);
        flashModel.setPlanNum(10);
        flashModel.setStartAt(System.currentTimeMillis());
        flashModel.setStatus("需要上传报告");
        flashModel.setTestProgress("10/10");
        flashModel.setSpendTime("1h 20m");
        flashModelList.add(flashModel);
        model.setFlashModelList(flashModelList);

        // when
        Map<String, String> paramMap = model.buildCardDataParamMap(mapper);

        // then
        Assertions.assertThat(paramMap.get("sys_full_json_obj"))
                .contains("spendTime");
    }


}
