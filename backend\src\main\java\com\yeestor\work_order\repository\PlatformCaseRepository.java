package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.PlatformCaseEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface PlatformCaseRepository extends JpaRepository<PlatformCaseEntity, Long> {
    @Query(value = "select pc from PlanPlatform pp, PlatformCase pc " +
            "where pp.planId = :planId and pp.mac = :mac and pp.number = :number " +
            "and pp.id = pc.platformId and pc.caseName = :caseName " +
            "and pc.caseStep = :caseStep")
    Optional<PlatformCaseEntity> findByPlanIdAndMacAndNumberAndCaseNameAndStep(
            long planId, String mac, String number, String caseName, Integer caseStep
    );

    @Query(value = "select pc from PlanPlatform pp, PlatformCase pc " +
            "where pp.planId = :planId and pp.mac = :mac and pp.number = :number " +
            "and pp.id = pc.platformId and pc.status = :status")
    List<PlatformCaseEntity> findByPlanIdAndMacAndNumberAndStatus(long planId, String mac, String number, PlatformCaseEntity.Status status);

    @Query(value = "select count(pc) from PlanPlatform pp, PlatformCase pc " +
            "where pp.planId = :planId and pp.mac = :mac and pp.number = :number " +
            "and pp.id = pc.platformId and pc.status = :status")
    int countByPlanIdAndMacAndNumberAndStatus(long planId, String mac, String number, PlatformCaseEntity.Status status);

    @Query(value = "select pc from PlanPlatform pp, PlatformCase pc " +
            "where pp.planId = :planId and pp.mac = :mac and pp.number = :number " +
            "and pp.id = pc.platformId ")
    List<PlatformCaseEntity> findByPlanIdAndMacAndNumber(long planId, String mac, String number);

    @Query(value = "select pc from PlanPlatform pp, PlatformCase pc " +
            "where pp.planId = :planId and pp.mac = :mac and pp.number in :numberList " +
            "and pp.id = pc.platformId and pc.status = :status")
    List<PlatformCaseEntity> findByPlanIdAndMacAndNumberInAndStatus(long planId, String mac, List<String> numberList, PlatformCaseEntity.Status status);

    List<PlatformCaseEntity> findByPlatformId(Long platformId);
}
