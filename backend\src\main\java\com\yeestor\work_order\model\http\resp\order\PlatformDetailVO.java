package com.yeestor.work_order.model.http.resp.order;

import com.yeestor.work_order.entity.PlanPlatformEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "详细终端平台信息")
public class PlatformDetailVO {
    private long id;

    @ApiModelProperty("plan 的名称")
    private String planName;

    @ApiModelProperty(name = "ip")
    private String ip;

    @ApiModelProperty("设备的mac 地址")
    private String mac;

    @ApiModelProperty("设备的pc 编号")
    private String no;

    @ApiModelProperty("手机平台名称")
    private String name;

    @ApiModelProperty("手机平台序列号")
    private String number;

    @ApiModelProperty("样品容量")
    private String volume;

    @ApiModelProperty("样品编号")
    private String sample;

    @ApiModelProperty("平台添加人员钉钉ID")
    private String addedBy;

    @ApiModelProperty("平台添加人员名称")
    private String addedPerson;

    @ApiModelProperty("设备的状态")
    private PlanPlatformEntity.Status status;

    @ApiModelProperty("测试的开始时间")
    private Long startAt;

    @ApiModelProperty("测试的开始人员")
    private String startPerson;

    @ApiModelProperty("测试的结束时间")
    private Long endAt;

    @ApiModelProperty("测试失败的原因")
    private String failReason;

    @ApiModelProperty("设备是否已经释放")
    private Long releaseAt;

    @ApiModelProperty("设备由谁来释放的")
    private String releaseBy;

    @ApiModelProperty("释放人的名称")
    private String releasePerson;

    @ApiModelProperty("人为手动终止测试的时间")
    private Long terminateAt;

    @ApiModelProperty("终止测试的人员，钉钉ID")
    private String terminateBy;

    @ApiModelProperty("终止测试的人员 名称")
    private String terminatePerson;

    @ApiModelProperty("终端测试下相关的case信息")
    private List<PlanCaseItemVO> caseList;

    public PlatformDetailVO setInfo(PlanPlatformEntity entity) {
        this.id = entity.getId();
        this.planName = entity.getPlanName();
        this.ip = entity.getIp();
        this.mac = entity.getMac();
        this.no = entity.getNo();
        this.name = entity.getName();
        this.number = entity.getNumber();
        this.volume = entity.getVolume();
        this.addedBy = entity.getAddedBy();
        this.addedPerson = entity.getAddedPerson();
        this.status = entity.getStatus();
        this.startAt = entity.getStartAt();
        this.startPerson = entity.getStartPerson();
        this.endAt = entity.getEndAt();
        this.failReason = entity.getFailReason();
        this.releaseAt = entity.getReleaseAt();
        this.releaseBy = entity.getReleaseBy();
        this.releasePerson = entity.getReleasePerson();
        this.sample = entity.getSample();

        this.terminateAt = entity.getTerminateAt();
        this.terminateBy = entity.getTerminateBy();
        this.terminatePerson = entity.getTerminatePerson();
        return null;
    }
}
