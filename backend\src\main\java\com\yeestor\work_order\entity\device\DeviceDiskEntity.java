package com.yeestor.work_order.entity.device;

import com.yeestor.entity.BaseEntity;
import com.yeestor.work_order.model.rms.SampleInfo;
import com.yeestor.work_order.utils.converter.SampleListConvert;
import lombok.Data;

import javax.persistence.*;
import java.util.List;

@Data
@Entity(name = "DeviceDisk")
@Table(name = "wo_device_disk")
public class DeviceDiskEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    /**
     * 工单id 。这里不采用外键。
     */
    @Column(name = "order_id")
    private long orderId ;

    @Column(name = "flash")
    private String flash ;

    @Column(name = "plan_id")
    private long planId ;


    @Column(name = "request_uuid",length = 100)
    private String requestId ;

    @Column(name = "trace_id",length = 100)
    private String traceId ;


    @Column(name = "device_id")
    private long deviceId ;
    /**
     * mac 地址
     */
    @Column(name = "mac" ,length = 100)
    private String mac ;
    /**
     * mac 地址
     */
    @Column(name = "ip" ,length = 50)
    private String ip ;

    /**
     * 设备编号
     */
    @Column(name = "no" ,length = 100)
    private String no ;

    @Column(name = "test_num")
    private Integer testNum;


    @Column(name="samples", columnDefinition = "json")
    @Convert(converter = SampleListConvert.class)
    private List<SampleInfo> samples;


}
