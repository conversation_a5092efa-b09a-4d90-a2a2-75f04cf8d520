package com.yeestor.work_order.controller.config;


import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.config.TimeoutConfigEntity;
import com.yeestor.work_order.model.http.req.config.TimeoutConfigParams;
import com.yeestor.work_order.model.http.resp.config.TimeoutConfigVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.repository.TimeoutConfigRepository;
import com.yeestor.work_order.service.role.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/notification")
@Api(value = "NotificationController", tags = {"通知相关的配置接口"})
public class NotificationController {

    private final TimeoutConfigRepository timeoutConfigRepository;
    private final RoleService roleService;

    @ApiOperation(value = "获取超时相关的配置", notes = "获取超时相关的配置")
    @GetMapping("/timeout/configs")
    public HandleResp<TimeoutConfigVO> fetchTimeoutConfig(
            @ApiParam(value = "产品线", required = true) String product,
            @ApiParam(value = "产品", required = true) String subProduct
    ) {
        TimeoutConfigVO timeoutConfigVO = new TimeoutConfigVO();
        timeoutConfigVO.setTypes(List.of(TimeoutConfigEntity.Type.values()));
        List<String> phases = new ArrayList<>();
        //将 WorkOrderEntity.Status 中的所有枚举值转换成字符串, 作为可选的阶段
        phases.addAll(Stream.of(WorkOrderEntity.Status.values()).map(Enum::name).collect(Collectors.toList()));
        // 将 OrderFlashEntity.Status 中的所有枚举值转换成字符串, 作为可选的阶段
        phases.addAll(Stream.of(OrderFlashEntity.Status.values()).map(Enum::name).collect(Collectors.toList()));
        timeoutConfigVO.setPhases(phases);

        List<TimeoutConfigEntity> configs = timeoutConfigRepository.findAllByProductAndSubProduct(product, subProduct);
        timeoutConfigVO.setConfigs(configs);

        return HandleResp.ok(timeoutConfigVO);
    }


    @ApiOperation(value = "设置超时相关的配置", notes = "设置超时相关的配置")
    @PostMapping("/timeout/config")
    public HandleResp<String> setTimeoutConfig(
            @Valid @ApiParam(value = "超时配置参数", required = true) @RequestBody TimeoutConfigParams body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {

        String operator = "设置超时相关的配置";
        // 检查权限.
        roleService.checkPermission(userDetail, body.getSubProduct(), operator, Permission.CREATE_TIMEOUT_CONFIG);

        switch (body.getType()) {
            case ORDER:
                WorkOrderEntity.Status.valueOf(body.getPhase());
                break;
            case FLASH:
                OrderFlashEntity.Status.valueOf(body.getPhase());
                break;
            default:
                throw new IllegalArgumentException("不支持的类型: " + body.getType());
        }
        TimeoutConfigEntity configEntity = timeoutConfigRepository.findByProductAndSubProductAndTypeAndPhase(
                body.getProduct(),
                body.getSubProduct(),
                body.getType(),
                body.getPhase()
        ).orElse(body.convert2Entity());
        configEntity.setTimeout(body.getTimeout());
        configEntity.setListeners(body.getListeners());
        timeoutConfigRepository.save(configEntity);


        return HandleResp.ok("配置成功!");
    }


}
