{"@timestamp":"2025-07-24T14:30:02.588+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.601+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.621+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.636+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113828, orderId=6400, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.655+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113830, orderId=6400, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.67+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113831, orderId=6400, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.671+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f55d01908953d4d","spanId":"0f55d01908953d4d","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.588+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:33:02.59+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.608+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.634+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113828, orderId=6400, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.65+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113830, orderId=6400, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.665+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113831, orderId=6400, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.666+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f24d67354b1110a3","spanId":"f24d67354b1110a3","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.583+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:36:02.585+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.605+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.629+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113828, orderId=6400, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113830, orderId=6400, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.66+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113831, orderId=6400, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.661+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5cd2ad2e060c33a","spanId":"b5cd2ad2e060c33a","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.812+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:51:02.822+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.845+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.861+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113828, orderId=6400, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.879+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113830, orderId=6400, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.894+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113831, orderId=6400, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.895+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.895+08:00","@version":"1","message":" getValidPlanList sumList: [120] leftFlashNum:120 index:1 plans:[OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:51:02.895+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 1 plans: [OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.896+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.992+08:00","@version":"1","message":"[32e45432] HTTP GET http://ereport.yeestor.com/wo/device/list?p=SD","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.202+08:00","@version":"1","message":"getAllDeviceList with SD got data DeviceListResp(code=0, data=[{ GE2_3_15,************,E0-D5-5E-9D-10-AB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_23,************1,18-C0-4D-BB-C2-BB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_22,*************,18-C0-4D-BB-C5-BB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_11,*************,2C-F0-5D-40-F5-23,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_18,***********02,E0-D5-5E-C2-AD-7C,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_29,*************,B4-2E-99-29-3B-F5,[{DUT=8}, {年限2=-1}] }, { GE2_3_17,*************,E0-D5-5E-9F-67-0E,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_16,************,B4-2E-99-29-3F-76,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_03,*************,B4-2E-99-E7-5F-C2,[{性能=4}, {年限2=-1}] }, { GE2_2_32,************,2C-F0-5D-40-F5-7E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_19,************,B4-2E-99-29-3F-8A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_25,************,18-C0-4D-BB-BF-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_29,************,18-C0-4D-BB-B7-30,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_13,************,2C-F0-5D-40-F5-51,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_21,************0,2C-F0-5D-40-F5-32,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE-PC-SDhigh-02,*************,E0-D5-5E-9F-6C-01,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-13,************,B4-2E-99-5A-E9-C2,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_3_03,************,B4-2E-99-5A-E4-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_17,************1,18-C0-4D-BA-04-2A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_26,*************,18-C0-4D-BB-C6-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_02,*************,18-C0-4D-BA-23-0F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE4_2_02,*************,B4-2E-99-5A-E4-D7,[{程控掉电=16}, {USB3.0HUB=8}] }, { GE2_1_19,*************,18-C0-4D-BA-04-85,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_02,************,B4-2E-99-29-3F-96,[{TCPIP掉电=4}, {Mars=4}, {DUT=6}, {年限2=-1}] }, { GE2_1_27,*************,18-C0-4D-BA-21-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_20,***********,18-C0-4D-BA-04-82,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_04,***********0,E0-D5-5E-9D-12-14,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_32,***********04,18-C0-4D-BA-03-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_19,***********17,2C-F0-5D-40-F5-28,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_10,************,B4-2E-99-29-3F-CD,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_06,*************,E0-D5-5E-B7-16-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE4_2_01,***********53,B4-2E-99-59-F7-26,[{程控掉电=16}, {USB3.0HUB=8}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_16,************,2C-F0-5D-40-F5-1D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_2_07,*************,2C-F0-5D-26-AA-49,[{性能=4}, {年限1=-1}] }, { GE3_2_06,************,18-C0-4D-3A-CC-E4,[{性能=4}, {年限1=-1}] }, { GE2_3_26,***********,E0-D5-5E-9A-F9-43,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_2_12,*************,2C-F0-5D-40-F5-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_09,*************,2C-F0-5D-40-F5-1C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_28,************,18-C0-4D-BA-25-1B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_12,***********,E0-D5-5E-9D-86-DB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_21,***********2,18-C0-4D-BB-C5-E4,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_25,***********00,2C-F0-5D-40-F7-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_14,*************,2C-F0-5D-40-F4-F7,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_24,************,18-C0-4D-BB-C5-BF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_15,************3,2C-F0-5D-40-F5-0E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_07,***********,E0-D5-5E-9F-6B-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=6}, {年限2=-1}] }, { GE2_3_21,***********1,E0-D5-5E-9D-C0-77,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE-PC-SDhigh-27,************,B4-2E-99-59-FA-3A,[{Mars=4}, {DUT=4}, {高温=4}] }, { GE-PC-SDhigh-23,*************,B4-2E-99-5A-EA-F1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-15,*************,B4-2E-99-5A-E6-A8,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-04,*************,B4-2E-99-29-3F-BA,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-05,*************,B4-2E-99-29-3F-E5,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-03,*************,B4-2E-99-29-3F-E8,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-11,*************,B4-2E-99-5A-E5-2B,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-18,*************,B4-2E-99-5A-E1-DC,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-25,*************,B4-2E-99-5A-E6-A3,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-16,************,B4-2E-99-5A-DF-B6,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-07,172.18.41.133,B4-2E-99-29-3F-AC,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-19,172.18.41.42,B4-2E-99-5A-E1-C2,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-12,************,B4-2E-99-5A-EB-33,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-09,************,B4-2E-99-29-3C-B6,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-10,************,B4-2E-99-29-40-6B,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-08,*************,B4-2E-99-29-3F-AF,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_3_05,***********,E0-D5-5E-C1-2B-4C,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_31,***********1,18-C0-4D-BB-C6-4E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_2_12,***********1,D4-5D-64-26-26-AE,[{性能=4}, {年限2=-1}] }, { GE-PC-SDhigh-01,************,E0-D5-5E-9F-6B-9C,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-26,***********9,B4-2E-99-5A-E2-F1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-06,*************,B4-2E-99-29-3F-59,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_11,************,E0-D5-5E-C1-30-6A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_10,************,2C-F0-5D-40-F6-79,[{性能=4}, {年限1=-1}] }, { GE-PC-SDhigh-20,************,B4-2E-99-5A-EC-20,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-22,***********5,B4-2E-99-5A-EA-81,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-14,*************,B4-2E-99-5A-EA-F0,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-21,*************,B4-2E-99-5A-EB-2F,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-24,*************,B4-2E-99-5A-EA-C1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE2_1_14,*************,18-C0-4D-BA-04-33,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_18,*************,18-C0-4D-BA-04-89,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_24,************6,2C-F0-5D-40-F5-3D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{温循=4}, {高温=4}, {Mars=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{高温=4}, {低温=4}, {Mars=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{温循=4}, {高温=4}, {Mars=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { GE2_1_30,************,18-C0-4D-BB-C6-AC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_2_04,***********0,18-C0-4D-BA-23-11,[{性能=4}, {年限1=-1}] }, { GE3_2_17,*************,0C-9D-92-75-B0-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE2_3_09,*************,E0-D5-5E-99-46-D5,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_05,************,18-C0-4D-B5-45-91,[{性能=4}, {年限1=-1}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE2_3_22,***********40,E0-D5-5E-9D-12-FA,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_23,************,E0-D5-5E-9D-86-FB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_24,***********49,B4-2E-99-29-3B-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_25,************,E0-D5-5E-9D-BB-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=4}] }, { GE2_3_27,************,B4-2E-99-29-40-6A,[{DUT=8}, {年限2=-1}] }, { GE2_3_28,*************,B4-2E-99-29-40-53,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_31,*************,E0-D5-5E-9F-63-7F,[{DUT=8}, {年限2=-1}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { GE-PC-SDhigh-32,************,D8-5E-D3-59-EB-B1,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-29,************8,D8-5E-D3-51-8F-12,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-28,************,D8-5E-D3-51-87-5A,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-36,************,18-C0-4D-AB-1E-33,[{Mars=4}, {高温=4}] }, { GE_PC_010,*************,D8-5E-D3-51-87-5C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_008,*************,D8-5E-D3-59-E9-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_011,************,D8-5E-D3-59-EB-2E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {温循=4}, {低温=4}, {高温=4}] }, { GE_PC_012,*************,D8-5E-D3-59-EB-68,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_013,*************,D8-5E-D3-59-EC-60,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_014,*************,D8-5E-D3-51-81-E2,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {温循=4}, {性能=4}, {低温=4}, {高温=4}] }, { GE_PC_015,************4,D8-5E-D3-51-8E-94,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_016,*************,D8-5E-D3-59-E8-AB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_020,*************,D8-5E-D3-59-E7-55,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_017,*************,D8-5E-D3-51-81-E0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {低温=4}, {高温=4}, {温循=4}, {性能=4}] }, { GE_PC_018,************,D8-5E-D3-59-E8-5F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_022,*************,D8-5E-D3-51-8E-C2,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_003,************7,D8-5E-D3-59-E5-08,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_009,*************,D8-5E-D3-59-EB-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_021,*************,D8-5E-D3-51-8F-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {低温=4}, {高温=4}, {温循=4}, {性能=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_019,***********,D8-5E-D3-51-81-E3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_031,************7,D8-5E-D3-5F-84-11,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE3_2_11,************,D4-5D-64-26-26-BC,[{性能=4}, {年限2=-1}] }, { GE3_2_23,***********3,08-BF-B8-6F-CA-49,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_24,************2,08-BF-B8-6F-C8-B8,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_26,************,08-BF-B8-6F-CA-D5,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_29,*************,08-BF-B8-6F-CA-DA,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_28,***********07,08-BF-B8-39-8D-08,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_30,************,08-BF-B8-6F-CA-D2,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_31,************,08-BF-B8-39-8C-19,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_32,*************,08-BF-B8-6F-CB-1E,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_15,***********31,08-BF-B8-39-68-1D,[{年限3=-1}] }, { GE-PC-SDhigh-48,*************,A4-0C-66-13-D6-0F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_055,***********08,A4-0C-66-14-25-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_047,***********12,A4-0C-66-14-28-45,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_049,************8,A4-0C-66-14-28-4D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_050,*************,A4-0C-66-14-2B-FE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_046,*************,A4-0C-66-14-2C-08,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_061,***********3,A4-0C-66-14-26-83,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_062,***********11,A4-0C-66-14-26-9D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_038,************1,A4-0C-66-14-26-45,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_053,***********1,A4-0C-66-14-27-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_054,************,A4-0C-66-14-26-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_058,************,A4-0C-66-14-25-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_034,************6,A4-0C-66-14-27-4B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_035,*************,A4-0C-66-14-27-B9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_037,***********37,A4-0C-66-14-28-89,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_036,*************,A4-0C-66-14-28-EE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_039,************4,A4-0C-66-14-25-B3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_040,***********05,A4-0C-66-14-26-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_041,***********48,A4-0C-66-14-26-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_042,************9,A4-0C-66-14-29-49,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_043,*************,A4-0C-66-14-25-B0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_044,************0,A4-0C-66-14-28-64,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_045,************,A4-0C-66-14-25-B1,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_048,***********48,A4-0C-66-14-28-39,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_051,***********,A4-0C-66-14-28-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_052,************,A4-0C-66-14-2C-42,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_056,************,A4-0C-66-14-26-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_057,***********6,A4-0C-66-14-2C-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_059,************,A4-0C-66-14-28-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_060,************3,A4-0C-66-14-2A-95,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-45,************,A4-0C-66-14-28-3B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-41,************,A4-0C-66-14-27-FC,[{Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-47,************,A4-0C-66-14-2A-5B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-66,************,8C-32-23-39-BA-EF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,************,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-67,*************,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-59,*************,8C-32-23-39-B9-28,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-60,*************,8C-32-23-39-BC-FF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-61,*************,8C-32-23-39-BD-51,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-62,************,8C-32-23-39-BB-6B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-63,************,8C-32-23-39-BA-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-64,************2,8C-32-23-39-B8-90,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-65,*************,8C-32-23-39-B8-96,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-50,*************,8C-32-23-39-BD-72,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-56,************,8C-32-23-39-BC-D4,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-49,************,8C-32-23-39-BC-41,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-54,*************,8C-32-23-39-BC-55,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-51,*************,8C-32-23-39-BB-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-55,************,8C-32-23-39-BB-CF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-57,************,8C-32-23-39-B9-B9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-52,************,8C-32-23-39-BB-F9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-46,*************,A4-0C-66-14-28-3A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-44,*************,A4-0C-66-14-28-4C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-43,*************,A4-0C-66-14-2A-BC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-42,************,A4-0C-66-14-27-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }], msg=测试机信息获取成功！, workPcLst=[{ GE3_1_12,************7,2C-4D-54-54-78-1D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限2=-1}, {开卡架=40}] }, { GE3_1_03,************8,B4-2E-99-29-3B-C7,[{开卡架=40}, {年限2=-1}] }, { GE3_1_11,*************,2C-4D-54-54-77-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限2=1}] }, { GE3_2_09,************,2C-F0-5D-40-F6-15,[{性能=4}, {年限1=-1}] }, { GE3_2_18.1,***********39,08-BF-B8-6F-CB-16,[{性能=4}, {Mars=4}, {TCPIP掉电=4}] }, { GE3_2_27,***********4,08-BF-B8-6F-C9-9D,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.427+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6400 flash: XCCB-6285DA-9T25-A_64GB available device: [{ GE2_3_15,************,E0-D5-5E-9D-10-AB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_23,************1,18-C0-4D-BB-C2-BB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_22,*************,18-C0-4D-BB-C5-BB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_11,*************,2C-F0-5D-40-F5-23,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_18,***********02,E0-D5-5E-C2-AD-7C,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_29,*************,B4-2E-99-29-3B-F5,[{DUT=8}, {年限2=-1}] }, { GE2_3_17,*************,E0-D5-5E-9F-67-0E,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_16,************,B4-2E-99-29-3F-76,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_03,*************,B4-2E-99-E7-5F-C2,[{性能=4}, {年限2=-1}] }, { GE2_2_32,************,2C-F0-5D-40-F5-7E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_19,************,B4-2E-99-29-3F-8A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_25,************,18-C0-4D-BB-BF-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_29,************,18-C0-4D-BB-B7-30,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_13,************,2C-F0-5D-40-F5-51,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_21,************0,2C-F0-5D-40-F5-32,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE-PC-SDhigh-02,*************,E0-D5-5E-9F-6C-01,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-13,************,B4-2E-99-5A-E9-C2,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_3_03,************,B4-2E-99-5A-E4-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_17,************1,18-C0-4D-BA-04-2A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_26,*************,18-C0-4D-BB-C6-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_02,*************,18-C0-4D-BA-23-0F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE4_2_02,*************,B4-2E-99-5A-E4-D7,[{程控掉电=16}, {USB3.0HUB=8}] }, { GE2_1_19,*************,18-C0-4D-BA-04-85,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_02,************,B4-2E-99-29-3F-96,[{TCPIP掉电=4}, {Mars=4}, {DUT=6}, {年限2=-1}] }, { GE2_1_27,*************,18-C0-4D-BA-21-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_20,***********,18-C0-4D-BA-04-82,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_04,***********0,E0-D5-5E-9D-12-14,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_32,***********04,18-C0-4D-BA-03-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_19,***********17,2C-F0-5D-40-F5-28,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_10,************,B4-2E-99-29-3F-CD,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_06,*************,E0-D5-5E-B7-16-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE4_2_01,***********53,B4-2E-99-59-F7-26,[{程控掉电=16}, {USB3.0HUB=8}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_16,************,2C-F0-5D-40-F5-1D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_2_07,*************,2C-F0-5D-26-AA-49,[{性能=4}, {年限1=-1}] }, { GE3_2_06,************,18-C0-4D-3A-CC-E4,[{性能=4}, {年限1=-1}] }, { GE2_3_26,***********,E0-D5-5E-9A-F9-43,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_2_12,*************,2C-F0-5D-40-F5-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_09,*************,2C-F0-5D-40-F5-1C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_28,************,18-C0-4D-BA-25-1B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_12,***********,E0-D5-5E-9D-86-DB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_21,***********2,18-C0-4D-BB-C5-E4,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_25,***********00,2C-F0-5D-40-F7-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_14,*************,2C-F0-5D-40-F4-F7,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_24,************,18-C0-4D-BB-C5-BF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_15,************3,2C-F0-5D-40-F5-0E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_07,***********,E0-D5-5E-9F-6B-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=6}, {年限2=-1}] }, { GE2_3_21,***********1,E0-D5-5E-9D-C0-77,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE-PC-SDhigh-27,************,B4-2E-99-59-FA-3A,[{Mars=4}, {DUT=4}, {高温=4}] }, { GE-PC-SDhigh-23,*************,B4-2E-99-5A-EA-F1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-15,*************,B4-2E-99-5A-E6-A8,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-04,*************,B4-2E-99-29-3F-BA,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-05,*************,B4-2E-99-29-3F-E5,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-03,*************,B4-2E-99-29-3F-E8,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-11,*************,B4-2E-99-5A-E5-2B,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-18,*************,B4-2E-99-5A-E1-DC,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-25,*************,B4-2E-99-5A-E6-A3,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-16,************,B4-2E-99-5A-DF-B6,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-07,172.18.41.133,B4-2E-99-29-3F-AC,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-19,172.18.41.42,B4-2E-99-5A-E1-C2,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-12,************,B4-2E-99-5A-EB-33,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-09,************,B4-2E-99-29-3C-B6,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-10,************,B4-2E-99-29-40-6B,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-08,*************,B4-2E-99-29-3F-AF,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_3_05,***********,E0-D5-5E-C1-2B-4C,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_31,***********1,18-C0-4D-BB-C6-4E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_2_12,***********1,D4-5D-64-26-26-AE,[{性能=4}, {年限2=-1}] }, { GE-PC-SDhigh-01,************,E0-D5-5E-9F-6B-9C,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-26,***********9,B4-2E-99-5A-E2-F1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-06,*************,B4-2E-99-29-3F-59,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_11,************,E0-D5-5E-C1-30-6A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_10,************,2C-F0-5D-40-F6-79,[{性能=4}, {年限1=-1}] }, { GE-PC-SDhigh-20,************,B4-2E-99-5A-EC-20,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-22,***********5,B4-2E-99-5A-EA-81,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-14,*************,B4-2E-99-5A-EA-F0,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-21,*************,B4-2E-99-5A-EB-2F,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-24,*************,B4-2E-99-5A-EA-C1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE2_1_14,*************,18-C0-4D-BA-04-33,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_18,*************,18-C0-4D-BA-04-89,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_24,************6,2C-F0-5D-40-F5-3D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{温循=4}, {高温=4}, {Mars=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{高温=4}, {低温=4}, {Mars=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{温循=4}, {高温=4}, {Mars=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { GE2_1_30,************,18-C0-4D-BB-C6-AC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_2_04,***********0,18-C0-4D-BA-23-11,[{性能=4}, {年限1=-1}] }, { GE3_2_17,*************,0C-9D-92-75-B0-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE2_3_09,*************,E0-D5-5E-99-46-D5,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_05,************,18-C0-4D-B5-45-91,[{性能=4}, {年限1=-1}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE2_3_22,***********40,E0-D5-5E-9D-12-FA,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_23,************,E0-D5-5E-9D-86-FB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_24,***********49,B4-2E-99-29-3B-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_25,************,E0-D5-5E-9D-BB-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=4}] }, { GE2_3_27,************,B4-2E-99-29-40-6A,[{DUT=8}, {年限2=-1}] }, { GE2_3_28,*************,B4-2E-99-29-40-53,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_31,*************,E0-D5-5E-9F-63-7F,[{DUT=8}, {年限2=-1}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { GE-PC-SDhigh-32,************,D8-5E-D3-59-EB-B1,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-29,************8,D8-5E-D3-51-8F-12,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-28,************,D8-5E-D3-51-87-5A,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-36,************,18-C0-4D-AB-1E-33,[{Mars=4}, {高温=4}] }, { GE_PC_010,*************,D8-5E-D3-51-87-5C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_008,*************,D8-5E-D3-59-E9-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_011,************,D8-5E-D3-59-EB-2E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {温循=4}, {低温=4}, {高温=4}] }, { GE_PC_012,*************,D8-5E-D3-59-EB-68,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_013,*************,D8-5E-D3-59-EC-60,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_014,*************,D8-5E-D3-51-81-E2,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {温循=4}, {性能=4}, {低温=4}, {高温=4}] }, { GE_PC_015,************4,D8-5E-D3-51-8E-94,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_016,*************,D8-5E-D3-59-E8-AB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_020,*************,D8-5E-D3-59-E7-55,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_017,*************,D8-5E-D3-51-81-E0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {低温=4}, {高温=4}, {温循=4}, {性能=4}] }, { GE_PC_018,************,D8-5E-D3-59-E8-5F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_022,*************,D8-5E-D3-51-8E-C2,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_003,************7,D8-5E-D3-59-E5-08,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_009,*************,D8-5E-D3-59-EB-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_021,*************,D8-5E-D3-51-8F-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {低温=4}, {高温=4}, {温循=4}, {性能=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_019,***********,D8-5E-D3-51-81-E3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_031,************7,D8-5E-D3-5F-84-11,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE3_2_11,************,D4-5D-64-26-26-BC,[{性能=4}, {年限2=-1}] }, { GE3_2_23,***********3,08-BF-B8-6F-CA-49,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_24,************2,08-BF-B8-6F-C8-B8,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_26,************,08-BF-B8-6F-CA-D5,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_29,*************,08-BF-B8-6F-CA-DA,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_28,***********07,08-BF-B8-39-8D-08,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_30,************,08-BF-B8-6F-CA-D2,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_31,************,08-BF-B8-39-8C-19,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_32,*************,08-BF-B8-6F-CB-1E,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_15,***********31,08-BF-B8-39-68-1D,[{年限3=-1}] }, { GE-PC-SDhigh-48,*************,A4-0C-66-13-D6-0F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_055,***********08,A4-0C-66-14-25-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_047,***********12,A4-0C-66-14-28-45,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_049,************8,A4-0C-66-14-28-4D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_050,*************,A4-0C-66-14-2B-FE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_046,*************,A4-0C-66-14-2C-08,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_061,***********3,A4-0C-66-14-26-83,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_062,***********11,A4-0C-66-14-26-9D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_038,************1,A4-0C-66-14-26-45,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_053,***********1,A4-0C-66-14-27-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_054,************,A4-0C-66-14-26-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_058,************,A4-0C-66-14-25-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_034,************6,A4-0C-66-14-27-4B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_035,*************,A4-0C-66-14-27-B9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_037,***********37,A4-0C-66-14-28-89,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_036,*************,A4-0C-66-14-28-EE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_039,************4,A4-0C-66-14-25-B3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_040,***********05,A4-0C-66-14-26-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_041,***********48,A4-0C-66-14-26-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_042,************9,A4-0C-66-14-29-49,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_043,*************,A4-0C-66-14-25-B0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_044,************0,A4-0C-66-14-28-64,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_045,************,A4-0C-66-14-25-B1,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_048,***********48,A4-0C-66-14-28-39,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_051,***********,A4-0C-66-14-28-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_052,************,A4-0C-66-14-2C-42,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_056,************,A4-0C-66-14-26-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_057,***********6,A4-0C-66-14-2C-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_059,************,A4-0C-66-14-28-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_060,************3,A4-0C-66-14-2A-95,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-45,************,A4-0C-66-14-28-3B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-41,************,A4-0C-66-14-27-FC,[{Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-47,************,A4-0C-66-14-2A-5B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-66,************,8C-32-23-39-BA-EF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,************,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-67,*************,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-59,*************,8C-32-23-39-B9-28,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-60,*************,8C-32-23-39-BC-FF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-61,*************,8C-32-23-39-BD-51,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-62,************,8C-32-23-39-BB-6B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-63,************,8C-32-23-39-BA-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-64,************2,8C-32-23-39-B8-90,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-65,*************,8C-32-23-39-B8-96,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-50,*************,8C-32-23-39-BD-72,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-56,************,8C-32-23-39-BC-D4,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-49,************,8C-32-23-39-BC-41,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-54,*************,8C-32-23-39-BC-55,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-51,*************,8C-32-23-39-BB-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-55,************,8C-32-23-39-BB-CF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-57,************,8C-32-23-39-B9-B9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-52,************,8C-32-23-39-BB-F9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-46,*************,A4-0C-66-14-28-3A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-44,*************,A4-0C-66-14-28-4C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-43,*************,A4-0C-66-14-2A-BC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-42,************,A4-0C-66-14-27-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.429+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.43+08:00","@version":"1","message":"[6400] Plan22 need Num: 120 to test. XCCB-6285DA-9T25-A_64GB left num:120 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.431+08:00","@version":"1","message":"subProduct SD flashName XCCB-6285DA-9T25-A_64GB plan [Plan22] attrs Mars belongTo tommie.zheng is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.438+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [GE3_2_16, GE3_2_17, GE_PC_031, GE_PC_061, GE_PC_062, GE_PC_034, GE_PC_035, GE_PC_036, GE_PC_037, GE_PC_038, GE_PC_039, GE_PC_040, GE_PC_041, GE_PC_042, GE_PC_043, GE_PC_044, GE_PC_045, GE_PC_046, GE_PC_047, GE_PC_048, GE_PC_049, GE_PC_050, GE_PC_051, GE_PC_052, GE_PC_053, GE_PC_054, GE_PC_055, GE_PC_056, GE_PC_057, GE_PC_058, GE_PC_059, GE_PC_060, GE2_1_02, GE2_1_14, GE2_1_17, GE2_1_18, GE2_1_19, GE2_1_20, GE2_1_21, GE2_1_22, GE2_1_23, GE2_1_24, GE2_1_25, GE2_1_26, GE2_1_27, GE2_1_28, GE2_1_29, GE2_1_30, GE2_1_31, GE2_1_32, GE2_2_09, GE2_2_11, GE2_2_12, GE2_2_13, GE2_2_14, GE2_2_15, GE2_2_16, GE2_2_19, GE2_2_20, GE2_2_21, GE2_2_22, GE2_2_23, GE2_2_24, GE2_2_25, GE2_2_26, GE2_2_27, GE2_2_28, GE2_2_29, GE2_2_30, GE2_2_31, GE2_2_32, GE2_3_02, GE2_3_03, GE2_3_04, GE2_3_05, GE2_3_06, GE2_3_07, GE2_3_09, GE2_3_10, GE2_3_11, GE2_3_12, GE2_3_15, GE2_3_16, GE2_3_17, GE2_3_18, GE2_3_19, GE2_3_21, GE2_3_22, GE2_3_23, GE2_3_24, GE2_3_25, GE2_3_26, GE2_3_28, GE-PC-SDhigh-41, GE-PC-SDhigh-42, GE-PC-SDhigh-43, GE-PC-SDhigh-44, GE-PC-SDhigh-45, GE-PC-SDhigh-46, GE-PC-SDhigh-47, GE-PC-SDhigh-48, GE-PC-SDhigh-49, GE-PC-SDhigh-50, GE-PC-SDhigh-51, GE-PC-SDhigh-52, GE-PC-SDhigh-54, GE-PC-SDhigh-55, GE-PC-SDhigh-56, GE-PC-SDhigh-57, GE-PC-SDhigh-59, GE-PC-SDhigh-60, GE-PC-SDhigh-61, GE-PC-SDhigh-62, GE-PC-SDhigh-63, GE-PC-SDhigh-64, GE-PC-SDhigh-65, GE-PC-SDhigh-66, GE-PC-SDhigh-67, GE-PC-SDhigh-68, GE-PC-SDhigh-69, GE-PC-SDhigh-70, GE-PC-SDhigh-71, GE-PC-SDhigh-72, GE3_2_23, GE3_2_24, GE3_2_26, GE3_2_28, GE3_2_29, GE3_2_30, GE3_2_31, GE3_2_32]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.439+08:00","@version":"1","message":"Plan22 test all sample is true","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.444+08:00","@version":"1","message":"[6400] plan:Plan22 use 31 pc: [{ GE-PC-SDhigh-41,************,A4-0C-66-14-27-FC,[{Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-42,************,A4-0C-66-14-27-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-43,*************,A4-0C-66-14-2A-BC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-44,*************,A4-0C-66-14-28-4C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-45,************,A4-0C-66-14-28-3B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-46,*************,A4-0C-66-14-28-3A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-47,************,A4-0C-66-14-2A-5B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-48,*************,A4-0C-66-13-D6-0F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-49,************,8C-32-23-39-BC-41,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-50,*************,8C-32-23-39-BD-72,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-51,*************,8C-32-23-39-BB-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-52,************,8C-32-23-39-BB-F9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-54,*************,8C-32-23-39-BC-55,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-55,************,8C-32-23-39-BB-CF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-56,************,8C-32-23-39-BC-D4,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-57,************,8C-32-23-39-B9-B9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-59,*************,8C-32-23-39-B9-28,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-60,*************,8C-32-23-39-BC-FF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-61,*************,8C-32-23-39-BD-51,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-62,************,8C-32-23-39-BB-6B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-63,************,8C-32-23-39-BA-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-64,************2,8C-32-23-39-B8-90,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-65,*************,8C-32-23-39-B8-96,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-66,************,8C-32-23-39-BA-EF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-67,*************,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,************,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.445+08:00","@version":"1","message":"assignDevices [6400] - find 1 run devices:  {Plan22=[{ GE-PC-SDhigh-41,************,A4-0C-66-14-27-FC,[{Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-42,************,A4-0C-66-14-27-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-43,*************,A4-0C-66-14-2A-BC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-44,*************,A4-0C-66-14-28-4C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-45,************,A4-0C-66-14-28-3B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-46,*************,A4-0C-66-14-28-3A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-47,************,A4-0C-66-14-2A-5B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-48,*************,A4-0C-66-13-D6-0F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-49,************,8C-32-23-39-BC-41,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-50,*************,8C-32-23-39-BD-72,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-51,*************,8C-32-23-39-BB-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-52,************,8C-32-23-39-BB-F9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-54,*************,8C-32-23-39-BC-55,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-55,************,8C-32-23-39-BB-CF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-56,************,8C-32-23-39-BC-D4,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-57,************,8C-32-23-39-B9-B9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-59,*************,8C-32-23-39-B9-28,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-60,*************,8C-32-23-39-BC-FF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-61,*************,8C-32-23-39-BD-51,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-62,************,8C-32-23-39-BB-6B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-63,************,8C-32-23-39-BA-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-64,************2,8C-32-23-39-B8-90,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-65,*************,8C-32-23-39-B8-96,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-66,************,8C-32-23-39-BA-EF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-67,*************,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,************,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.448+08:00","@version":"1","message":"工单[6400] flash XCCB-6285DA-9T25-A_64GB , 参与此次Plan预分配的plan共有 [Plan22] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.532+08:00","@version":"1","message":" add 31 devices :[{ GE-PC-SDhigh-41,************,A4-0C-66-14-27-FC,[{Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-42,************,A4-0C-66-14-27-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-43,*************,A4-0C-66-14-2A-BC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-44,*************,A4-0C-66-14-28-4C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-45,************,A4-0C-66-14-28-3B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-46,*************,A4-0C-66-14-28-3A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-47,************,A4-0C-66-14-2A-5B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-48,*************,A4-0C-66-13-D6-0F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-49,************,8C-32-23-39-BC-41,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-50,*************,8C-32-23-39-BD-72,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-51,*************,8C-32-23-39-BB-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-52,************,8C-32-23-39-BB-F9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-54,*************,8C-32-23-39-BC-55,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-55,************,8C-32-23-39-BB-CF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-56,************,8C-32-23-39-BC-D4,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-57,************,8C-32-23-39-B9-B9,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-59,*************,8C-32-23-39-B9-28,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-60,*************,8C-32-23-39-BC-FF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-61,*************,8C-32-23-39-BD-51,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-62,************,8C-32-23-39-BB-6B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-63,************,8C-32-23-39-BA-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-64,************2,8C-32-23-39-B8-90,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-65,*************,8C-32-23-39-B8-96,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-66,************,8C-32-23-39-BA-EF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-67,*************,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,************,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }]  to plan:Plan22","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.554+08:00","@version":"1","message":"plan:Plan22 assign info update to ActualDeviceNum: 31, ExceptedSampleNum: 94","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.647+08:00","@version":"1","message":"[YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB] - Plan22 holdDevices  :[PlanDeviceEntity(id=277387, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=A4-0C-66-14-27-FC, no=GE-PC-SDhigh-41, position=GE3_1_02, score=170, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277388, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=A4-0C-66-14-27-2D, no=GE-PC-SDhigh-42, position=GE3_1_03, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277389, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=A4-0C-66-14-2A-BC, no=GE-PC-SDhigh-43, position=GE3_1_04, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277390, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=A4-0C-66-14-28-4C, no=GE-PC-SDhigh-44, position=GE3_1_05, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277391, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=A4-0C-66-14-28-3B, no=GE-PC-SDhigh-45, position=GE3_1_06, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277392, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=A4-0C-66-14-28-3A, no=GE-PC-SDhigh-46, position=GE3_1_07, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277393, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=A4-0C-66-14-2A-5B, no=GE-PC-SDhigh-47, position=GE3_1_09, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277394, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=A4-0C-66-13-D6-0F, no=GE-PC-SDhigh-48, position=GE3_1_10, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277395, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BC-41, no=GE-PC-SDhigh-49, position=GE3_1_11, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277396, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=8C-32-23-39-BD-72, no=GE-PC-SDhigh-50, position=GE3_1_12, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277397, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=8C-32-23-39-BB-27, no=GE-PC-SDhigh-51, position=GE3_1_13, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277398, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BB-F9, no=GE-PC-SDhigh-52, position=GE3_1_14, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277399, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=8C-32-23-39-BC-55, no=GE-PC-SDhigh-54, position=GE3_1_15, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277400, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BB-CF, no=GE-PC-SDhigh-55, position=GE3_1_16, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277401, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BC-D4, no=GE-PC-SDhigh-56, position=GE3_1_17, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277402, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-B9-B9, no=GE-PC-SDhigh-57, position=GE3_1_18, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277403, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=8C-32-23-39-B9-28, no=GE-PC-SDhigh-59, position=GE3_1_19, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277404, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=8C-32-23-39-BC-FF, no=GE-PC-SDhigh-60, position=GE3_1_20, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277405, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=8C-32-23-39-BD-51, no=GE-PC-SDhigh-61, position=GE3_1_21, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277406, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BB-6B, no=GE-PC-SDhigh-62, position=GE3_1_22, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277407, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BA-E5, no=GE-PC-SDhigh-63, position=GE3_1_23, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277408, orderId=6400, planId=113829, planName=Plan22, ip=************2, mac=8C-32-23-39-B8-90, no=GE-PC-SDhigh-64, position=GE3_1_24, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277409, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=8C-32-23-39-B8-96, no=GE-PC-SDhigh-65, position=GE3_1_25, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277410, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BA-EF, no=GE-PC-SDhigh-66, position=GE3_1_26, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277411, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=8C-32-23-39-B8-CE, no=GE-PC-SDhigh-67, position=GE3_1_27, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277412, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BD-7B, no=GE-PC-SDhigh-68, position=GE3_1_28, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277413, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BA-F0, no=GE-PC-SDhigh-69, position=GE3_1_29, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277414, orderId=6400, planId=113829, planName=Plan22, ip=*************, mac=8C-32-23-39-BA-F6, no=GE-PC-SDhigh-70, position=GE3_1_30, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277415, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-BC-10, no=GE-PC-SDhigh-71, position=GE3_1_31, score=310, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277416, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=8C-32-23-39-B8-74, no=GE-PC-SDhigh-72, position=GE3_1_32, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277417, orderId=6400, planId=113829, planName=Plan22, ip=************, mac=E0-D5-5E-B7-14-50, no=GE3_2_16, position=, score=180, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.739+08:00","@version":"1","message":"lock device:A4-0C-66-14-27-FC to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.787+08:00","@version":"1","message":"lock device:A4-0C-66-14-27-2D to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.798+08:00","@version":"1","message":"lock device:A4-0C-66-14-2A-BC to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.812+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-4C to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.821+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-3B to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.829+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-3A to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.839+08:00","@version":"1","message":"lock device:A4-0C-66-14-2A-5B to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.849+08:00","@version":"1","message":"lock device:A4-0C-66-13-D6-0F to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.86+08:00","@version":"1","message":"lock device:8C-32-23-39-BC-41 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.869+08:00","@version":"1","message":"lock device:8C-32-23-39-BD-72 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.876+08:00","@version":"1","message":"lock device:8C-32-23-39-BB-27 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.886+08:00","@version":"1","message":"lock device:8C-32-23-39-BB-F9 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.895+08:00","@version":"1","message":"lock device:8C-32-23-39-BC-55 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.904+08:00","@version":"1","message":"lock device:8C-32-23-39-BB-CF to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.914+08:00","@version":"1","message":"lock device:8C-32-23-39-BC-D4 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.923+08:00","@version":"1","message":"lock device:8C-32-23-39-B9-B9 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.934+08:00","@version":"1","message":"lock device:8C-32-23-39-B9-28 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.942+08:00","@version":"1","message":"lock device:8C-32-23-39-BC-FF to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.95+08:00","@version":"1","message":"lock device:8C-32-23-39-BD-51 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.957+08:00","@version":"1","message":"lock device:8C-32-23-39-BB-6B to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.966+08:00","@version":"1","message":"lock device:8C-32-23-39-BA-E5 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.977+08:00","@version":"1","message":"lock device:8C-32-23-39-B8-90 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.983+08:00","@version":"1","message":"lock device:8C-32-23-39-B8-96 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:03.991+08:00","@version":"1","message":"lock device:8C-32-23-39-BA-EF to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.002+08:00","@version":"1","message":"lock device:8C-32-23-39-B8-CE to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.012+08:00","@version":"1","message":"lock device:8C-32-23-39-BD-7B to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.02+08:00","@version":"1","message":"lock device:8C-32-23-39-BA-F0 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.029+08:00","@version":"1","message":"lock device:8C-32-23-39-BA-F6 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.036+08:00","@version":"1","message":"lock device:8C-32-23-39-BC-10 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.047+08:00","@version":"1","message":"lock device:8C-32-23-39-B8-74 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.058+08:00","@version":"1","message":"lock device:E0-D5-5E-B7-14-50 to orderNo:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.064+08:00","@version":"1","message":"[25f98e83] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.229+08:00","@version":"1","message":"lockDevice with ipList:[************, ************, *************, *************, ************, *************, ************, *************, ************, *************, *************, ************, *************, ************, ************, ************, *************, *************, *************, ************, ************, ************2, *************, ************, *************, ************, ************, *************, ************, ************, ************] - macList:[A4-0C-66-14-27-FC, A4-0C-66-14-27-2D, A4-0C-66-14-2A-BC, A4-0C-66-14-28-4C, A4-0C-66-14-28-3B, A4-0C-66-14-28-3A, A4-0C-66-14-2A-5B, A4-0C-66-13-D6-0F, 8C-32-23-39-BC-41, 8C-32-23-39-BD-72, 8C-32-23-39-BB-27, 8C-32-23-39-BB-F9, 8C-32-23-39-BC-55, 8C-32-23-39-BB-CF, 8C-32-23-39-BC-D4, 8C-32-23-39-B9-B9, 8C-32-23-39-B9-28, 8C-32-23-39-BC-FF, 8C-32-23-39-BD-51, 8C-32-23-39-BB-6B, 8C-32-23-39-BA-E5, 8C-32-23-39-B8-90, 8C-32-23-39-B8-96, 8C-32-23-39-BA-EF, 8C-32-23-39-B8-CE, 8C-32-23-39-BD-7B, 8C-32-23-39-BA-F0, 8C-32-23-39-BA-F6, 8C-32-23-39-BC-10, 8C-32-23-39-B8-74, E0-D5-5E-B7-14-50],no:YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.234+08:00","@version":"1","message":"自动分配GE-PC-SDhigh-41(************),GE-PC-SDhigh-42(************),GE-PC-SDhigh-43(*************),GE-PC-SDhigh-44(*************),GE-PC-SDhigh-45(************),GE-PC-SDhigh-46(*************),GE-PC-SDhigh-47(************),GE-PC-SDhigh-48(*************),GE-PC-SDhigh-49(************),GE-PC-SDhigh-50(*************),GE-PC-SDhigh-51(*************),GE-PC-SDhigh-52(************),GE-PC-SDhigh-54(*************),GE-PC-SDhigh-55(************),GE-PC-SDhigh-56(************),GE-PC-SDhigh-57(************),GE-PC-SDhigh-59(*************),GE-PC-SDhigh-60(*************),GE-PC-SDhigh-61(*************),GE-PC-SDhigh-62(************),GE-PC-SDhigh-63(************),GE-PC-SDhigh-64(************2),GE-PC-SDhigh-65(*************),GE-PC-SDhigh-66(************),GE-PC-SDhigh-67(*************),GE-PC-SDhigh-68(************),GE-PC-SDhigh-69(************),GE-PC-SDhigh-70(*************),GE-PC-SDhigh-71(************),GE-PC-SDhigh-72(************),GE3_2_16(************)给XCCB-6285DA-9T25-A_64GB下的Plan22","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.235+08:00","@version":"1","message":"add 31 devices to plan: Plan22 ,expect num: 120, left num: 26","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.235+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6400, flash:XCCB-6285DA-9T25-A_64GB, planEntity:OrderPlanEntity(id=113829, orderId=6400, name=Plan22, status=QUEUE, priority=91)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:04.275+08:00","@version":"1","message":"测试单: YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB plan: Plan22已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:05.372+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:05.377+08:00","@version":"1","message":"此次分配flash批次 XCCB-6285DA-9T25-A_64GB 下的Plan共消耗 120 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:05.383+08:00","@version":"1","message":"更新Flash:XCCB-6285DA-9T25-A_64GB的样片数量从120变更为0","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.873+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.877+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.881+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.13+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:12:03.609+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.611+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.775+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:15:02.777+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.779+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.895+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.896+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.898+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.767+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:21:02.768+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.769+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.774+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:27:02.777+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.779+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.441+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6400","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.452+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6635, orderId=6400, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250426_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6400","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.455+08:00","@version":"1","message":"[6400] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6400","traceType":"分配设备"}
