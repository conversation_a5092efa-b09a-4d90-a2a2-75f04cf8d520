package com.yeestor.work_order.entity.analysis;

import com.yeestor.entity.BaseEntity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity(name = "OrderFailAnalysis")
@Table(name = "wo_order_fail_analysis")
public class OrderFailAnalysisEntity extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    /**
     * 工单id
     */
    @Column(name="order_id")
    private long orderId ;

    /**
     * Flash批次
     */
    @Column(name="flash")
    private String flash ;

    /**
     * 原因和测试项目的键值对 的json  或者 跳过的原因
     */
    @Lob
    @Column(name="result", length=2048)
    private String result ;

    /**
     * 完成fail 类型，0表示完成，1表示跳过
     */
    @Column(name="type")
    private int type ;


    @Column(name="created_by")
    private String createdBy ;

    @Column(name="created_person")
    private String createdPerson ;

}
