package com.yeestor.work_order.service.role;

import com.yeestor.work_order.model.http.req.RoleParams;
import com.yeestor.work_order.model.http.resp.RoleVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class RoleServiceTest {
    @Autowired
    RoleService roleService;


    @Test
    void findAllRole() {
        List<RoleVO> roleVOList = roleService.findAllRole("","") ;
        Assertions.assertThat(roleVOList).isNotNull().hasSize(0);
    }


    @Test
    void createRole(){
        RoleParams roleParams = new RoleParams();
        roleParams.setName("test");
        roleParams.setDesc("desc");
        roleParams.setProducts(Collections.singletonList("SD"));
        roleParams.setPermissions(Arrays.stream(Permission.values()).map(Permission::name).collect(Collectors.toList()));
        roleParams.setUsers(Arrays.asList("0653290119972081","152015301836281814", "16322748657306344"));

        roleService.createRole(
                roleParams
        );

    }

}