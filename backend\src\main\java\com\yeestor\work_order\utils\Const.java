package com.yeestor.work_order.utils;


import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class Const {

    public static final String DEVICES_ASSIGNED_TO_PLAN = "分配设备至Plan";

    public static final String DEVICES_REPLACED = "设备被抢占";


    public static final String PRODUCT_GE = "GE";
    public static final String PRODUCT_EM = "EM";
    public static final String PRODUCT_SSD = "SSD";
    public static final String PRODUCT_INDUS = "IND";

    public static final String SUB_PRODUCT_SD = "SD";
    public static final String SUB_PRODUCT_U2 = "U2";
    public static final String SUB_PRODUCT_U3 = "U3";

    public static final String SUB_PRODUCT_EMMC = "eMMC";
    public static final String SUB_PRODUCT_UFS = "UFS";

    public static final String SUB_PRODUCT_SATA = "SATA";
    public static final String SUB_PRODUCT_PCIE = "PCIe";

    public static final String SUB_PRODUCT_IND_SATA = "IND_SATA";
    public static final String SUB_PRODUCT_IND_PCIE = "IND_PCIE";
    public static final String SUB_PRODUCT_IND_SD = "IND_SD";
    public static final String SUB_PRODUCT_IND_U2 = "IND_U2";
    public static final String SUB_PRODUCT_IND_U3 = "IND_U3";
    public static final String SUB_PRODUCT_IND_EMMC = "IND_EMMC";
    public static final String SUB_PRODUCT_IND_UFS = "IND_UFS";

    private static final Map<String, String> subProductToProductMap = new HashMap<>();

    static {
        subProductToProductMap.put(SUB_PRODUCT_SD, PRODUCT_GE);
        subProductToProductMap.put(SUB_PRODUCT_U2, PRODUCT_GE);
        subProductToProductMap.put(SUB_PRODUCT_U3, PRODUCT_GE);

        subProductToProductMap.put(SUB_PRODUCT_EMMC, PRODUCT_EM);
        subProductToProductMap.put(SUB_PRODUCT_UFS, PRODUCT_EM);
        subProductToProductMap.put(SUB_PRODUCT_EMMC.toUpperCase(Locale.ROOT), PRODUCT_EM);

        subProductToProductMap.put(SUB_PRODUCT_SATA, PRODUCT_SSD);
        subProductToProductMap.put(SUB_PRODUCT_PCIE, PRODUCT_SSD);
        subProductToProductMap.put(SUB_PRODUCT_PCIE.toUpperCase(Locale.ROOT), PRODUCT_SSD);

        subProductToProductMap.put(SUB_PRODUCT_IND_SD, PRODUCT_INDUS);
        subProductToProductMap.put(SUB_PRODUCT_IND_U2, PRODUCT_INDUS);
        subProductToProductMap.put(SUB_PRODUCT_IND_U3, PRODUCT_INDUS);
        subProductToProductMap.put(SUB_PRODUCT_IND_EMMC, PRODUCT_INDUS);
        subProductToProductMap.put(SUB_PRODUCT_IND_UFS, PRODUCT_INDUS);
        subProductToProductMap.put(SUB_PRODUCT_IND_SATA, PRODUCT_INDUS);
        subProductToProductMap.put(SUB_PRODUCT_IND_PCIE, PRODUCT_INDUS);
    }

    public static String getProductBySubProduct(String subProduct) {
        String product = subProductToProductMap.get(subProduct.toUpperCase(Locale.ROOT));
        if (product == null) {
            throw new IllegalArgumentException("Unknown subProduct: " + subProduct);
        }
        return product;
    }

}
