package com.yeestor.work_order.entity;

import com.yeestor.entity.BaseEntity;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity(name = "FlashNotice")
@Table(name = "wo_flash_notice")
public class FlashNoticeEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    /**
     * 工单id 。这里不采用外键。
     */
    @Column(name = "order_id")
    private long orderId ;

    /**
     * Flash 的批次信息。不是Flash id。
     */
    @Column(name = "flash")
    private String flash ;

    /**
     * 钉钉机器人卡片更新id
     */
    @Column(name = "out_track_id")
    private String outTrackId ;

}
