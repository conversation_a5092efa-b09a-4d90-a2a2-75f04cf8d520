package com.yeestor.work_order.model.redis;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.device.DeviceLockInfoEntity;
import com.yeestor.work_order.model.base.DeviceBaseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.data.redis.core.TimeToLive;
import org.springframework.data.redis.core.index.Indexed;

@Data
@Builder
@JsonAutoDetect
@NoArgsConstructor
@AllArgsConstructor
@RedisHash(value = "DeviceLockData")
public class DeviceLockData implements DeviceBaseInfo {

    @Id
    private String ip ;

    private String mac ;

    /**
     * 完整的工单号
     */
    @Indexed
    private String orderFlashNo;
    /**
     * 工单号
     */
    @Indexed
    private long orderId ;

    /**
     * Flash 批次
     */
    @Indexed
    private String flash ;
    /**
     * 分配到的指定的Plan
     */
    @Indexed
    private String planName ;

    private long lockAt ;

    /**
     * 产品属性,如SD, 非产品线属性(GE)
     */
    @Indexed
    private String product ;

    /**
     * 设备的状态, 主要是用于区分设备是占用还是正在运行中.
     */
    @Indexed
    private String status ;

    @TimeToLive
    private long timeout ;


    public DeviceLockInfoEntity toInfoEntity(
            WorkOrderEntity orderEntity,
            PlanDeviceEntity deviceEntity
    ){
        DeviceLockInfoEntity entity = new DeviceLockInfoEntity();
        entity.setOrderId(this.orderId);
        entity.setFlash(this.flash);
        entity.setPlanId(deviceEntity.getPlanId());
        entity.setPlanName(this.planName);
        entity.setDeviceId(deviceEntity.getId());
        entity.setIp(this.ip);
        entity.setMac(this.mac);
        entity.setPosition(deviceEntity.getPosition());
        entity.setNo(deviceEntity.getNo());
        entity.setLockedOrderFlashNo(this.orderFlashNo);
        entity.setProduct(orderEntity.getProduct());
        entity.setSubProduct(orderEntity.getSubProduct());
        entity.setLocked(true);
        return entity;
    }

}
