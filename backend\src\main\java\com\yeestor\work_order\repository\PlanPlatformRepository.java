package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.PlanPlatformEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface PlanPlatformRepository extends JpaRepository<PlanPlatformEntity, Long> {
    List<PlanPlatformEntity> findAllByPlanId(long planId);
    List<PlanPlatformEntity> findAllByMacAndNumberAndStatusIn(
            String mac,
            String number,
            List<PlanPlatformEntity.Status> fixedStatusList
    );

    void deleteAllByPlanId(long planId);

    int countByPlanIdAndReleaseAtNotNull(long planId);

    List<PlanPlatformEntity> findAllByPlanIdAndMacAndNumberIn(
            long planId,
            String mac,
            List<String> numberList
    );

    List<PlanPlatformEntity> findAllByPlanIdAndMacAndNumber(
            long planId,
            String mac,
            String number
    );

    List<PlanPlatformEntity> findAllByPlanIdAndStatusIn(long planId, List<PlanPlatformEntity.Status> fixedStatusList);
}
