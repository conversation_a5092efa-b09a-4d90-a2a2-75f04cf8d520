package com.yeestor.work_order.model.ci;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.entity.OrderDetailEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.exception.ErrorImportOrderException;
import com.yeestor.work_order.utils.TextUtils;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yeestor.work_order.utils.Const.*;

@Data
@Slf4j
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
public class OrderInfoModel {

    public static final String SUB_PRODUCT_UNKNOWN = "UNKNOWN";

    public static final String VERSION_TYPE_ALPHA = "Alpha";
    public static final String VERSION_TYPE_RELEASE = "Release";


    @Data
    @JsonAutoDetect
    public static class ParamsModel {
        private String version;
        private List<String> flashName;
        private String fwType;
        private String cfgFileName;
        private String specialMpVersionConf;
        private String versionType;
        private String fwSvnPath;
        private String fwVersion;
        private String mpVersion;
        private String fwReleaseNote;
        private String fwSvnPathCheck;
        private String scanReleaseNote;
        private String scanPath;
        private String scanPathCheck;
        private String toolPath;
        private String mpReleaseNote;
        private String productPath;
        private String testName;
        private String[] times;
        private int priority;
        private String carbonCopy;
        private String productId;
        private String projectId;
        private String executionId ;
        private BugLink[] bugLink;
        private List<String> orderPlan;
        private String htmlText;
        private String projectName;
        private String startTime;
        private String endTime;
        private Short createType;

        public String getFullVersion(String mpFilePath){
            return Optional.ofNullable(mpFilePath).map(s -> {
                // \\172.18.2.239\1软件工具\04外发工具版本\MPTOOLS\202211YS6285\MPTool_v2.0.0.11.891_20220215(6285_EN).7z
                // s 为文件路径，截取得到最后的文件名，然后去掉文件后缀
                String spiltStr = "\\\\";
                if (!s.contains(spiltStr)) {
                    return version;
                }

                String[] strs = s.split(spiltStr);
                if (strs.length <= 1) {
                    return version;
                }
                String filename = strs[strs.length - 1];
                return filename.substring(0, filename.lastIndexOf("."));
            }).orElse(version);
        }
    }

    @Data
    @JsonAutoDetect
    public static class BugLink {
        private String bugId;
        private String solved;
        private long time;
    }

    private String mpFilePath;
    private long createTime;
    private String mailList;
    private String params;
    private String job;
    private String userId;
    private String productName;
    private String chipName;

    /**
     * 产品版本类型，分为工业级和消费级版本
     * consumer -> 消费级版本
     * industrial -> 工业级版本
     */
    private String productType;

    private String ciID ;
    private ParamsModel paramsModel;


    /**
     * format String {@link #params} to {@link ParamsModel}
     *
     * @return return formatted model
     */
    @SneakyThrows
    public ParamsModel formatParams() {
        if (paramsModel != null) {
            return paramsModel;
        }
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        paramsModel = mapper.readValue(params, ParamsModel.class);
        return paramsModel;
    }


    public String getProduct() {
        return getProductBySubProduct(getSubProduct());
    }

    public String getSubProduct() {
        String subProduct = SUB_PRODUCT_UNKNOWN;
        if ("USB".equals(productName)) {
            if (chipName.equals("1581") || chipName.equals("1583")) {
                subProduct = "U2";
            } else if (chipName.equals("5081") || chipName.equals("5083") || chipName.equals("5085")) {
                subProduct = "U3";
            }
        } else if ("SD".equals(productName)) {
            subProduct = "SD";
        } else if ("EMMC".equals(productName)) {
            subProduct = "eMMC";
        } else if ("UFS".equals(productName)) {
            subProduct = "UFS";
        } else if ("PCIE".equals(productName)) {
            subProduct = "PCIe";
        } else if ("SATA".equals(productName)) {
            subProduct = SUB_PRODUCT_SATA;
        }
        if ("industrial".equals(productType)) {
            return PRODUCT_INDUS + "_" + subProduct.toUpperCase();
        }
        return subProduct;
    }

    public List<Tuple2<WorkOrderEntity, OrderDetailEntity>> buildEntityList(
            String testTaskLink,
            String prefix
    ) {
        ParamsModel paramsModel = formatParams();
        if (paramsModel.getFlashName() == null) {
            return new ArrayList<>();
        }

        return paramsModel.getFlashName()
                .stream()
                .map(flash -> toEntity(flash, paramsModel, ciID, testTaskLink, prefix))
                .collect(Collectors.toList());

    }

    public void checkParams(){
        String subProduct = getSubProduct();
        if (OrderInfoModel.SUB_PRODUCT_UNKNOWN.equals(subProduct)) {
            log.error("subProduct:{} is unknown", subProduct);
            throw new ErrorImportOrderException("SubProduct cannot found!");
        }
        List<Tuple2<WorkOrderEntity, OrderDetailEntity>> results = buildEntityList("", "");
        if (results.isEmpty()) {
            log.error("flashName is empty");
            throw new ErrorImportOrderException("FlashName cannot found!");
        }

    }

    public Tuple2<WorkOrderEntity, OrderDetailEntity> toEntity(
            String flash,
            ParamsModel paramsModel,
            String ciId,
            String testTaskLink,
            String prefix
    ) {
        String subProduct = getSubProduct();
        WorkOrderEntity entity = new WorkOrderEntity();

        entity.setCreatedAt(System.currentTimeMillis());
        entity.setStatus(WorkOrderEntity.Status.CREATED);

        entity.setBuildBy(userId);
        entity.setBuildStartAt(createTime);
        entity.setBuildEndAt(System.currentTimeMillis());

        String fullVersion = paramsModel.getFullVersion(mpFilePath);
        entity.setVersion(paramsModel.getVersion());
        entity.setVersionType(paramsModel.getVersionType());
        entity.setFullVersion(fullVersion);
        String orderVersion;  // MP 版本号
        String fwVersion;      // fw 版本号
        String flashName = flash;   // flashName
        // 如果是UFS或者eMMC
        if (
                SUB_PRODUCT_EMMC.equalsIgnoreCase(subProduct)
                        || SUB_PRODUCT_UFS.equalsIgnoreCase(subProduct)
                        || SUB_PRODUCT_IND_EMMC.equalsIgnoreCase(subProduct)
        ) {
            // EM产线的版本号需要特殊处理
            String pattern = "\\d{6}";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(fullVersion);
            if (m.find()) {
                entity.setVersion(m.group());
            } else {
                throw new ErrorImportOrderException("导入工单失败, flyCode版本号不满足限制条件:" + fullVersion + "！");
            }
            if (flashName.length() > 11) {
                flashName = flashName.substring(flashName.length() - 11);
            }
            orderVersion = paramsModel.getMpVersion();
            fwVersion = paramsModel.getFwVersion();
        } else if (SUB_PRODUCT_PCIE.equalsIgnoreCase(subProduct)) {
            String[] split = fullVersion.split("_");
            orderVersion = split[1];
            fwVersion = split[2];
        } else {
            orderVersion = TextUtils.parseMpToolVersion(fullVersion);
            fwVersion = "";
        }

        entity.setNo(
                TextUtils.buildOrderNo(
                        prefix,
                        getChipName(),
                        "MPTOOL",
                        orderVersion,
                        flashName,
                        null,
                        fwVersion,
                        getSubProduct()
                ));
        entity.setBuildId(Long.parseLong(ciId));

        entity.setPriority(paramsModel.getPriority());

        // 产品线， 产品，主控
        entity.setProduct(getProduct());
        entity.setSubProduct(getSubProduct());
        entity.setChip(chipName);
        entity.setMpToolPath(getMpFilePath());
        entity.setFlash(flash);
        // 工单的导入方式
        short createType = Optional.ofNullable(paramsModel.getCreateType()).orElse((short) 0) ;
        entity.setFeature( createType == 1 ? WorkOrderEntity.FEATURE_FAE : 0 );

        OrderDetailEntity detailEntity = new OrderDetailEntity();

        detailEntity.setCiJson(getParams());
        detailEntity.setProductType(getProductType());
        detailEntity.setFwSvnPath(paramsModel.getFwSvnPath());
        detailEntity.setFwSvnVersion(paramsModel.getFwReleaseNote());
        detailEntity.setVersionLog(paramsModel.getHtmlText());
        detailEntity.setJobName(getJob());
        detailEntity.setMpToolPath(paramsModel.getToolPath());
        detailEntity.setMpFilePath(getMpFilePath());
        detailEntity.setMpToolVersion(paramsModel.getMpReleaseNote());
        detailEntity.setCap("Unknown");
        detailEntity.setDriverVersion(paramsModel.getVersion());
        detailEntity.setMailList(getMailList());

        // 禅道 相关信息
        detailEntity.setZentaoProduct(paramsModel.getProductId());
        detailEntity.setZentaoExecutionId(paramsModel.getExecutionId());

        if (testTaskLink != null && testTaskLink.contains("testtask-view-")) {
            String testtaskId = testTaskLink.substring(testTaskLink.indexOf("testtask-view-") + "testtask-view-".length(), testTaskLink.indexOf(".html"));
            detailEntity.setZentaoTestTask(testtaskId);
        }
        else {
            detailEntity.setZentaoTestTask(testTaskLink);
        }
        // TODO 解析测试点、
        detailEntity.setTestPoint("");
        detailEntity.setRequirement(paramsModel.getHtmlText());
        detailEntity.setPlanList(String.join(";", paramsModel.getOrderPlan()));


        return Tuples.of(entity, detailEntity);
    }

}


