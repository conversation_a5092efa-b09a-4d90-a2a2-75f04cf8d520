package com.yeestor.work_order.model.http.req.terminal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "AddPlatformParams", description = "Plan 增加平台设备的参数")
public class AddPlatformParams {
    @Min(value=1,message = "无效的工单ID")
    @ApiModelProperty("工单ID")
    private long orderId;

    @NotBlank(message = "flash 批次号不能为空")
    @ApiModelProperty("flash批次")
    private String flash;

    @Min(value=1,message = "无效的Plan ID")
    @ApiModelProperty("Plan ID")
    private long planId ;

    @NotEmpty(message = "平台列表不能为空")
    @ApiModelProperty(value = "平台列表", required = true)
    private List<PlatformInfo> platformList;
}
