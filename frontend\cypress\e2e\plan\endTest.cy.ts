///<reference types="cypress" />

// context('with plan operate', () => {
//   describe('plan-operate', () => {
//     // 需要操作的工单id
//     const orderId = 363;
//     const flashName = 'flashname01_32GB';
//     const planName = 'Plan44';
//     beforeEach(() => {
//       cy.viewport(1920, 960);
//       cy.visit(`http://172.18.11.245:8512/work_order/GE/SD/${orderId}`).wait(5000);
//     });

//     it('开始Plan测试', () => {
//       cy.get('#flash-tab-' + flashName)
//         .click()
//         .wait(1000);
//       endPlan(planName);
//     });
//   });
// });

const endPlan = function (name: string) {
  cy.get(`a#plan-operate-menu-${name}`).click().get(`button#plan-end-${name}`).click().wait(20000);

  //  检验是否开始测试成功
  cy.get(`img#plan-view-${name}`).click().get(`div#plan-view-status-${name}`).contains('已完成');

  cy.get(`span#plan-view-title-${name}`).click();
};

export { endPlan };
