package com.yeestor.work_order.service.flow;

import com.yeestor.utils.TimeUtils;
import com.yeestor.work_order.controller.order.FlowController;
import com.yeestor.work_order.entity.analysis.OrderFailAnalysisEntity;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.document.DocumentEntity;
import com.yeestor.work_order.entity.review.ReviewInfoEntity;
import com.yeestor.work_order.exception.IllegalFlowException;
import com.yeestor.work_order.repository.analysis.FailAnalysisRepository;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.repository.document.FlashDocumentRepository;
import com.yeestor.work_order.repository.review.ReviewInfoRepository;
import com.yeestor.work_order.repository.review.ReviewResultRepository;
import com.yeestor.work_order.service.plan.PlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
@RequiredArgsConstructor
public class FlowService {

    private final OrderPlanRepository planRepository;
    private final FlashDocumentRepository flashDocumentRepository;
    private final ReviewResultRepository reviewResultRepository;

    private final ReviewInfoRepository reviewInfoRepository;
    private final FailAnalysisRepository failAnalysisRepository;

    private final PlanService planService ;

    public Map<String, Object> fetchFlashFlow(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {
        long orderId = orderEntity.getId();
        String flash = flashEntity.getFlash();

        List<FlowController.FlashFlow> flashFlows = new ArrayList<>();
        List<FlowController.FlashFlowEdge> flashFlowEdges = new ArrayList<>();
        HashMap<String, Object> data = new HashMap<>();
        OrderFlashEntity.Status status = flashEntity.getStatus();

        data.put("flow", flashFlows);
        data.put("edge", flashFlowEdges);
        data.put("current", status);
        data.put("time", TimeUtils.getDistanceTime(flashEntity.getCreatedAt(), Optional.ofNullable(flashEntity.getCompleteAt()).orElse(System.currentTimeMillis())));

        AtomicReference<FlowController.FlashFlow> flow = new AtomicReference<>(FlowController.FlashFlow.builder()
                .status(OrderFlashEntity.Status.NEW)
                .person(orderEntity.getConfirmPerson())
                .time(orderEntity.getConfirmAt())
                .build());
        flashFlows.add(
                flow.get()
        );

        final long[] lastTime = {flow.get().getTime()};
        AtomicReference<OrderFlashEntity.Status> lastStatus = new AtomicReference<>(OrderFlashEntity.Status.NEW);
        if (status.greaterThan(OrderFlashEntity.Status.WAITING_FOR_START)) {
            planRepository.findFirstConfirmedPlanList(orderId, flash, OrderPlanEntity.TYPE_AUTO)
                    .stream()
                    .findFirst()
                    .ifPresentOrElse(p -> {
                                flow.set(FlowController.FlashFlow.builder()
                                        .status(OrderFlashEntity.Status.WAITING_FOR_START)
                                        .person(p.getConfirmedPerson())
                                        .time(p.getConfirmedAt())
                                        .build());
                                flashFlows.add(flow.get());
                                log.info("confirmedAt: {}", p);
                                long spendTime = flow.get().getTime() - lastTime[0];
                                flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                                        .source(lastStatus.get())
                                        .target(OrderFlashEntity.Status.WAITING_FOR_START)
                                        .spendTime(spendTime)
                                        .build());

                                lastStatus.set(OrderFlashEntity.Status.WAITING_FOR_START);
                                lastTime[0] = flow.get().getTime();
                            },
                            () -> {
                                // 没有找到自动的Plan，只有一种可能，就是都是手动Plan，这个时候，就不需要计算了。
                                flow.set(FlowController.FlashFlow.builder()
                                        .status(OrderFlashEntity.Status.WAITING_FOR_START)
                                        .person("System")
                                        .time(lastTime[0])
                                        .build());
                                flashFlows.add(flow.get());
                                long spendTime = flow.get().getTime() - lastTime[0];
                                flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                                        .source(lastStatus.get())
                                        .target(OrderFlashEntity.Status.WAITING_FOR_START)
                                        .spendTime(spendTime)
                                        .build());

                                lastStatus.set(OrderFlashEntity.Status.WAITING_FOR_START);
                                lastTime[0] = flow.get().getTime();
                            });
        }

        if (
                status.greaterThan(OrderFlashEntity.Status.IN_PROGRESS) &&
                        flashEntity.getTestStartAt() != null
        ) {
            flow.set(
                    FlowController.FlashFlow.builder()
                            .status(OrderFlashEntity.Status.IN_PROGRESS)
                            .person(flashEntity.getTestPerson())
                            .time(flashEntity.getTestStartAt())
                            .build()
            );
            flashFlows.add(flow.get());

            long spendTime = flow.get().getTime() - lastTime[0];
            flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                    .source(lastStatus.get())
                    .target(OrderFlashEntity.Status.IN_PROGRESS)
                    .spendTime(spendTime)
                    .build());

            lastStatus.set(OrderFlashEntity.Status.IN_PROGRESS);
            lastTime[0] = flow.get().getTime();
        }

        if (
                status.greaterThan(OrderFlashEntity.Status.WAITING_FOR_MERGE)
        ) {

            if (flashEntity.getTestEndAt() == null) {
                // 基本可以认为是已经撤销了.
                FlowController.FlashFlow flashFlow = flow.get();

                flashFlows.remove(flashFlow);
                flashFlow.setRevoked(true);
                flashFlow.setRevokePerson(flashEntity.getRevokeBy());
                flashFlow.setRevokeReason(flashEntity.getRevokeReason());
                flashFlow.setRevokeTime(flashEntity.getRevokeAt());
                flashFlows.add(flashFlow);

                long spendTime = flashEntity.getRevokeAt() - lastTime[0];
                flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                        .source(lastStatus.get())
                        .target(OrderFlashEntity.Status.WAITING_FOR_MERGE)
                        .spendTime(spendTime)
                        .revoked(true)
                        .time(flashEntity.getRevokeAt())
                        .person(flashEntity.getRevokeBy())
                        .reason(flashEntity.getRevokeReason())
                        .build());
                return data;
            }

            flow.set(
                    FlowController.FlashFlow.builder()
                            .status(OrderFlashEntity.Status.WAITING_FOR_MERGE)
                            .person(flashEntity.getTestPerson())
                            .time(flashEntity.getTestEndAt())
                            .build()
            );

            flashFlows.add(flow.get());
            long spendTime = flow.get().getTime() - lastTime[0];
            flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                    .source(lastStatus.get())
                    .target(OrderFlashEntity.Status.WAITING_FOR_MERGE)
                    .spendTime(spendTime)
                    .build());

            lastStatus.set(OrderFlashEntity.Status.WAITING_FOR_MERGE);
            lastTime[0] = flow.get().getTime();
        }

        if (status.greaterThan(OrderFlashEntity.Status.WAITING_FOR_REPORT)) {
            flow.set(
                    FlowController.FlashFlow.builder()
                            .status(OrderFlashEntity.Status.WAITING_FOR_REPORT)
                            .person(flashEntity.getTestPerson())
                            .time(flashEntity.getMergeCompleteAt())
                            .build()
            );
            flashFlows.add(flow.get());
            long spendTime = flow.get().getTime() - lastTime[0];
            flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                    .source(lastStatus.get())
                    .target(OrderFlashEntity.Status.WAITING_FOR_REPORT)
                    .spendTime(spendTime)
                    .build());

            lastStatus.set(OrderFlashEntity.Status.WAITING_FOR_REPORT);
            lastTime[0] = flow.get().getTime();
        }

        if (
                status.greaterThan(OrderFlashEntity.Status.WAITING_FOR_FAIL_ANALYSIS)
        ) {


            List<DocumentEntity> docs = flashDocumentRepository.findLastUploadReport(orderId, flash, PageRequest.of(0, 1));

            docs.stream().findFirst().ifPresent(lastDocumentEntity -> {
                flow.set(
                        FlowController.FlashFlow.builder()
                                .status(OrderFlashEntity.Status.WAITING_FOR_FAIL_ANALYSIS)
                                .person(lastDocumentEntity.getCreatedPerson())
                                .time(lastDocumentEntity.getCreatedAt())
                                .build()
                );

                flashFlows.add(flow.get());
                long spendTime = flow.get().getTime() - lastTime[0];
                flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                        .source(lastStatus.get())
                        .target(OrderFlashEntity.Status.WAITING_FOR_FAIL_ANALYSIS)
                        .spendTime(spendTime)
                        .build());

                lastStatus.set(OrderFlashEntity.Status.WAITING_FOR_FAIL_ANALYSIS);
                lastTime[0] = flow.get().getTime();

            });
        }

        if (
                status.greaterThan(OrderFlashEntity.Status.FAIL_ANALYSIS_STARTED) &&
                        flashEntity.getStartFailAt() != null
        ) {
            Map<String, String> extraInfo = new HashMap<>();
            extraInfo.put("assignTo", flashEntity.getFailAssignTo());

            flow.set(
                    FlowController.FlashFlow.builder()
                            .status(OrderFlashEntity.Status.FAIL_ANALYSIS_STARTED)
                            .person(flashEntity.getStartFailPerson())
                            .extraInfo(extraInfo)
                            .time(flashEntity.getStartFailAt())
                            .build()
            );

            flashFlows.add(flow.get());
            long spendTime = flow.get().getTime() - lastTime[0];
            flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                    .source(lastStatus.get())
                    .target(OrderFlashEntity.Status.FAIL_ANALYSIS_STARTED)
                    .spendTime(spendTime)
                    .build());

            lastStatus.set(OrderFlashEntity.Status.FAIL_ANALYSIS_STARTED);
            lastTime[0] = flow.get().getTime();
        }

        if (
                status.greaterThan(OrderFlashEntity.Status.WAITING_FOR_REVIEW)
        ) {

            failAnalysisRepository.findFirstByOrderIdAndFlash(orderId, flash)
                    .ifPresent(e -> {
                        flow.set(
                                FlowController.FlashFlow.builder()
                                        .status(OrderFlashEntity.Status.WAITING_FOR_REVIEW)
                                        .person(e.getCreatedPerson())
                                        .time(e.getCreatedAt())
                                        .build()
                        );
                        flashFlows.add(flow.get());
                        long spendTime = flow.get().getTime() - lastTime[0];
                        flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                                .source(lastStatus.get())
                                .target(OrderFlashEntity.Status.WAITING_FOR_REVIEW)
                                .spendTime(spendTime)
                                .build());

                        lastStatus.set(OrderFlashEntity.Status.WAITING_FOR_REVIEW);
                        lastTime[0] = flow.get().getTime();
                    });
        }

        if (
                status.greaterThan(OrderFlashEntity.Status.REVIEW_STARTED)
        ) {
            reviewInfoRepository.findFirstByOrderIdAndFlash(orderId, flash)
                    .ifPresent(e -> {
                        flow.set(
                                FlowController.FlashFlow.builder()
                                        .status(OrderFlashEntity.Status.REVIEW_STARTED)
                                        .person(e.getCreatedPerson())
                                        .time(e.getCreatedAt())
                                        .build()
                        );

                        flashFlows.add(flow.get());
                        long spendTime = flow.get().getTime() - lastTime[0];
                        flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                                .source(lastStatus.get())
                                .target(OrderFlashEntity.Status.REVIEW_STARTED)
                                .spendTime(spendTime)
                                .build());

                        lastStatus.set(OrderFlashEntity.Status.REVIEW_STARTED);

                        lastTime[0] = flow.get().getTime();
                    });

        }

        if (
                status.greaterThan(OrderFlashEntity.Status.COMPLETED)
                        && flashEntity.getCompleteAt() != null
        ) {
            reviewResultRepository.findFirstByOrderIdAndFlash(orderId, flash)
                    .ifPresent(e -> {
                        Long time = e.getCreatedAt() != 0 ? e.getCreatedAt() : flashEntity.getCompleteAt();
                        flow.set(
                                FlowController.FlashFlow.builder()
                                        .status(OrderFlashEntity.Status.COMPLETED)
                                        .person(e.getCreatedPerson())
                                        .time(time)
                                        .build()
                        );
                        flashFlows.add(flow.get());

                        long spendTime = flow.get().getTime() - lastTime[0];

                        flashFlowEdges.add(FlowController.FlashFlowEdge.builder()
                                .source(lastStatus.get())
                                .target(OrderFlashEntity.Status.COMPLETED)
                                .spendTime(spendTime)
                                .build());

                        lastStatus.set(OrderFlashEntity.Status.COMPLETED);
                        lastTime[0] = flow.get().getTime();
                    });
        }

        return data;
    }


    /**
     * 获取工单当前流程的开始时间
     *
     * @param order 工单实体类
     * @return 工单当前流程的开始时间
     */
    public long getOrderCurrentStartTime(WorkOrderEntity order) {
        WorkOrderEntity.Status orderStatus = order.getStatus();
        switch (orderStatus) {
            case CREATED:
                return order.getCreatedAt();
            case CONFIRMED_FLASH:
                return order.getConfirmAt();
            case TESTING:
                return order.getTestStartAt();
            case EVALUATING:
                return order.getTestEndAt();
            case COMPLETED:
                return order.getEndAt();
            case REVOKED:
                return order.getRevokeAt();
            default:
                throw new IllegalFlowException("工单流程异常");
        }
    }

    /**
     * 获取Flash批次当前流程的开始时间
     * @param flash Flash批次实体类
     * @return Flash批次当前流程的开始时间
     */
    public long getFlashCurrentStartTime(OrderFlashEntity flash) {
        OrderFlashEntity.Status flashStatus = flash.getStatus();
        switch (flashStatus) {
            case NEW:
                return flash.getCreatedAt();
            case WAITING_FOR_START:
                return getWaitForStartTime(flash).getT2();
            case IN_PROGRESS:
                return flash.getTestStartAt();
            case WAITING_FOR_MERGE:
                return flash.getTestEndAt();
            case WAITING_FOR_REPORT:
                return flash.getMergeCompleteAt();
            case WAITING_FOR_FAIL_ANALYSIS:
                return getWAitForFailAnalysisTime(flash).getT2();
            case FAIL_ANALYSIS_STARTED:
                return flash.getStartFailAt() ;
                case WAITING_FOR_REVIEW:
                    return getWaitForReviewTime(flash).getT2();
            case REVIEW_STARTED:
                return getReviewStartTime(flash).getT2();
            case COMPLETED:
                return flash.getCompleteAt();
            case CANCELLED:
                // 不知道为什么自己要将REVOKED 以及 CANCELLED 这两种状态混用, 而不是使用同一个名字.
                return flash.getRevokeAt();
            default:
                throw new IllegalFlowException("Flash 批次流程异常");
        }

    }


    /**
     * 获取Flash 批次的等待开始时间.
     * FOR {@link OrderFlashEntity.Status#WAITING_FOR_START}
     * @param flash flash 批次实体类
     * @return 首个Plan的确认人员, flash 批次的等待开始时间
     */
    private Tuple2<String,Long> getWaitForStartTime(
            OrderFlashEntity flash
    ) {
        // 首先获取整个批次的Plan 列表
        List<OrderPlanEntity> planEntityList = planRepository.findAllByOrderIdAndFlash(flash.getOrderId(), flash.getFlash());

        if (planEntityList.isEmpty()) {
            throw new IllegalStateException("Flash 批次的Plan 列表为空");
        }

        // 将Plan 列表按照 顺序进行排序
        List<OrderPlanEntity> sortedPlanList = planService.sortPlanList(planEntityList);
        OrderPlanEntity firstPlan = sortedPlanList.get(0);


        return Tuples.of(
                Optional.ofNullable(firstPlan.getConfirmedPerson()).orElse("eSee.System"),
                firstPlan.getConfirmedAt()
        );

    }

    /**
     * 获取Flash 批次的 等待评审 状态的开始时间
     * FOR {@link OrderFlashEntity.Status#WAITING_FOR_FAIL_ANALYSIS}
     * @param flash  flash 批次实体类
     * @return 上传报告的人员, flash 批次的 等待评审 状态的开始时间
     */
    private Tuple2<String,Long>  getWAitForFailAnalysisTime(
            OrderFlashEntity flash
    ) {
        // 获取Flash 批次的文档
        List<DocumentEntity> docs = flashDocumentRepository.findLastUploadReport(flash.getOrderId(), flash.getFlash(),
                PageRequest.of(0, 1));

        if (docs.isEmpty()) {
            throw new IllegalFlowException("Flash批次的文档列表为空");
        }

        DocumentEntity doc = docs.get(0);

        return Tuples.of(
                doc.getCreatedPerson(),
                doc.getCreatedAt()
        );
    }


    /**
     * 获取Flash 批次的 等待评审 状态的开始时间
     * FOR {@link OrderFlashEntity.Status#WAITING_FOR_REVIEW}
     * @param flash  flash 批次实体类
     * @return 操作人员, flash 批次的 等待评审 状态的开始时间
     */
    private Tuple2<String,Long> getWaitForReviewTime(
            OrderFlashEntity flash
    ) {

        OrderFailAnalysisEntity failAnalysisEntity = failAnalysisRepository.findFirstByOrderIdAndFlash(
                        flash.getOrderId(),
                        flash.getFlash()
                )
                .orElseThrow(() -> new IllegalFlowException("Flash批次的评审列表为空"));
        return Tuples.of(
                failAnalysisEntity.getCreatedPerson(),
                failAnalysisEntity.getCreatedAt()
        );

    }


    /**
     * 获取Flash 批次的 评审 状态的开始时间
     * FOR {@link OrderFlashEntity.Status#REVIEW_STARTED}
     *
     * @param flash  flash 批次实体类
     * @return 操作人员, flash 批次的 评审 状态的开始时间
     */
    private Tuple2<String,Long> getReviewStartTime(
            OrderFlashEntity flash
    ) {
        ReviewInfoEntity reviewInfoEntity = reviewInfoRepository.findFirstByOrderIdAndFlash(
                        flash.getOrderId(),
                        flash.getFlash()
                )
                .orElseThrow(() -> new IllegalFlowException("Flash批次的评审信息为空"));
        return Tuples.of(
                reviewInfoEntity.getCreatedPerson(),
                reviewInfoEntity.getCreatedAt()
        );
    }


}
