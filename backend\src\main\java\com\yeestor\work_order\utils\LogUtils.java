package com.yeestor.work_order.utils;

import org.slf4j.MDC;

public class LogUtils {

    public static final String MDC_KEY_ORDER_NUMBER = "no";
    public static final String MDC_KEY_TRACE_TYPE = "traceType";

    public static final String MDC_KEY_FLASH = "flash";

    private LogUtils() {
    }


    public static void setOrderTracePoint(
            long orderId,
            String traceType
    ){
        MDC.put(MDC_KEY_ORDER_NUMBER, String.valueOf(orderId));
        MDC.put(MDC_KEY_TRACE_TYPE, traceType);
    }

    public static void  setOrderAndFlashTracePoint(
            long orderId,
            String flash,
            String traceType
    ){
        MDC.put(MDC_KEY_ORDER_NUMBER, String.valueOf(orderId));
        MDC.put(MDC_KEY_FLASH, flash);
        MDC.put(MDC_KEY_TRACE_TYPE, traceType);
    }


    public static void clearTracePoint(){
        MDC.remove(MD<PERSON>_KEY_ORDER_NUMBER);
        MDC.remove(MD<PERSON>_KEY_TRACE_TYPE);
        MDC.remove(MDC_KEY_FLASH);
    }

}
