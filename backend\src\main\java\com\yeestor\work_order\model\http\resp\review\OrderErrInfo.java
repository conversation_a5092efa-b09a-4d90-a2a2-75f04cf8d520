package com.yeestor.work_order.model.http.resp.review;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.HashMap;

@Data
@ApiModel(value = "OrderErrInfo", description = "工单错误信息")
public class OrderErrInfo {

    @ApiModelProperty(value = "flash 批次对应的错误列表, key: flash批次, value: 错误列表")
    private HashMap<String, ErrDiskInfo> errDiskInfo = new HashMap<>();

}
