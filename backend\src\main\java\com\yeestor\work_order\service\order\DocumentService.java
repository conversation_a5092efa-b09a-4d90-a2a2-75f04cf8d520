package com.yeestor.work_order.service.order;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.document.DocumentEntity;
import com.yeestor.work_order.entity.document.FlashDocumentEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.resp.review.ErrDiskInfo;
import com.yeestor.work_order.model.http.resp.SheetVO;
import com.yeestor.work_order.model.http.resp.review.OrderErrInfo;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.model.rms.ReportStatusChangeParams;
import com.yeestor.work_order.repository.OrderDetailRepository;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.repository.document.DocumentRepository;
import com.yeestor.work_order.repository.document.FlashDocumentRepository;
import com.yeestor.work_order.service.DownloadService;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.role.RoleService;
import com.yeestor.work_order.utils.DownloadUtils;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.work_order.utils.ZentaoAPI;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentService {

    private final OrderFlashRepository flashRepository;
    private final OrderDetailRepository orderDetailRepository ;
    private final DataChangeListener dataChangeListener ;
    private final DocumentRepository documentRepository ;
    private final FlashDocumentRepository flashDocumentRepository ;
    private final DownloadService downloadService ;
    private final ZentaoAPI zentaoAPI ;
    private final NotificationService notificationService ;
    private final OrderService orderService ;

    private final RoleService roleService ;
    private final WorkOrderRepository orderRepository ;

    /**
     * 更新报告状态
     * @param orderNo RMS工单号
     * @param reportInfoList 报告信息列表
     */
    @Transactional
    public void updateDocumentStatus(String orderNo, List<ReportStatusChangeParams.ReportInfo> reportInfoList) {
        // 根据工单号查询 工单Flash批次实体类
        OrderFlashEntity orderFlashEntity = flashRepository.findByOrderFlashNo(orderNo).orElse(null);
        assert orderFlashEntity != null;
        String traceType = "RMS报告合并回调" ;
        LogUtils.setOrderAndFlashTracePoint(orderFlashEntity.getOrderId(), orderFlashEntity.getFlash(), traceType);
        log.info("[RMS回调] 报告回调，reportInfoList={} Flash批次： {} 的状态为：{}", reportInfoList, orderFlashEntity.getFlash(), orderFlashEntity.getStatus());


        // 移除往期报告。
        flashDocumentRepository.deleteAllByOrderIdAndFlashAndSource(orderFlashEntity.getOrderId(), orderFlashEntity.getFlash(), "RMS");

        // 更新报告状态
        reportInfoList.forEach(reportInfo -> {
            long size = DownloadUtils.getSizeWithWebclient(reportInfo.getUrl());
            DocumentEntity documentEntity = new DocumentEntity() ;
                String url = reportInfo.getUrl() ;
            // 检查url 是否以http:// 或者https:// 开头，如果不是，则添加http://
            if (!url.startsWith("http://") && !url.startsWith("https://")) {
                url = "http://" + url ;
            }

            documentEntity.setUrl(url);
            documentEntity.setName(reportInfo.getName());
            documentEntity.setCreatedAt(System.currentTimeMillis());
            // 解析获取大小，ContentType 等。
            documentEntity.setSize(size);
            documentEntity.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            DocumentEntity result = documentRepository.save(documentEntity);

            FlashDocumentEntity flashDocumentEntity = new FlashDocumentEntity() ;
            flashDocumentEntity.setDocumentId(result.getId());
            flashDocumentEntity.setFlashId(orderFlashEntity.getId());
            flashDocumentEntity.setOrderId(orderFlashEntity.getOrderId());
            flashDocumentEntity.setFlash(orderFlashEntity.getFlash());
            flashDocumentEntity.setType(reportInfo.getType());
            flashDocumentEntity.setSource("RMS");
            flashDocumentRepository.save(flashDocumentEntity);
        });

        // 判断Flash 批次的状态是否是等待合并报告，如果是等待合并报告，则进入到下一个状态
        if( orderFlashEntity.getStatus() == OrderFlashEntity.Status.WAITING_FOR_MERGE) {
            // 到这个地方就需要等待 报告上传。
            orderFlashEntity.setStatus(OrderFlashEntity.Status.WAITING_FOR_REPORT);
            orderFlashEntity.setUpdatedAt(System.currentTimeMillis());
            orderFlashEntity.setMergeCompleteAt(System.currentTimeMillis());
            flashRepository.save(orderFlashEntity);

            log.info("Flash:{} 已进入等待上传报告的状态.",orderFlashEntity.getFlash());

            dataChangeListener.onDataChange(
                    DataChangeEvent.builder()
                            .type(DataChangeEvent.Type.FLASH_WAIT_REPORT)
                            .orderId(orderFlashEntity.getOrderId())
                            .flash(orderFlashEntity.getFlash())
                            .build()
            );
            WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderFlashEntity.getOrderId());
            notificationService.sendNeedUploadReportNotification(orderEntity,orderFlashEntity);

        }

        LogUtils.clearTracePoint();
    }


    /**
     * 解析 ErrDisk 文件，如果没有的话，则返回空
     * @param orderId 工单的ID
     * @return 返回工单中的错误信息。
     */
    public OrderErrInfo parseOrderErrDisk(long orderId) {
        // 查询出需要获取报告的Flash批次
        OrderErrInfo orderErrInfo = new OrderErrInfo() ;

        flashRepository.findAllByOrderId(orderId).forEach(flash -> {
            ErrDiskInfo errDiskInfo  ;
            try {
                errDiskInfo = parseErrDisk(flash);
                orderErrInfo.getErrDiskInfo().put(flash.getFlash(), Optional.ofNullable(errDiskInfo).orElse(new ErrDiskInfo()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return orderErrInfo ;
    }


    /**
     * 解析 Flash 批次的 ErrDisk 文件
     * @param flashEntity Flash批次实体类
     * @return 返回flash批次的错误信息列表
     */
    public ErrDiskInfo parseErrDisk(OrderFlashEntity flashEntity) {

        List<DocumentEntity> documentEntityList = flashDocumentRepository.findFlashErrorDocument(flashEntity.getId(), "error");
        if ( documentEntityList.size() > 0){
            return this.parseErrorDocument(documentEntityList.get(0)) ;
        }

        return null ;
    }


    /**
     * 将从RMS中拿到的Document解析成ErrDiskInfo
     * @param documentEntity 文档实体类
     * @return 返回错误信息
     */
    public ErrDiskInfo parseErrorDocument(DocumentEntity documentEntity) {

        Optional<InputStream> inputStreamOptional = downloadService.downloadFile(documentEntity) ;

        if(!inputStreamOptional.isPresent()){
            return null ;
        }

        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(inputStreamOptional.get());
        } catch (IOException e) {
            e.printStackTrace();
        }
        if(workbook == null){
            return null ;
        }

        DataFormatter dataFormatter = new DataFormatter();
        ErrDiskInfo errDiskInfo = new ErrDiskInfo() ;
        // 汇总统计
        Sheet sheet = workbook.getSheetAt(0);

        List<ErrDiskInfo.TestCountItem> statistics = new ArrayList<>() ;
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            Cell keyCell = row.getCell(0);
            String key = dataFormatter.formatCellValue(keyCell);
            String value = dataFormatter.formatCellValue(row.getCell(1));
            // 使用!切割，并取第一个值作为address

            String address = Optional.ofNullable(keyCell.getHyperlink())
                    .map(Hyperlink::getAddress)
                    .map(addressStr -> addressStr.split("!")[0])
                    .orElse(null);

            statistics.add(new ErrDiskInfo.TestCountItem(key,value, i - 1, address));
        }
        errDiskInfo.setStatistics(statistics);

        int sheetCount = workbook.getNumberOfSheets();
        for (int i = 1; i < sheetCount ; i++) {
            sheet = workbook.getSheetAt(i);
            String sheetName = sheet.getSheetName();
            List<ErrDiskInfo.ErrDiskLogItem> errDiskList = new ArrayList<>() ;
            for (int j = 1; j <= sheet.getLastRowNum(); j++) {
                Row row = sheet.getRow(j);
                ErrDiskInfo.ErrDiskLogItem errDiskLogItem = new ErrDiskInfo.ErrDiskLogItem() ;
                errDiskLogItem.setNo(dataFormatter.formatCellValue(row.getCell(0)));
                errDiskLogItem.setFailType(dataFormatter.formatCellValue(row.getCell(1)));
                errDiskLogItem.setPcNo(dataFormatter.formatCellValue(row.getCell(2)));
                errDiskLogItem.setLogPath(dataFormatter.formatCellValue(row.getCell(3)));

                if(StringUtils.hasText(errDiskLogItem.getNo()) && StringUtils.hasText(errDiskLogItem.getLogPath())){
                    errDiskList.add(errDiskLogItem);
                }
            }
            if(!errDiskList.isEmpty()){
                errDiskInfo.getLogSheets().put(sheetName,errDiskList);
            }
        }
        try {
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return errDiskInfo;
    }


    /**
     * 解析工单的总览信息
     * @param orderId 工单ID
     * @return 返回工单的总览信息，key 为flash批次，value 为总览信息
     */
    public HashMap<String, SheetVO> parseOrderSummary(long orderId) {
        HashMap<String, SheetVO> summaryInfo = new HashMap<>() ;
        flashRepository.findAllByOrderId(orderId).forEach(flash -> {
            try {

                List<DocumentEntity> documentEntityList = flashDocumentRepository.findFlashErrorDocument(flash.getId(), "report","LOCAL");
                if(documentEntityList.size() > 0){
                    // 只处理第一个
                    summaryInfo.put(flash.getFlash(), parseWorkbook(documentEntityList.get(0)));
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return summaryInfo;
    }

    /**
     * 将上传的文件解析出工单的总览信息
     * @param documentEntity 文档信息
     * @return 返回Flash批次的总览信息
     */
    public SheetVO parseWorkbook(DocumentEntity documentEntity) throws Exception {

        Optional<InputStream> inputStreamOptional = downloadService.downloadFile(documentEntity) ;

        if(!inputStreamOptional.isPresent()){
            return null ;
        }
        ObjectMapper mapper = new ObjectMapper();
        try(Workbook workbook = WorkbookFactory.create(inputStreamOptional.get())) {
            List<? extends PictureData> dataList = workbook.getAllPictures();
            log.debug("dataList:{}", dataList);
            SheetVO sheetVO = new SheetVO();
            // 获取工单表
            Sheet sheet = workbook.getSheetAt(0);
            String sheetName = sheet.getSheetName();
            sheetVO.setName(sheetName);
            sheetVO.setIdx(0);
            sheetVO.setCols(15);

            int lastRowNum = sheet.getLastRowNum();
            DataFormatter dataFormatter = new DataFormatter();
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();

            List<SheetVO.RowVO> rowList = new ArrayList<>();

            log.debug("lastRowNum: {}", lastRowNum);
            // 获取每个工作表中的数据、
            for (int j = 0; j <= lastRowNum; j++) {
                Row row = sheet.getRow(j);
                if (row == null) {
                    continue;
                }
                if (row.getZeroHeight()) {
                    continue;
                }

                int heightPx = (int) ((row.getHeightInPoints() / 72) * 96);
                // 获取每行的数据
                int lastCellNum = row.getLastCellNum();

                HashMap<String, SheetVO.CellVO> cellValueHashMap = new HashMap<>();
                for (int k = 0; k < lastCellNum; k++) {
                    Cell cell = row.getCell(k);
                    if (cell == null) {
                        continue;
                    }
                    String cellValue = dataFormatter.formatCellValue(cell);
                    if (cell.getCellType() == CellType.FORMULA) {
                        // 执行公式,并获取公式的值
                        cellValue = dataFormatter.formatCellValue(cell, evaluator);
                    }

                    CellStyle style = cell.getCellStyle();
                    org.apache.poi.ss.util.CellRangeAddress range = DocumentService.getMergedRegion(sheet, j, k);

                    HashMap<String, String> styleMap = SheetVO.cellStyleToCss(style);
                    int widthPx = (int) sheet.getColumnWidthInPixels(k);

                    styleMap.put("width", widthPx + "px");
                    if (range != null) {
                        int firstRow = range.getFirstRow();
                        int lastRow = range.getLastRow();
                        int firstColumn = range.getFirstColumn();
                        int lastColumn = range.getLastColumn();
                        if (k == firstColumn && j == firstRow) {
                            int rowSpan = lastRow - firstRow;
                            // 循环遍历每一行，如果其中一行为ZeroHeight ，则rowSpan - 1
                            for (int m = firstRow + 1; m <= lastRow; m++) {
                                Row row1 = sheet.getRow(m);
                                if (row1.getZeroHeight()) {
                                    rowSpan--;
                                }
                            }

                            cellValueHashMap.put(String.valueOf(k), SheetVO.CellVO.builder()
                                    .value(cellValue)
                                    .colSpan(lastColumn - firstColumn)
                                    .rowSpan(rowSpan)
                                    .styles(mapper.writeValueAsString(styleMap))
                                    .build());
                            k = lastColumn;
                        } else {
                            Row firstRowModel = sheet.getRow(firstRow);
                            Cell firstColumnModel = firstRowModel.getCell(firstColumn);
                            if (firstColumnModel != null && firstRowModel.getZeroHeight()) {

                                cellValueHashMap.put(String.valueOf(k), SheetVO.CellVO.builder()
                                        .value(dataFormatter.formatCellValue(firstColumnModel))
                                        .styles(mapper.writeValueAsString(styleMap))
                                        .build());
                            }

                        }

                    } else {

                        cellValueHashMap.put(String.valueOf(k), SheetVO.CellVO.builder()
                                .value(cellValue)
                                .styles(mapper.writeValueAsString(styleMap))
                                .build());
                    }
                }
                SheetVO.RowVO rowVO = new SheetVO.RowVO();
                HashMap<String, String> rowStyleMap = new HashMap<>();
                rowStyleMap.put("lineHeight", heightPx + "px");
                rowVO.setCells(cellValueHashMap);
                rowVO.setStyles(mapper.writeValueAsString(rowStyleMap));
                rowList.add(rowVO);
            }
            sheetVO.setRowList(rowList);
            return sheetVO;
        }
    }


    /**
     * 判断是否为合并单元格
     * @param sheet 工作表
     * @param rowIndex 行索引
     * @param columnIndex 列索引
     * @return null 没有合并地址。 否则返回合并单元格的起始行索引和结束行索引
     */
    public static org.apache.poi.ss.util.CellRangeAddress getMergedRegion(org.apache.poi.ss.usermodel.Sheet sheet, int rowIndex, int columnIndex) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            org.apache.poi.ss.util.CellRangeAddress range = sheet.getMergedRegion(i);
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();

            // 判断是否是合并单元格
            if (rowIndex >= firstRow && rowIndex <= lastRow && columnIndex >= firstColumn && columnIndex <= lastColumn) {
                return range ;
            }
        }
        return null ;
    }


    public void deleteFlashReport(Long orderId, String flash, Long fileId){
        flashDocumentRepository.deleteByIdAndOrderIdAndFlash(fileId, orderId, flash);
    }

}
