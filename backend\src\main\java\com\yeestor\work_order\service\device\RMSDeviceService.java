package com.yeestor.work_order.service.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.device.DeviceControlEntity;
import com.yeestor.work_order.entity.device.DeviceLockInfoEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.base.DeviceBaseInfo;
import com.yeestor.work_order.model.rms.*;
import com.yeestor.work_order.repository.PlanDeviceRepository;
import com.yeestor.work_order.model.redis.DeviceLockRepository;
import com.yeestor.work_order.repository.device.DeviceControlRepository;
import com.yeestor.work_order.repository.device.DeviceLockInfoRepository;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.work_order.utils.RMSApis;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.util.function.Tuple2;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

import static com.yeestor.work_order.utils.Const.getProductBySubProduct;


@Slf4j
@Service
@RequiredArgsConstructor
public class RMSDeviceService {

    private static final String DEVICE_LIST_KEY = "device_list";
    private static final String DEVICE_INFO_KEY = "device_info";

    private static final String REDIS_KEY_SEPARATOR = "_";

    private final RMSApis rmsApis;
    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper mapper;
    private final PlanDeviceRepository deviceRepository;
    private final DeviceLockInfoRepository deviceLockInfoRepository;
    private final DeviceLockRepository deviceLockRepository ;
    private final DeviceControlRepository deviceControlRepository;
    private final NotificationService notificationService;

    @PostConstruct
    public void init(){
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }


    /**
     * 根据 产品线 和 设备状态（非{@link DeviceModel.Status#READY}状态下的） 获取设备列表
     * @param product 产品线
     * @return 非ready状态下的空闲电脑。
     * @see DeviceModel.Status#READY
     */
    public List<DeviceModel> getAvailableDevices(String product){
        // 拿到对应产品线的设备列表
        List<DeviceModel> deviceList = rmsApis.getDeviceList(product);
        // 过滤掉 ip 为空的设备
        deviceList = deviceList.stream().filter(d -> StringUtils.hasText(d.getIp())).collect(Collectors.toList());
        // 过滤掉 mac 为空的设备
        deviceList = deviceList.stream().filter(d -> StringUtils.hasText(d.getMac())).collect(Collectors.toList());
        // 将设备列表中的信息 缓存到Redis中。
        deviceList.forEach(d -> {
            try {
                redisTemplate.opsForHash().put(DEVICE_INFO_KEY,product + "-" + d.getMac(),mapper.writeValueAsString(d));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        });
        return deviceList;

    }

    /**
     * 将这些设备锁定住， 这样其他的工单就无法开启测试。
     *
     * @param subProduct 子产品线
     * @param orderFlashNo Flash 批次的工单号, eReport的工单号
     * @param planEntity  Plan 实体
     * @param deviceList 设备 列表
     * @param status 设备状态
     */
    @Async
    public void holdDevices(
            String subProduct,
            String orderFlashNo,
            OrderPlanEntity planEntity,
            List<PlanDeviceEntity> deviceList,
            PlanDeviceEntity.Status status
    ) {
        if (deviceList.isEmpty()){
            return;
        }
        log.info("[{}] - {} holdDevices  :{} with status: {}",orderFlashNo,planEntity.getName(), deviceList,status);
        // 设备列表中,此时依然可能出现 ip 为空的设备,但是这个地方不做处理,因为在上面的 getAvailableDevices 方法中已经做了处理。
        List<String> ipList = new ArrayList<>();
        List<String> macList = new ArrayList<>();

        deviceList
                .stream()
                .filter(DeviceBaseInfo::isNotEmpty)
                .forEach(d -> {
                    String mac = d.getMac();
                    // 1. 查找并解锁此 MAC 地址下, 不属于当前 Plan 的所有其他锁
                    //    这可以防止一个设备被错误地锁定在多个不同的测试计划中。
                    List<DeviceLockInfoEntity> deviceLocks = deviceLockInfoRepository.findByMacInAndLockedTrue(List.of(mac));
                    for (DeviceLockInfoEntity lockInfo : deviceLocks) {
                        //  如果锁的工单和当前工单不一致，或者锁的plan和当前plan不一致，则解锁
                        if (!orderFlashNo.equals(lockInfo.getLockedOrderFlashNo()) || lockInfo.getPlanId() != planEntity.getId()) {
                            lockInfo.setLocked(false);
                            lockInfo.setUnlockTime(System.currentTimeMillis());
                            lockInfo.setUpdatedAt(System.currentTimeMillis());
                            deviceLockInfoRepository.save(lockInfo);
                        }
                    }

                    // 2. 如果锁的工单和当前工单一致，则不处理, 直接更新处理.
                    // 如果不一致,则增加一个新的LockInfo
                    DeviceLockInfoEntity deviceLockInfo = deviceLocks.stream()
                            .filter(l -> l.getPlanId() == planEntity.getId())
                            .findFirst()
                            .orElseGet(d::convert2LockInfoEntity);

                    log.info("lock device:{} to orderNo:{} planName:{} devStatus:{}", mac, orderFlashNo, planEntity.getName(), status);
                    // 那么更新下时间
                    deviceLockInfo.setUpdatedAt(System.currentTimeMillis());
                    deviceLockInfo.setLockedOrderFlashNo(orderFlashNo);
                    deviceLockInfo.setFlash(planEntity.getFlash());

                    // 更新产品线相关信息
                    deviceLockInfo.setSubProduct(subProduct);
                    deviceLockInfo.setProduct(getProductBySubProduct(subProduct));

                    DeviceModel deviceModel = fetchDeviceByMac(subProduct, d.getMac()).orElse(null);
                    if (deviceModel != null) {
                        deviceLockInfo.setAttrModelList(deviceModel.getAttrModelList());
                    }
                    deviceLockInfo.setPlanName(planEntity.getName());
                    deviceLockInfo.setPlanType(planEntity.getType());

                    deviceLockInfoRepository.save(deviceLockInfo);

                    ipList.add(d.getIp());
                    macList.add(d.getMac());
                }) ;


        rmsApis.lockDevice(ipList,macList,orderFlashNo);
    }

    public String buildRedisDeviceKey(
            String subProduct,
            long orderId,
            String ip,
            String mac
    ){
        return subProduct+REDIS_KEY_SEPARATOR+ orderId +REDIS_KEY_SEPARATOR + ip+ REDIS_KEY_SEPARATOR +mac;
    }

    /**
     * 释放 旧的设备。
     * @param subProduct 子产品线
     * @param orderFlashNo Flash 批次的工单号, eReport的工单号
     * @param planEntity  Plan 实体
     * @param deviceList 需要解锁的设备列表
     */
    @Async
    public void releaseDevices(
            String subProduct,
            String orderFlashNo,
            OrderPlanEntity planEntity,
            List<? extends DeviceBaseInfo> deviceList) {
        unlockPlanDevice(subProduct, orderFlashNo, planEntity, deviceList);
        List<String> macList = deviceList.stream().map(DeviceBaseInfo::getMac).collect(Collectors.toList());
        List<DeviceLockInfoEntity> deviceLocks = deviceLockInfoRepository.findByMacInAndLockedTrue(macList);
        for (DeviceLockInfoEntity deviceLockInfoEntity : deviceLocks) {
            deviceLockInfoEntity.setLocked(false);
            deviceLockInfoEntity.setUnlockTime(System.currentTimeMillis());
            deviceLockInfoEntity.setUpdatedAt(System.currentTimeMillis());
            deviceLockInfoRepository.save(deviceLockInfoEntity);
        }
    }

    /**
     * 移除加锁redis并解锁设备
     * @param subProduct 产品
     * @param orderFlashNo 工单号
     * @param planEntity Plan实体类
     * @param deviceList 设备列表
     */
    @Async
    public void unlockPlanDevice(
            String subProduct,
            String orderFlashNo,
            OrderPlanEntity planEntity,
            List<? extends DeviceBaseInfo> deviceList
    ) {
        if (deviceList.isEmpty()) {
            return;
        }
        long orderId = Optional.ofNullable(planEntity).map(OrderPlanEntity::getOrderId).orElse(0L);
        String planName = Optional.ofNullable(planEntity).map(OrderPlanEntity::getName).orElse("unknown");

        List<String> deviceIpList = deviceList.stream().map(DeviceBaseInfo::getIp).collect(Collectors.toList());
        List<String> deviceMacList = deviceList.stream().map(DeviceBaseInfo::getMac).collect(Collectors.toList());
        log.info("解锁 [{}] 工单下 {} 的设备:{} ", orderFlashNo, planName, deviceIpList);

        deviceList.forEach(d -> {
            // 移除升级后的key
            String key = buildRedisDeviceKey(subProduct, orderId, d.getIp(), d.getMac());
            Long result = redisTemplate.opsForHash().delete(DEVICE_LIST_KEY, key);
            log.info("[{}] - {} 移除redis: {} 结果为:{} ", orderId, planName, key, result);
            deviceLockRepository.deleteById(d.getIp());
        });

        rmsApis.unlockDevice(deviceIpList, deviceMacList, orderFlashNo);
    }


    /**
     * 通过 产品和mac 列表 获取设备列表
     * @param subProduct 产品
     * @param macList mac列表
     * @return 设备详情
     */
    public List<DeviceModel> fetchDevicesByMacList(String subProduct, List<String> macList) {
        List<DeviceModel> deviceModelList = macList.stream()
                .map(mac -> {
                    try {
                        return mapper.readValue( String.valueOf(redisTemplate.opsForHash().get(DEVICE_INFO_KEY, subProduct + "-" + mac)), DeviceModel.class);
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                    return null;
                })
                .filter(Objects::nonNull) // 如果设备不存在，那么就过滤掉。理论上如果请求过 getAvailableDevices 的话，都是存在设备的
                .collect(Collectors.toList());
        log.info("deviceModelList: {}", deviceModelList);
        return deviceModelList;
    }

    /**
     * 通过 产品和设备mac 获取设备
     * @param subProduct 产品
     * @param mac 设备mac
     * @return 设备详情
     */
    public Optional<DeviceModel> fetchDeviceByMac(String subProduct, String mac) {
        try {
            DeviceModel model = mapper.readValue( String.valueOf(redisTemplate.opsForHash().get(DEVICE_INFO_KEY, subProduct + "-" + mac)), DeviceModel.class);
            return Optional.ofNullable(model);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
       return Optional.empty();
    }

    /**
     * 停止测试。
     * @param stopTestParams 停止测试参数
     */
    public HandleResp<PlanTestRespModel> stopTestPlan(StopTestParams stopTestParams) {
        // 调用RMS的接口。
        return rmsApis.stopTestPlan(stopTestParams);
    }

    /**
     * 获取产品线的设备数量统计
     *
     * @param subProduct 需要查询的产品
     * @return 设备数量统计
     */
    public DeviceCountInfoVO fetchDeviceCount(String subProduct) {
        LogUtils.clearTracePoint();
        Tuple2<List<DeviceModel>, List<DeviceModel>> deviceTuples = rmsApis.getAllDeviceList(subProduct);
        List<DeviceModel> deviceList = deviceTuples.getT1();
        List<DeviceCountInfoVO.SimpleDeviceInfo> busyDeviceList = new ArrayList<>();

        List<DeviceLockInfoEntity> lockedDevices = deviceLockInfoRepository.findAllBySubProductAndLockedIsTrue(subProduct);
        lockedDevices.forEach(deviceLockInfoEntity -> {
            DeviceCountInfoVO.SimpleDeviceInfo deviceInfo = deviceLockInfoEntity.convert2SimpleDeviceInfo();
            deviceInfo.setStatus("ERROR");
            deviceRepository.findById(deviceLockInfoEntity.getDeviceId())
                    .ifPresent(deviceEntity -> deviceInfo.setStatus(deviceEntity.getStatus().name()));

            busyDeviceList.add(deviceInfo);

        });

        List<DeviceCountInfoVO.SimpleDeviceInfo> unknownDeviceList = deviceTuples.getT2().stream()
                .map(d-> DeviceCountInfoVO.of(d,null,null))
                .collect(Collectors.toList());

        return DeviceCountInfoVO.builder()
                .product(subProduct)
                .busy(busyDeviceList.size())
                .idle(deviceList.size())
                .idleDeviceList(deviceList.stream().map(d -> DeviceCountInfoVO.of(d,null,null)).collect(Collectors.toList()))
                .busyDeviceList(busyDeviceList)
                .unkownDeviceList(unknownDeviceList)
                .build();
    }

    public List<DeviceModel> getOccupiedDevices(String subProduct) {
        // 查询出所有的占用中的设备后, 在通过ip查询设备信息。
        List<DeviceModel> deviceList = new ArrayList<>();
        deviceLockInfoRepository.findAllBySubProductAndLockedIsTrue(subProduct)
                        .forEach(deviceLockInfoEntity -> {
                            PlanDeviceEntity deviceEntity = deviceRepository.findById(deviceLockInfoEntity.getDeviceId()).orElse(null);

                            if(deviceEntity == null){
                                log.error("getOccupiedDevices: deviceEntity is null, deviceLockInfoEntity: {}",deviceLockInfoEntity);
                                return;
                            }

                            if (!Objects.equals(deviceEntity.getStatus().name(), PlanDeviceEntity.Status.OCCUPIED.name())) {
                                return;
                            }

                            DeviceModel deviceModel = deviceLockInfoEntity.convert2DeviceModel();
                            deviceModel.setOwner(deviceEntity.getOwner());
                            deviceModel.setScore(deviceEntity.getScore());

                            deviceList.add(deviceModel);
                        });

        return deviceList;
    }

    public DeviceCountInfoVO fetchDeviceCountBySubProduct(String subProduct) {
        Tuple2<List<DeviceModel>, List<DeviceModel>> deviceTuples = rmsApis.getAllDeviceList(subProduct);

        List<DeviceLockInfoEntity> lockedDevices = deviceLockInfoRepository.findAllBySubProductAndLockedIsTrue(subProduct);
        List<DeviceCountInfoVO.SimpleDeviceInfo> busyDeviceList = new ArrayList<>();

        lockedDevices.forEach(d -> {
            DeviceCountInfoVO.SimpleDeviceInfo deviceInfo = d.convert2SimpleDeviceInfo();

            Optional<PlanDeviceEntity> historyDeviceOptional = deviceRepository.findById(d.getDeviceId());
            if (historyDeviceOptional.isEmpty()) {
                log.error("device not found in history device table, deviceId:{}", d.getDeviceId());
                deviceInfo.setStatus("ERROR");
            } else {
                deviceInfo.setStatus(historyDeviceOptional.get().getStatus().name());
            }
            busyDeviceList.add(deviceInfo);
        });

        List<DeviceCountInfoVO.SimpleDeviceInfo> unknownDeviceList = deviceTuples.getT2().stream()
                .map(d -> DeviceCountInfoVO.of(d, null, null))
                .collect(Collectors.toList());

        List<DeviceModel> deviceList = deviceTuples.getT1();
        return DeviceCountInfoVO.builder()
                .product(subProduct)
                .busy(busyDeviceList.size())
                .idle(deviceList.size())
                .idleDeviceList(deviceList.stream().map(d -> DeviceCountInfoVO.of(d, null, null)).collect(Collectors.toList()))
                .busyDeviceList(busyDeviceList)
                .unkownDeviceList(unknownDeviceList)
                .build();
    }

    /**
     * 查询指定Mac地址中设备状态为status的设备信息
     *
     * @param subProduct 所属产品
     * @param macList    Mac地址
     * @param status     设备状态
     * @return 设备列表
     */
    public List<DeviceModel> fetchDevicesByStatusAndMacList(String subProduct, List<String> macList, String status) {
        List<DeviceModel> modelList = getAvailableDevices(subProduct);
        return modelList.stream()
                .filter(m -> macList.contains(m.getMac()) && m.getStatus().equals(status))
                .collect(Collectors.toList());
    }

    /**
     * 电脑开机【异步处理】
     *
     * @param subProduct 产品
     * @param flash      flash名称
     * @param title      操作名称
     * @param userName   引发操作的人员
     * @param deviceList 设备列表
     */
    @Async
    public void startupDevice(String subProduct, String flash, String title, String userName, List<PlanDeviceEntity> deviceList) {
        List<String> macList = deviceList.stream().map(PlanDeviceEntity::getMac).collect(Collectors.toList());
        log.info("macList: {}", macList);
        List<String> needOpenMacList = deviceList.stream().map(PlanDeviceEntity::getMac).collect(Collectors.toList());
        log.info("需要开机的设备编号有: {}", deviceList.stream().map(PlanDeviceEntity::getNo).collect(Collectors.toList()));
        DeviceControlParams params = DeviceControlParams.builder()
                .macList(needOpenMacList)
                .username(userName)
                .product(subProduct)
                .build();
        log.info("即将打开下列电脑设备: {}", params);

        List<DeviceControlEntity> deviceControlEntityList = deviceList.stream()
                .filter(d -> needOpenMacList.contains(d.getMac()))
                .map(d -> d.convert2ControlEntity(title, flash, userName))
                .collect(Collectors.toList());

        // 设备关机
        HandleResp<DeviceControlResp> resp = rmsApis.startupDevice(params);
        if (resp.isSuccess()) {
            DeviceControlResp data = resp.getData();
            List<DeviceControlResp.DeviceControlResult> failList = data.getFailLst();
            log.info("开机失败的电脑设备: {}", failList.stream().map(DeviceControlResp.DeviceControlResult::getMac).collect(Collectors.toList()));

            for (DeviceControlEntity d : deviceControlEntityList) {
                DeviceControlResp.DeviceControlResult result = failList.stream()
                        .filter(item -> Objects.equals(item.getMac(), d.getMac()))
                        .findFirst().orElse(new DeviceControlResp.DeviceControlResult());
                d.setReason(result.getReason());
            }
        }

        deviceControlRepository.saveAll(deviceControlEntityList);
    }

    /**
     * 电脑关机【异步处理】
     *
     * @param subProduct 产品
     * @param flash      flash名称
     * @param title      操作名称
     * @param userName   引发操作的人员
     * @param deviceList 设备列表
     */
    @Async
    public void shutdownDevice(String subProduct, String flash, String title, String userName, List<PlanDeviceEntity> deviceList) {
        List<String> macList = deviceList.stream().map(PlanDeviceEntity::getMac).collect(Collectors.toList());
        log.info("需要关机的设备编号有: {}", deviceList.stream().map(PlanDeviceEntity::getNo).collect(Collectors.toList()));
        DeviceControlParams params = DeviceControlParams.builder()
                .macList(macList)
                .username(userName)
                .product(subProduct)
                .build();
        log.info("即将关闭下列电脑设备: {}", params);

        List<DeviceControlEntity> deviceControlEntityList = deviceList.stream()
                .map(d -> d.convert2ControlEntity(title, flash, userName))
                .collect(Collectors.toList());
        // 设备开机
        HandleResp<DeviceControlResp> resp = rmsApis.shutdownDevice(params);

        if (resp.isSuccess()) {
            DeviceControlResp data = resp.getData();
            List<DeviceControlResp.DeviceControlResult> failList = data.getFailLst();

            log.info("关机失败的电脑设备: {}", failList.stream().map(DeviceControlResp.DeviceControlResult::getMac).collect(Collectors.toList()));

            for (DeviceControlEntity d : deviceControlEntityList) {
                DeviceControlResp.DeviceControlResult result = failList.stream()
                        .filter(item -> Objects.equals(item.getMac(), d.getMac()))
                        .findFirst().orElse(new DeviceControlResp.DeviceControlResult());
                d.setReason(result.getReason());
            }
        }

        deviceControlRepository.saveAll(deviceControlEntityList);
    }


    /**
     * 启动或者释放时，通过IP查询设备的锁定信息.【当前情况下，只会存在一个锁定信息】
     *
     * @param planId Plan Id
     * @param mac    设备的mac 地址
     * @return DeviceLockInfoEntity 设备锁定信息
     * @throws DataNotFoundException 找不到对应的设备的锁定信息
     */
    public DeviceLockInfoEntity findLockInfoInTest(
            long planId,
            String mac
    ) {
        List<DeviceLockInfoEntity> devices = deviceLockInfoRepository.findAllByMacAndLockedTrue(mac);
//        log.info("检测planId为{}下的设备Mac{}是否被重复锁定!", planId, mac);
        if (devices.isEmpty()) {
            throw new DataNotFoundException("找不到对应的设备, 请联系管理员处理");
        }
        if (devices.size() > 1) {
            notificationService.sendAssignPlanErrNotice("解锁或者启动时检测：" + mac + "已被其他Plan锁定，请查看并定位问题");
            throw new IllegalStateException("当前设备被多个Plan锁定, 请联系管理员处理");
        }
        DeviceLockInfoEntity deviceLockInfoEntity = devices.get(0);
        if (deviceLockInfoEntity.getPlanId() != planId) {
            throw new DataNotFoundException("当前Plan与设备锁定的Plan不一致, 请联系管理员处理");
        }
        return deviceLockInfoEntity;
    }

    /**
     * 锁定设备前判断当前设备是否被占用
     *
     * @param mac    设备的mac 地址
     * @throws DataNotFoundException 找不到对应的设备的锁定信息
     */
    public void checkInfoBeforeLock(String mac) {
        List<DeviceLockInfoEntity> devices = deviceLockInfoRepository.findAllByMacAndLockedTrue(mac);
        if (!devices.isEmpty()) {
            notificationService.sendAssignPlanErrNotice("上锁时检测：" + mac + "已被其他Plan锁定，请查看并定位问题");
            throw new IllegalStateException("锁定前检测到异常：当前设备已被其他Plan占用, 请联系管理员处理");
        }
    }

    public List<DeviceLockInfoEntity> findAllLockDevicesByMacList(List<String> macList) {
        return deviceLockInfoRepository.findByMacInAndLockedTrue(macList) ;
    }
}
