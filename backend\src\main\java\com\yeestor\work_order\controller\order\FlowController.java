package com.yeestor.work_order.controller.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.service.flow.FlowService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/flow/")
@Api(tags = {"Flow"}, value = "用于生成流程图等信息.")
public class FlowController {

    @Data
    @Builder
    public static class FlashFlow {
        private OrderFlashEntity.Status status;
        private String person;
        private Long time;

        @ApiModelProperty("流程节点的额外信息")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Map<String,String> extraInfo  ;

        @JsonInclude(JsonInclude.Include.NON_DEFAULT)
        private boolean revoked ;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String revokeReason ;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String revokePerson;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long revokeTime;
    }

    @Data
    @Builder
    public static class FlashFlowEdge {
        private OrderFlashEntity.Status source;
        private OrderFlashEntity.Status target;
        private long spendTime;

        @JsonInclude(JsonInclude.Include.NON_DEFAULT)
        private boolean revoked ;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String reason ;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String person;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long time;
    }


    private final OrderService orderService;
    private final FlashService flashService;

    private final FlowService flowService ;

    @GetMapping(path = "/{orderId}/{flash}/info")
    public HandleResp<Object> fetchFlashFlowInfo(
            @PathVariable("orderId") long orderId,
            @PathVariable("flash") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        Map<String, Object> data = flowService.fetchFlashFlow(orderEntity,flashEntity) ;

        return HandleResp.ok(data, "");
    }

    @GetMapping(path = "/{orderNo}/flash_info")
    public HandleResp<Object> fetchFlashFlowInfo(
            @PathVariable("orderNo") String orderNo,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderNo) ;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(flashEntity.getOrderId());

        Map<String, Object> data = flowService.fetchFlashFlow(orderEntity,flashEntity) ;

        return HandleResp.ok(data, "");
    }

}
