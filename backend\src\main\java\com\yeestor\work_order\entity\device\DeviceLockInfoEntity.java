package com.yeestor.work_order.entity.device;

import com.yeestor.entity.BaseEntity;
import com.yeestor.work_order.model.base.DeviceBaseInfo;
import com.yeestor.work_order.model.rms.AttrModel;
import com.yeestor.work_order.model.rms.DeviceCountInfoVO;
import com.yeestor.work_order.model.rms.DeviceModel;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.List;

/**
 * 设备锁定信息, 创建的时候,就表示设备已经锁定.
 */

@Getter
@Setter
@Entity(name = "DeviceLockInfo")
@Table(name = "wo_device_lock_info")
@ToString
public class DeviceLockInfoEntity extends BaseEntity implements DeviceBaseInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "order_id")
    private long orderId ;

    @Column(name = "flash")
    private String flash ;

    @Column(name = "plan_id")
    private long planId ;

    @Column(name = "plan_type")
    private int planType ;

    @Column(name = "plan_name")
    private String planName ;

    @Column(name = "device_id")
    private long deviceId ;

    @Column(name = "ip",length = 50)
    private String ip ;

    @Column(name = "mac",length = 100)
    private String mac ;

    @Column(name = "position",length = 100)
    private String position ;
    /**
     * 机器的属性列表
     */
    @Column(name="attrbuites", columnDefinition = "json")
    @Convert(converter = AttrModel.AttrModelConverter.class)
    private List<AttrModel> attrModelList ;

    /**
     * 设备编号
     */
    @Column(name = "no",length = 100)
    private String no ;


    /**
     * 锁定的 Flash 批次的号, 及eReport 中的 工单号
     */
    @Column(name = "locked_order_flash_no")
    private String lockedOrderFlashNo ;


    /**
     * 增加产品和子产品,用于区分不同的产品线
     */
    @Column(name = "product",length = 50)
    private String product ;

    @Column(name = "sub_product",length = 50)
    private String subProduct ;

    /**
     * 解锁时间
     */
    @Column(name = "unlock_time")
    private Long unlockTime ;


    @Column(name = "locked")
    private boolean locked = true ;

    @Setter(AccessLevel.NONE)
    @Version
    @Column(name = "version")
    private Integer version;


    public DeviceCountInfoVO.SimpleDeviceInfo convert2SimpleDeviceInfo(){
        DeviceCountInfoVO.SimpleDeviceInfo deviceInfo = new DeviceCountInfoVO.SimpleDeviceInfo();
        deviceInfo.setProduct(subProduct);
        deviceInfo.setPcNo(no);
        deviceInfo.setIp(ip);
        deviceInfo.setMac(mac);
        deviceInfo.setPosition(position);
        deviceInfo.setAttrModelList(attrModelList);
        deviceInfo.setPlan(planName);
        deviceInfo.setOrderId(orderId);
        deviceInfo.setPlanId(planId);
        deviceInfo.setFlash(flash);

        return deviceInfo ;
    }


    public DeviceModel convert2DeviceModel(){
        DeviceModel model = new DeviceModel();
        model.setProduct(subProduct);
        model.setPcNo(no);
        model.setIp(ip);
        model.setMac(mac);
        model.setPosition(position);
        model.setStatus("占用中");
        model.setAttrModelList(attrModelList);

        return model ;
    }

}
