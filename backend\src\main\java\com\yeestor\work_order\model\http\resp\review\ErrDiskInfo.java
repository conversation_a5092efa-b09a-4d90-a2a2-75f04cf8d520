package com.yeestor.work_order.model.http.resp.review;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Data
@ApiModel(value = "ErrDiskInfo", description = "自动测试过程中生成的错误日志记录表")
public class ErrDiskInfo {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "ErrDiskLogItem", description = "每个测试项中的错误日志，以样片为维度")
    public static class ErrDiskLogItem {

        @ApiModelProperty("样片编号")
        private String no ;

        @ApiModelProperty("Fail 类型")
        private String failType ;

        @ApiModelProperty("PC 编号")
        private String pcNo ;

        @ApiModelProperty("Log 路径")
        private String logPath ;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "TestCountItem", description = "每个测试项中的错误日志，以样片为维度")
    public static class TestCountItem {

        @ApiModelProperty("测试项目")
        private String key ;

        @ApiModelProperty("Fail数量，一般情况为数字，但是有些项目为字符串")
        private String value ;

        @ApiModelProperty("项目的序号，为行号， 从 0开始")
        private int idx ;

        @ApiModelProperty("测试项目指向的sheet 的名称")
        private String link ;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty("研发备注的信息")
        private String msg;

        public TestCountItem(String key, String value, int idx, String link) {
            this.key = key;
            this.value = value;
            this.idx = idx;
            this.link = link;
        }
    }

        /**
         * Err 汇总表的信息
         */
        private List<TestCountItem> statistics = new ArrayList<>();

        private LinkedHashMap<String, List<ErrDiskLogItem>> logSheets = new LinkedHashMap<>();
}


