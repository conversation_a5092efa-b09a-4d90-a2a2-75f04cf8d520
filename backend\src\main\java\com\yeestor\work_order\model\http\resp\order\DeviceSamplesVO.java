package com.yeestor.work_order.model.http.resp.order;

import com.yeestor.work_order.entity.DeviceSampleEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "设备中样片的测试信息记录")
public class DeviceSamplesVO {
    @ApiModelProperty("编号id")
    private Long id ;

    @ApiModelProperty("样片编号")
    private String no ;

    @ApiModelProperty("样片测试端口")
    private Integer port ;

    @ApiModelProperty("测试结果记录")
    private Boolean success ;

    @ApiModelProperty("设备IP")
    private String deviceIp ;

    public DeviceSamplesVO(DeviceSampleEntity sampleEntity){
        this.setId(sampleEntity.getId());
        this.setNo(sampleEntity.getNo());
        this.setPort(sampleEntity.getPort());
        this.setDeviceIp(sampleEntity.getDeviceIp());
        this.setSuccess(sampleEntity.getSuccess());
    }
}
