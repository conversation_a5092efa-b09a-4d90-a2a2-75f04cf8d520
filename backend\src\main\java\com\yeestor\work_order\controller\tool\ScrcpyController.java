package com.yeestor.work_order.controller.tool;

import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.model.rms.*;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.plan.TerminalService;
import com.yeestor.work_order.utils.LogUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = {"eScrcpyTool"}, value = "eScrcpyTool中的API")
public class ScrcpyController {
    private final FlashService flashService ;
    private final OrderPlanRepository planRepository;
    private final TerminalService terminalService;

    @GetMapping("/terminal/case/list")
    @ApiOperation(value = "获取平台自动化测试的case列表", notes = "通过产品线、所属产品、工具名称来获取平台自动化测试的case列表")
    public HandleResp<List<CaseModel>> terminalCaseList(
            @ApiParam(value = "产品线", allowableValues = "EM") @RequestParam(value = "p") String product,
            @ApiParam(value = "子产品", allowableValues = "eMMC") @RequestParam(value = "sp") String subProduct

    ) {
        log.info("product:{} subProduct:{}", product, subProduct);
        return HandleResp.ok(new ArrayList<>(), "查询成功！");
    }

    @GetMapping("/terminal/plan/list")
    @ApiOperation(value = "获取终端平台中的plan 列表 ", notes = "通过产品线、所属产品、工具名称来获取终端平台对应的plan列表")
    public HandleResp<List<TerminalPlanModel>> terminalListPlan(
            @ApiParam(value = "产品线", allowableValues = "EM") @RequestParam(value = "p") String product,
            @ApiParam(value = "子产品", allowableValues = "eMMC,UFS") @RequestParam(value = "sp") String subProduct,
            @ApiParam(value = "工具", allowableValues = "eScrcpyTool") @RequestParam(value = "tool") String tool

    ) {
        log.info("product:{} subProduct:{} tool:{}", product, subProduct, tool);
        return HandleResp.ok(new ArrayList<>(), "查询成功！");
    }

    @GetMapping("/terminal/plan/group/list")
    @ApiOperation(value = "获取终端平台中的plan合集列表 ", notes = "通过产品线、所属产品、工具名称来获取终端平台对应的plan合集列表")
    public HandleResp<List<TerminalPlanGroupModel>> terminalListPlanGroup(
            @ApiParam(value = "产品线", allowableValues = "GE") @RequestParam(value = "p") String product,
            @ApiParam(value = "子产品", allowableValues = "eMMC,UFS") @RequestParam(value = "sp") String subProduct,
            @ApiParam(value = "工具", allowableValues = "eScrcpyTool") @RequestParam(value = "tool") String tool
    ) {
        log.info("product:{} subProduct:{} tool:{}", product, subProduct, tool);
        return HandleResp.ok(new ArrayList<>(), "查询成功！");
    }

    @PostMapping("/terminal/lock")
    @ApiOperation(value = "锁定终端平台 ", notes = "通过mac地址以及工单号来锁定对应的平台。")
    public HandleResp<String> lockTerminal(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @RequestBody TerminalOrderLockParams params
    ) {
        log.info("serverType:{} params:{}", serverType, params);
        return HandleResp.ok("", "锁定成功！");
    }

    @PostMapping("/terminal/unlock")
    @ApiOperation(value = "解锁终端平台 ", notes = "通过mac地址以及工单号来解锁对应的平台。")
    public HandleResp<String> unlockTerminal(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @RequestBody TerminalOrderLockParams params
    ) {
        log.info("serverType:{} params:{}", serverType, params);
        return HandleResp.ok("", "解锁成功！");
    }

    @PostMapping("/terminal/plan/test")
    @ApiOperation(value = "开始在平台中执行Plan测试", notes = "在终端平台中开始测试Plan[对应的case合集]，允许多个电脑下启动多个平台")
    public HandleResp<TerminalPlatformStatusResp> startTerminalTest(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @RequestBody TerminalPlanTestParams body
    ) {
        log.info("startTerminalTest serverType:{} body:{}", serverType, body);
        return HandleResp.ok(null, "启动成功！");
    }

    @PostMapping("/terminal/plan/stop")
    @ApiOperation(value = "停止平台上执行的Plan测试", notes = "停止指定终端平台的测试，允许同时停止多个平台的测试")
    public HandleResp<TerminalPlatformStatusResp> stopTerminalTest(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @RequestBody TerminalPlanStopParams body
    ) {
        log.info("stopTerminalTest serverType:{} body:{}", serverType, body);
        return HandleResp.ok(new TerminalPlatformStatusResp(), "停止成功！");
    }

    @PostMapping("/terminal/plan/status")
    @ApiOperation(value = "终端平台启动测试后，case的测试结果回调", notes = "测试过程中，如果case测试完成或者测试失败，需要回调当前case的测试状态【成功或者失败】，并回调错误信息")
    public HandleResp<String> terminalCaseStatusChangeCallback(
            @RequestBody @Valid TerminalCaseStatusChangeParams body,
            @ApiParam(value = "工单号") @RequestHeader("ORDER-NO") String orderNo,
            @ApiIgnore HttpServletRequest request
    ) {
        log.info("TerminalCaseStatusChangeParams body:{} orderNo:{} -- request.getRemoteAddr():{}", body, orderNo, request.getRemoteAddr());
        // 判断工单号是否存在
        OrderFlashEntity orderFlashEntity = flashService.findFlashOrElseThrow(body.getOrderNo());
        String flashName = orderFlashEntity.getFlash();
        long orderId = orderFlashEntity.getOrderId();
        LogUtils.setOrderAndFlashTracePoint(orderId, flashName, "测试平台case信息回调");
        OrderPlanEntity planEntity = planRepository.findByOrderIdAndFlashAndName(orderId, flashName, body.getPlan());
        boolean isPass = body.getStatus().equals("pass");
        terminalService.updatePlatformCaseAtCallBack(orderFlashEntity, planEntity, body.getDeviceList(), isPass);
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "update success!");
    }

    @PostMapping("/terminal/plan/case/start")
    @ApiOperation(value = "终端平台开始测试case时的启动回调", notes = "记录测试case的开始时间")
    public HandleResp<String> terminalCaseStartCallback(
            @RequestBody @Valid TerminalCaseStartParams body,
            @ApiParam(value = "工单号") @RequestHeader("ORDER-NO") String orderNo,
            @ApiIgnore HttpServletRequest request
    ) {
        log.info("terminalCaseStartCallback body:{} orderNo:{} -- request.getRemoteAddr():{}", body, orderNo, request.getRemoteAddr());
        // 判断工单号是否存在
        OrderFlashEntity orderFlashEntity = flashService.findFlashOrElseThrow(body.getOrderNo());
        String flashName = orderFlashEntity.getFlash();
        long orderId = orderFlashEntity.getOrderId();
        LogUtils.setOrderAndFlashTracePoint(orderId, flashName, "测试平台case启动回调");
        OrderPlanEntity planEntity = planRepository.findByOrderIdAndFlashAndName(orderId, flashName, body.getPlan());
        terminalService.updateCaseStartCallBack(planEntity, body);
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "update success!");
    }

    @GetMapping("/terminal/device/list")
    @ApiOperation(value = "获取终端设备列表", notes = "通过产品线来获取对应产品线的终端设备列表")
    public HandleResp<List<TerminalDeviceModel>> terminalDeviceList(
            @ApiParam(value = "产品线", allowableValues = "EM") @RequestParam(value = "p") String product,
            @ApiParam(value = "子产品", allowableValues = "eMMC,UFS") @RequestParam(value = "sp") String subProduct
    ) {
        log.info("product:{} subProduct:{}", product, subProduct);
        return HandleResp.ok(new ArrayList<>(), "查询成功！");
    }
}
