package com.yeestor.work_order.service.order.change;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

public interface DataChangeListener {

    void startHandleData();

    enum EmitterType {
        /**
         * 推送工单到已经打开对应的工单的网页
         */
        Order,
        /**
         * 推送信息到指定的用户.
         */
        USER,
        /**
         * 推送Plan信息到指定的Plan详情页面中
         */
        Plan,
        /**
         * 设备上盘推送
         */
        DEVICE_DISK
    }


    void onDataChange(DataChangeEvent event);

    void addEmitter(EmitterType type, Object data,SseEmitter emitter) ;

    void removeEmitter(EmitterType type, Object data,SseEmitter emitter) ;

    List<SseEmitter> getOrderEmitters(EmitterType type, Object data);
}
