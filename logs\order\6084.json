{"@timestamp":"2025-07-23T15:10:03.806+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:10:03.814+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:03.819+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:03.819+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.534+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:14:03.536+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.541+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.541+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.44+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:03.442+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.445+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.445+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.788+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:22:03.789+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.792+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.793+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.391+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:26:03.392+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.395+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.395+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.136+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:50:05.147+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.154+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.154+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.027+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:04.029+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.033+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.033+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.319+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:58:04.32+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.324+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.324+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.468+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:02:03.469+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.473+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.473+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.485+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:03.487+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.49+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.49+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.486+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:10:03.488+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.49+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.49+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.383+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:09.394+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.4+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.4+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.486+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:22:03.487+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.489+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.49+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.509+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:26:03.511+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.514+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.514+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.409+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:03.41+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.413+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.413+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.424+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:34:03.425+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.427+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.427+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.587+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:38:03.588+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.592+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.592+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.46+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:03.462+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.464+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.464+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.4+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:46:03.4+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.403+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.403+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.335+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:50:03.336+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.337+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.338+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:07.809+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:07.888+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:07.894+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:07.894+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.817+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:03.82+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.828+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.828+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.578+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:10:03.58+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.586+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.586+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.478+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:03.48+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.484+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.484+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.553+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:03.554+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.557+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.558+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.567+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:22:03.568+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.571+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.571+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.403+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:03.405+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.407+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.408+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.264+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:04.266+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.269+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.27+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.159+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:34:04.16+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.164+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.164+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:04.998+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:05.015+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.021+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.022+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.166+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:04.171+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.176+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.176+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.587+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:04.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.618+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.618+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.755+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:03.758+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.763+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.763+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.861+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:10:03.864+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.868+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.869+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.571+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:03.572+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.578+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.578+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.903+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:03.904+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.908+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.909+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.541+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:03.543+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.547+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.547+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.605+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:03.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.61+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.61+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.095+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"6084","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:06.127+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6306, orderId=6084, flash=8803_WT_128G_2CE_128GB, orderFlashNo=YS8803##MP020300###0010##3D_TLC_E09T#250024_8803_WT_128G_2CE_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.134+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"6084","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.135+08:00","@version":"1","message":"[6084] - [8803_WT_128G_2CE_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"6084","traceType":"分配设备"}
