package com.yeestor.work_order.model.http.req.terminal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.util.List;

@Data
@ApiModel(value = "PlanPlatformParams", description = "释放或者停止终端平台的参数")
public class PlanPlatformParams {
    @ApiModelProperty("工单id")
    @Min(value = 1, message = "无效的工单id")
    private long orderId ;

    @ApiModelProperty("plan id")
    private long planId;

    @ApiModelProperty("原因描述")
    private String reason ;

    @ApiModelProperty(value = "平台列表", required = true)
    private List<PlatformInfo> platformList;
}
