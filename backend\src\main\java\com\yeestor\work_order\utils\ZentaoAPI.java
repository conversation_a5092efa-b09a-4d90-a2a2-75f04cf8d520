package com.yeestor.work_order.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.model.http.resp.order.BugInfo;
import com.yeestor.work_order.model.zt.BuildInfo;
import com.yeestor.work_order.model.zt.ListResp;
import com.yeestor.work_order.model.zt.TestTaskInfo;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.*;

import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

@Slf4j
@Component
public class ZentaoAPI {

    @Data
    public static class ZentaoResp {
        private String data ;
        private String status;
        private String md5 ;

        public ZentaoResp(){
            this.status = "Failed";
            this.md5 = "";
            this.data = "{}";
        }

        Map<String, Object> dataMap(ObjectMapper mapper){
            try {
                return mapper.readValue(data, new TypeReference<>() {
                });
            } catch (JsonProcessingException e) {
                throw new IllegalStateException(e);
            }
        }
    }

    enum ZentaoUrlType {
        TOKEN("tokens",true),
        TASK_INFO("testtask-cases-%s.json"),
        TASK_INFO_API("testtasks/%s",true),
        BUILD_INFO("build-view-%s.json"),
        BUILD_INFO_API("builds/%s",true),
        EXECUTION_BUILD_LIST("executions/%s/builds",true),
        PROJECT_BUILD_LIST("projects/%s/builds",true),
        BUG_INFO("bug-view-%s.json") ;
        private final String path;
        ZentaoUrlType(String path, boolean isApiInterface) {
            if(isApiInterface) {
                this.path = "api.php/v1/" + path;
            }
            else {
                this.path = path;
            }
        }
        ZentaoUrlType(String path) {
            this(path, false);
        }

        public String buildPath(Object... args) {
            return String.format(path, args);
        }
    }

    private WebClient webClient ;
    private final ObjectMapper mapper = new ObjectMapper();

    private static final String TOKEN_HEADER_KEY = "token";

    public ZentaoAPI(){
        webClient = WebClient.builder()
                .codecs(config -> config.defaultCodecs().maxInMemorySize(20 * 1024 * 1024)) // 最大20M
                .baseUrl("http://zt.yeestor.com")
                .build();
        mapper.configure(FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    ZentaoResp parseZentaoResp(String resp){
        try {
            return mapper.readValue(resp, ZentaoResp.class);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    public void initToken(){
        HashMap<String,String> params = new HashMap<>() ;
        params.put("account","ci");
        params.put("password","Ci1234!");

        HashMap<String,String> map = webClient.post()
                .uri(ZentaoUrlType.TOKEN.path)
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HashMap<String,String>>() {
                })
                .blockOptional()
                .orElse(new HashMap<>());

        webClient = webClient.mutate().defaultHeader(TOKEN_HEADER_KEY, map.get(TOKEN_HEADER_KEY)).build();
    }


    @SneakyThrows
    public TestTaskInfo getTestTaskInfo(int taskId) {
        initToken();
        String respBody = webClient.get()
                .uri(ZentaoUrlType.TASK_INFO_API.buildPath(taskId))
                .retrieve()
                .bodyToMono(String.class)
                .doOnError(e -> log.warn("getTestTaskInfo with {} got exception:{}", taskId,  e.getMessage()))
                .blockOptional()
                .orElse("");

        log.info("getTestTaskInfo:{}",respBody);
        return mapper.readValue(respBody, TestTaskInfo.class);
    }


    public ListResp getBuildListByExecutionId(int executionId){
        initToken();
        String url = ZentaoUrlType.EXECUTION_BUILD_LIST.buildPath(executionId);
        return webClient.get()
                .uri(url)
                .retrieve()
                .bodyToMono(ListResp.class)
                .doOnError(e -> log.warn("getBuildListByExecutionId with {} got exception:{}", executionId,  e.getMessage()))
                .blockOptional()
                .orElse(new ListResp());
    }


    public Map<String,Object> getBuildInfo(String buildId){
        initToken() ;
        ZentaoResp resp = webClient.get()
                .uri(ZentaoUrlType.BUILD_INFO.buildPath(buildId))
                .retrieve()
                .bodyToMono(String.class)
                .map(this::parseZentaoResp)
                .doOnError(e -> log.warn("getBuildInfo with {} got exception:{}", buildId,  e.getMessage()))
                .onErrorReturn(new ZentaoResp())
                .blockOptional()
                .orElse(new ZentaoResp());
        return resp.dataMap(mapper);
    }

    @SneakyThrows
    public BuildInfo getBuildInfoById(int buildId) {
        initToken();
        String url = ZentaoUrlType.BUILD_INFO_API.buildPath(buildId);
        log.info("getBuildInfoById url:{}",url);
        String respBody = webClient.get()
                .uri(url)
                .retrieve()
                .bodyToMono(String.class)
                .doOnError(e -> log.warn("getBuildInfoById with {} got exception:{}", buildId,  e.getMessage()))
                .blockOptional()
                .orElse("");

        log.info("getBuildInfoById with {} got resp:{}", buildId, respBody);
        return mapper.readValue(respBody, BuildInfo.class);
    }

    public List<BugInfo> getBuildGeneratedBugs(String buildId) {
        Map<String, Object> buildInfo = getBuildInfo(buildId);
        Object object = buildInfo.get("generatedBugs");
        log.debug("generatedBugs:{} -- ",object);
        if(object == null || object instanceof List) {
            return new ArrayList<>();
        }
        Map<String,Object> generatedBugs = (Map<String, Object>) object;
        return generatedBugs.values().stream()
                .map(BugInfo::of)
                .collect(java.util.stream.Collectors.toList());
    }

    public BugInfo getBugInfo(String bugId) {
        initToken() ;
        ZentaoResp resp = webClient.get()
                .uri(ZentaoUrlType.BUG_INFO.buildPath(bugId))
                .retrieve()
                .bodyToMono(String.class)
                .map(this::parseZentaoResp)
                .doOnError(e -> log.warn("getBugInfo with {} got exception:{}", bugId,  e.getMessage()))
                .onErrorReturn(new ZentaoResp())
                .blockOptional()
                .orElse(new ZentaoResp());
        Map<String,Object> dataMap = resp.dataMap(mapper);
        Map<String,Object> bugInfo = ((Map<String,Object>)dataMap.getOrDefault("bug", new HashMap<String,Object>()));
        return BugInfo.of(bugInfo) ;
    }


}
