package com.yeestor.work_order.model.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "Permission", description = "支持的权限列表")
public class PermissionModel {


    @Data
    @Builder
    public static class LocaleLanguage {

        private String name ;
        private String locale ;

    }


    @ApiModelProperty(value = "权限分组", name = "group", example = "WorkOrder")
    private LocaleLanguage group;


    @ApiModelProperty(value = "权限列表", name = "permissions", example = "[" +
            "\"CONFIRM_FLASH\"," +
            "\"UPDATE_PRIORITY\"," +
            "\"CANCEL_ORDER\"," +
            "\"STOP_ORDER\"," +
            "\"CONFIRM_ENVIRONMENT\"," +
            "\"START_TEST\"," +
            "\"REPEAT_TEST\"," +
            "\"UPLOAD_REPORT\"]"
    )
    private List<LocaleLanguage> permissions;

}