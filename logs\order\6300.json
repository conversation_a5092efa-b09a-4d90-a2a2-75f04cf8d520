{"@timestamp":"2025-07-23T15:10:02.608+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:10:02.614+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.614+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.633+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.633+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.638+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.64+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.64+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.646+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.646+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.646+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.649+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.649+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.65+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.65+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.65+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.606+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:14:02.608+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.608+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.614+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.614+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.614+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.614+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.614+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.614+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.614+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.615+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.617+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.617+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.618+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.618+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.614+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.616+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.619+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.62+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.62+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.621+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.621+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.621+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.621+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.621+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.624+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.624+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.624+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.624+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.624+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.606+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:22:02.607+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.607+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.61+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.61+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.61+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.61+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.612+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.612+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.613+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.613+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.613+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:02.842+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:50:02.878+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:02.927+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.023+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.025+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.033+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.038+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.042+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.043+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.043+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.043+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.05+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.05+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.05+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.051+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.051+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.617+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:02.619+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.619+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.624+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.625+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.625+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.625+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.625+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.625+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.625+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.625+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.629+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.633+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.633+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.633+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.634+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.614+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:02.615+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.619+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.619+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.62+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.62+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.62+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.62+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.62+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.62+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.622+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.623+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.623+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.623+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.623+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:02.799+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:03.065+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:03.066+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:03.093+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:03.315+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:03.32+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.089+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.415+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.416+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.417+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.417+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.425+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.425+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.425+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.426+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.427+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.615+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.617+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.617+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.621+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.621+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.622+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.622+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.622+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.622+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.622+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.622+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.624+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.624+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.624+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.625+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.625+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.619+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:02.621+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.621+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.624+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.624+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.624+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.624+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.624+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.624+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.624+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.624+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.627+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.627+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.627+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.627+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.627+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.611+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:46:02.612+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.612+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.615+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.615+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.615+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.616+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.617+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.617+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.617+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.617+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.617+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:03.053+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:04.159+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=30), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.16+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.169+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.17+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.17+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.171+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.171+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.171+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.171+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.172+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.178+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.179+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.179+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.18+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.18+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.78+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.791+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=30), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.791+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.797+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.797+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.798+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.798+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.798+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.798+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.799+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.799+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.805+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.805+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.805+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.805+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.806+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.731+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:02.732+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=30), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.733+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.737+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.737+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.737+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.737+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.737+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.738+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.738+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.738+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.743+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.743+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.743+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.744+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.744+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.788+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.79+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=30), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.79+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.794+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.794+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.794+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.794+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.795+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.795+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.795+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.795+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.798+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.798+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.798+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.798+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.799+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.706+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:02.708+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=30), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.708+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.712+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.712+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.712+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.712+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.712+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.712+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.713+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.713+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.715+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.715+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.715+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.715+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.715+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.804+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.805+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=30), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.805+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.81+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.81+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.811+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.811+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.811+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.811+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.811+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.811+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.814+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.815+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.815+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.815+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.815+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.741+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:34:02.743+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=30), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.743+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.749+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.749+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.749+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.75+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.75+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.75+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.75+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.75+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.753+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.754+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.754+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.754+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.755+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.049+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:03.142+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=30), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.143+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.16+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.162+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.164+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.164+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.165+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.165+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.165+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.165+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.177+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.178+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.178+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.178+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.179+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.997+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:03.046+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=30), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.046+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.051+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.051+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.052+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.052+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.052+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.052+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.053+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.053+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.058+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.059+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.059+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.059+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.06+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.794+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:02.796+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.796+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.799+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.799+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.799+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.799+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.8+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.8+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.8+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.8+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.801+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.802+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.802+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.802+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.802+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.878+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.88+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.88+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.886+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.886+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.886+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.886+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.887+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.887+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.887+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.887+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.89+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.89+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.89+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.89+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.891+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.788+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:02.79+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.79+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.794+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.794+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.794+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.794+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.795+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.795+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.795+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.795+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.798+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.799+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.799+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.799+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.799+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.65+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.664+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.664+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.674+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.687+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.687+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.688+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.688+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.689+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.689+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.689+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.695+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.695+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.695+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.696+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.696+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6300","traceType":"分配设备"}
