package com.yeestor.work_order.utils;

import com.yeestor.admin.api.UserFeignClient;
import com.yeestor.admin.model.DepartmentDTO;
import com.yeestor.admin.model.UserDTO;
import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.exception.DataNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class DingTalkUtils {

    public static final String SYSTEM_USER_ID = "0" ;
    public static final String SYSTEM_USER_NAME = "eSee.system" ;
    private final UserFeignClient userFeignClient ;

    public String getPersonName(String userDingTalkID){

        HandleResp<UserDTO> userDetailResp = userFeignClient.findUserByDingTalkId(userDingTalkID);
        if (userDetailResp.isSuccess()) {
            UserDTO userDetail = userDetailResp.getData();
            return userDetail.getJobNumber() ;
        }
        return null ;

    }

    public static String getCurrentUserDingTalkID(){
        OAuthUserDetail userDetail = getCurrentUserDetail();
        return userDetail.getUid();
    }

    public static String getCurrentUserName(){
        OAuthUserDetail userDetail = getCurrentUserDetail();
        return userDetail.getUsername();
    }

    private static OAuthUserDetail getCurrentUserDetail(){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication() ;
        if (authentication == null) {
            throw new IllegalStateException("用户未登录");
        }
        return (OAuthUserDetail) authentication.getPrincipal();
    }

    /**
     * 获取用户归属的分公司信息
     * @param dingID 用户钉钉ID
     * @return 公司代称
     */
    public String getLocationByDingId(String dingID){
        HandleResp<UserDTO> matchUsers = userFeignClient.findUserByDingTalkId(dingID);
        if (!matchUsers.isSuccess()) {
            throw new DataNotFoundException("找不到对应的用户");
        }
        UserDTO userDTO = matchUsers.getData();
        return getUserDepartmentLocation(userDTO.getDepartments());
    }

    /**
     * 获取测试人员所在的公司信息
     *
     * @param departments 部门信息
     * @return 公司信息
     */
    public String getUserDepartmentLocation(List<DepartmentDTO> departments) {
        List<String> nameList = departments.stream().map(DepartmentDTO::getName).collect(Collectors.toList());
        log.info("nameList: {}", nameList);
        if (nameList.contains("深圳办公室")) {
            return "SZ";
        } else if (nameList.contains("合肥办公室")) {
            return "HF";
        }
        throw new DataNotFoundException("该测试人员所在的分公司暂时不支持设备分配！");
    }

}
