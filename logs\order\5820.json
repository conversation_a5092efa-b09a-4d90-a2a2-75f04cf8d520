{"@timestamp":"2025-07-23T15:10:03.896+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:10:03.903+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:03.941+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:03.941+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.573+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:14:03.574+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.577+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.579+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.464+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:03.465+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.468+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.468+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.817+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:22:03.819+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.822+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.823+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.413+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:26:03.414+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.417+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.418+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.244+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:50:05.256+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.262+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.262+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.063+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:04.065+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.068+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.069+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.347+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:58:04.349+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.352+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.353+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.492+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:02:03.493+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.496+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.497+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.503+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:03.504+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.506+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.506+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.508+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:10:03.509+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.512+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.512+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.471+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:09.482+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.487+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.488+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.508+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:22:03.509+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.512+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.512+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.532+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:26:03.534+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.537+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.537+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.429+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:03.43+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.433+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.433+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.44+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:34:03.441+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.442+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.443+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.619+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:38:03.62+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.624+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.52+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:03.521+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.525+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.526+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.418+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:46:03.419+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.421+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.422+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.351+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:50:03.352+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.354+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.354+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:08.16+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:08.183+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:08.189+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:08.189+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.863+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:03.868+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.876+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.876+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.62+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:10:03.624+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.628+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.509+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:03.511+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.515+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.515+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.579+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:03.58+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.584+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.584+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.601+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:22:03.602+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.606+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.606+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.423+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:03.424+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.426+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.426+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.296+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:04.297+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.301+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.302+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.196+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:34:04.199+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.203+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.203+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.116+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:05.133+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.139+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.139+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.22+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:04.222+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.226+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.227+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.774+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:04.793+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.804+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.806+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.796+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:03.798+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.802+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.802+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.893+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:10:03.895+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.899+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.899+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.598+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:03.6+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.602+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.602+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.933+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:03.934+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.938+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.938+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.572+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:03.573+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.576+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.577+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.634+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:03.636+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.639+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.217+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5820","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:06.238+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6019, orderId=5820, flash=8803BB_WTx2_128G_128GB, orderFlashNo=YS8803##MP020000###0010##3D_TLC_E09T#250017_8803BB_WTx2_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.244+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5820","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.245+08:00","@version":"1","message":"[5820] - [8803BB_WTx2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5820","traceType":"分配设备"}
