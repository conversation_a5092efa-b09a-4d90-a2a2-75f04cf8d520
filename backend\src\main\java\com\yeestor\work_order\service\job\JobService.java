package com.yeestor.work_order.service.job;

import com.yeestor.work_order.model.http.resp.JobInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static org.quartz.CronScheduleBuilder.cronSchedule;
import static org.quartz.DateBuilder.futureDate;
import static org.quartz.JobBuilder.newJob;
import static org.quartz.TriggerBuilder.newTrigger;

/**
 * 将启动Job 放在这个Service 中来进行处理。
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JobService {

    private final Scheduler scheduler ;

    /**
     * 在多少秒后启动Job
     * @param jobName 任务名称
     * @param groupName 任务组名称
     * @param dataMap 任务数据
     * @param jobClazz 任务类
     * @param futureDate 启动时间
     */
    public void startJobAtFutureDate(
            String jobName,
            String groupName,
            Map<String ,Object> dataMap,
            Class<? extends Job> jobClazz,
            Date futureDate
    ){
        if(isJobExist(jobName,groupName)){
            cancelJobIfExist(jobName,groupName);
        }

        JobDataMap jobDataMap = new JobDataMap();

        if(dataMap != null){
            jobDataMap.putAll(dataMap);
        }
        JobDetail job = newJob(jobClazz)
                .withIdentity(jobName, groupName)
                .usingJobData(jobDataMap)
                .build();

        startJob(job,jobName,groupName,futureDate);
    }

    public void scheduleAssignDeviceJob(String p, String sp) {
        log.info("scheduleAssignDeviceJob: {}-{}", p,sp);
        TriggerKey triggerKey = TriggerKey.triggerKey("AutoAssignDevice_" + p + "_" + sp, AutoAssignDeviceJob.GROUP_NAME) ;
        Trigger trigger ;
        try {
            trigger = scheduler.getTrigger(triggerKey);
            if(trigger != null){
                if(scheduler.getTriggerState(triggerKey) == Trigger.TriggerState.ERROR){
                    scheduler.resumeTrigger(triggerKey);
                }

                return;
            }
        } catch (SchedulerException e) {
           e.printStackTrace();
        }

        // 默认3分钟分配一次
        int intervalMinutes = 3;
        if ("SSD".equals(p) || "EM".equals(p)) {
            // 如果是SSD产品线则4分钟分配一次
            intervalMinutes = 4;  // 单次循环时间改为4分钟
        }

        JobDetail job = AutoAssignDeviceJob.buildJobDetail(p,sp) ;
        trigger = newTrigger()
                .withIdentity(triggerKey)
                .startNow()
                .withSchedule(
                        SimpleScheduleBuilder.simpleSchedule()
                                .withIntervalInMinutes(intervalMinutes)
                                .repeatForever()
                )
                .build();
        try {
            scheduler.scheduleJob(job, trigger);
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查 超时任务是否存在, 如果不存在,则增加
     */
    public void scheduleTimeoutCheckJob() {
        TriggerKey triggerKey = TriggerKey.triggerKey(TimeoutCheckJob.GROUP_NAME, TimeoutCheckJob.GROUP_NAME) ;
        if (isJobExist(TimeoutCheckJob.GROUP_NAME, TimeoutCheckJob.GROUP_NAME)){
            return;
        }
        JobDetail job = TimeoutCheckJob.buildJobDetail() ;
        Trigger trigger = newTrigger()
                .withIdentity(triggerKey)
                .startNow()
                // 周一到周五 的10 点 触发
                .withSchedule(cronSchedule("0 0 10 ? * MON-FRI *"))
                .build() ;
        try {
            scheduler.scheduleJob(job, trigger) ;
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    /**
     * 每周日早上6点，执行更新测试关系数据表
     */
    public void scheduleDeviceTestCheckJob(){
        TriggerKey triggerKey = TriggerKey.triggerKey(CheckDeviceTestJob.GROUP_NAME, CheckDeviceTestJob.GROUP_NAME) ;
        if (isJobExist(CheckDeviceTestJob.GROUP_NAME, CheckDeviceTestJob.GROUP_NAME)){
            return;
        }
        JobDetail job = CheckDeviceTestJob.buildJobDetail() ;
        log.info("自动更新测试关系数据表已被触发！");
        Trigger trigger = newTrigger()
                .withIdentity(triggerKey)
                .startNow()
                .withSchedule(cronSchedule("0 0 6 ? * SUN"))
                .build() ;
        try {
            scheduler.scheduleJob(job, trigger) ;
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    /**
     * 每周五下午6点，执行统计数据并发送邮件
     */
    public void scheduleStatOrderJob() {
        TriggerKey triggerKey = TriggerKey.triggerKey(TimeoutStatOrderJob.GROUP_NAME, TimeoutStatOrderJob.GROUP_NAME);
        if (isJobExist(TimeoutStatOrderJob.GROUP_NAME, TimeoutStatOrderJob.GROUP_NAME)) {
            return;
        }
        JobDetail job = TimeoutStatOrderJob.buildJobDetail();
        Trigger trigger = newTrigger()
                .withIdentity(triggerKey)
                .startNow()
                // 周五下午18 点 触发
                .withSchedule(cronSchedule("0 0 18 ? * FRI"))
                .build();
        log.info("自动统计数据已被触发！");
        try {
            scheduler.scheduleJob(job, trigger);
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }


    /**
     * 在多少秒后启动Job
     * @param jobName 任务名称
     * @param groupName 任务组名称
     * @param dataMap 任务数据
     * @param jobClazz 任务类
     * @param afterSeconds 启动时间
     */
    public void startJobAfter(
            String jobName,
            String groupName,
            Map<String ,Object> dataMap,
            Class<? extends Job> jobClazz,
            int afterSeconds
    ){
        startJobAtFutureDate(jobName,groupName,dataMap,jobClazz,futureDate(afterSeconds, DateBuilder.IntervalUnit.SECOND));
    }

    public void startJob(JobDetail jobDetail,
                         String triggerName, String triggerGroupName,
                         Date futureDate){

        Trigger trigger = newTrigger()
                .withSchedule(
                        SimpleScheduleBuilder.simpleSchedule()
                                .withMisfireHandlingInstructionFireNow()
                )
                .withIdentity(triggerName, triggerGroupName)
                .startAt(futureDate)
                .build();

        try {
            scheduler.scheduleJob(jobDetail, trigger);
            scheduler.start();

        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断Job是否存在
     * @param jobName 任务名称
     * @param groupName 任务组名称
     * @return true 存在 false 不存在
     */
    public boolean isJobExist(String jobName, String groupName){

        try {
            boolean jobExist = scheduler.checkExists(new JobKey(jobName,groupName));
            boolean triggerExist = scheduler.checkExists(new TriggerKey(jobName,groupName));
            log.debug("{} - {} jobExist :{} , triggerExist :{}", jobName,groupName,jobExist,triggerExist);
            if(jobExist || triggerExist){
                return true;
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 如果Job存在的话，就立即出发Job
     * @param jobName 任务名称
     * @param groupName 任务组名称
     */
    public void triggerJobIfExist(String jobName, String groupName){

        JobKey jobKey = new JobKey(jobName,groupName);
        TriggerKey triggerKey = new TriggerKey(jobName,groupName);
        try {
            if(scheduler.checkExists(jobKey) && scheduler.checkExists(triggerKey)){
                scheduler.triggerJob(jobKey);
            }
            else{
                log.warn("try to trigger {}:{} failed!",jobName,groupName);
            }
        } catch (SchedulerException e) {
            e.printStackTrace();
        }

    }

    /**
     * 如果Job存在的话，就立即删除Job
     * @param jobName 任务名称
     * @param groupName 任务组名称
     */
    public void cancelJobIfExist(String jobName, String groupName){
        JobKey jobKey = new JobKey(jobName,groupName);
        TriggerKey triggerKey = new TriggerKey(jobName,groupName);
        try {
            if(!scheduler.checkExists(jobKey)){
                return;
            }
            scheduler.unscheduleJob(triggerKey);
            scheduler.deleteJob(jobKey);
            log.debug("delete job :{}",jobKey);
        } catch (SchedulerException e) {
            e.printStackTrace();
        }

    }

    public List<JobInfoVo> listJob() {
        // 获取所有的定时任务，
        List<JobInfoVo> jobInfoVos = new ArrayList<>();
        try {
            Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.anyJobGroup());
            for (JobKey jobKey : jobKeys) {

                log.info("jobKey :{}", jobKey);
                // 获取jobDetail
                JobDetail jobDetail = scheduler.getJobDetail(jobKey);
                log.info("jobDetail :{}", jobDetail);
                // 获取trigger
                List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
                List<JobInfoVo.TriggerInfoVO> triggerInfoList = triggers.stream().map(JobInfoVo.TriggerInfoVO::buildFrom).collect(Collectors.toList());

                jobInfoVos.add(
                        JobInfoVo.builder()
                            .triggerTimeList(triggerInfoList)
                            .jobName(jobKey.getName())
                            .groupName(jobKey.getGroup())
                            .jobClass(jobDetail.getJobClass().getName())
                            .dataMap(jobDetail.getJobDataMap())
                            .build()
                );

            }
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
        return jobInfoVos ;
    }

    /**
     * @since 1.0
     * @deprecated 已废弃
     * 删除key中包含指定字符串的任务.
     * @param contains 包含的字符串
     */
    @SneakyThrows
    public void deleteJob(String contains){

        Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.anyJobGroup());
        for (JobKey jobKey : jobKeys) {

            // 获取trigger
            List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
            triggers.forEach(trigger -> {
                if(trigger.getKey().getName().contains(contains)){
                    return;
                }
                try {
                    scheduler.unscheduleJob(trigger.getKey());
                    scheduler.deleteJob(trigger.getJobKey());
                } catch (SchedulerException e) {
                    e.printStackTrace();
                }
            });
        }
    }

}
