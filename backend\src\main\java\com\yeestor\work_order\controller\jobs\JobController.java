package com.yeestor.work_order.controller.jobs;

import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.model.http.resp.JobInfoVo;
import com.yeestor.work_order.service.job.JobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/job")
@Api(value = "JobController", tags = {"任务相关的接口"})
public class JobController {

    private final JobService jobService ;


    @GetMapping("/list")
    @ApiOperation(value = "获取任务列表", notes = "获取任务列表")
    public HandleResp<List<JobInfoVo>> fetchAllJob() {
        return HandleResp.ok(jobService.listJob(), "获取所有任务成功");
    }


}
