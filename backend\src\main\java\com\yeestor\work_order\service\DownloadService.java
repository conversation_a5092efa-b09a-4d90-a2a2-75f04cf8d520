package com.yeestor.work_order.service;

import com.yeestor.work_order.entity.document.DocumentEntity;
import com.yeestor.work_order.utils.DownloadUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.Optional;
import java.util.stream.Stream;

@Slf4j
@Service
public class DownloadService {

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

    private Path rootPath;

    @SneakyThrows
    @PostConstruct
    private void init(){
        log.info("init DownloadService");
        rootPath = Paths.get("tmp-dir") ;
        Files.createDirectories(rootPath);
    }


    /**
     * 每周天凌晨23点,清理时间超过7天的文件
     */
    @Async
    @Scheduled(cron = "0 0 23 * * *")
    public void clearOldFiles() {
        // 如果是对应单体应用这样写来说是没有问题的 ，但是如果是分布式应用的话，应该如何处理。
        try (Stream<Path> paths =  Files.walk(rootPath)) {
            paths.sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .filter(File::isFile)
                    .filter(file -> file.lastModified() < System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000)
                    .forEach(File::delete);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @SneakyThrows
    public Optional<InputStream> downloadFile(DocumentEntity documentEntity){
        // TODO： 检查文件是否存在,如果存在的话，就返回文件
        String patternName = genDocumentFileName(documentEntity);
        Path filePath = rootPath.resolve(patternName);
        if(Files.exists(filePath)){
            return Optional.of(Files.newInputStream(filePath));
        }
        // TODO: 如果文件不存在的话，就下载文件，存储下来，然后再返回。

        Optional<InputStream> inputStreamOptional = DownloadUtils.downloadFileWithWebclient(documentEntity.getUrl())
                .map(DataBuffer::asInputStream)
                .blockOptional()
                ;
        if(!inputStreamOptional.isPresent()){
            return Optional.empty();
        }
        InputStream in = inputStreamOptional.get();
        try {
            Files.copy(in, filePath, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Optional.of(Files.newInputStream(filePath));
    }


    private String genDocumentFileName(DocumentEntity documentEntity) {
        String suffix = documentEntity.getUrl().substring(documentEntity.getUrl().lastIndexOf("."));
        return String.format("%s_%05X_%s.%s", dateFormat.format(new Date(documentEntity.getCreatedAt())), documentEntity.getId(), documentEntity.getName().hashCode(), suffix);
    }


}
