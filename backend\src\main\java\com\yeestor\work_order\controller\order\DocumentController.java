package com.yeestor.work_order.controller.order;


import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.document.DocumentEntity;
import com.yeestor.work_order.entity.document.FlashDocumentEntity;
import com.yeestor.work_order.entity.document.PlanDocumentEntity;
import com.yeestor.work_order.model.http.resp.order.FlashDocumentItemVO;
import com.yeestor.work_order.model.http.resp.order.OrderDocumentVO;
import com.yeestor.work_order.model.http.resp.order.PlanDocumentItemVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.repository.document.DocumentRepository;
import com.yeestor.work_order.repository.document.FlashDocumentRepository;
import com.yeestor.work_order.repository.document.PlanDocumentRepository;
import com.yeestor.work_order.service.order.DocumentService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.role.RoleService;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.work_order.utils.RMSApis;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/document/")
@Api(tags = {"Document"}, value = "用于上传报告文件，也提供报告文件解析接口")
public class DocumentController {


    private final FlashDocumentRepository flashDocumentRepository ;
    private final PlanDocumentRepository planDocumentRepository ;
    private final DocumentRepository documentRepository ;
    private final OrderService orderService ;
    private final FlashService flashService ;
    private final PlanService planService ;

    private final RoleService roleService ;

    private final RMSApis rmsApis ;

    private final DataChangeListener dataChangeListener ;

    private final DocumentService documentService ;

    @Transactional
    @PostMapping("/plan/{planId}/upload")
    @ApiOperation(value = "上传手动Plan的报告文件", notes = "上传指定Plan的报告文件")
    public HandleResp<String> uploadPlanReport(
            @ApiParam("Plan id") @PathVariable("planId") long planId,
            @ApiParam("上传的文件列表") @RequestParam("file") MultipartFile[] files,
            @ApiIgnore@AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        if( files.length == 0 ){
            return HandleResp.failed("没有上传文件");
        }
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                return HandleResp.failed("文件为空");
            }
        }
        String operate = "上传Plan报告文件";
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        // 检查 plan 是否是手动测试的plan .
        if ( !planEntity.isManualPlan()){
            return HandleResp.failed("不是手动测试的Plan");
        }
        LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(), planEntity.getFlash(), operate);

        // 获取 对应的Flash批次。
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());
        // 获取工单实体类
        WorkOrderEntity order = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        // 上传文件到RMS
        Map<String,String> urlMap = rmsApis.uploadReport(0, flashEntity.getOrderFlashNo(),order.getSubProduct(), planEntity.getName(),userDetail.getUsername(), Arrays.stream(files).map(MultipartFile::getResource).collect(Collectors.toList()));

        for (MultipartFile file : files) {
            String name = file.getOriginalFilename();
            log.info("上传文件：{}", name);
            //  保存文件的相关信息到数据库。

            DocumentEntity documentEntity = new DocumentEntity() ;
            String url = urlMap.get(name);
            documentEntity.setName(name);
            documentEntity.setUrl("http://"+url);
            documentEntity.setContentType(file.getContentType());
            documentEntity.setSize(file.getSize() / 1024);
            documentEntity.setCreatedAt(System.currentTimeMillis());
            documentEntity.setCreatedBy(userDetail.getUid());
            documentEntity.setCreatedPerson(userDetail.getUsername());
            DocumentEntity resultDocument = documentRepository.save(documentEntity);

            PlanDocumentEntity planDocumentEntity = new PlanDocumentEntity();
            planDocumentEntity.setOrderId(planEntity.getOrderId());
            planDocumentEntity.setPlanId(planId);
            planDocumentEntity.setDocumentId(resultDocument.getId());
            planDocumentEntity.setIsRetest(false);
            planDocumentRepository.save(planDocumentEntity);

        }

        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.PLAN_UPLOADED_REPORT )
                        .orderId(order.getId())
                        .flash(planEntity.getFlash())
                        .planId(planEntity.getId())
                        .build()
        );

        LogUtils.clearTracePoint();
        return HandleResp.ok(null,"上传成功");
    }


    @Transactional
    @PostMapping("/order/{orderId}/upload")
    @ApiOperation(value = "上传Flash批次的报告", notes = "上传指定工单的指定Flash批次的报告文件")
    public HandleResp<String> uploadReport(
            @ApiParam("工单id") @PathVariable("orderId") long orderId,
            @ApiParam("flash 批次") @RequestParam("flash") String flash,
            @ApiParam("上传的文件列表") @RequestParam("file") MultipartFile[] files,
            @ApiIgnore@AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        if( files.length == 0 ){
            return HandleResp.failed("没有上传文件");
        }
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                return HandleResp.failed("文件为空");
            }
        }
        String operate = "上传报告" ;
        // 获取工单实体类
        WorkOrderEntity order = orderService.findOrderOrElseThrow(orderId);

        roleService.checkPermission(userDetail,order.getSubProduct(),operate, Permission.UPLOAD_REPORT);

        // 获取flash批次实体类
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);

        LogUtils.setOrderAndFlashTracePoint(orderId, flash, operate);

        Map<String,String> urlMap = rmsApis.uploadReport(1, flashEntity.getOrderFlashNo(), order.getSubProduct(),null,userDetail.getUsername(), Arrays.stream(files).map(MultipartFile::getResource).collect(Collectors.toList()));
        for (MultipartFile file : files) {

            String name = file.getOriginalFilename();
            log.info("上传文件：{} 至{}", name,flash);
            //  保存文件的相关信息到数据库。
            FlashDocumentEntity flashDocumentEntity = new FlashDocumentEntity();
            flashDocumentEntity.setOrderId(orderId);
            flashDocumentEntity.setFlash(flash);
            flashDocumentEntity.setFlashId(flashEntity.getId());
            flashDocumentEntity.setType("report");

            DocumentEntity documentEntity = new DocumentEntity() ;
            documentEntity.setUrl("http://"+urlMap.get(name));
            documentEntity.setName(name);
            documentEntity.setContentType(file.getContentType());
            documentEntity.setSize(file.getSize() / 1024);
            documentEntity.setCreatedAt(System.currentTimeMillis());
            documentEntity.setCreatedBy(userDetail.getUid());
            documentEntity.setCreatedPerson(userDetail.getUsername());
            DocumentEntity resultDocument = documentRepository.save(documentEntity);

            flashDocumentEntity.setDocumentId(resultDocument.getId());
            flashDocumentEntity.setSource("LOCAL");
            flashDocumentRepository.save(flashDocumentEntity);
        }
        // 文档上传后就进入了 等待Fail 状态 。
        flashService.updateToWaitingForFailAnalysis(order, flashEntity);

        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.FLASH_WAIT_FAIL_ANALYSIS)
                        .orderId(orderId)
                        .flash(flash)
                        .build()
        );
        LogUtils.clearTracePoint();
        return HandleResp.ok(null,"上传成功");
    }

    @GetMapping("/{orderId}/{planId}/report")
    @ApiOperation(value = "获取某个工单下的某个手动plan的报告", notes = "获取某个工单下的某个手动plan的报告")
    public HandleResp<List<PlanDocumentItemVO>> getOrderReport(
            @PathVariable("orderId") @ApiParam("工单id") long orderId,
            @PathVariable("planId") @ApiParam("Plan id") long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        // 获取 plan 实体
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);


        // TODO: 检查用户及权限。 只有 工单的构建者（研发），相关的测试，以及管理者可以获取报告。
        //plan 必须是手动plan .
        if(!planEntity.isManualPlan()) {
            return HandleResp.failed("Plan不是手动plan");
        }
        List<PlanDocumentItemVO> planDocumentEntityList = planDocumentRepository.findAllByPlanId(planId) ;
        return HandleResp.ok(planDocumentEntityList, "获取报告成功");
    }

    @GetMapping("/{orderId}/report")
    @ApiOperation(value = "获取某个工单下的所有报告", notes = "获取某个工单下的所有报告")
    public HandleResp<OrderDocumentVO> fetchOrderDocumentList(
            @PathVariable("orderId") @ApiParam("工单id") long orderId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);

        List<PlanDocumentItemVO> planDocumentInfo = planDocumentRepository.findAllByOrderId(orderEntity.getId());
        List<FlashDocumentItemVO> flashDocumentInfo = flashDocumentRepository.findAllByOrderId(orderEntity.getId());

        return HandleResp.ok(OrderDocumentVO.builder()
                        .flashDocuments(flashDocumentInfo)
                        .planDocuments(planDocumentInfo)
                .build(), "获取报告成功");
    }


    @PostMapping("/{orderId}/{flash}/merge")
    public void mergeRMSReport(
            @PathVariable("orderId") @ApiParam("工单id") long orderId,
            @PathVariable("flash") @ApiParam("flash批次") String flash ,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "合并RMS报告" ;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId) ;
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        LogUtils.setOrderAndFlashTracePoint(orderId, flash, operate);
        log.info("用户{}发起合并RMS报告的请求.flash:{}",userDetail.getUsername(),flash);
        rmsApis.mergeOrderReport(flashEntity.getOrderFlashNo(), orderEntity.getSubProduct());
        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.FLASH_WAIT_MERGE_REPORT)
                        .orderId(orderId)
                        .flash(flash)
                        .build()
        );
        LogUtils.clearTracePoint();
    }


    @Transactional
    @PostMapping("/report/{orderId}/{flash}/{fileId}/delete")
    @ApiOperation(value = "删除Flash批次的报告", notes = "删除指定工单的指定Flash批次的报告文件")
    public HandleResp<String> deleteReport(
            @ApiParam("工单id") @PathVariable("orderId") long orderId,
            @ApiParam("flash 批次") @PathVariable("flash") String flash,
            @ApiParam("文件id") @PathVariable("fileId") long fileId,
            @ApiIgnore@AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        /* 检查文件 */
        if (flashDocumentRepository.countByOrderIdAndFlashAndTypeAndSource(orderId, flash, "report", "LOCAL") <= 1) {
            return HandleResp.failed("仅有一个上传文件无法删除！");
        }
        // 获取工单实体类
        WorkOrderEntity order = orderService.findOrderOrElseThrow(orderId);

        String operate = "删除汇总报告" ;
        roleService.checkPermission(userDetail, order.getSubProduct(), operate, Permission.DELETE_REPORT);
        LogUtils.setOrderAndFlashTracePoint(orderId, flash, operate);

        documentService.deleteFlashReport(orderId, flash, fileId);

        LogUtils.clearTracePoint();

        return HandleResp.ok(null,"删除成功");
    }

}
