package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

@Data
@Builder
@ApiModel(value = "OrderCreateParams", description = "工单创建所需参数")
public class OrderCreateParams {

    @ApiModelProperty(value = "产品")
    private String product_type;
    @ApiModelProperty(value = "主控")
    private String chip ;
    @ApiModelProperty(value = "Flash")
    private String flash_type ;

    @ApiModelProperty(value = "工单号")
    private String key_value ;

    @ApiModelProperty(value = "版本类型")
    private String version_type ;

    @ApiModelProperty(value = "优先级")
    private int priority ;

    @ApiModelProperty(value = "firmware svn 地址")
    private String fw_svn;

    @ApiModelProperty(value = "firmware 版本")
    private String fw_ver ;

    @ApiModelProperty(value = "量产工具路径")
    private String mptool_path ;

    @ApiModelProperty(value = "量产工具版本")
    private String mptool_ver ;

    @ApiModelProperty(value = "驱动版本")
    private String driver_ver ;

    @ApiModelProperty(value = "容量")
    private String cap ;

    @ApiModelProperty(value = "版本日志")
    private String version_log ;

    @ApiModelProperty(value = "测试要点")
    private String test_point ;

    @ApiModelProperty(value = "邮件列表")
    private String mail_list ;

    @ApiModelProperty(value = "构建人")
    private String builder ;

    @ApiModelProperty(value = "Jenkins Job name")
    private String job_name ;

    @ApiModelProperty(value = "无所谓的参数")
    private String date_type ;

    public MultiValueMap<String, String> toFormData() {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("product_type", product_type);
        formData.add("chip", chip);
        formData.add("flash_type", flash_type);
        formData.add("key_value", key_value);
        formData.add("version_type", version_type);
        formData.add("priority", String.valueOf(priority));
        formData.add("fw_svn", fw_svn);
        formData.add("fw_ver", fw_ver);
        formData.add("mptool_path", mptool_path);
        formData.add("mptool_ver", mptool_ver);
        formData.add("driver_ver", driver_ver);
        formData.add("cap", cap);
        formData.add("version_log", version_log);
        formData.add("test_point", test_point);
        formData.add("mail_list", mail_list);
        formData.add("builder", builder);
        formData.add("job_name", job_name);
        formData.add("date_type", date_type);
        return formData;
    }
}
