{"@timestamp":"2025-07-23T15:10:03.993+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:10:04.002+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:04.005+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:04.006+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.599+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:14:03.6+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.604+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.604+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.491+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:03.493+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.497+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.497+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.845+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:22:03.847+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.851+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.851+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.435+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:26:03.437+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.439+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.439+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.351+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:50:05.363+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.37+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.372+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.095+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:04.097+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.101+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.101+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.379+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:58:04.381+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.384+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.384+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.517+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:02:03.518+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.522+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.522+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.519+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:03.52+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.523+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.523+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.529+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:10:03.53+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.534+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.535+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.553+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:09.564+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.569+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.569+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.529+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:22:03.53+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.532+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.532+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.559+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:26:03.56+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.563+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.563+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.45+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:03.451+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.454+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.454+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.455+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:34:03.456+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.458+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.458+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.644+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:38:03.646+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.651+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.651+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.545+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:03.547+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.549+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.55+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.436+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:46:03.438+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.441+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.441+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.367+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:50:03.368+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.37+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.37+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:09.023+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:09.25+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:09.256+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:09.256+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.908+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:03.911+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.915+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.915+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.663+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:10:03.666+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.673+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.673+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.539+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:03.541+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.546+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.546+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.604+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:03.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.609+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.609+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.628+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:22:03.635+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.64+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.44+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:03.441+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.444+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.444+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.332+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:04.334+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.338+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.339+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.23+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:34:04.232+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.24+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.24+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.246+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:05.266+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.32+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.321+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.258+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:04.261+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.267+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.267+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.963+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:04.979+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.99+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.991+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.825+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:03.827+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.832+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.832+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.924+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:10:03.928+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.931+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.932+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:03.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.622+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.622+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.961+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:03.964+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.968+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.968+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.603+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:03.604+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.609+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.609+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.661+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:03.663+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.666+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.667+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.34+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5597","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:06.355+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5801, orderId=5597, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010603###0010##3D_TLC_E09T#250011_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.361+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5597","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.361+08:00","@version":"1","message":"[5597] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5597","traceType":"分配设备"}
