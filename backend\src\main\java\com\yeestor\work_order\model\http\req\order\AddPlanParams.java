package com.yeestor.work_order.model.http.req.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "AddPlanParams", description = "新增计划参数")
public class AddPlanParams {

    @Min(value=1,message = "无效的工单ID")
    @ApiModelProperty("工单ID")
    private long orderId ;

    @NotBlank(message = "flash 批次号不能为空")
    @ApiModelProperty("flash批次")
    private String flash ;

    @NotEmpty(message = "选择的plan 不能为空")
    @ApiModelProperty("Plan信息列表，不能包含已经有的Plan")
    private List<OrderFlashConfirmV2Req.PlanItem> planList ;
}
