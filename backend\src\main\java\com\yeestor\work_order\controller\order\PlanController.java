package com.yeestor.work_order.controller.order;

import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.entity.device.DeviceTestHistoryEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.req.AddTempPlanReq;
import com.yeestor.work_order.model.http.req.order.AddDeviceParams;
import com.yeestor.work_order.model.http.req.order.AddPlanParams;
import com.yeestor.work_order.model.http.req.order.ChangePriorityParams;
import com.yeestor.work_order.model.http.req.order.OrderFlashConfirmV2Req;
import com.yeestor.work_order.model.http.req.plan.BatchPlanOperateReq;
import com.yeestor.work_order.model.http.req.plan.DonePlanReq;
import com.yeestor.work_order.model.http.resp.order.PlanDetailResp;
import com.yeestor.work_order.model.http.resp.order.PlanDetailVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.model.rms.PlanModel;
import com.yeestor.work_order.repository.*;
import com.yeestor.work_order.repository.document.PlanDocumentRepository;
import com.yeestor.work_order.service.device.QueueService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.service.plan.PlanAssignService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.plan.TestHistoryService;
import com.yeestor.work_order.service.role.RoleService;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.work_order.utils.RMSApis;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.util.function.Tuples;
import springfox.documentation.annotations.ApiIgnore;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/plan/")
@Api(tags = {"Plan"}, value = "Plan 相关接口。主要包含测试plan ，释放plan设备等")
public class PlanController {

    private final OrderPlanRepository planRepository;

    private final WorkOrderRepository workOrderRepository ;
    private final OrderFlashRepository orderFlashRepository ;
    private final PlanDeviceRepository planDeviceRepository ;
    private final PlanDocumentRepository planDocumentRepository ;
    private final PlanService planService ;
    private final FlashService flashService ;
    private final OrderService orderService ;
    private final DeviceService deviceService ;
    private final QueueService queueService;
    private final TestHistoryService testHistoryService;

    private final RMSApis rmsApis ;
    private final RoleService roleService;
    private final TempPlanRepository tempPlanRepository ;
    private final DataChangeListener dataChangeListener ;
    private final PlanAssignService planAssignService;

    private final Tracer tracer;

    @PostMapping("/{planId}/stop")
    @ApiOperation(value = "停止某个plan 的测试", notes = "停止某个plan的测试")
    public HandleResp<String> stopPlan(
            @PathVariable("planId") @ApiParam("Plan id") long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "停止Plan的测试" ;
        // 获取 plan 实体
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId) ;
        if (planEntity.getStatus().greaterThan(OrderPlanEntity.Status.COMPLETED)) {
            throw new IllegalArgumentException("当前 " + planEntity.getName() + " 状态为: " + planEntity.getStatus() + " 不允许取消测试！");
        }

        // 获取 工单实体
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId()) ;
        LogUtils.setOrderAndFlashTracePoint(orderEntity.getId(),planEntity.getFlash(), operate);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(),operate, Permission.CANCEL_PLAN);

        // 获取 工单Flash 批次的实体
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());

        planService.terminatePlan(orderEntity,flashEntity, planEntity);
        LogUtils.clearTracePoint();
        return HandleResp.ok(null,"停止成功");
    }

    @PostMapping("/{orderId}/{planId}/start")
    @ApiOperation(value = "启动某个工单下的某个Plan的测试", notes = "测试人员在某个，进行此操作需要相应的权限")
    public HandleResp<String> startTestPlan(
            @PathVariable("orderId") @ApiParam("工单id") long orderId,
            @PathVariable("planId") @ApiParam("Plan id") long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "启动Plan测试" ;
        LogUtils.setOrderTracePoint(orderId, operate);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(),operate,Permission.OPERATE_PLAN,Permission.START_TEST );

        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);

        // 如果plan 已经启动，则不需要再次启动
        if(planEntity.getStatus() == OrderPlanEntity.Status.RUNNING) {
            return HandleResp.ok(null,"Plan已经启动，不需要再次启动");
        }

        // 获取 对应的Flash批次。
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId,planEntity.getFlash());

        planService.startPlan(orderEntity,flashEntity,planEntity,userDetail);

        LogUtils.clearTracePoint();
        return HandleResp.ok(null,"Plan启动成功！");
    }

    @PostMapping("/{orderId}/{planId}/{type}/start")
    @ApiOperation(value = "启动Plan测试，允许选择样片的编号方式", notes = "允许选择继承编号或者重新编号执行Plan")
    public HandleResp<String> customStartTestPlan(
            @PathVariable("orderId") @ApiParam("工单id") long orderId,
            @PathVariable("planId") @ApiParam("Plan id") long planId,
            @PathVariable("type") @ApiParam("启动类型") String type,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate;
        boolean isReNumber = type.equals("renumber");
        if (isReNumber) {
            operate = "样片重新编号，并启动Plan测试";
        } else {
            operate = "样片继承编号，并启动Plan测试";
        }
        LogUtils.setOrderTracePoint(orderId, operate);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN, Permission.START_TEST);

        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        // 保存Plan的启动方式
        planEntity.setStartType(type);
        planRepository.save(planEntity);

        // 如果plan 已经启动，则不需要再次启动
        if (planEntity.getStatus() == OrderPlanEntity.Status.RUNNING) {
            return HandleResp.ok(null, "Plan已经启动，不需要再次启动");
        }

        // 获取 对应的Flash批次。
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, planEntity.getFlash());

        planService.startPlan(orderEntity, flashEntity, planEntity, userDetail);

        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "Plan启动成功！");
    }


    @Transactional
    @PostMapping("/{orderId}/{planId}/done")
    @ApiOperation(value = "完成某个工单下的某个手动Plan的测试", notes = "完成某个工单下的某个手动Plan的测试，plan必须是手动plan，并且已经启动测试。")
    public HandleResp<String> doneManualTestPlan(
            @PathVariable("orderId") @ApiParam("工单id") long orderId,
            @PathVariable("planId") @ApiParam("Plan id") long planId,
            @RequestBody @Valid DonePlanReq param,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "完成Plan测试";
        LogUtils.setOrderTracePoint(orderId, operate);

        String userDingTalkID = userDetail.getUid();
        String personName = userDetail.getUsername();

        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(),operate,Permission.OPERATE_PLAN,Permission.OPERATE_PLAN );

        //plan 必须是手动plan .
        if(!planEntity.isManualPlan()) {
            return HandleResp.failed("Plan不是手动plan");
        }
        // plan 必须是 正在测试状态
        if(planEntity.getStatus() != OrderPlanEntity.Status.RUNNING) {
            return HandleResp.failed("Plan不是正在测试状态");
        }
        // 当plan需要报告，且没有上传报告的时候，会返回错误
        if( Optional.ofNullable(planEntity.getNeedReport()).orElse(false) && !planDocumentRepository.existsByPlanId(planId)){
            return HandleResp.failed("Plan没有报告");
        }

        // 获取 对应的Flash批次。
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId,planEntity.getFlash());


        planEntity.setUpdatedAt(System.currentTimeMillis());
        planEntity.setStatus(OrderPlanEntity.Status.COMPLETED);
        planEntity.setEndAt(System.currentTimeMillis());
        planEntity.setEndBy(userDingTalkID);
        planEntity.setEndPerson(personName);
        planEntity.setEndReason(param.getReason());

        if("PASS".equalsIgnoreCase(param.getResult())) {
            planEntity.setEndStatus(OrderPlanEntity.END_STATUS_SUCCESS);
        } else if("FAIL".equalsIgnoreCase(param.getResult())) {
            planEntity.setEndStatus(OrderPlanEntity.END_STATUS_FAIL);
        } else {
            planEntity.setEndStatus(OrderPlanEntity.END_STATUS_UNKNOWN);
        }

        planRepository.save(planEntity);

        // 如果手动plan关联有设备的话，就将对应的设备给释放掉。
        List<PlanDeviceEntity> deviceEntities = planDeviceRepository.findAllByPlanId(planId);
        deviceService.releaseDevices(flashEntity, planEntity, deviceEntities,userDingTalkID,personName);

        // Plan 的状态已经完成, 取消Plan下的所有的任务
        planService.cancelPlanJob(flashEntity, planEntity);

        // 检查plan的状态，并且判断是否需要进入到完成状态
        flashService.checkWaitMerge( orderEntity, flashEntity);

        // 手动plan完成
        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_MANUAL_DONE)
                .orderId(planEntity.getOrderId())
                .flash(planEntity.getFlash())
                .planId(planEntity.getId())
                .build()
        );

        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "手动plan已完成！");
    }


    @Transactional
    @ApiOperation(value = "给指定工单下的指定Flash批次添加plan.", notes = "给指定工单下的指定Flash批次添加plan,此plan不能是flash批次中已有的plan.")
    @PostMapping("/add")
    public HandleResp<String> addPlanToFlash(
            @RequestBody @Valid AddPlanParams body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "添加plan";
        LogUtils.setOrderAndFlashTracePoint(body.getOrderId(),body.getFlash(),operate);
        log.info("add plan to flash, orderId: {}, flash: {}, plan: {}", body.getOrderId(), body.getFlash(), body.getPlanList());
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(body.getOrderId());

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(),operate,Permission.OPERATE_PLAN);

        OrderFlashEntity flashEntity =  flashService.findFlashOrElseThrow(body.getOrderId(), body.getFlash());

        if (flashEntity.getStatus().ordinal() > OrderFlashEntity.Status.IN_PROGRESS.ordinal()) {
            return HandleResp.failed("当前Flash批次测试已结束，不允许添加测试Plan！");
        }

        List<PlanModel> planModelList = planService.fetchRMSPlanList(orderEntity.getProduct(), orderEntity.getSubProduct(),"");
        // 判断该批次中是否已经存在了对应的

        for (OrderFlashConfirmV2Req.PlanItem planItem: body.getPlanList()){
            String plan = planItem.getName();
            if(planRepository.existsByOrderIdAndFlashAndName(body.getOrderId(),body.getFlash(),plan)){
                throw new DataNotFoundException("plan:["+ plan +"]已存在");
            }

            if(!planItem.isTemporal() && planModelList.stream().noneMatch(planModel -> planModel.getName().equals(plan))){
                throw new DataNotFoundException("RMS中找不到对应的plan:["+ plan +"]!");
            }
        }

        List<PlanModel> allPlanList = new ArrayList<>();

        //增加所有的非临时plan
        planModelList.stream()
                .filter(planModel -> body.getPlanList().stream().anyMatch(
                        item -> {
                            if(!item.isTemporal() && item.getName().equals(planModel.getName())){
                                if (StringUtils.hasText(item.getTestUserID())) {
                                    planModel.setBelongTo(item.getTestUserID());
                                    planModel.setBelongToPerson(item.getTestUserName());
                                }
                                else {
                                    planModel.setBelongTo(userDetail.getUid());
                                    planModel.setBelongToPerson(userDetail.getUsername());
                                }
                                return true;
                            }
                            return false;
                        }
                ))
                .forEach(allPlanList::add);

        // 增加临时Plan
        List<PlanModel> temporalPlanList = body.getPlanList().stream()
                .filter(OrderFlashConfirmV2Req.PlanItem::isTemporal)
                .map(item -> {
                    TempPlanEntity tempPlan = tempPlanRepository.findByNameAndSubProduct(item.getName(), orderEntity.getSubProduct()).orElse(null);
                    assert tempPlan != null;
                    return tempPlan.convert2PlanModel() ;
                })
                .collect(Collectors.toList());
        allPlanList.addAll(temporalPlanList);

        // 如果planModelList 中不包含名称为 对应的plan，则报错。
        allPlanList.forEach(planModel-> {
            // 给Plan增加用户传递过来的阶段参数,默认为ACCEPT
            OrderPlanEntity.Phase phase = body.getPlanList()
                    .stream()
                    .filter(item -> Objects.equals(item.getName(),planModel.getName()))
                    .map(OrderFlashConfirmV2Req.PlanItem::getPhase)
                    .findFirst()
                    .orElse(OrderPlanEntity.Phase.ACCEPT);
            planModel.setPhase(phase) ;
            planService.addPlanToFlash(flashEntity, planModel, userDetail);
        });

        LogUtils.clearTracePoint();

        return HandleResp.ok(null, "添加成功");
    }

    @Transactional
    @ApiOperation(value = "给指定的Plan添加设备.", notes = "给指定的Plan添加设备.")
    @PostMapping("/{planId}/device/add")
    public HandleResp<String> addDevices(
            @PathVariable("planId") @ApiParam("Plan id")  @Min(value=1,message = "无效的Plan ID") long planId,
            @RequestBody @Valid AddDeviceParams body,
            @ApiIgnore @AuthenticationPrincipal @NotNull(message = "尚未登录") OAuthUserDetail userDetail
    ) {
        String operate = "添加设备";
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(),planEntity.getFlash(), operate);
        log.info("{} add devices {} to {}-{}:",userDetail.getUsername(), body, planEntity.getName(),planId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash()) ;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(body.getOrderId());

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN);
        if (
                planEntity.getStatus() == OrderPlanEntity.Status.STOPPED ||
                planEntity.getStatus() == OrderPlanEntity.Status.NEW ||
                planEntity.getStatus() == OrderPlanEntity.Status.QUEUE
        ) {
            return HandleResp.failed(planEntity.getStatus() + "状态下的Plan 不能添加设备!");
        }

        // 如果这个时候发现 设备正在运行中,或者已经被确认.则返回错误。
        deviceService.checkDevicesNotConfirmedOrRunning(planEntity.getOrderId(),body.getMacList());
        planService.forceAddDevicesToPlan(flashEntity, planEntity, body.getMacList(), userDetail);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.DEVICE_ADDED)
                .orderId(body.getOrderId())
                .flash(body.getFlash())
                .planId(body.getPlanId())
                .build()
        );

        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "添加成功");
    }

    @ApiOperation(value = "临时调整Plan优先级.", notes = "临时调整Plan优先级。需要操作Plan 或者开始测试的权限")
    @PostMapping("/priority")
    public HandleResp<String> changePriority(
            @RequestBody @Valid ChangePriorityParams body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "调整Plan优先级";
        LogUtils.setOrderAndFlashTracePoint(body.getOrderId(), body.getFlash(), operate);
        log.debug("changePriority params:{}",body);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(body.getOrderId());

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN, Permission.START_TEST);
        body.getPlans().stream()
                .map(p -> {
                    OrderPlanEntity planEntity = planRepository.findByOrderIdAndFlashAndName(body.getOrderId(), body.getFlash(), p.getPlanName());
                    if (planEntity == null) {
                        throw new DataNotFoundException("id:" + body.getOrderId() + "的工单不存在 plan:" + p.getPlanName());
                    }
                    return Tuples.of(planEntity, p.getPriority());
                })
                .forEach(p -> {
                    planService.updatePlanPriority(p.getT1(), p.getT2());

                    dataChangeListener.onDataChange(DataChangeEvent.builder()
                            .type(DataChangeEvent.Type.PLAN_PRIORITY_CHANGED)
                            .orderId(body.getOrderId())
                            .flash(body.getFlash())
                            .planId(p.getT1().getId())
                            .build()
                    );

                });

        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "操作成功");
    }

    @ApiOperation(value = "暂缓Plan的分配", notes = "暂缓Plan的分配。需要操作Plan 或者开始测试的权限")
    @PostMapping("/{planId}/pause")
    public HandleResp<String> pauseAssignedPlan(
            @PathVariable("planId") @ApiParam("Plan ID") Long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {

        String operate = "暂缓Plan的分配" ;
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId) ;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId()) ;
        LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(),planEntity.getFlash(), operate);
        log.info("pausePlan: pause plan {}-{}",planEntity.getName(),planId);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN, Permission.START_TEST);

        if(
                planEntity.getStatus() == OrderPlanEntity.Status.RUNNING ||
                        planEntity.getStatus() == OrderPlanEntity.Status.STOPPED ||
                        planEntity.getStatus() == OrderPlanEntity.Status.COMPLETED
        ){
            log.info("{} 的状态为：{}，不能暂缓！", planEntity.getName(), planEntity.getStatus());
            return HandleResp.failed("Plan状态不正确，不能暂缓分配!");
        }

        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash()) ;

        if(planEntity.isTestAll()) {
            log.info("{} 需要测试全部的样片，需要同时暂缓对应的Flash:{}！", planEntity.getName(), planEntity.getFlash());
            flashService.disableFlash(flashEntity);
        }
        planEntity.setDisabled(true);
        planService.pausePlan(planEntity,flashEntity);
        log.info("Flash:{} 下的 : {} 暂缓成功! ",flashEntity.getFlash() ,planEntity.getName());

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_PAUSE)
                .orderId(flashEntity.getOrderId())
                .flash(flashEntity.getFlash())
                .planId(planId)
                .build()
        );
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "操作成功");
    }

    @ApiOperation(value = "恢复分配", notes = "恢复分配,恢复分配后，plan 可以重新进入到队列中进行等待！")
    @PostMapping("/{planId}/resume")
    @Transactional
    public HandleResp<String> resume(
            @PathVariable("planId") @ApiParam("Plan ID") Long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "恢复Plan的分配" ;
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId) ;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId()) ;
        LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(),planEntity.getFlash(), "恢复Plan的分配");
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate,Permission.OPERATE_PLAN, Permission.START_TEST);

        if ( !planEntity.isDisabled() ) {
            log.info("{} 没有暂缓,不需要恢复", planEntity.getName());
            LogUtils.clearTracePoint();
            return HandleResp.failed("Plan状态不正确，不能恢复分配!");
        }
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderEntity.getId(), planEntity.getFlash());

        if(planEntity.isTestAll() || flashEntity.isDisabled()) {
            flashEntity.setDisabled(false);
            flashEntity.setUpdatedAt(System.currentTimeMillis());
            orderFlashRepository.save(flashEntity);
            log.info("Flash：{} 被暂缓了, 已恢复分配 ! ", flashEntity.getFlash());
        }

        planEntity.setDisabled(false);
        planEntity.setUpdatedAt(System.currentTimeMillis());
        planRepository.save(planEntity);
        // 调整Plan分配状态
        planAssignService.updatePlanStatusToWaiting(planId);


        log.info(" Flash:{} 下的 {} 恢复分配成功 ! ",flashEntity.getFlash() ,planEntity.getName());

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_RESUME)
                .orderId(flashEntity.getOrderId())
                .flash(flashEntity.getFlash())
                .planId(planId)
                .build()
        );
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "操作成功");
    }

    @Transactional
    @ApiOperation(value = "增加临时Plan",notes = "用于增加一些临时性的Plan")
    @PostMapping("/temp/add")
    public HandleResp<PlanModel> addTempPlan(
            @Valid @RequestBody AddTempPlanReq body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {

        String operate = "增加临时Plan" ;
        WorkOrderEntity orderEntity = workOrderRepository.findById(body.getOrderId()).orElseThrow(() -> new DataNotFoundException("id:" + body.getOrderId() + "的工单不存在"));
        LogUtils.setOrderTracePoint(body.getOrderId(), operate);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate,  Permission.OPERATE_PLAN, Permission.START_TEST);

        TempPlanEntity tempPlanEntity = planService.saveTemPlan(body, orderEntity, userDetail);

        log.info("临时Plan: {} 添加成功!", tempPlanEntity.getName());

        LogUtils.clearTracePoint();

        return HandleResp.ok(tempPlanEntity.convert2PlanModel(), "添加成功!");

    }


    @PostMapping("/{planId}/assign")
    @ApiOperation(value = "给指定的Plan分配操作人",notes = "给指定的Plan分配操作人，主要用于通知")
    public HandleResp<String> assignPlanOperator(
            @PathVariable("planId") @ApiParam("Plan ID") Long planId,
            @RequestParam("operatorId") @ApiParam("操作人钉钉ID") String dingId,
            @RequestParam("operatorName") @ApiParam("操作人钉钉 名称") String username,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "指定Plan操作人" ;
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(), planEntity.getFlash(),operate);

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(), operate,  Permission.OPERATE_PLAN, Permission.START_TEST);

        planEntity.setBelongTo(dingId);
        planEntity.setBelongToPerson(username);
        planEntity.setUpdatedAt(System.currentTimeMillis());
        planRepository.save(planEntity);

        log.info("{} 已由{} 指派给{}",planEntity.getName(),userDetail.getUsername(),username);

        LogUtils.clearTracePoint();
        return HandleResp.ok(null,"操作成功");
    }


    @PostMapping("/batch/start")
    @ApiOperation(value = "批量启动Plan",notes = "批量启动Plan, 目前还没有优化")
    public HandleResp<String> batchStartPlan(
            @Valid @RequestBody BatchPlanOperateReq body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "批量启动Plan" ;
        LogUtils.setOrderAndFlashTracePoint(body.getOrderId(), body.getFlashName(), operate);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(body.getOrderId());
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(body.getOrderId(), body.getFlashName());

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(), operate,  Permission.OPERATE_PLAN, Permission.START_TEST);

        List<OrderPlanEntity> planEntities = planRepository.findAllById(body.getPlanIdList());
        if(planEntities.size() != body.getPlanIdList().size()){
            throw new DataNotFoundException("部分Plan不存在");
        }

        // 检查是否有数据不对应的情况
        planService.checkBatchPlanValid(body.getOrderId(), body.getFlashName(),planEntities);

        for(OrderPlanEntity planEntity : planEntities){
            if(planEntity.getStatus().ordinal()  >= OrderPlanEntity.Status.RUNNING.ordinal()){
                throw new DataNotFoundException("Plan:" + planEntity.getName() + "已经启动");
            }
        }

        for (OrderPlanEntity planEntity : planEntities) {
            planService.startPlan(
                    orderEntity,
                    flashEntity,
                    planEntity,
                    userDetail
            );
        }

        log.info("批量启动Plan成功!");

        LogUtils.clearTracePoint();
        return HandleResp.ok(null,"操作成功");
    }



    @PostMapping("/batch/cancel")
    @ApiOperation(value = "批量停止并取消Plan ",notes = "给指定的Plan分配操作人，主要用于通知")
    public HandleResp<String> batchCancelPlan(
            @Valid @RequestBody BatchPlanOperateReq body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "批量停止Plan" ;
        LogUtils.setOrderAndFlashTracePoint(body.getOrderId(), body.getFlashName(), operate);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(body.getOrderId());
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(body.getOrderId(), body.getFlashName());
        roleService.checkPermission(userDetail,orderEntity.getSubProduct(), operate,  Permission.CANCEL_PLAN);

        List<OrderPlanEntity> planEntities = planRepository.findAllById(body.getPlanIdList());
        if(planEntities.size() != body.getPlanIdList().size()){
            throw new DataNotFoundException("部分Plan不存在");
        }

        // 检查是否有数据不对应的情况
        planService.checkBatchPlanValid(body.getOrderId(), body.getFlashName(),planEntities);


        for (OrderPlanEntity planEntity : planEntities) {
            planService.terminatePlan(
                    orderEntity,
                    flashEntity,
                    planEntity
            );
        }

        log.info("批量启动Plan成功!");



        LogUtils.clearTracePoint();
        return HandleResp.ok(null,"操作成功");
    }


    @Transactional
    @ApiOperation(value = "已取消的Plan重新测试.", notes = "已取消的Plan重新测试.")
    @PostMapping("/{orderId}/{planId}/retest")
    public HandleResp<String> retryCanceledPlan(
            @PathVariable("orderId") @ApiParam("工单id") long orderId,
            @PathVariable("planId") @ApiParam("Plan id")  @Min(value=1,message = "无效的Plan ID") long planId,
            @ApiIgnore @AuthenticationPrincipal @NotNull(message = "尚未登录") OAuthUserDetail userDetail
    ) {
        String operate = "重测已取消Plan";
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, planEntity.getFlash());
        if (flashEntity.getStatus() == OrderFlashEntity.Status.NEW
                || flashEntity.getStatus() == OrderFlashEntity.Status.WAITING_FOR_START
                || flashEntity.getStatus() == OrderFlashEntity.Status.IN_PROGRESS
        ) {
            LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(), planEntity.getFlash(), operate);
            planService.retryCanceledPlan(planEntity, userDetail);
            log.info("plan: {} 已加入测试列表中!", planEntity.getName());
            LogUtils.clearTracePoint();
            return HandleResp.ok(null, "重测开始！");
        } else {
            return HandleResp.failed("Flash状态不支持重测Plan！");
        }
    }

    @SneakyThrows
    @ApiOperation(value = "使用EventSource监听Plan详细信息.", notes = "使用EventSource监听Plan详细信息.")
    @GetMapping(value = "/{orderId}/{flash}/{planId}/detail",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter fetchPlanDetailInfo(
            @PathVariable("orderId") @ApiParam("order ID") Long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @PathVariable("planId") @ApiParam("Plan ID") Long planId
    ){
        SseEmitter sseEmitter = new SseEmitter(-1L);
        dataChangeListener.addEmitter(
                DataChangeListener.EmitterType.Plan,
                planId,
                sseEmitter
        );
        Consumer<Throwable> consumer = e -> dataChangeListener.removeEmitter(
                    DataChangeListener.EmitterType.Plan,
                    planId,
                    sseEmitter
            );
        sseEmitter.onTimeout(()-> consumer.accept(null));
        sseEmitter.onCompletion(()-> consumer.accept(null));
        sseEmitter.onError(consumer);

        new Thread(()->{
            WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
            OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
            OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
            PlanDetailVO planDetail = planService.fetchPlanInfo(orderEntity.getSubProduct(), planEntity, flashEntity);

            PlanDetailResp resp = PlanDetailResp.convertToSimple(orderEntity, planDetail);
            resp.setFlashStatus(flashEntity.getStatus());
            try {
                sseEmitter.send(SseEmitter.event()
                        .name("plan")
                        .id(String.valueOf(System.currentTimeMillis()))
                        .data(resp, MediaType.APPLICATION_JSON)
                );
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }).start();
        return sseEmitter ;

    }

    @ApiOperation(value = "获取设备样片上盘情况")
    @GetMapping("/device/disk")
    public SseEmitter fetchDeviceDisk(
            @RequestParam(value = "uuid") String uuid ,
            @RequestParam(value = "planId") Long planId,
            @NotEmpty(message = "设备列表为空") @RequestParam(value = "macList") List<String> macList,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){

        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());
        List<PlanDeviceEntity> devices = deviceService.findDevicesByPlanIdAndMacIn(planId, macList);

        // 10分钟超时
        SseEmitter sseEmitter = new SseEmitter(600000L);
        dataChangeListener.addEmitter(
                DataChangeListener.EmitterType.DEVICE_DISK,
                uuid+";"+planId,
                sseEmitter
        );
        Consumer<Throwable> consumer = e -> dataChangeListener.removeEmitter(
                DataChangeListener.EmitterType.DEVICE_DISK,
                uuid+";"+planId,
                sseEmitter
        );

        Span span = tracer.currentSpan();
        assert span != null;
        String traceId = span.context().traceId();
        span.tag("DEVICE_DISK", traceId) ;
        span.tag(LogUtils.MDC_KEY_ORDER_NUMBER, String.valueOf(planEntity.getOrderId()));
        span.tag(LogUtils.MDC_KEY_FLASH, planEntity.getFlash());
        log.info("{} 开始获取设备磁盘信息, macList: {}",userDetail , macList);

        // 先获取这些设备基础信息, 将其转换成 DeviceDiskEntity , 保存到数据库中
        deviceService.prepareForDeviceDisk(planEntity, devices,uuid, traceId);
        // 然后再获取这些设备的磁盘信息
        rmsApis.getDeviceDiskInfo(macList, flashEntity.getOrderFlashNo());

        sseEmitter.onTimeout(()-> consumer.accept(null));
        sseEmitter.onCompletion(()-> consumer.accept(null));
        sseEmitter.onError(consumer);
        try {
            sseEmitter.send(SseEmitter.event()
                    .name("device")
                    .id(String.valueOf(System.currentTimeMillis()))
                    .data(new HashMap<>(), MediaType.APPLICATION_JSON)
            );
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return sseEmitter;
    }

    @PostMapping("/{planId}/reassign/device")
    @ApiOperation(value = "Plan重新分配",notes = "用于解决设备被分配给多个Plan无法解除锁定问题")
    public HandleResp<String> reassignPlan(
            @PathVariable("planId") @ApiParam("Plan ID") Long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "Plan重新分配" ;
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(), planEntity.getFlash(), operate);
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.MANUAL_UNLOCK);

        log.info("重新分配 {} , 由 {} 操作.", planEntity.getName(), userDetail.getUsername());
        planService.reassignPlan(planId);
        LogUtils.clearTracePoint();
        return HandleResp.ok(null,"操作成功");
    }

    @ApiOperation(value = "获取Plan测试中记录的描述信息.", notes = "获取Plan测试中记录的描述信息.")
    @GetMapping("/noteMsg/{planId}")
    public HandleResp<String> fetchPlanNoteMsg(@PathVariable("planId") long planId) {
        String msg = planService.fetchPlanNote(planId);
        return HandleResp.ok(msg, "描述信息获取成功！");
    }

    @ApiOperation(value = "保存Plan测试中记录的描述信息.", notes = "保存Plan测试中记录的描述信息.")
    @PostMapping("/noteMsg/save")
    public HandleResp<String> handlePlanNoteMsg(
            @RequestParam(value = "planId") Long planId,
            @RequestParam(value = "msg") String msg
    ) {
        planService.handlePlanNote(planId, msg);
        return HandleResp.ok(null, "描述信息保存成功！");
    }

    @PostMapping("/{planId}/assign/check")
    @ApiOperation(value = "Plan排队检测",notes = "用于检测Plan未分配的原因")
    public HandleResp<String> checkPlanAssign(
            @PathVariable("planId") @ApiParam("Plan ID") Long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());
        if(planEntity.getStatus().ordinal() != OrderPlanEntity.Status.QUEUE.ordinal()){
            return HandleResp.ok(null,"Plan已分配无需检测！");
        }

        if(planEntity.isDisabled()){
            return HandleResp.ok(null,"Plan已暂缓分配！");
        }

        if(flashEntity.isDisabled()){
            return HandleResp.ok(null,"Flash已暂缓分配！");
        }
        List<OrderPlanEntity> allPlanEntityList = planService.fetchAllPlanByFlash(planEntity.getOrderId(), planEntity.getFlash());

        if(!planService.checkParent(planEntity, allPlanEntityList)){
            return HandleResp.ok(null,"关联的Plan尚未测试完成，请等候！");
        }
        // 此次分配的进程中，某个测试人员剩余的Plan可分配额度
        int leftPlanNum = planService.leftPlanCountByUserId(allPlanEntityList, planEntity.getBelongTo());
        if(leftPlanNum == 0){
            return HandleResp.ok(null,"已成功分配5个Plan，请先确认环境！");
        }

        // 此次分配进程中涉及到的Flash批次下所有可分配Plan
        List<OrderPlanEntity> canAssignList = queueService.fetchCanAssignPlanList(allPlanEntityList);
        // 此次分配进程中，此plan负责人的待分配队列
        List<OrderPlanEntity> userCanAssignList = canAssignList.stream()
                .filter(p -> planEntity.getBelongTo().equals(p.getBelongTo()))
                .collect(Collectors.toList());

        boolean containsPlanId = userCanAssignList.subList(0, Math.min(userCanAssignList.size(), leftPlanNum))
                .stream().anyMatch(plan -> plan.getId() == planId);
        boolean isAssign = planAssignService.isAssignQueue(planId);

        if(!containsPlanId || !isAssign){
            return HandleResp.ok(null,"Plan不在队列中，请先完成其他测试！");
        }

        if(flashEntity.getLeftNum() < planEntity.getTestNum()){
            return HandleResp.ok(null,"Flash剩余样片不足以完成Plan的预分配！");
        }

        return HandleResp.ok(null,"Plan正在排队，请检查设备是否充足！");
    }

    @ApiOperation(value = "获取Plan测试负责人历史测试使用的设备")
    @GetMapping("/{planId}/history/device")
    public HandleResp<List<DeviceTestHistoryEntity>> fetchPlanTestDevice(@PathVariable("planId") long planId) {
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        WorkOrderEntity workOrderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        return HandleResp.ok(testHistoryService.findDeviceTestHistoryByPlan(workOrderEntity.getSubProduct(), planEntity.getName(), planEntity.getBelongTo()), "查看测试负责人历史测试设备成功！");
    }
}
