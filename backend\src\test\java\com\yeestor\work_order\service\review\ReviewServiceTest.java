package com.yeestor.work_order.service.review;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class ReviewServiceTest {

    @Autowired
    ReviewService reviewService;

    @Test
    void fetchInfo() {
        reviewService.fetchInfo(2092, "CY-6287EN-JGS-B_32GB");
    }
}