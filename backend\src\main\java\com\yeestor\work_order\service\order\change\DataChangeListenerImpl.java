package com.yeestor.work_order.service.order.change;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.device.DeviceDiskEntity;
import com.yeestor.work_order.model.event.DeviceDiskEvent;
import com.yeestor.work_order.model.http.resp.order.WorkOrderDetailVO;
import com.yeestor.work_order.model.rms.DeviceInfo;
import com.yeestor.work_order.model.rms.SampleInfo;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.repository.device.DeviceDiskRepository;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.utils.LogUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataChangeListenerImpl implements DataChangeListener {

    private final ConcurrentHashMap<String, List<SseEmitter>> emittersMap = new ConcurrentHashMap<>();

    private FluxSink<DataChangeEvent> sink;

    @Lazy
    @Autowired
    private OrderService orderService;
    @Lazy
    @Autowired
    private FlashService flashService;
    @Lazy
    @Autowired
    private PlanService planService;

    private final OrderFlashRepository orderFlashRepository ;

    private final OrderPlanRepository orderPlanRepository ;
    private final DeviceDiskRepository deviceDiskRepository ;
    private final Tracer tracer ;

    @Override
    public void onDataChange(DataChangeEvent event) {
        this.sink.next(event);
    }

    @org.springframework.context.event.EventListener
    public void handleDiskDiskEvent(DeviceDiskEvent diskEvent) {
        // 处理回调.
        Span currentSpan = tracer.currentSpan();
        log.debug("handleDiskDiskEvent -- {}",diskEvent);
        OrderFlashEntity orderFlashEntity = flashService.findFlashOrElseThrow(diskEvent.getOrderNo());

        if (currentSpan == null) {
            currentSpan = tracer.nextSpan().name("handleDiskDiskEvent").start();
        }
        currentSpan.tag(LogUtils.MDC_KEY_ORDER_NUMBER, String.valueOf(orderFlashEntity.getOrderId()));
        currentSpan.tag(LogUtils.MDC_KEY_FLASH, orderFlashEntity.getFlash());
        DeviceInfo deviceInfo = diskEvent.getDiskInfo();

        DeviceDiskEntity diskEntity = deviceDiskRepository.findFirstByOrderIdAndFlashAndMacOrderByCreatedAtDesc(
                orderFlashEntity.getOrderId(),
                orderFlashEntity.getFlash(),
                deviceInfo.getMac()
        );

        if(diskEntity == null){
            return;
        }
        String traceId = diskEntity.getTraceId();
        currentSpan.tag("DEVICE_DISK", traceId) ;
        List<SampleInfo> sampleInfoLst = deviceInfo.getSampleNoLst();
        diskEntity.setSamples(sampleInfoLst);


        log.info(" Devices:{} samples: {} ",deviceInfo.getNo(),deviceInfo.getSampleNoLst());

        // 保存到数据库中.
        deviceDiskRepository.save(diskEntity);

        // 发送事件.
        sendEvent(
                EmitterType.DEVICE_DISK,
                diskEntity.getRequestId() + ";" + diskEntity.getPlanId(),
                "device-disk",
                diskEntity.getDeviceId()+ "-"+ System.currentTimeMillis(),
                diskEntity
        );

    }

    @Override
    public void startHandleData() {

        Flux.<DataChangeEvent>create(s -> {
                    this.sink = s;
                    s.onDispose(() -> this.sink = null);
                })
                .groupBy(DataChangeEvent::groupField)
                .subscribe(g ->
                        g.sampleTimeout(d -> Mono.delay(Duration.ofMillis(200)),1)
                        .subscribe(this::handleData)
                );
    }


    private void handleData(DataChangeEvent e) {
        if(e.getOrderId() <= 0){
            return;
        }
        log.debug("handleData -- {}",e);
        List<SseEmitter> emitterList = getOrderEmitters(DataChangeListener.EmitterType.Order, e.getOrderId());

        WorkOrderDetailVO detailVO = orderService.fetchOrderInfo(e.getOrderId());
        if(e.getFlash() != null && e.getPlanId() != null){
            // plan id以及Flash 不为空的时候,则认为是Plan发生了变化.
            OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(e.getOrderId(),e.getFlash()) ;
            orderPlanRepository
                    .findById(e.getPlanId())
                    .map(p -> planService.fetchPlanInfo(detailVO.getSubProduct(), p, flashEntity))
                    .ifPresent(pdi -> {
                        List<SseEmitter> planEmitters = getOrderEmitters(EmitterType.Plan, e.getPlanId()) ;

                        HashMap<String,Object> data= new HashMap<>();
                        data.put("type",e.getType()) ;
                        data.put("data",pdi);
                        sendEvent("plan",String.valueOf(System.currentTimeMillis()),data,emitterList);
                        sendEvent("plan-change",String.valueOf(System.currentTimeMillis()), data, planEmitters);
                    });

        }
        else if(e.getFlash() != null) {
            //只有Flash不为空的话,则只更新Flash.
            orderFlashRepository.findByOrderIdAndFlash(e.getOrderId(), e.getFlash())
                    .map(WorkOrderDetailVO.FlashInfoVO::entityToVO)
                    .ifPresent(flashInfo -> sendEvent("flashinfo", String.valueOf(System.currentTimeMillis()), flashInfo, emitterList));

        }
        else {
            sendEvent("order",String.valueOf(System.currentTimeMillis()),detailVO,emitterList);
        }
    }

    private void sendEvent(String eventTye, String id ,Object data, List<SseEmitter> emitterList ){
        emitterList.forEach(emitter -> {
            try {
                emitter.send(SseEmitter.event()
                        .name(eventTye)
                        .id(id)
                        .data(data,MediaType.APPLICATION_JSON)
                );
            }
            catch (ClientAbortException e) {
                emitter.complete();
            }
            catch (Exception e) {
                log.error("sendEvent error",e);
            }
        });
    }


    private void sendEvent(EmitterType type, Object topic,
                           String eventTye, String id ,Object data) {
        List<SseEmitter> emitterList = getOrderEmitters(type, topic);
        sendEvent(eventTye,id,data,emitterList);
    }


    @Override
    public void addEmitter(EmitterType type, Object data, SseEmitter emitter) {
        String key = type + "_" + data;
        List<SseEmitter> emitters = emittersMap.getOrDefault(key,
                Collections.synchronizedList(new ArrayList<>())
        );
        emitters.add(emitter);
        emittersMap.put(key, emitters);
    }

    @Override
    public void removeEmitter(EmitterType type, Object data, SseEmitter emitter) {
        String key = type + "_" + data;
        List<SseEmitter> emitters = emittersMap.getOrDefault(key, new ArrayList<>());
        emitters.remove(emitter);
        emittersMap.put(key, emitters);
    }

    @Override
    public List<SseEmitter> getOrderEmitters(EmitterType type, Object data) {
        String key = type + "_" + data;
        return emittersMap.getOrDefault(key, new ArrayList<>());
    }


}
