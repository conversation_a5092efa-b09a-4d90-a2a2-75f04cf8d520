{"@timestamp":"2025-07-23T15:10:02.674+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7eb8f25a522c6da6","spanId":"7eb8f25a522c6da6","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:10:02.683+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7eb8f25a522c6da6","spanId":"7eb8f25a522c6da6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.688+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7eb8f25a522c6da6","spanId":"7eb8f25a522c6da6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.688+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7eb8f25a522c6da6","spanId":"7eb8f25a522c6da6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.689+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7eb8f25a522c6da6","spanId":"7eb8f25a522c6da6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.649+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58e036a169212286","spanId":"58e036a169212286","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:14:02.651+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58e036a169212286","spanId":"58e036a169212286","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.655+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58e036a169212286","spanId":"58e036a169212286","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.655+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58e036a169212286","spanId":"58e036a169212286","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.655+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58e036a169212286","spanId":"58e036a169212286","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.667+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8893899740977c32","spanId":"8893899740977c32","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.669+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8893899740977c32","spanId":"8893899740977c32","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.674+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8893899740977c32","spanId":"8893899740977c32","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.675+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8893899740977c32","spanId":"8893899740977c32","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.675+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8893899740977c32","spanId":"8893899740977c32","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.649+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"543e802c7a38ac83","spanId":"543e802c7a38ac83","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:26:02.65+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"543e802c7a38ac83","spanId":"543e802c7a38ac83","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"543e802c7a38ac83","spanId":"543e802c7a38ac83","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.654+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"543e802c7a38ac83","spanId":"543e802c7a38ac83","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.654+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"543e802c7a38ac83","spanId":"543e802c7a38ac83","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.101+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b0f216ce68a63d04","spanId":"b0f216ce68a63d04","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:50:03.131+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b0f216ce68a63d04","spanId":"b0f216ce68a63d04","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.143+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b0f216ce68a63d04","spanId":"b0f216ce68a63d04","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.143+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b0f216ce68a63d04","spanId":"b0f216ce68a63d04","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.143+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b0f216ce68a63d04","spanId":"b0f216ce68a63d04","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.688+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c069faa28aab869c","spanId":"c069faa28aab869c","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:02.69+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c069faa28aab869c","spanId":"c069faa28aab869c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.695+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c069faa28aab869c","spanId":"c069faa28aab869c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.695+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c069faa28aab869c","spanId":"c069faa28aab869c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.695+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c069faa28aab869c","spanId":"c069faa28aab869c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.652+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ef7db2913ed3602","spanId":"9ef7db2913ed3602","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:58:02.654+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ef7db2913ed3602","spanId":"9ef7db2913ed3602","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.658+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ef7db2913ed3602","spanId":"9ef7db2913ed3602","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.658+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ef7db2913ed3602","spanId":"9ef7db2913ed3602","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.658+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ef7db2913ed3602","spanId":"9ef7db2913ed3602","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.649+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a1e85f21984a499","spanId":"1a1e85f21984a499","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:02:02.651+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a1e85f21984a499","spanId":"1a1e85f21984a499","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a1e85f21984a499","spanId":"1a1e85f21984a499","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.655+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a1e85f21984a499","spanId":"1a1e85f21984a499","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.655+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a1e85f21984a499","spanId":"1a1e85f21984a499","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.666+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6324cd648490022","spanId":"f6324cd648490022","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:02.667+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6324cd648490022","spanId":"f6324cd648490022","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6324cd648490022","spanId":"f6324cd648490022","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.671+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6324cd648490022","spanId":"f6324cd648490022","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.671+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6324cd648490022","spanId":"f6324cd648490022","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.662+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2730f5804bf8493","spanId":"a2730f5804bf8493","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:10:02.663+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2730f5804bf8493","spanId":"a2730f5804bf8493","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.669+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2730f5804bf8493","spanId":"a2730f5804bf8493","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.669+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2730f5804bf8493","spanId":"a2730f5804bf8493","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.669+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2730f5804bf8493","spanId":"a2730f5804bf8493","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.686+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9a0ffab72c9d941","spanId":"b9a0ffab72c9d941","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:05.229+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9a0ffab72c9d941","spanId":"b9a0ffab72c9d941","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.238+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9a0ffab72c9d941","spanId":"b9a0ffab72c9d941","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.238+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9a0ffab72c9d941","spanId":"b9a0ffab72c9d941","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.238+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9a0ffab72c9d941","spanId":"b9a0ffab72c9d941","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.644+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3b3ecc38b590e82","spanId":"d3b3ecc38b590e82","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:22:02.645+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3b3ecc38b590e82","spanId":"d3b3ecc38b590e82","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.649+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3b3ecc38b590e82","spanId":"d3b3ecc38b590e82","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.649+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3b3ecc38b590e82","spanId":"d3b3ecc38b590e82","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.649+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3b3ecc38b590e82","spanId":"d3b3ecc38b590e82","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.71+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2375ae4eb8999603","spanId":"2375ae4eb8999603","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:26:02.712+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2375ae4eb8999603","spanId":"2375ae4eb8999603","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.716+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2375ae4eb8999603","spanId":"2375ae4eb8999603","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.716+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2375ae4eb8999603","spanId":"2375ae4eb8999603","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.717+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2375ae4eb8999603","spanId":"2375ae4eb8999603","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.656+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b85886158e8557df","spanId":"b85886158e8557df","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.657+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b85886158e8557df","spanId":"b85886158e8557df","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.661+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b85886158e8557df","spanId":"b85886158e8557df","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.661+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b85886158e8557df","spanId":"b85886158e8557df","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.661+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b85886158e8557df","spanId":"b85886158e8557df","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.651+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bc6dc19c22d063f","spanId":"5bc6dc19c22d063f","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:34:02.652+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bc6dc19c22d063f","spanId":"5bc6dc19c22d063f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.655+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bc6dc19c22d063f","spanId":"5bc6dc19c22d063f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.655+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bc6dc19c22d063f","spanId":"5bc6dc19c22d063f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.655+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bc6dc19c22d063f","spanId":"5bc6dc19c22d063f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.664+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea69b3b3f094ff61","spanId":"ea69b3b3f094ff61","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:38:02.667+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea69b3b3f094ff61","spanId":"ea69b3b3f094ff61","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea69b3b3f094ff61","spanId":"ea69b3b3f094ff61","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.672+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea69b3b3f094ff61","spanId":"ea69b3b3f094ff61","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.672+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea69b3b3f094ff61","spanId":"ea69b3b3f094ff61","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.664+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2efda38662ae6781","spanId":"2efda38662ae6781","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:02.665+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2efda38662ae6781","spanId":"2efda38662ae6781","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.669+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2efda38662ae6781","spanId":"2efda38662ae6781","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.669+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2efda38662ae6781","spanId":"2efda38662ae6781","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.669+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2efda38662ae6781","spanId":"2efda38662ae6781","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"649ad95fd74d5700","spanId":"649ad95fd74d5700","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:46:02.637+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"649ad95fd74d5700","spanId":"649ad95fd74d5700","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"649ad95fd74d5700","spanId":"649ad95fd74d5700","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.64+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"649ad95fd74d5700","spanId":"649ad95fd74d5700","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.64+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"649ad95fd74d5700","spanId":"649ad95fd74d5700","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.646+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f97c1369312eb0b","spanId":"0f97c1369312eb0b","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:50:02.647+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f97c1369312eb0b","spanId":"0f97c1369312eb0b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.649+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f97c1369312eb0b","spanId":"0f97c1369312eb0b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.65+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f97c1369312eb0b","spanId":"0f97c1369312eb0b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.65+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f97c1369312eb0b","spanId":"0f97c1369312eb0b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.815+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf99a1ea2713757f","spanId":"cf99a1ea2713757f","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:02.916+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf99a1ea2713757f","spanId":"cf99a1ea2713757f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.929+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf99a1ea2713757f","spanId":"cf99a1ea2713757f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.93+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf99a1ea2713757f","spanId":"cf99a1ea2713757f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.93+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf99a1ea2713757f","spanId":"cf99a1ea2713757f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.693+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b15a7098df3407","spanId":"88b15a7098df3407","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.695+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b15a7098df3407","spanId":"88b15a7098df3407","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.702+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b15a7098df3407","spanId":"88b15a7098df3407","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.702+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b15a7098df3407","spanId":"88b15a7098df3407","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.703+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b15a7098df3407","spanId":"88b15a7098df3407","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.678+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e68afee0e06d1e5","spanId":"1e68afee0e06d1e5","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:10:02.679+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e68afee0e06d1e5","spanId":"1e68afee0e06d1e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.684+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e68afee0e06d1e5","spanId":"1e68afee0e06d1e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.684+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e68afee0e06d1e5","spanId":"1e68afee0e06d1e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.684+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e68afee0e06d1e5","spanId":"1e68afee0e06d1e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.666+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee13f8b5ee842a60","spanId":"ee13f8b5ee842a60","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:02.669+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee13f8b5ee842a60","spanId":"ee13f8b5ee842a60","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee13f8b5ee842a60","spanId":"ee13f8b5ee842a60","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.679+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee13f8b5ee842a60","spanId":"ee13f8b5ee842a60","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.679+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee13f8b5ee842a60","spanId":"ee13f8b5ee842a60","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.671+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d9430ad7cf8a70f","spanId":"1d9430ad7cf8a70f","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.672+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d9430ad7cf8a70f","spanId":"1d9430ad7cf8a70f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.676+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d9430ad7cf8a70f","spanId":"1d9430ad7cf8a70f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.676+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d9430ad7cf8a70f","spanId":"1d9430ad7cf8a70f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.676+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d9430ad7cf8a70f","spanId":"1d9430ad7cf8a70f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.661+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6019b3db77be0fea","spanId":"6019b3db77be0fea","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:22:02.663+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6019b3db77be0fea","spanId":"6019b3db77be0fea","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.667+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6019b3db77be0fea","spanId":"6019b3db77be0fea","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.667+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6019b3db77be0fea","spanId":"6019b3db77be0fea","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.667+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6019b3db77be0fea","spanId":"6019b3db77be0fea","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.655+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29df09b9a4034014","spanId":"29df09b9a4034014","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:02.656+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29df09b9a4034014","spanId":"29df09b9a4034014","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29df09b9a4034014","spanId":"29df09b9a4034014","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.66+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29df09b9a4034014","spanId":"29df09b9a4034014","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.66+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29df09b9a4034014","spanId":"29df09b9a4034014","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.75+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c7406482eb48403","spanId":"5c7406482eb48403","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.752+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c7406482eb48403","spanId":"5c7406482eb48403","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.756+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c7406482eb48403","spanId":"5c7406482eb48403","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.757+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c7406482eb48403","spanId":"5c7406482eb48403","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.757+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c7406482eb48403","spanId":"5c7406482eb48403","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.674+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d00da3069194905","spanId":"2d00da3069194905","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:34:02.675+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d00da3069194905","spanId":"2d00da3069194905","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.682+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d00da3069194905","spanId":"2d00da3069194905","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.683+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d00da3069194905","spanId":"2d00da3069194905","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.683+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d00da3069194905","spanId":"2d00da3069194905","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.947+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"628cc1defb5200ce","spanId":"628cc1defb5200ce","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:02.961+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"628cc1defb5200ce","spanId":"628cc1defb5200ce","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.975+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"628cc1defb5200ce","spanId":"628cc1defb5200ce","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.975+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"628cc1defb5200ce","spanId":"628cc1defb5200ce","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.976+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"628cc1defb5200ce","spanId":"628cc1defb5200ce","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.726+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a687510c634e503f","spanId":"a687510c634e503f","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.731+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a687510c634e503f","spanId":"a687510c634e503f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.744+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a687510c634e503f","spanId":"a687510c634e503f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.745+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a687510c634e503f","spanId":"a687510c634e503f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.745+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a687510c634e503f","spanId":"a687510c634e503f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.736+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f577efb112606ab","spanId":"2f577efb112606ab","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:02.793+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f577efb112606ab","spanId":"2f577efb112606ab","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.807+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f577efb112606ab","spanId":"2f577efb112606ab","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.807+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f577efb112606ab","spanId":"2f577efb112606ab","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.807+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f577efb112606ab","spanId":"2f577efb112606ab","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.693+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd98cc432bae81c4","spanId":"dd98cc432bae81c4","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.698+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd98cc432bae81c4","spanId":"dd98cc432bae81c4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.704+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd98cc432bae81c4","spanId":"dd98cc432bae81c4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.705+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd98cc432bae81c4","spanId":"dd98cc432bae81c4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.705+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd98cc432bae81c4","spanId":"dd98cc432bae81c4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.654+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44c84e0f599f19d1","spanId":"44c84e0f599f19d1","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:10:02.655+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44c84e0f599f19d1","spanId":"44c84e0f599f19d1","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.659+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44c84e0f599f19d1","spanId":"44c84e0f599f19d1","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.659+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44c84e0f599f19d1","spanId":"44c84e0f599f19d1","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.659+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44c84e0f599f19d1","spanId":"44c84e0f599f19d1","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.666+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5058249441ab2b8","spanId":"a5058249441ab2b8","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:02.667+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5058249441ab2b8","spanId":"a5058249441ab2b8","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.674+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5058249441ab2b8","spanId":"a5058249441ab2b8","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.675+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5058249441ab2b8","spanId":"a5058249441ab2b8","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.675+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5058249441ab2b8","spanId":"a5058249441ab2b8","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.686+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ea856e8d001bee","spanId":"54ea856e8d001bee","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.687+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ea856e8d001bee","spanId":"54ea856e8d001bee","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.692+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ea856e8d001bee","spanId":"54ea856e8d001bee","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.692+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ea856e8d001bee","spanId":"54ea856e8d001bee","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.692+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ea856e8d001bee","spanId":"54ea856e8d001bee","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.656+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9210219c7e9a3eaa","spanId":"9210219c7e9a3eaa","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:02.658+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9210219c7e9a3eaa","spanId":"9210219c7e9a3eaa","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.662+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9210219c7e9a3eaa","spanId":"9210219c7e9a3eaa","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.664+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9210219c7e9a3eaa","spanId":"9210219c7e9a3eaa","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.664+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9210219c7e9a3eaa","spanId":"9210219c7e9a3eaa","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.653+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f60d7e30382ef136","spanId":"f60d7e30382ef136","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:02.654+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f60d7e30382ef136","spanId":"f60d7e30382ef136","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.658+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f60d7e30382ef136","spanId":"f60d7e30382ef136","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.658+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f60d7e30382ef136","spanId":"f60d7e30382ef136","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.658+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f60d7e30382ef136","spanId":"f60d7e30382ef136","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.66+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:08.667+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.672+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.672+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.672+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6263","traceType":"分配设备"}
