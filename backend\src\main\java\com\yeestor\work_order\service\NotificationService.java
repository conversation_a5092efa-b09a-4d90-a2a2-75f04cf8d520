package com.yeestor.work_order.service;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.dingtalk.api.DingTalkFeignClient;
import com.yeestor.dingtalk.model.InteractiveCardParams;
import com.yeestor.dingtalk.utils.ConversationMessageBuilder;
import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.model.http.req.ShareInfoParams;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.repository.FlashNoticeRepository;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.role.RoleService;
import com.yeestor.work_order.utils.PathUtils;
import com.yeestor.work_order.utils.TextUtils;
import com.yeestor.utils.TimeUtils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.yeestor.utils.TimeUtils.getDistanceTime;
import static com.yeestor.work_order.utils.Const.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationService {

    private static final String CARD_TEMPLATE_ID = "ecea0954-f846-4c98-a740-0fc5dd5e6815" ;
    private static final String ORDER_CARD_TEMPLATE_ID = "b6852774-0ca1-448d-85fb-f1f1e9ab883f.schema";
    private static final String FLASH_CARD_TEMPLATE_ID = "fc9dbba1-d13e-4530-9cee-64c524a60125.schema";
    private final DingTalkFeignClient dingTalkFeignClient;
    private final RoleService roleService ;
    private final Environment environment ;
    private final PathUtils pathUtils;

    private final FlashNoticeRepository flashNoticeRepository;

    @Setter(onMethod_ = { @Autowired, @Lazy} )
    private OrderService orderService;
    @Setter(onMethod_ = { @Autowired, @Lazy} )
    private FlashService flashService;
    @Setter(onMethod_ = { @Autowired, @Lazy} )
    private PlanService planService;
    @Setter(onMethod_ = { @Autowired, @Lazy} )
    private DeviceService deviceService;


    /**
     * Plan 启动完成通知
     * @param orderId 工单id
     * @param orderFlashNo 工单号
     * @param planId plan id
     * @param allDeviceList 设备列表
     */
    public void sendPlanStartStatusNotification(
            long orderId,
            String orderFlashNo,
            long planId ,
            List<PlanDeviceEntity> allDeviceList
    ){

        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity orderFlashEntity = flashService.findFlashOrElseThrow(orderFlashNo);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        log.info("测试单: {} plan: {}开始测试", orderFlashEntity.getOrderFlashNo(), planEntity.getName());

        int expectNum = planEntity.getTestNum() ;
        int realNum = allDeviceList.stream().map(p -> Optional.ofNullable(p.getActualNum()).orElse(0)).reduce(Integer::sum).orElse(0) ;

        List<PlanDeviceEntity> failedDeviceList = allDeviceList.stream()
                .filter(
                    d->d.getActualNum() == null || d.getStatus() == PlanDeviceEntity.Status.FINISHED_FAILED
                )
                .collect(Collectors.toList());


        String deviceFormatStr = failedDeviceList.stream()
                .map(d->"\t"+d.getNo()+"-"+ d.getIp()+"\n")
                .collect(Collectors.joining()) ;

        String failedDevicesStr =  !failedDeviceList.isEmpty()  ? "**失败设备:**  \n  "+ deviceFormatStr : "" ;

        String url = pathUtils.getFlashDingUrl(orderEntity, planEntity.getFlash());
        String text = String.format(
                "# %s启动完成! \n  " +
                        "## %s \n  "+
                        "**Flash批次:** %s  \n  "+
                        "**设备总数:** %s台  \n  "+
                        "**启动成功:** %s台设备  \n  "+
                        "**启动失败:** %s台设备  \n  "+
                        "**样片数量:** %s  \n  "+
                        "**启动时间:** %s  \n  "+
                        "%s"+
                "[查看详情](%s)  \n  ",
                planEntity.getName(),
                orderEntity.getNo(),
                orderFlashEntity.getFlash(),
                allDeviceList.size(),
                allDeviceList.size() - failedDeviceList.size(),
                failedDeviceList.size(),
                realNum + "/" + expectNum,
                TimeUtils.formatTimestamp(planEntity.getStartAt()),
                failedDevicesStr,
                url
        );
        List<String> userIdList = Collections.singletonList(planEntity.getStartBy());
        String title =  "Plan启动完成！" + TimeUtils.formatTimestamp(System.currentTimeMillis()) ;

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        userIdList,
                        title,
                        text
                )
        );
    }

    /**
     * 平台启动后的工作通知，包括成功数量、失败信息等
     * @param orderId 工单id
     * @param orderFlashNo 测试单号
     * @param planId plan id
     * @param allPlatformList 平台信息信息
     */
    public void sendTerminalPlanStartStatusNotification(
            long orderId,
            String orderFlashNo,
            long planId ,
            List<PlanPlatformEntity> allPlatformList
    ){
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity orderFlashEntity = flashService.findFlashOrElseThrow(orderFlashNo);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        log.info("测试单: {} 终端plan: {}开始测试", orderFlashEntity.getOrderFlashNo(), planEntity.getName());

        // 执行失败的
        List<PlanPlatformEntity> failedList = allPlatformList.stream()
                .filter(d-> d.getStatus() == PlanPlatformEntity.Status.FINISHED_FAILED)
                .collect(Collectors.toList());

        // 失败信息
        String formatStr = failedList.stream()
                .map(d->"\t"+d.getNo()+"-"+ d.getNumber()+"\n")
                .collect(Collectors.joining());

        String failedStr =  !failedList.isEmpty()  ? "**失败设备:**  \n  "+ formatStr : "" ;

        String url = pathUtils.getFlashDingUrl(orderEntity, planEntity.getFlash());
        String text = String.format(
                "# %s启动完成! \n  " +
                        "## %s \n  "+
                        "**Flash批次:** %s  \n  "+
                        "**平台总数:** %s台  \n  "+
                        "**启动成功:** %s个平台  \n  "+
                        "**启动失败:** %s个平台  \n  "+
                        "**启动时间:** %s  \n  "+
                        "%s  \n  "+
                        "[查看详情](%s)  \n  ",
                planEntity.getName(),
                orderEntity.getNo(),
                orderFlashEntity.getFlash(),
                allPlatformList.size(),
                allPlatformList.size() - failedList.size(),
                failedList.size(),
                TimeUtils.formatTimestamp(planEntity.getStartAt()),
                failedStr,
                url
        );
        List<String> userIdList = Collections.singletonList(planEntity.getStartBy());
        String title =  "Plan启动完成！" + TimeUtils.formatTimestamp(System.currentTimeMillis()) ;

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        userIdList,
                        title,
                        text
                )
        );


    }

    public HandleResp<String> sendToConversation(ShareInfoParams params, OAuthUserDetail userDetail) {

        return dingTalkFeignClient.sendToConversation(
                params.getCid(),
                userDetail.getUid(),

                ConversationMessageBuilder.buildText(Collections.emptyList(),
                        pathUtils.buildDingtalkAppUrl(params.getLinkUrl())  )
        );
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderFlashInteractiveModel {
        private String flash ;
        private int num ;
        private int planNum ;
        private long startAt ;
        private String spendTime ;
        private String status ;
        private String testProgress ;
        private String autoLoc;
        private String testPerson;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderInteractiveModel {

        private String title ;
        private String version ;
        private String versionType ;
        private String builder ;
        private String buildAt ;
        private String flash ;
        private String product ;
        private String detailUrl ;

        @JsonIgnore
        private List<OrderFlashInteractiveModel> flashModelList ;

        public Map<String,String> buildCardDataParamMap(ObjectMapper mapper) {

            Map<String,String> paramMap = mapper.convertValue(this, new TypeReference<>() {
            });

            if(flashModelList != null && !flashModelList.isEmpty()) {
                HashMap<String,Object> sysFullJsonObj = new HashMap<>();
                sysFullJsonObj.put("flashBatch", flashModelList);
                try {
                    paramMap.put("sys_full_json_obj", mapper.writeValueAsString(sysFullJsonObj));
                } catch (JsonProcessingException e) {
                    log.error("buildCardDataParamMap error with:",e);
                }
            }

            return paramMap ;
        }
    }

    /**
     * 获取Flash的钉钉卡片信息
     * @param flashEntity flash信息
     * @return 卡片模板
     */
    public OrderFlashInteractiveModel fetchFlashInteractiveInfo(OrderFlashEntity flashEntity) {
        List<OrderPlanEntity> allPlanList = planService.fetchAllPlanByFlash(flashEntity.getOrderId(), flashEntity.getFlash());

        List<OrderPlanEntity> testedPlanList =allPlanList.stream().filter(planEntity ->
             planEntity.getStatus().greaterThan(OrderPlanEntity.Status.RUNNING)).collect(Collectors.toList());

        String spendTime = null ;
        if (flashEntity.getTestEndAt() != null && flashEntity.getTestStartAt() != null) {
            spendTime = getDistanceTime(flashEntity.getTestStartAt(), flashEntity.getTestEndAt());
        }

        return OrderFlashInteractiveModel.builder()
                .flash(flashEntity.getFlash())
                .num(flashEntity.getNum())
                .planNum(allPlanList.size())
                .status(flashEntity.getStatus().getDesc())
                .startAt(Optional.ofNullable(flashEntity.getTestStartAt()).orElse(0L))
                .spendTime(spendTime)
                .testProgress(testedPlanList.size() + "/" + allPlanList.size())
                .autoLoc(flashEntity.getAutoLoc() ? "继承样片编号" : "样片重新编号")
                .testPerson(flashEntity.getTestPerson())
                .build();
    }


    /**
     * 获取工单批次的钉钉卡片通知信息
     * @param orderEntity 工单信息
     * @param title 标题
     * @param flashEntity  flash信息
     * @return
     */
    public OrderInteractiveModel fetchOrderInteractiveInfo(WorkOrderEntity orderEntity, String title, OrderFlashEntity flashEntity ){

        String detailUrl = pathUtils.getFlashDingUrl(orderEntity, flashEntity.getFlash());

        List<OrderFlashInteractiveModel> flashInteractiveModelList = Arrays.asList(fetchFlashInteractiveInfo(flashEntity));

        return OrderInteractiveModel.builder()
                .title(title)
                .version(getVersion(orderEntity.getFullVersion(), orderEntity.getSubProduct(), orderEntity.getFeature()))
                .versionType(orderEntity.getVersionType())
                .builder(orderEntity.getBuildPerson())
                .buildAt(TimeUtils.formatNullableTimestamp(orderEntity.getBuildStartAt()).orElse(null))
                .flash(orderEntity.getFlash())
                .product(orderEntity.getProduct()+" - "+orderEntity.getSubProduct())
                .detailUrl(detailUrl)
                .flashModelList(flashInteractiveModelList)
                .build();
    }

    public HashMap<String,String> getOrderParam(String title, WorkOrderEntity orderEntity){
        String url = pathUtils.getFlashDingUrl(orderEntity,null);
        HashMap<String,String> cardDataParamMap = new HashMap<>();
        cardDataParamMap.put("title", title);
        if (orderEntity.getFeature() == WorkOrderEntity.FEATURE_RIVAL) {
            cardDataParamMap.put("version", "竞品测试");
        } else {
            cardDataParamMap.put("version", getVersion(orderEntity.getFullVersion(), orderEntity.getSubProduct(), orderEntity.getFeature()));
        }

        cardDataParamMap.put("versionType", orderEntity.getVersionType());
        cardDataParamMap.put("builder", orderEntity.getBuildPerson());
        cardDataParamMap.put("buildAt", TimeUtils.formatTimestamp(orderEntity.getBuildStartAt()));
        cardDataParamMap.put("flash", orderEntity.getFlash());
        cardDataParamMap.put("product", orderEntity.getProduct() + "-" + orderEntity.getSubProduct());
        cardDataParamMap.put("detailUrl", url);
        return cardDataParamMap;
    }

    /**
     * 导入工单，发送一个卡片信息
     * @param title 通知标题
     * @param orderEntity 工单信息
     * @return 卡片信息id
     */
    public String sendOrderNotification(String title, WorkOrderEntity orderEntity) {
        // 发送通知给 需要确认Flash的通知的 权限的 人员。
        List<String> userIdList = roleService.getUserListByProductAndPermission(orderEntity.getSubProduct(), Permission.CONFIRM_FLASH_NOTIFICATION);

        // 通知构建人工单已导入成功
        String subProduct = orderEntity.getSubProduct();
        // todo 暂时不给SATA发送通知
        if(!"SATA".equalsIgnoreCase(subProduct)){
            userIdList.add(orderEntity.getBuildBy());
        }

        HashMap<String,String> cardDataParamMap = getOrderParam(title, orderEntity);
        log.info("cardDataParamMap: {}", cardDataParamMap);

        UUID uuid = UUID.randomUUID();

        log.info("send ImportOrderNotification to userIdList {}", userIdList);
        sendDingInteractiveMsg(userIdList, uuid.toString(), "", ORDER_CARD_TEMPLATE_ID, cardDataParamMap);

        return uuid.toString();
    }

    /**
     * 更新工单消息卡片信息
     * @param title 通知标题
     * @param orderEntity 工单信息
     */
    public void updateOrderNotification(String title, WorkOrderEntity orderEntity) {
        HashMap<String,String> cardDataParamMap = getOrderParam(title, orderEntity);
        updateDingInteractiveMsg(orderEntity.getOutTrackId(), ORDER_CARD_TEMPLATE_ID, cardDataParamMap);
    }

    /**
     * 工单确认Flash后，发送一条 Flash 通知卡片（此时为新建）。
     * @param title 标题
     * @param orderEntity 工单实体
     * @param flashEntity Flash信息
     */
    @SneakyThrows
    public void sendFlashInteractiveMsg(
            String title,
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity
    ) {
        UUID uuid = UUID.randomUUID();
        // 保存flash通知的uuid
        FlashNoticeEntity flashNoticeEntity = new FlashNoticeEntity();
        flashNoticeEntity.setOrderId(orderEntity.getId());
        flashNoticeEntity.setFlash(flashEntity.getFlash());
        flashNoticeEntity.setOutTrackId(uuid.toString());
        flashNoticeRepository.save(flashNoticeEntity);
        // 获取该Flash的测试负责人与测试干系人列表
        List<String> userIdList = new ArrayList<>(flashEntity.getTestPersonIds());

        Map<String, String> cardDataParamMap = fetchOrderInteractiveInfo(orderEntity, title, flashEntity).buildCardDataParamMap(new ObjectMapper());
        log.info("send ConfirmFlashNotification to user {}", userIdList);
        sendDingInteractiveMsg(userIdList, uuid.toString(), "", FLASH_CARD_TEMPLATE_ID, cardDataParamMap);
    }

    /**
     * 确认flash后，所有有关Flash信息变更的卡片都通过更新原有的消息卡片
     * @param title 标题
     * @param orderEntity 工单信息
     * @param flashEntity flash批次信息
     */
    @SneakyThrows
    public void updateFlashInteractiveMsg(
            String title,
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity
    ) {

        Map<String,String> cardDataParamMap = fetchOrderInteractiveInfo(orderEntity, title, flashEntity).buildCardDataParamMap(new ObjectMapper());
        List<String> uuidList = flashNoticeRepository.findUUIDByOrderIdAndFlash(orderEntity.getId(), flashEntity.getFlash());
        String uuid = uuidList.stream().findFirst().orElse("");

        // 此处判断一下如果没有该项数据，则新增一条卡片消息
        if("".equals(uuid)){
            sendFlashInteractiveMsg("你有新的Flash批次待测试", orderEntity, flashEntity);
        }else{
            log.info("update Flash: {}，uuid: {}", flashEntity.getFlash(), uuid);
            updateDingInteractiveMsg(uuid, FLASH_CARD_TEMPLATE_ID, cardDataParamMap);
        }
    }

    /**
     * 新建一个通知，发送交互卡片。
     * @param userIdList 发送的用户ID列表
     * @param outTrackId 卡片id
     * @param callbackRouteKey 回调信息
     * @param cardTemplateId 回消息卡片id
     * @param cardDataParamMap 钉钉卡片消息
     */
    @SneakyThrows
    public void sendDingInteractiveMsg(
            List<String> userIdList,
            String outTrackId,
            String callbackRouteKey,
            String cardTemplateId,
            Map<String,String> cardDataParamMap
    ) {
        InteractiveCardParams params = InteractiveCardParams.builder()
                .containName(Arrays.asList(environment.getActiveProfiles()).contains("prod") ? "iSee" : "iSeeTest")
                .receiverUserIdList(userIdList)
                .cardTemplateId(cardTemplateId)
                .outTrackId(outTrackId)
                .callbackRouteKey(callbackRouteKey)
                .cardDataParamMap(cardDataParamMap)
                .conversationType(0)
                .userIdType(1)
                .build();
        log.info("sendDingInteractiveMsg: {}", params);
        dingTalkFeignClient.sendInteractiveMsg(params);

    }

    /**
     * 新建一个通知，发送交互卡片。
     * @param uuid 卡片id
     * @param cardTemplateId 回消息卡片id
     * @param cardDataParamMap 钉钉卡片消息
     */
    @SneakyThrows
    public void updateDingInteractiveMsg(
            String uuid,
            String cardTemplateId,
            Map<String,String> cardDataParamMap
    ) {

        InteractiveCardParams params = InteractiveCardParams.builder()
                .containName(Arrays.asList(environment.getActiveProfiles()).contains("prod") ? "iSee" : "iSeeTest")
                .outTrackId(uuid)
                .cardTemplateId(cardTemplateId)
                .cardDataParamMap(cardDataParamMap)
                .userIdType(1)
                .build() ;
        log.info("updateDingInteractiveMsg: {}", params);
        dingTalkFeignClient.updateInteractiveMsg(params);

    }

    /**
     * 在工作通知中，发送fail分析开始通知给研发。
     * @param orderEntity 工单
     * @param userIdList 研发人员
     */
    public void sendFailAnalysisNotification(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, List<String> userIdList) {
        log.info("sendFailAnalysisNotification -- to {} ", userIdList);
        String url = pathUtils.getFlashDingUrl(orderEntity, flashEntity.getFlash());
        String text = String.format(
                "# %s 待完成Fail分析！  \n  " +
                        "**测试版本:** %s  \n  "+
                        "**Flash型号:** %s  \n  "+
                        "**版本类型:** %s  \n  "+
                        "**Flash批次:** %s   \n   "+
                        "**版本发布时间:** %s  \n  "+
                        "**测试开始时间:** %s  \n  "+
                        "**测试完成时间:** %s  \n  "+
                        "[查看详情](%s)  \n  ",
                flashEntity.getFlash(),
                getVersion(orderEntity.getFullVersion(), orderEntity.getSubProduct(), orderEntity.getFeature()),
                orderEntity.getFlash(),
                orderEntity.getVersionType(),
                flashEntity.getFlash(),
                TimeUtils.formatTimestamp(orderEntity.getBuildEndAt()),
                TimeUtils.formatTimestamp(flashEntity.getTestStartAt()),
                TimeUtils.formatTimestamp(flashEntity.getTestEndAt()),
                url
        );
        String title =  "你有Flash需要进行Fail分析！" + TimeUtils.formatTimestamp(System.currentTimeMillis());

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        userIdList,
                        title,
                        text
                )
        );

        //更新工单的 互动卡片 状态 至Fail 分析中
        updateFlashInteractiveMsg("Fail分析中", orderEntity, flashEntity);
    }

    /**
     * 在工作通知中，发送fail分析完成通知给研发。
     * @param orderEntity 工单
     * @param flashEntity flash信息
     */
    public void sendFailAnalysisFinishNotification(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {
        log.info("sendFailAnalysisFinishNotification -- to {} ", Collections.singletonList(flashEntity.getTestBy()));
        String url = pathUtils.getFlashDingUrl(orderEntity, flashEntity.getFlash());
        String text = String.format(
                "# %s 已完成Fail分析！  \n  " +
                        "**测试版本:** %s  \n  "+
                        "**版本类型:** %s  \n  "+
                        "**Flash批次:** %s   \n   "+
                        "**版本发布时间:** %s  \n  "+
                        "**发起Fail分析时间:** %s  \n  "+
                        "**完成Fail分析时间:** %s  \n  "+
                        "[查看详情](%s)  \n  ",
                flashEntity.getFlash(),
                getVersion(orderEntity.getFullVersion(), orderEntity.getSubProduct(), orderEntity.getFeature()),
                orderEntity.getVersionType(),
                flashEntity.getFlash(),
                TimeUtils.formatTimestamp(orderEntity.getBuildEndAt()),
                TimeUtils.formatTimestamp(flashEntity.getStartFailAt()),
                TimeUtils.formatTimestamp(System.currentTimeMillis()),
                url
        );
        String title =  "你有Flash已完成Fail分析！" + TimeUtils.formatTimestamp(System.currentTimeMillis());

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        Collections.singletonList(flashEntity.getTestBy()),
                        title,
                        text
                )
        );

        //更新工单的 互动卡片 状态至 等待发起Review
        updateFlashInteractiveMsg("等待发起Review", orderEntity, flashEntity);
    }

    /**
     * 在工作通知中，发送需要上传报告的通知给测试负责人
     * @param orderEntity 工单
     * @param flashEntity flash 批次
     */
    public void sendNeedUploadReportNotification(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {
        // 发送通知给 Flash批次的 测试负责人。
        List<String> userIdList = Collections.singletonList(flashEntity.getTestBy());

        String url = pathUtils.getFlashDingUrl(orderEntity,flashEntity.getFlash());
        String text = String.format(
                "# %s 待上传测试报告！  \n  " +
                        "**测试版本:** %s  \n  "+
                        "**版本类型:** %s  \n  "+
                        "**Flash批次:** %s   \n   "+
                        "**测试负责人:** %s   \n   "+
                        "**版本发布时间:** %s  \n  "+
                        "**测试开始时间:** %s  \n  "+
                        "**测试完成时间:** %s  \n  "+
                        "[查看详情](%s)  \n  ",
                flashEntity.getFlash(),
                getVersion(orderEntity.getFullVersion(), orderEntity.getSubProduct(), orderEntity.getFeature()),
                orderEntity.getVersionType(),
                flashEntity.getFlash(),
                flashEntity.getTestPerson(),
                TimeUtils.formatTimestamp(orderEntity.getBuildEndAt()),
                TimeUtils.formatTimestamp(flashEntity.getTestStartAt()),
                TimeUtils.formatTimestamp(flashEntity.getTestEndAt()),
                url
        );
        String title =  "Flash批次需要上传测试报告！" + TimeUtils.formatTimestamp(System.currentTimeMillis());

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        userIdList,
                        title,
                        text
                )
        );
    }

    /**
     * 发送 plan 测试结束状态 给到测试人员
     * @param orderEntity 所属工单
     * @param flashEntity 所属Flash
     */
    public void sendPlanTestEndNotification(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity
    ) {
        log.info("测试单: {} plan: {}测试结束", flashEntity.getOrderFlashNo(), planEntity.getName());
        // 仅需要发送给测试人员。
        String url = pathUtils.getPlanPageDingUrl(orderEntity, flashEntity.getFlash(), planEntity);

        List<PlanDeviceEntity> allDevices = deviceService.findDevicesByPlanId(planEntity.getId());
        long successCount = allDevices.stream().filter(p -> p.getStatus() == PlanDeviceEntity.Status.FINISHED_SUCCESS).count();
        long failedCount = allDevices.stream().filter(p -> p.getStatus() == PlanDeviceEntity.Status.FINISHED_FAILED).count();


        String text = String.format(
                "# %s 已经测试完成！  \n" +
                        "**设备总数:** %s   \n  "+
                        "**成功/失败:** %s   \n  "+
                        "**测试版本:** %s   \n  "+
                        "**Flash批次:** %s   \n   "+
                        "**测试样片数量：** %s   \n   "+
                        "**测试负责人:** %s   \n   "+
                        "**开始测试人:** %s   \n   "+
                        "**测试开始时间:** %s    \n   "+
                        "**测试结束时间:** %s    \n   "+
                        "[查看详情](%s)    \n   ",
                planEntity.getName(),
                allDevices.size(),
                successCount + "/" + failedCount,
                getVersion(orderEntity.getFullVersion(), orderEntity.getSubProduct(), orderEntity.getFeature()),
                flashEntity.getFlash(),
                planEntity.getTestNum(),
                planEntity.getBelongToPerson(),
                planEntity.getStartPerson(),
                TimeUtils.formatTimestamp(planEntity.getStartAt()),
                TimeUtils.formatTimestamp(System.currentTimeMillis()),
                url
        );

        String title =  "Plan已经测试完成！" + TimeUtils.formatTimestamp(System.currentTimeMillis());

        // 此处需要通知确认环境、测试负责人、开始测试人员的集合
        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        planEntity.getTestEndNoticePerson(),
                        title,
                        text
                )
        );
    }

    /**
     * Plan 分配设备完成时，发送准备确认设备的通知（plan测试负责人）
     * @param orderEntity 工单
     * @param flashEntity  flash批次信息
     * @param planEntity plan信息
     */
    public void sendPlanReadyNotification(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, OrderPlanEntity planEntity) {
        log.info("测试单: {} plan: {}已分配完成", flashEntity.getOrderFlashNo(), planEntity.getName());
        String flashName = flashEntity.getFlash() ;
        String url = pathUtils.getPlanPageDingUrl(orderEntity, flashName, planEntity);

        String text = String.format(
                "# %s 分配完成!  \n  " +
                        "**测试版本：** %s    \n   "+
                        "**版本发布人：** %s    \n  "+
                        "**Flash批次：** %s   \n   "+
                        "**测试样片数量：** %s   \n   "+
                        "**资源分配完成时间：** %s    \n   "+
                        "[查看详情](%s)    \n   ",
                planEntity.getName(),
                getVersion(orderEntity.getFullVersion(), orderEntity.getSubProduct(), orderEntity.getFeature()),
                orderEntity.getBuildPerson(),
                flashName,
                planEntity.getTestNum(),
                TimeUtils.formatTimestamp(System.currentTimeMillis()),
                url
        );
        // 获取plan测试负责人的钉钉ID
        List<String> userIdList = List.of(flashEntity.getTestBy());

        String title = "你有Plan分配设备完成！" + TimeUtils.formatTimestamp(System.currentTimeMillis());

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        userIdList,
                        title,
                        text
                )
        );
    }

    /**
     * 发送超时通知
     *
     * @param orderEntity 工单实体
     * @param users      用户列表
     * @param flash      Flash 批次 , 可以为空, 如果为空时,URL 不带 flash 参数
     * @param phase      阶段 (中文描述)
     * @param expectTime  预期花费时间
     * @param timeDuration    超出时长
     *
     */
    public void sendTimeoutNotification(
            WorkOrderEntity orderEntity,
            List<String> users,
            String flash,
            String phase,
            long expectTime,
            long timeDuration
    ) {
        String product = orderEntity.getProduct();
        String subProduct = orderEntity.getSubProduct();
        long orderId = orderEntity.getId();
        String orderNo = orderEntity.getNo();

        String url = pathUtils.getFlashPageUrl(product, subProduct, orderId, flash);
        String text = String.format(
                "工单:[%s](%s) 在 **%s**阶段停滞 **%s**，请及时处理！"  ,
                orderNo,
                url,
                phase,
                getPastTimes(timeDuration)
        );

        String title = String.format("工单%s已经超过%s阶段最长处理时长%s,请及时处理！", orderId, phase, expectTime);

        dingTalkFeignClient.sendRobotConversationMsg(
                Arrays.asList(environment.getActiveProfiles()).contains("prod") ? "iSee" : "iSeeTest",
                ConversationMessageBuilder.buildMarkdown(
                users,
                title,
                text
        ));
    }

    /**
     * 系统出现预期之外的错误发送工作通知
     * @param msg 通知内同
     */
    public void sendErrorNotification(String msg) {
        StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
        String text = String.format(
                "错误信息: %s  \n  " +
                        "错误位置: %s  \n  " +
                        "错误方法: %s  \n  " +
                        "错误行数: %s  \n  ",
                msg,
                stackTraceElements[2].getClassName(),
                stackTraceElements[2].getMethodName(),
                stackTraceElements[2].getLineNumber()
        );
        String title = "eSee 出现了非预期错误,请及时检查！" + TimeUtils.formatTimestamp(System.currentTimeMillis());

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        Arrays.asList("0653290119972081", "16322748657306344", "121117211321199187", "2464164652960922"),
                        title,
                        text
                )
        );
    }

    /**
     * 系统设备回调异常通知
     * @param msg 通知内同
     */
    public void sendDeviceCallBackNotification(String msg) {
        StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
        String text = String.format(
                "错误信息: %s  \n  " +
                        "错误位置: %s  \n  " +
                        "错误方法: %s  \n  " +
                        "错误行数: %s  \n  ",
                msg,
                stackTraceElements[2].getClassName(),
                stackTraceElements[2].getMethodName(),
                stackTraceElements[2].getLineNumber()
        );
        String title = "eSee 设备回调出现异常！" + TimeUtils.formatTimestamp(System.currentTimeMillis());

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        Arrays.asList("16322748657306344"),
                        title,
                        text
                )
        );
    }

    /**
     * 分配过程中，设备分配异常通知
     * @param msg 通知内容
     */
    public void sendAssignPlanErrNotice(String msg) {
        String title = "Plan分配设备异常！" + TimeUtils.formatTimestamp(System.currentTimeMillis());

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        Collections.singletonList("16322748657306344"),
                        title,
                        msg
                )
        );
    }

    /**
     * 获取工单对应版本信息
     * @param fullVersion 版本号
     * @param productName 产品
     * @param feature 版本类型
     * @return
     */
    public String getVersion(String fullVersion, String productName, int feature) {
        if (feature == WorkOrderEntity.FEATURE_RIVAL) {
            return "竞品版本";
        }
        if (productName.equalsIgnoreCase(SUB_PRODUCT_EMMC)
                || productName.equalsIgnoreCase(SUB_PRODUCT_UFS)
                || productName.equalsIgnoreCase(SUB_PRODUCT_IND_EMMC)
        ) {
            final String[] split = fullVersion.split("_V");
            return String.format("V%s", split[split.length - 1]);
        } else {
            return TextUtils.parseMpToolVersion(fullVersion);
        }
    }

    /**
     * 滞留时间获取
     * @param timeDuration 滞留多少小时
     * @return 滞留时间
     */
    public static String getPastTimes(long timeDuration) {
        long months = timeDuration / 24 / 30;
        long days = (timeDuration % (24 * 30)) / 24;
        long hours = timeDuration % 24;
        if (months > 0) {
            return months + " 个月";
        }
        return days + " 天 " + hours + " 小时";
    }


}
