package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "TerminalCaseStatusChangeParams", description = "plan下case状态变更")
public class TerminalCaseStatusChangeParams {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "TerminalCaseStatusChangeParams-CaseInfo", description = "成功或者失败的case信息。")
    public static class CaseInfo {

        @NotBlank(message = "case名不能为空")
        @ApiModelProperty(value = "case名称", required = true)
        private String caseName;

        @JsonAlias("case_step")
        @Min(value = 0, message = "case在Plan中的索引不能小于0")
        @ApiModelProperty(value = "Case在Plan中的索引", required = true)
        private Integer caseStep;

        @ApiModelProperty(value = "设备IP", required = true)
        private String ip;

        // 过渡阶段，暂定为false
        @ApiModelProperty(value = "设备MAC地址")
        private String mac;

        @ApiModelProperty(value = "设备编号", required = true)
        private String no;

        @ApiModelProperty(value = "测试终端平台序列号")
        private String number;

        @ApiModelProperty(value = "case测试信息", required = true)
        private String msg;

        @ApiModelProperty(value = "错误类型")
        @JsonAlias("error_type")
        private String errorType;

    }

    @NotBlank(message = "工单号不能为空")
    @ApiModelProperty(value = "工单号", required = true)
    private String orderNo;

    @NotBlank(message = "Plan名不能为空")
    @ApiModelProperty(value = "计划名称", required = true)
    private String plan;

    @NotBlank(message = "状态不能为空！")
    @ApiModelProperty(value = "plan状态", required = true)
    private String status;


    @NotEmpty(message = "设备列表不能为空")
    @ApiModelProperty(value = "设备列表", required = true)
    private List<TerminalCaseStatusChangeParams.CaseInfo> deviceList;
}
