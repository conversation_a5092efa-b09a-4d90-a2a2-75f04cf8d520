{"@timestamp":"2025-07-24T16:12:04+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6394","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:12:04.376+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6638, orderId=6394, flash=WK-6285ENAB-0T28-B_128GB, orderFlashNo=YS6285##MP#########0203##0T26-DDR_TLC250422_Alpha_WK-6285ENAB-0T28-B_128GB, num=94, leftNum=94)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:04.394+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:04.395+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:04.395+08:00","@version":"1","message":"[6394] - [WK-6285ENAB-0T28-B_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.836+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6394","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:15:02.838+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6638, orderId=6394, flash=WK-6285ENAB-0T28-B_128GB, orderFlashNo=YS6285##MP#########0203##0T26-DDR_TLC250422_Alpha_WK-6285ENAB-0T28-B_128GB, num=94, leftNum=94)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.855+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.856+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.856+08:00","@version":"1","message":"[6394] - [WK-6285ENAB-0T28-B_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.957+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6394","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.959+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6638, orderId=6394, flash=WK-6285ENAB-0T28-B_128GB, orderFlashNo=YS6285##MP#########0203##0T26-DDR_TLC250422_Alpha_WK-6285ENAB-0T28-B_128GB, num=94, leftNum=94)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.977+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.977+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.977+08:00","@version":"1","message":"[6394] - [WK-6285ENAB-0T28-B_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.831+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6394","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:21:02.832+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6638, orderId=6394, flash=WK-6285ENAB-0T28-B_128GB, orderFlashNo=YS6285##MP#########0203##0T26-DDR_TLC250422_Alpha_WK-6285ENAB-0T28-B_128GB, num=94, leftNum=94)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.848+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.848+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.848+08:00","@version":"1","message":"[6394] - [WK-6285ENAB-0T28-B_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.843+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6394","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:27:02.844+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6638, orderId=6394, flash=WK-6285ENAB-0T28-B_128GB, orderFlashNo=YS6285##MP#########0203##0T26-DDR_TLC250422_Alpha_WK-6285ENAB-0T28-B_128GB, num=94, leftNum=94)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.862+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.863+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.863+08:00","@version":"1","message":"[6394] - [WK-6285ENAB-0T28-B_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.57+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6394","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.588+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6638, orderId=6394, flash=WK-6285ENAB-0T28-B_128GB, orderFlashNo=YS6285##MP#########0203##0T26-DDR_TLC250422_Alpha_WK-6285ENAB-0T28-B_128GB, num=94, leftNum=94)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.631+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113859, orderId=6394, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6394","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.632+08:00","@version":"1","message":"[6394] - [WK-6285ENAB-0T28-B_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6394","traceType":"分配设备"}
