package com.yeestor.work_order.service.device;


import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.device.DeviceTestHistoryEntity;
import com.yeestor.work_order.model.rms.AttrModel;
import com.yeestor.work_order.model.rms.DeviceModel;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.service.plan.PlanAssignService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.plan.TestHistoryService;
import com.yeestor.work_order.service.product.ProductContext;
import com.yeestor.work_order.utils.DingTalkUtils;
import com.yeestor.work_order.utils.LogUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MarkerFactory;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuple4;
import reactor.util.function.Tuples;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yeestor.work_order.utils.Const.*;

/**
 * DeviceService 是用来进行设备的资源分配的
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QueueService {

    private final OrderFlashRepository orderFlashRepository;

    private final PlanService planService ;

    private final OrderService orderService ;

    private final DeviceService deviceService ;

    private final DataChangeListener dataChangeListener ;

    private final ProductContext productContext ;
    private final TestHistoryService testHistoryService;
    private final PlanAssignService planAssignService;
    private final DingTalkUtils dingTalkUtils;

    private static final String TEST_SD_H2_RACK = "GE3_1";     // SD 中H2 Plan测试的固定机柜
    private static final List<String> TEST_SD_SPEC_LIST = Arrays.asList(TEST_SD_H2_RACK, "GE2_3");  // SD 常规测试Plan时，不参与分配设备的机柜

    private static final String TEST_U3_TCPIP_RACK = "GE3_2";      // U3 中 TCPIP掉电 属性的额Plan测试所在机柜
    private static final String TEST_USB_PUBLIC_RACK  = "GE4_1";   // U2和U3的公共机柜
    private static final List<String> TEST_U3_RACK_LIST = Arrays.asList("GE4_3", "GE4_2", "GE7_1", "GE7_2");  // U3 主用机柜
    private static final List<String> TEST_U2_RACK_LIST = Arrays.asList("GE5_1", "GE5_2");  // U2 主用机柜

    public void assignDevicesByProduct(String product){
        // 拿到最高优先级的工单。
        List<WorkOrderEntity> orderEntityList = orderService.findTop10OrderList(product);
        log.debug("[{}] start check order's plan . find {} orders", product,orderEntityList.size());
        // 循环检查这些工单是否满足资源分配条件
        orderEntityList.forEach(orderEntity -> {
            final long orderId = orderEntity.getId();
            LogUtils.setOrderTracePoint(orderId, "分配设备");
            log.info(MarkerFactory.getMarker("QueueService"),"start check order plan's devices ----------- ;");
            // 拿到u2 下的设备列表

            // 找到工单下中所有Flash批次，然后按照优先级排序。
            List<OrderFlashEntity> flashEntityList = orderFlashRepository.findAllByOrderIdAndNotDisabled(orderEntity.getId());
            log.info("find {} flash: {}",flashEntityList.size(),flashEntityList);

            // 工单的每次运行都是以一个flash批次为单位的。

            for (OrderFlashEntity flashEntity : flashEntityList) {
                productContext.assignDevices(orderEntity, flashEntity);
            }

        });
        log.debug("[{}] end check order plan's devices ----------- ;",product);
        LogUtils.clearTracePoint();

    }

    /**
     * 分配设备方法
     * @param orderEntity 工单信息
     * @param flashEntity flash信息
     * @param flashName flash名称
     * @param planEntityList plan列表信息
     * @param runDevices plan列表下的设备
     */
    public void saveData(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            String flashName,
            List<OrderPlanEntity> planEntityList,
            Map<String, List<DeviceModel>> runDevices
    ) {
        List<OrderPlanEntity> canTestPlanList = planEntityList.stream()
                .filter(planEntity -> runDevices.containsKey(planEntity.getName()))
                .collect(Collectors.toList());

        // Plan 预分配设备，并发送通知等
        planService.assignDevicesToPlan(orderEntity, flashEntity, runDevices, canTestPlanList);

        canTestPlanList.forEach(p ->
                dataChangeListener.onDataChange(
                        DataChangeEvent.builder()
                                .type(DataChangeEvent.Type.PLAN_READY)
                                .orderId(p.getOrderId())
                                .flash(flashName)
                                .planId(p.getId())
                                .build()
                )
        );

    }


    /**
     * 获取plan 运行所需的设备列表。
     * @param planEntity 工单下的plan
     * @param runnableDeviceList 需要过滤的设备
     * @param testNum 样片数量。
     * @return 支持运行的设备列表
     */
    public List<DeviceModel> getPlanRunnableDevices(OrderPlanEntity planEntity, List<DeviceModel> runnableDeviceList, int testNum) {
        log.debug("[{}] getPlanRunnableDevices [{}] testNum: {} ", planEntity.getOrderId(), planEntity.getName(), testNum);

        List<String> attrs = Arrays.asList(planEntity.getAttrs().split(";"));
        log.info("[{}] runnableDeviceList: {}", planEntity.getOrderId(), runnableDeviceList);
        // 尝试在单一机柜对Plan进行分配
        List<DeviceModel> planDevices = findMatchingGroupDevices(runnableDeviceList, attrs, testNum);
        if (planDevices.isEmpty()) {
            // 如果一个机柜里的机器都不能满足的话，则考虑跨机柜
            planDevices = findMinDevices(attrs, testNum, runnableDeviceList);
        }
        log.debug("[{}] find {} planDevices! ", planEntity.getOrderId(), planDevices.size());
        return planDevices;

    }

    /**
     * 1、现在已分配的机柜范围内找设备，找到了直接返回，没有找到执行下一步
     * 2、扩大范围，在所有已知的空闲设备中找到匹配的设备
     * @param planName 测试plan名称
     * @param rackList 机柜信息
     * @param runnableDeviceList 空闲设备列表
     * @param attrs plan属性
     * @param testNum plan需要测试样片的数量
     * @return Plan匹配设备
     */
    public List<DeviceModel> getCanRunnableDevicesInRack(String planName, List<Map.Entry<String, Long>> rackList, List<DeviceModel> runnableDeviceList, List<String> attrs, int testNum) {
        log.info("rackList: {}", rackList);
        for (Map.Entry<String, Long> entry : rackList) {
            String rackName = entry.getKey();   // 机柜名称
            // 当前机柜下的空闲设备
            List<DeviceModel> rackCanRunDevice = runnableDeviceList.stream()
                    .filter(dev -> dev.getPosition().startsWith(rackName))
                    .collect(Collectors.toList());
            // 尝试在单一机柜对Plan进行分配(获取连号设备)
            List<DeviceModel> planDevices = getAdjacentDeviceInCounter(rackCanRunDevice, testNum, attrs);
            if(planDevices.size() > 0){
                log.info("{}已分配的机柜范围内：在机柜{}下找到空闲的可使用设备：{}", planName, rackName, planDevices.stream().map(DeviceModel::getPosition).collect(Collectors.toList()));
                return planDevices;
            }
        }
        // 尝试在未使用过的机柜中获取连号设备
        // FIXME 由于此处是所有机柜的设备，所以还是可能存在分配到不是连号的设备的
        List<DeviceModel> planDevices = getAdjacentDeviceInCounter(runnableDeviceList, testNum, attrs);
        if(planDevices.size() > 0){
            log.info("{}已分配，找到空闲的可使用设备：{}", planName,planDevices.stream().map(DeviceModel::getPosition).collect(Collectors.toList()));
            return planDevices;
        }
        // 如果一个机柜里的机器都不能满足的话，则考虑跨机柜
        return findMinDevices(attrs, testNum, runnableDeviceList);
    }

    /**
     * 获取可支持Plan进行分配的设备信息
     * @param orderEntity 工单实体类
     * @param planEntity plan实体累
     * @param filterDevices 此轮分配已用的设备
     * @param availableDevices rms中未被占用的设备
     * @return 预分配给Plan的设备信息
     */
    public List<DeviceModel> fetchPlanRunnableDevices(
            WorkOrderEntity orderEntity,
            OrderPlanEntity planEntity,
            List<DeviceModel> filterDevices,
            List<DeviceModel> availableDevices
    ){
        long orderId = orderEntity.getId();
        String subProduct = orderEntity.getSubProduct();
        String planName = planEntity.getName();

        log.info("subProduct {} flashName {} plan [{}] attrs {} belongTo {} is ready for allocation",
                subProduct,
                planEntity.getFlash(),
                planName,
                planEntity.getAttrs(),
                planEntity.getBelongToPerson());
        // 先检索出满足Plan属性的所有测试设备
        List<DeviceModel> deviceList = deviceService.matchPlanAttrsDevices(orderEntity, planEntity, availableDevices, false);

        // 过滤掉此轮已分配给其他Plan的设备
        List<DeviceModel> runnableDeviceList = deviceList.stream()
                .filter(deviceModel -> filterDevices.stream().noneMatch(deviceModel1 -> Objects.equals(deviceModel1.getMac(), deviceModel.getMac())))
                .sorted(Comparator.comparing(DeviceModel::getPosition))
                .collect(Collectors.toList());

        // 过滤掉已分配的设备之后，还剩余处于空闲状态的设备
        log.info("fetchPlanRunnableDevices runnableDeviceList: {}", runnableDeviceList.stream()
                .map(DeviceModel::getPcNo)
                .collect(Collectors.toList()));

        // 如果当前Plan属于eMMC或者PCIe产品，则先进行如下分配操作
        if (SUB_PRODUCT_PCIE.equals(subProduct)) {
            log.info("PCIe {}不是测试所有样片的Plan", planName);
            return findDevicesToSSDPlan(orderId, subProduct, planEntity, runnableDeviceList);
        }
        else if(SUB_PRODUCT_SATA.equals(subProduct) || SUB_PRODUCT_IND_SATA.equals(subProduct)){
            log.info("SATA {}不是测试所有样片的Plan", planName);
            List<DeviceModel> canTestList = findDevicesToSSDPlan(orderId, subProduct, planEntity, runnableDeviceList);

            if(canTestList.isEmpty()){
                log.info("不包含特殊属性的设备中未到满足测试的设备！");
                // 如果不包含特殊属性的设备中未到满足测试的设备，则扩大搜索范围【从包含特殊属性的设备中查找】
                deviceList = deviceService.matchPlanAttrsDevices(orderEntity, planEntity, availableDevices, true);
                runnableDeviceList = deviceList.stream()
                        .filter(deviceModel -> filterDevices.stream().noneMatch(deviceModel1 -> Objects.equals(deviceModel1.getMac(), deviceModel.getMac())))
                        .sorted(Comparator.comparing(DeviceModel::getPosition))
                        .collect(Collectors.toList());
                canTestList = findDevicesToSSDPlan(orderId, subProduct, planEntity, runnableDeviceList);
            }

            return canTestList;
        }
        else if(SUB_PRODUCT_EMMC.equals(subProduct) || SUB_PRODUCT_IND_EMMC.equals(subProduct)){
            // 获取公司信息
            String location = dingTalkUtils.getLocationByDingId(planEntity.getBelongTo());
            log.info("location: {}", location);

            // 筛选出对应公司下的所有满足测试的设备
            List<DeviceModel> locationDeviceList = deviceList.stream().filter(item -> item.getRegion().equalsIgnoreCase(location)).collect(Collectors.toList());
            // 过滤掉此轮已分配给其他Plan的设备
            List<DeviceModel> canTestDeviceList = locationDeviceList.stream()
                    .filter(deviceModel -> filterDevices.stream().noneMatch(deviceModel1 -> Objects.equals(deviceModel1.getMac(), deviceModel.getMac())))
                    .sorted(Comparator.comparing(DeviceModel::getPosition))
                    .collect(Collectors.toList());
            return findDevicesToEMMcPlan(orderId, subProduct, planEntity, canTestDeviceList);
        }
        else if(SUB_PRODUCT_UFS.equals(subProduct)){
            List<DeviceModel> locationDeviceList = deviceList.stream().filter(item -> item.getRegion().equalsIgnoreCase("SZ")).collect(Collectors.toList());
            return findDevicesToEMMcPlan(orderId, subProduct, planEntity, locationDeviceList);
        }
        else if (SUB_PRODUCT_SD.equals(subProduct) || SUB_PRODUCT_IND_SD.equals(subProduct)) {
            // 工业级产品线SD产品工单暂时沿用消费级SD产品的分配策略
            return findDevicesToSDPlan(planEntity, runnableDeviceList);
        }
        else if(SUB_PRODUCT_U3.equals(subProduct)){
            return findDevicesToU3Plan(planEntity, runnableDeviceList);
        }
        else if(SUB_PRODUCT_U2.equals(subProduct)){
            return findDevicesToU2Plan(planEntity, runnableDeviceList);
        }
        else {
            return new ArrayList<>();
        }
    }


    /**
     * 将Device按照机柜的编号分组，并按照连号获取设备信息，其中机柜以position为标准（单一机柜完成预分配功能）
     * @param deviceModelList 空闲设备
     * @param attrs plan属性
     * @param num plan需要测试的样片数量
     * @return 设备信息
     */
    public List<DeviceModel> findMatchingGroupDevices(List<DeviceModel> deviceModelList, List<String> attrs , int num){
        // 以 DeviceModel position 的 - 最后的横杠前面的作为 分组的key
        Map<String, List<DeviceModel>> deviceMap = deviceModelList.stream()
                .filter(d-> d.getPosition().contains("_"))
                .collect(Collectors.groupingBy(d -> d.getPosition().substring(0, d.getPosition().lastIndexOf("_"))));

        List<Tuple4<String,Integer,List<DeviceModel>,Integer>> allDeviceTuples = new ArrayList<>() ;

        List<Tuple3<String,Integer,List<DeviceModel>>> deviceTuples = new ArrayList<>();
        deviceMap.forEach((k,ds) -> {
            int sum = ds.stream()
                    .map(DeviceModel::getScore)
                    .reduce(Integer::sum)
                    .orElse(0);
            int count = ds.stream().map(d -> attrs.stream().map(d::getTestNumByAttr).min(Comparator.comparingInt(Integer::intValue)).orElse(0))
                    .reduce(Integer::sum)
                    .orElse(0);
            allDeviceTuples.add(Tuples.of(k, sum/ds.size(),ds,count)) ;
            deviceTuples.add(Tuples.of(k, sum/ds.size(),ds)) ;
            log.info("group {} has {} devices , score is {} , testNum is {} ", k, ds.size(), sum/ds.size(), count);
        });
        deviceTuples.sort((a,b) -> b.getT2() - a.getT2());
        if (allDeviceTuples.isEmpty()) {
            return new ArrayList<>() ;
        }
        log.debug("group device: {}", deviceTuples);

        // 判断哪个机柜中的设备能够满足测试的样片数量
        for (Tuple3<String, Integer, List<DeviceModel>> deviceTuple : deviceTuples) {
            List<DeviceModel> devices = deviceTuple.getT3();
            List<DeviceModel> x = findMinDevices(attrs, num, devices);
            if (!x.isEmpty()) {
                return x;
            }

        }

        // 如果所有的设备加起来都不满足的话，则直接返回空列表
        int count = allDeviceTuples.stream().map(Tuple4::getT4).reduce(0, Integer::sum) ;
        if(count < num){
            return new ArrayList<>();
        }

        // 单一机柜无法满足的话，可以尝试一下 组合机柜的方式。
        // 将机柜进行排序,将数量最多的机柜放在最前面，然后依次大于等于 num - 最多数量的机柜的数量的机柜，如果有的话，就是从小到大排序后，再排小于num - 最多数量的机柜的数量的机柜 ；如果没有的话，就是从大到小排序。
        allDeviceTuples.sort((a,b) -> b.getT4() - a.getT4());
        Tuple4<String,Integer,List<DeviceModel>,Integer> maxDeviceTuple = allDeviceTuples.get(0) ;

        List<DeviceModel> devices = new ArrayList<>(maxDeviceTuple.getT3());

        AtomicInteger countSum = new AtomicInteger(maxDeviceTuple.getT4());



        do {
            //先假设接下来只需要一个机柜的数量，这样的话，只要在这个机柜里面去找对应的数量就行了。
            List<DeviceModel> compactDevice = allDeviceTuples.stream()
                    .filter(d -> maxDeviceTuple != d)
                    .filter(d -> d.getT4() >= (num - countSum.get()))
                    .min(Comparator.comparingInt(Tuple4::getT4))
                    .map(Tuple4::getT3)
                    .orElseGet(ArrayList::new);

            // 如果 找到的话，直接返回就行
            if (!compactDevice.isEmpty()) {
                devices.addAll(findMinDevices(attrs, num - countSum.get(), compactDevice));
                countSum.set(num);
                return devices;
            }
            // 如果没有找到的话，就是从大到小排序，然后再按照 二分法的方式，找到最接近的列表，

            List<Tuple4<String, Integer, List<DeviceModel>, Integer>> matchDevices = allDeviceTuples.stream()
                    .filter(d -> maxDeviceTuple != d)
                    .sorted((a, b) -> b.getT4() - a.getT4())
                    .collect(Collectors.toList());

            int index = getSumIndex(matchDevices.stream().map(Tuple4::getT4).collect(Collectors.toList()), num - maxDeviceTuple.getT4());

            // 找到index 后，将index之前的机柜的设备全部加入到设备列表中。
            matchDevices.subList(0, index).forEach(d -> {
                devices.addAll(d.getT3());
                countSum.getAndAdd(d.getT4());
            });
            // 最后的一个机柜，就可以使用 do-while的形式，再次进行循环就可以满足条件了。
        } while (countSum.get() < num);

        return new ArrayList<>();
    }

    /**
     * 从devices 中找出最少匹配attrs 属性的num 的设备
     * @param attrs 属性列表
     * @param num 属性最少需要多少台电脑
     * @param devices 搜索的设备
     * @return 最少匹配的电脑数量。
     */
    private List<DeviceModel> findMinDevices(
            List<String> attrs,
            int num,
            List<DeviceModel> devices
    )
    {
        List<Integer> testNumList = devices.stream()
                .map(d -> attrs.stream()
                        .map(d::getTestNumByAttr)
                        .min(Comparator.comparingInt(Integer::intValue))
                        .orElse(0))
                .collect(Collectors.toList());
        int count = testNumList.stream().reduce(0, Integer::sum) ;
        if(count >= num){

            int index = getSumIndex(testNumList, num);

            // 如果没能找到足够的设备的话。
            if( index != -1 ){
                // 因为数组中索引是从0 开始的，所以要+1 ，另外sumList 本来就是一个各位数的和，所以第n位 表示了是前n+1台设备的和。
                int pcCount = index + 1 ;
                return devices.subList(0, pcCount);
            }
        }
        return new ArrayList<>();
    }

    /**
     * 先从 numList 中计算出各位数的和，然后再从sumList 中找到最接近num的数。
     * @param numList 数字列表，需要是从小到大排序的。
     * @param num 需要找到的数字，如果找不到的话，就返回-1
     * @return 返回累计最接近num的数的索引，如果找不到的话，就返回-1
     */
    private int getSumIndex(List<Integer> numList, int num){
        List<Integer> sumList = new ArrayList<>();
        int lastSum = 0 ;
        for (Integer integer : numList) {
            lastSum += integer;
            sumList.add(lastSum);
        }

        int index = Collections.binarySearch(sumList, num);
        index = index >= 0 ? index : -index - 1;
        if( index < sumList.size() ){
            return index ;
        }
        return -1 ;
    }

    /**
     * 获取当前工单下符合分配状态的Plan
     * @param allPlanEntityList plan列表
     * @return 排队中的plan
     */
    public List<OrderPlanEntity> fetchCanAssignPlanList(List<OrderPlanEntity> allPlanEntityList) {
        // 将Plan 进行排序。
        List<OrderPlanEntity> sortedPlanList = planService.sortPlanList(allPlanEntityList);
        // 然后 从 sortedPlanList 中获取可以测试的Plan。
        List<OrderPlanEntity> planEntityList = sortedPlanList.stream()
                .filter(OrderPlanEntity::isQueue)
                .filter(planService::isPlanInQueue)
                .collect(Collectors.toList());

        // 找到第一个测试全部样片的Plan，在这个Plan之前的Plan都可以测试。
        int testAllIndex = planEntityList.stream()
                .filter(p -> p.isTestAll() && p.getStatus() != OrderPlanEntity.Status.RUNNING)
                .mapToInt(planEntityList::indexOf)
                .findFirst()
                .orElse(-1);
        if (testAllIndex >= 0) {
            planEntityList = planEntityList.subList(0, testAllIndex + 1);
        }
        log.info("fetchCanAssignPlanList planEntityList: {}", planEntityList);
        return planEntityList;
    }

    /**
     *
     * 从筛选出来的Plan中，获取可以测试的Plan列表。
     * @param allPlanEntityList 所有的Plan列表
     * @param leftFlashNum 剩余的Flash 样片的数量
     * @param leftPlanCount 剩余的Plan 的配额
     * @param needTestPlanList 预筛选的Plan 列表
     * @return 返回从 {needTestPlanList} 中筛选出来适合的Plan。
     */
    public List<OrderPlanEntity> getValidPlanList(
            List<OrderPlanEntity> allPlanEntityList,
            Integer leftFlashNum,
            int leftPlanCount,
            List<OrderPlanEntity> needTestPlanList
    ) {
        // 如果剩余的数量小于需要测试的数量，那么就只取小于 剩余数量的plan。
        List<OrderPlanEntity> planEntityList = needTestPlanList.stream()
                .filter(p -> p.getTestNum() <= leftFlashNum)
                // 判断父plan是否完成，如果完成，则可以测试。
                .filter(p -> planService.checkParent(p, allPlanEntityList))
                // 过滤手动Plan，因为手动Plan 不需要分配设备
                .filter(p -> !p.isManualPlan())
                .sorted(Comparator.comparing(OrderPlanEntity::getPriority).reversed())
                .collect(Collectors.toList());
        if (planEntityList.isEmpty()) {
            return new ArrayList<>();
        }

        List<Integer> sumList = new ArrayList<>();
        AtomicInteger lastSum = new AtomicInteger();
        planEntityList.forEach(p -> {
            lastSum.addAndGet(p.getTestNum());
            sumList.add(lastSum.get());
        });
        // 使用 binarySearch 来查找最后一个小于等于 剩余数量的plan 总数。
        int index = Collections.binarySearch(sumList, leftFlashNum);
        if (index < 0) {
            index = -index - 1;
        } else {
            index = index == 0 ? 1 : index;
        }

        log.info(MarkerFactory.getMarker(
                QueueService.class.getSimpleName()),
                " getValidPlanList sumList: {} leftFlashNum:{} index:{} plans:{}",
                sumList,
                leftFlashNum,
                index,
                planEntityList
        );
        return planEntityList.subList(0, Math.min(Math.min(index, planEntityList.size()), leftPlanCount));
    }

    /**
     * SD 预分配时获取设备的处理方法
     * @param planEntity 待分配的Plan实体类
     * @param canTestDeviceList 此时可用的空闲设备
     * @return 匹配的设备信息
     */
    public List<DeviceModel> findDevicesToSDPlan(OrderPlanEntity planEntity, List<DeviceModel> canTestDeviceList){
        int testNum = planEntity.getTestNum();
        String planName = planEntity.getName();
        boolean isTestAll = planEntity.isTestAll();
        List<String> attrs = Arrays.asList(planEntity.getAttrs().split(";"));

        log.info("{} test all sample is {}", planName, isTestAll);
        List<DeviceModel> deviceListInCounter = canTestDeviceList.stream()
                .filter(dev -> dev.getPosition().startsWith(TEST_SD_H2_RACK))
                .collect(Collectors.toList());          // GE3_1机柜下的可测空闲设备

        List<DeviceModel> otherDeviceList = canTestDeviceList.stream()
                .filter(dev -> !TEST_SD_SPEC_LIST.contains(dev.getCabinetPosition()))
                .collect(Collectors.toList());          // 除了GE3_1之外机柜下的可测空闲设备

//        log.info("otherDeviceList: {}", otherDeviceList.stream().map(dev -> dev.getPcNo()).collect(Collectors.toList()));
        List<DeviceModel> resultDevices = new ArrayList<>();
        // SD的 Plan22和Plan39优先在GE3_1中分配，不够再从其他机柜分配
        if (isTestAll) {
            List<DeviceModel> combinedList = new ArrayList<>(deviceListInCounter);
            combinedList.addAll(otherDeviceList);
            int sum = 0;
            // 优先使用GE3_1机柜下的设备
            for(DeviceModel device : combinedList){
                resultDevices.add(device);
                sum += device.getTestNumByAttr(planEntity.getAttrs());
                if (sum > testNum) {
                    break;
                }
            }
            if(sum < testNum){
                resultDevices = new ArrayList<>();
            }
        } else {
            // 剩余的其他Plan则在GE2_1、GE2_2、GE7_1、GE7_2
            // 设备按照机柜进行划分
            Map<String, List<DeviceModel>> groupedDevices = new HashMap<>();
            for (DeviceModel device : otherDeviceList) {
                String cabinet = device.getCabinetPosition();
                if (!groupedDevices.containsKey(cabinet)) {
                    groupedDevices.put(cabinet, new ArrayList<>());
                }
                groupedDevices.get(cabinet).add(device);
            }
            List<Map.Entry<String, List<DeviceModel>>> sortedEntries = new ArrayList<>(groupedDevices.entrySet());

            // 获取同一机柜的连号设备
            for (Map.Entry<String, List<DeviceModel>> entry : sortedEntries){
                String counterName = entry.getKey();
                List<DeviceModel> counterDevices = entry.getValue();
                List<DeviceModel> resultList = getAdjacentDeviceInCounter(counterDevices, testNum, attrs);
                if(resultList.size() > 0){
                    return resultList;
                }
                log.info("{}在机柜 {} 下未找到连号设备！", planName, counterName);
            }
            // 允许跨机柜非连号设备
            resultDevices = findMinDevices(attrs, testNum, otherDeviceList);

            // 如果在其他机柜找不到设备
            if(resultDevices.size() == 0){
                List<DeviceModel> counterList = getAdjacentDeviceInCounter(deviceListInCounter, testNum, attrs);
                if(counterList.size() > 0){
                    return counterList;
                }
                resultDevices = findMinDevices(attrs, testNum, deviceListInCounter);
            }
        }
        return resultDevices;
    }

    /**
     * U3 预分配时获取设备的处理方法
     * @param planEntity 待分配的Plan实体类
     * @param canTestDeviceList 此时可用的空闲设备
     * @return 匹配的设备信息
     */
    public List<DeviceModel> findDevicesToU3Plan(OrderPlanEntity planEntity, List<DeviceModel> canTestDeviceList) {
        int testNum = planEntity.getTestNum();
        List<String> attrs = Arrays.asList(planEntity.getAttrs().split(";"));

        List<DeviceModel> resultList;
        if (planEntity.hasContainAttr("TCPIP掉电")) {
            List<DeviceModel> deviceListInCounter = canTestDeviceList.stream()
                    .filter(dev -> dev.getPosition().startsWith(TEST_U3_TCPIP_RACK))
                    .collect(Collectors.toList());
            // 在GE3_2中寻找连号的设备
            resultList = getAdjacentDeviceInCounter(deviceListInCounter, testNum, attrs);
            if (!resultList.isEmpty()) {
                return resultList;
            }
            // 如果在GE3_2中找不到连号设备，则判断非连号设备是否满足分配
            resultList = findMinDevices(attrs, testNum, deviceListInCounter);
            if (!resultList.isEmpty()) {
                return resultList;
            }
        } else {
            for (String rackName : TEST_U3_RACK_LIST) {
                log.info("在机柜{}下寻找连号的设备", rackName);
                List<DeviceModel> deviceListInCounter = canTestDeviceList.stream()
                        .filter(dev -> dev.getPosition().startsWith(rackName))
                        .collect(Collectors.toList());
                resultList = getAdjacentDeviceInCounter(deviceListInCounter, testNum, attrs);
                if (!resultList.isEmpty()) {
                    log.info("在机柜{}下找到连号设备: {}", rackName, resultList.stream()
                            .map(DeviceModel::getPosition)
                            .collect(Collectors.toList()));
                    return resultList;
                }
            }

            // 在强性能机柜寻找连号设备 GE4_1机柜
            List<DeviceModel> deviceListInStrongerCounter = canTestDeviceList.stream()
                    .filter(dev -> dev.getPosition().startsWith(TEST_USB_PUBLIC_RACK))
                    .collect(Collectors.toList());
            // 在 GE4_1机柜 中寻找连号的设备
            resultList = getAdjacentDeviceInCounter(deviceListInStrongerCounter, testNum, attrs);
            if (!resultList.isEmpty()) {
                log.info("在机柜GE4_1下找到连号设备: {}", resultList.stream()
                        .map(DeviceModel::getPosition)
                        .collect(Collectors.toList()));
                return resultList;
            }
        }
        // 不做机柜限制再进行检索
        return findMinDevices(attrs, testNum, canTestDeviceList);
    }

    /**
     * U2 预分配时获取设备的处理方法
     * @param planEntity 待分配的Plan实体类
     * @param canTestDeviceList 此时可用的空闲设备
     * @return 匹配的设备信息
     */
    public List<DeviceModel> findDevicesToU2Plan(OrderPlanEntity planEntity, List<DeviceModel> canTestDeviceList){
        int testNum = planEntity.getTestNum();
        List<String> attrs = Arrays.asList(planEntity.getAttrs().split(";"));

        List<DeviceModel> resultList;
        // 优先从常用机柜中查找设备
        for (String rackName : TEST_U2_RACK_LIST) {
            log.info("在机柜{}下寻找连号的设备", rackName);
            List<DeviceModel> deviceListInCounter = canTestDeviceList.stream()
                    .filter(dev -> dev.getPosition().startsWith(rackName))
                    .collect(Collectors.toList());
            resultList = getAdjacentDeviceInCounter(deviceListInCounter, testNum, attrs);
            if (!resultList.isEmpty()) {
                log.info("在机柜{}下找到连号设备: {}", rackName, resultList.stream()
                        .map(DeviceModel::getPosition)
                        .collect(Collectors.toList()));
                return resultList;
            }
        }

        // 在常用机柜的设备
        List<DeviceModel> deviceListInCounter = canTestDeviceList.stream()
        .filter(dev -> TEST_U2_RACK_LIST.contains(dev.getCabinetPosition()))
        .collect(Collectors.toList());

        // 非连号设备是否满足分配（主用机柜找不到连号设备，则考虑非连号设备）
        resultList = findMinDevices(attrs, testNum, deviceListInCounter);
        if (!resultList.isEmpty()) {
            return resultList;
        }

        // 除了 GE5_1和GE5_2机柜外，其他机柜设备也可使用
        return findMinDevices(attrs, testNum, canTestDeviceList);
    }

    /**
     * 预分配时获取设备的处理方法
     * @param orderId 工单id
     * @param subProduct 工单所属产品
     * @param planEntity 待分配的Plan实体类
     * @param runnableDeviceList 此时可用的空闲设备
     * @return 匹配的设备信息
     */
    public List<DeviceModel> findDevicesToSSDPlan(long orderId, String subProduct, OrderPlanEntity planEntity, List<DeviceModel> runnableDeviceList) {
        int testNum = planEntity.getTestNum();
        String planName = planEntity.getName();
        List<String> attrs = Arrays.asList(planEntity.getAttrs().split(";"));

        // 获取负责人所有已测试的Plan的机柜分布情况
        List<Map.Entry<String, Long>> rackList = planAssignService.findTestRacksByOrderIdAndFlashAndBelong(orderId, planEntity.getFlash(), planEntity.getBelongTo());

        // 获取当前测试负责人下Plan的历史测试记录
        List<DeviceTestHistoryEntity> deviceTestList = testHistoryService.findDeviceTestHistoryByPlan(subProduct, planEntity.getName(), planEntity.getBelongTo());

        // 如果rackList长度为0，说明此次分配为此负责人第一次分配
        if (rackList.size() == 0) {
            List<DeviceModel> resultList = findDevicesByHistory(runnableDeviceList, deviceTestList, planEntity);
            if (!resultList.isEmpty()) {
                return resultList;
            }
        }

        // 如果存在机柜信息记录，则说明需要依照已分配的机柜进行分配辅助（此时分配还是已连号为标准，不是连号放在后面处理）
        for (Map.Entry<String, Long> entry : rackList) {
            String rackName = entry.getKey();   // 机柜名称
            log.info("{}已存在使用机柜，在机柜{}下寻找连号的设备", planName, rackName);
            // 当前机柜下的空闲设备
            List<DeviceModel> oneRackCanRunDevice = runnableDeviceList.stream()
                    .filter(dev -> dev.getPosition().startsWith(rackName))
                    .collect(Collectors.toList());
            // Plan历史测试设备中的空闲设备
            List<DeviceTestHistoryEntity> rackFreeHistoryTestList = deviceTestList.stream()
                    .filter(history -> {
                        Optional<DeviceModel> device = oneRackCanRunDevice.stream().filter(dev -> history.getMac().equals(dev.getMac())).findFirst();
                        device.ifPresent(d -> history.setAttrModelList(d.getAttrModelList()));
                        return device.isPresent();
                    })
                    .collect(Collectors.toList());
            // 获取连号设备
            List<String> macList = testHistoryService.findMostUsedAdjacentDeviceInHistory(rackFreeHistoryTestList, testNum, attrs);
            if (macList.size() > 0) {
                log.info("{}在机柜{}下找到设备历史中常用的连号设备：{}", planName, rackName, macList);
                return oneRackCanRunDevice.stream()
                        .filter(dev -> macList.stream().anyMatch(mac -> mac.equals(dev.getMac())))
                        .collect(Collectors.toList());
            }
        }

        // todo 如果需要历史测试设备中查找非连号设备（这里后面考虑一下是否需要）

        // 第一步：现在已分配的机柜范围内找设备，找到了直接返回，没有找到执行下一步；
        // 第二部：扩大范围，在所有已知的空闲设备中找到匹配的设备（不限制机柜范围）
        return getCanRunnableDevicesInRack(planName, rackList, runnableDeviceList, attrs, testNum);
    }

    /**
     * 预分配时获取设备的处理方法
     * @param orderId 工单id
     * @param subProduct 工单所属产品
     * @param planEntity 待分配的Plan实体类
     * @param runnableDeviceList 此时可用的空闲设备
     * @return 匹配的设备信息
     */
    public List<DeviceModel> findDevicesToEMMcPlan(long orderId, String subProduct, OrderPlanEntity planEntity, List<DeviceModel> runnableDeviceList) {
        int testNum = planEntity.getTestNum();
        String planName = planEntity.getName();
        List<String> attrs = Arrays.asList(planEntity.getAttrs().split(";"));

        // 获取负责人所有已测试的Plan的机柜分布情况
        List<Map.Entry<String, Long>> rackList = planAssignService.findTestRacksByOrderIdAndFlashAndBelong(orderId, planEntity.getFlash(), planEntity.getBelongTo());

        // 获取当前测试负责人下Plan的历史测试记录
        List<DeviceTestHistoryEntity> deviceTestList = testHistoryService.findDeviceTestHistoryByPlan(subProduct, planEntity.getName(), planEntity.getBelongTo());

        // 如果rackList长度为0，说明此次分配为此负责人第一次分配
        if (rackList.size() == 0) {
            List<DeviceModel> resultList = findDevicesByHistory(runnableDeviceList, deviceTestList, planEntity);
            if (resultList.size() > 0) {
                return resultList;
            }
        }

        // 第一步：现在已分配的机柜范围内找设备，找到了直接返回，没有找到执行下一步；
        // 第二部：扩大范围，在所有已知的空闲设备中找到匹配的设备（不限制机柜范围）
        return getCanRunnableDevicesInRack(planName, rackList, runnableDeviceList, attrs, testNum);
    }

    /**
     * 从历史测试信息中获取测试设备
     *
     * @param runnableDeviceList 空闲设备
     * @param deviceTestList     历史测试记录
     * @param planEntity         plan 实体类
     * @return 设备列表
     */
    public List<DeviceModel> findDevicesByHistory(
            List<DeviceModel> runnableDeviceList,
            List<DeviceTestHistoryEntity> deviceTestList,
            OrderPlanEntity planEntity
    ) {
        int testNum = planEntity.getTestNum();
        String planName = planEntity.getName();
        List<String> attrs = Arrays.asList(planEntity.getAttrs().split(";"));
        // 获取Plan历史测试设备中空闲的数据
        List<DeviceTestHistoryEntity> freeHistoryTestList = deviceTestList.stream()
                .filter(history -> {
                    Optional<DeviceModel> device = runnableDeviceList.stream().filter(dev -> history.getMac().equals(dev.getMac())).findFirst();
                    device.ifPresent(d -> history.setAttrModelList(d.getAttrModelList()));
                    return device.isPresent();
                })
                .collect(Collectors.toList());

        log.info("{}是该测试负责人第一个在该批次下分配plan", planName);
        // 获取连号设备
        List<String> macList = testHistoryService.findMostUsedAdjacentDeviceInHistory(freeHistoryTestList, testNum, attrs);
        // 如果没有找到连号的设备，则考虑使用非连号设备
        if (macList.size() == 0) {
            log.info("正在考虑使用非连号的设备");
            macList = testHistoryService.findMostUsedDeviceInHistory(freeHistoryTestList, testNum, attrs);
        }
        if (macList.size() > 0) {   // 若连非连号的设备都找不到，在后面则需要考虑这种分配情况
            List<String> finalMacList = macList;
            log.info("{}在历史设备中找到满足条件的设备：{}", planName, macList);
            return runnableDeviceList.stream()
                    .filter(dev -> finalMacList.stream().anyMatch(mac -> mac.equals(dev.getMac())))
                    .collect(Collectors.toList());
        }

        // 如果在历史设备中没有找到合适的设备，则扩大搜索范围，查看所有空闲设备
        return getPlanRunnableDevices(planEntity, runnableDeviceList, testNum);
    }

    /**
     * 获取某一个机柜下满足连号的设备信息
     * @param devices 某一机柜下的可用设备
     * @param testNum 待测数量
     * @param attrs 测试属性
     * @return 设备信息
     */
    private static List<DeviceModel> getAdjacentDeviceInCounter(List<DeviceModel> devices, int testNum, List<String> attrs) {
        log.info("devices: {}", devices.stream().map(DeviceModel::getPosition).collect(Collectors.toList()));
        int devLen = devices.size();
        for (int i = 0; i < devLen; i++) {
            boolean isConsecutive = true;
            int size = 1;
            int sum = findMinNum(attrs, devices.get(i).getAttrModelList());
            for (int j = 1; j < devLen - i; j++) {      // 此处需要遍历到数组最后一个
                if (sum >= testNum) {       // 如果此时的设备可进行的测试数量大于Plan的测试数量
                    break;
                }
                if (devices.get(i + j).getNoNumber() - devices.get(i + j - 1).getNoNumber() != 1) {
                    isConsecutive = false;
                    break;
                } else {
                    size++;
                    sum += findMinNum(attrs, devices.get(i + j).getAttrModelList());
                    if (sum >= testNum) {       // 如果此时的设备可进行的测试数量大于Plan的测试数量
                        break;
                    }
                }
            }
            // 如果当前index是数组最后一个数，但是测试数量并不满足，则跳出循环
            log.info("for index: {} size: {} sum: {} testNum: {}", i, size, sum, testNum);
            if (sum < testNum) {
                isConsecutive = false;
            }

            if (isConsecutive) {
                log.info("wait testNum: {} assign sum: {} ", testNum, sum);
                List<DeviceModel> subsequence = new ArrayList<>();
                subsequence.add(devices.get(i));
                for (int j = 1; j < size; j++) {
                    subsequence.add(devices.get(i + j));
                }
                return subsequence;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 检查该设备满足属性信息的最大测试数量
     *
     * @param attributes    属性列表
     * @param attributeList 属性测试信息表
     * @return 满足测试的最大数量
     */
    public static int findMinNum(List<String> attributes, List<AttrModel> attributeList) {
        int minNum = Integer.MAX_VALUE;
        if (attributeList.size() == 0) {
            return 0;
        }

        for (String attribute : attributes) {
            for (AttrModel attr : attributeList) {
                if (attr.getAttrName().equals(attribute) && attr.getNum() < minNum) {
                    minNum = attr.getNum();
                }
            }
        }

        return minNum;
    }

}
