package com.yeestor.work_order.service.permission;

import com.yeestor.utils.Enums;
import com.yeestor.work_order.controller.role.PermissionController;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.model.permission.PermissionGroup;
import com.yeestor.work_order.model.permission.PermissionModel;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class PermissionTest {


    @Test
    void testPermissionValueOf() {
        String name = "Test" ;
        Optional<Permission> permissionOptional = Enums.getIfPresent(Permission.class,name);
        Assertions.assertThat(permissionOptional).isNotPresent();
        name = "UPLOAD_REPORT" ;
        permissionOptional = Enums.getIfPresent(Permission.class,name);
        Assertions.assertThat(permissionOptional).isPresent();

    }


    @Test
    void  listPermissions() {


        Map<PermissionGroup, List<Permission>> listHashMap = Arrays.stream(Permission.values())
                .filter(p ->
                        Optional.of(p).map(Permission::getGroup).isPresent()
                        )
                .collect(Collectors.groupingBy(p -> Optional.of(p).map(Permission::getGroup).orElse(PermissionGroup.WORK_ORDER)));


        // 将 listHashMap 转换成 PermissionController.PermissionModel 的列表 。
        List<PermissionModel> permissionModels = new ArrayList<>();
        listHashMap.forEach((k,v) -> {
            PermissionModel permissionModel = new PermissionModel();
            permissionModel.setGroup(PermissionModel.LocaleLanguage.builder()
                    .name(k.name())
                    .locale(k.getLocale())
                    .build());
            permissionModel.setPermissions(
                    v.stream()
                            .map(p -> PermissionModel.LocaleLanguage
                                    .builder()
                                    .name(p.name())
                                    .locale(p.getLocale())
                                    .build())
                            .collect(Collectors.toList())
            );
            permissionModels.add(permissionModel);

        });
        log.info("permissionModels:{}",permissionModels);
    }

}
