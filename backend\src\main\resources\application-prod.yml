server:
  port: 8793

management:
  endpoints:
    web:
      exposure:
        include: '*'
sentry:
  dsn: https://<EMAIL>/api/v4/error_tracking/collector/157

spring:
  application:
    name: system-workorder
    title: 工单系统-生产
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  main:
    allow-bean-definition-overriding: true
  boot:
    admin:
      client:
        url: ${ADMIN_SERVER_URL:http://gateway.yeestor.com:8788/admin}
        instance:
          prefer-ip: true
  datasource:
    url: ************************************************************************
    username: root
    password: Reliable123!

  jpa:
    open-in-view: false
    show-sql: false
    hibernate:
      ddl-auto: 'update'
  redis:
    database: 0
    host: ***********
    port: 6379
    timeout: 1000

  data:
    redis:
      repositories:
        enabled: false

  quartz:
    enable: true
    auto-startup: false
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    properties:
      org:
        quartz:
          scheduler:
            instanceId: AUTO
          jobStore:
            isClustered: true


  ### 邮箱信息配置，使用的事ci公共邮箱
  mail:
    host: smtp.mxhichina.com
    username: <EMAIL>
    password: 4DvKl1K0hyqTBEAR
    port: 465
    properties:
      from: <EMAIL>
      mail:
        smtp:
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory

eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_SERVER_ADDRESS:http://gateway.yeestor.com:8788/eureka/}
      fetch-registry: true
      register-with-eureka: true
      lease-renewal-interval-in-seconds: 10

  instance:
    preferIpAddress: true
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}
    lease-renewal-interval-in-seconds: 10

order:
  prefix: order

logging:
#  config: classpath:log4j2.yml
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.jdbc.core.JdbcTemplate: DEBUG
    org.springframework.jdbc.core.StatementCreatorUtils: TRACE
    org.springframework.web: DEBUG
    org.springframework.web.HttpLogging: DEBUG
    org.springframework.security: DEBUG
    org.springframework.security.oauth2: DEBUG
