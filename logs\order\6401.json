{"@timestamp":"2025-07-24T15:51:02.588+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:51:02.597+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6636, orderId=6401, flash=YS-6285ENAB-HYV8-A_64GB, orderFlashNo=YS6285##MP#########16682#3DV8_HY_TLC_250427_YS-6285ENAB-HYV8-A_64GB, num=98, leftNum=98)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.621+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.639+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113841, orderId=6401, name=Plan36, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.656+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113842, orderId=6401, name=Plan37, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.684+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113833, orderId=6401, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.704+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113836, orderId=6401, name=Plan12, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.723+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113840, orderId=6401, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.742+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113844, orderId=6401, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.76+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113834, orderId=6401, name=Plan8, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.776+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113835, orderId=6401, name=Plan9, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.792+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113837, orderId=6401, name=Plan17, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.811+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113846, orderId=6401, name=Plan49, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.812+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.812+08:00","@version":"1","message":"[6401] - [YS-6285ENAB-HYV8-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f692e4e451770aa","spanId":"0f692e4e451770aa","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.592+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.595+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6636, orderId=6401, flash=YS-6285ENAB-HYV8-A_64GB, orderFlashNo=YS6285##MP#########16682#3DV8_HY_TLC_250427_YS-6285ENAB-HYV8-A_64GB, num=98, leftNum=98)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.615+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.63+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113841, orderId=6401, name=Plan36, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.658+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113842, orderId=6401, name=Plan37, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.679+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113833, orderId=6401, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.699+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113836, orderId=6401, name=Plan12, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.727+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113840, orderId=6401, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.753+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113844, orderId=6401, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.788+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113834, orderId=6401, name=Plan8, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.832+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113835, orderId=6401, name=Plan9, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.849+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113837, orderId=6401, name=Plan17, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.872+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113846, orderId=6401, name=Plan49, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.873+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.873+08:00","@version":"1","message":"[6401] - [YS-6285ENAB-HYV8-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0432473de6beff","spanId":"cd0432473de6beff","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.585+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:12:02.955+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6636, orderId=6401, flash=YS-6285ENAB-HYV8-A_64GB, orderFlashNo=YS6285##MP#########16682#3DV8_HY_TLC_250427_YS-6285ENAB-HYV8-A_64GB, num=98, leftNum=98)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.978+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.994+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113841, orderId=6401, name=Plan36, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.008+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113842, orderId=6401, name=Plan37, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.024+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113833, orderId=6401, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.039+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113836, orderId=6401, name=Plan12, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.054+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113840, orderId=6401, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.069+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113844, orderId=6401, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.085+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113834, orderId=6401, name=Plan8, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.1+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113835, orderId=6401, name=Plan9, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.115+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113837, orderId=6401, name=Plan17, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.13+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113846, orderId=6401, name=Plan49, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.13+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.13+08:00","@version":"1","message":"[6401] - [YS-6285ENAB-HYV8-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.584+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:15:02.586+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6636, orderId=6401, flash=YS-6285ENAB-HYV8-A_64GB, orderFlashNo=YS6285##MP#########16682#3DV8_HY_TLC_250427_YS-6285ENAB-HYV8-A_64GB, num=98, leftNum=98)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.61+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.636+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113841, orderId=6401, name=Plan36, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113842, orderId=6401, name=Plan37, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113833, orderId=6401, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.683+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113836, orderId=6401, name=Plan12, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.698+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113840, orderId=6401, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.714+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113844, orderId=6401, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.729+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113834, orderId=6401, name=Plan8, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.745+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113835, orderId=6401, name=Plan9, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.76+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113837, orderId=6401, name=Plan17, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.774+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113846, orderId=6401, name=Plan49, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.775+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.775+08:00","@version":"1","message":"[6401] - [YS-6285ENAB-HYV8-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.592+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.594+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6636, orderId=6401, flash=YS-6285ENAB-HYV8-A_64GB, orderFlashNo=YS6285##MP#########16682#3DV8_HY_TLC_250427_YS-6285ENAB-HYV8-A_64GB, num=98, leftNum=98)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.618+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.637+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113841, orderId=6401, name=Plan36, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.695+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113842, orderId=6401, name=Plan37, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.723+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113833, orderId=6401, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.771+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113836, orderId=6401, name=Plan12, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.801+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113840, orderId=6401, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.827+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113844, orderId=6401, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.849+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113834, orderId=6401, name=Plan8, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.864+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113835, orderId=6401, name=Plan9, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.878+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113837, orderId=6401, name=Plan17, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.893+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113846, orderId=6401, name=Plan49, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.893+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.894+08:00","@version":"1","message":"[6401] - [YS-6285ENAB-HYV8-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.585+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:21:02.586+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6636, orderId=6401, flash=YS-6285ENAB-HYV8-A_64GB, orderFlashNo=YS6285##MP#########16682#3DV8_HY_TLC_250427_YS-6285ENAB-HYV8-A_64GB, num=98, leftNum=98)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.611+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.632+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113841, orderId=6401, name=Plan36, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.647+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113842, orderId=6401, name=Plan37, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113833, orderId=6401, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.676+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113836, orderId=6401, name=Plan12, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.69+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113840, orderId=6401, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.704+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113844, orderId=6401, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.719+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113834, orderId=6401, name=Plan8, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.733+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113835, orderId=6401, name=Plan9, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.752+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113837, orderId=6401, name=Plan17, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.766+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113846, orderId=6401, name=Plan49, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.767+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.767+08:00","@version":"1","message":"[6401] - [YS-6285ENAB-HYV8-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.585+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:27:02.587+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6636, orderId=6401, flash=YS-6285ENAB-HYV8-A_64GB, orderFlashNo=YS6285##MP#########16682#3DV8_HY_TLC_250427_YS-6285ENAB-HYV8-A_64GB, num=98, leftNum=98)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.613+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.633+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113841, orderId=6401, name=Plan36, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.651+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113842, orderId=6401, name=Plan37, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.666+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113833, orderId=6401, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.681+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113836, orderId=6401, name=Plan12, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.696+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113840, orderId=6401, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.711+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113844, orderId=6401, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.726+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113834, orderId=6401, name=Plan8, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.743+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113835, orderId=6401, name=Plan9, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.758+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113837, orderId=6401, name=Plan17, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.773+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113846, orderId=6401, name=Plan49, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.774+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.774+08:00","@version":"1","message":"[6401] - [YS-6285ENAB-HYV8-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:02.887+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:02.92+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6636, orderId=6401, flash=YS-6285ENAB-HYV8-A_64GB, orderFlashNo=YS6285##MP#########16682#3DV8_HY_TLC_250427_YS-6285ENAB-HYV8-A_64GB, num=98, leftNum=98)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.132+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.196+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113841, orderId=6401, name=Plan36, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.213+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113842, orderId=6401, name=Plan37, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.235+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113833, orderId=6401, name=Plan4, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.263+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113836, orderId=6401, name=Plan12, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.29+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113840, orderId=6401, name=Plan23, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.312+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113844, orderId=6401, name=Plan41, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.339+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113834, orderId=6401, name=Plan8, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.361+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113835, orderId=6401, name=Plan9, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.391+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113837, orderId=6401, name=Plan17, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.417+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113846, orderId=6401, name=Plan49, status=QUEUE, priority=25) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.44+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113839, orderId=6401, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.441+08:00","@version":"1","message":"[6401] - [YS-6285ENAB-HYV8-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6401","traceType":"分配设备"}
