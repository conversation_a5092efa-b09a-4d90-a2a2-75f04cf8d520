package com.yeestor.work_order.service.product.impl;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.service.product.ProductContext;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 通用产品线的产品服务
 */
@Slf4j
@Setter(onMethod = @__({@Autowired, @Lazy}))
@Component
public class GeneralProductService extends DefaultProductService {

    protected ProductContext productContext;

    @PostConstruct
    private void init(){
        productContext.registerProductService("U2",this);
        productContext.registerProductService("SD",this);
        productContext.registerProductService("U3",this);
    }

    @Override
    public void autoCompletePlan(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, OrderPlanEntity planEntity) {
        // 首先检查Plan 并更新设备下的释放状态.
        planService.checkDeviceReleaseStatus(orderEntity,flashEntity, planEntity);
        // 如果Plan 已经到了COMPLETED 的状态，则就不需要再去处理释放了.
        if(planEntity.getStatus() == OrderPlanEntity.Status.COMPLETED){
            return;
        }
        super.autoCompletePlan(orderEntity, flashEntity, planEntity);

    }
}
