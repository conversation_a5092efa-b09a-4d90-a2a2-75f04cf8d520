package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("启动plan测试返回的格式")
public class PlanTestRespModel {

    @ApiModel(value = "DeviceTestResult", description = "成功或者设备信息。")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DeviceTestResult {
        private String ip ;
        private String no ;
        private String reason ;
    }

    @ApiModelProperty("启动成功的IP列表")
    private List<DeviceTestResult> successLst ;

    @ApiModelProperty("启动失败的IP列表")
    private List<DeviceTestResult> failLst ;

}
