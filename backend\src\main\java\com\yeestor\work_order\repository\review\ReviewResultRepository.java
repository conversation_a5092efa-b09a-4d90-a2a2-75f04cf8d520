package com.yeestor.work_order.repository.review;

import com.yeestor.work_order.entity.review.ReviewResultEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ReviewResultRepository extends JpaRepository<ReviewResultEntity, Long> {
    List<ReviewResultEntity> findAllByOrderId(long id);

    Optional<ReviewResultEntity> findFirstByOrderIdAndFlash(long orderId, String flash);

    List<ReviewResultEntity> findAllByOrderIdAndFlashOrderByIdDesc(long orderId, String flash);

    boolean existsByOrderIdAndFlashAndResult(long orderId, String flash, Boolean result) ;

    Optional<ReviewResultEntity> findFirstByOrderIdAndFlashOrderByIdDesc(long orderId, String flash);

    // 判断当前Flash批次最后一次review记录是否通过
    default boolean findFlashReviewResult(long orderId, String flash, Boolean result) {
        return findFirstByOrderIdAndFlashOrderByIdDesc(orderId, flash)
                .map(entity -> entity.getResult() == result)
                .orElse(false);
    }
}
