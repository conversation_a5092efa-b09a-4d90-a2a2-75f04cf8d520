package com.yeestor.work_order.model.http.resp.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yeestor.work_order.entity.WorkOrderEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Data
@NoArgsConstructor
@ApiModel(value = "工单列表项")
public class WorkOrderItemVO {

    @ApiModelProperty(value = "工单id")
    private long id;

    @ApiModelProperty(value = "工单号")
    private String no;

    @ApiModelProperty(value = "产品线")
    private String product;

    @ApiModelProperty(value = "产品")
    private String subProduct;

    @ApiModelProperty(value = "主控")
    private String chip;

    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "构建人")
    private String buildPerson ;

    @ApiModelProperty(value = "构建Flash名称")
    private String buildFlash ;

    @ApiModelProperty(value = "完整版本，由自动构建工具生成！")
    private String fullVersion;

    @ApiModelProperty(value = "Plan 数量")
    private int planNum;

    @ApiModelProperty(value = "优先级")
    private int priority;

    @ApiModelProperty(value = "发起时间")
    private long createTime;

    /**
     * 工单状态
     * @see WorkOrderEntity.Status
     */
    @ApiModelProperty(value = "工单状态")
    private WorkOrderEntity.Status status;

    @ApiModelProperty("测试结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long endTime;

    @ApiModelProperty("Flash 信息列表")
    private List<WorkOrderDetailVO.FlashInfoVO> flashInfoList;

    @ApiModelProperty("预期最少需要多少设备")
    private int expectNum;

    @ApiModelProperty(value = "工单占用的设备数量")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private int occupiedDeviceCount;

    @ApiModelProperty(value = "总共错误的Plan数量")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private int errorPlanCount ;

    @ApiModelProperty(value = "撤销时间")
    private Long revokeAt ;

    @ApiModelProperty(value = "撤销人")
    private String revokePerson ;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "工单在队列中的位置, 从0开始,如果为空,则表示不在队列中")
    private Integer queueIndex;

    @ApiModelProperty(value = "工单特性，0：普通工单,1：Flash 品质验证,2: FAE, 4: 内部验证")
    private int feature ;

    @ApiModelProperty(value = "环境已经准备好的Plan数量")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private int readyNum ;

    /**
     * 将工单实体类转换为工单VO类
     * @param orderEntity 工单实体类
     * @return       工单VO类
     */
    public static WorkOrderItemVO convert(WorkOrderEntity orderEntity, int planNum) {
        WorkOrderItemVO orderVO = new WorkOrderItemVO();
        orderVO.setId(orderEntity.getId());
        orderVO.setNo(orderEntity.getNo());
        orderVO.setProduct(orderEntity.getProduct());
        orderVO.setSubProduct(orderEntity.getSubProduct());
        orderVO.setChip(orderEntity.getChip());
        orderVO.setFullVersion(orderEntity.getFullVersion());
        orderVO.setVersion(orderEntity.getVersion());
        orderVO.setPlanNum(planNum);
        orderVO.setPriority(orderEntity.getPriority());
        orderVO.setCreateTime(orderEntity.getCreatedAt());
        orderVO.setStatus(orderEntity.getStatus());
        orderVO.setBuildPerson(orderEntity.getBuildPerson());
        orderVO.setBuildFlash(orderEntity.getFlash());
        orderVO.setFeature(orderEntity.getFeature());
        if(orderEntity.getEndAt() != null) {
            orderVO.setEndTime(orderEntity.getEndAt());
        }
        // 如果存在撤销信息
        if(orderEntity.getRevokeBy() != null){
            orderVO.setRevokeAt(orderEntity.getRevokeAt());
            orderVO.setRevokePerson(orderEntity.getRevokePerson());
        }
        return orderVO;
    }


    public WorkOrderItemVO(long id, String no, String product, String subProduct, String chip, String version,
                           long planNum, int priority, Date createDate, WorkOrderEntity.Status status, Long endTime) {
        this.id = id;
        this.no = no;
        this.product = product;
        this.subProduct = subProduct;
        this.chip = chip;
        this.version = version;
        this.planNum = (int) planNum;
        this.priority = priority;
        this.createTime = createDate.getTime();
        this.status = status;
        this.endTime = endTime;
    }
}
