///<reference types="cypress" />

import { orderId, flashName, testPersons } from './case';

context('with plan list state test', () => {
  describe('plan-state', () => {
    const stageList = ['ACCEPT', 'VERIFY', 'INTEGRITY'];
    const stateList = ['QUEUE', 'READY', 'CONFIRMED', 'RUNNING', 'COMPLETED', 'STOPPED'];
    const sortNameList = ['default', 'downPriority', 'upPriority', 'dowmName'];
    const waitTime = 100;

    beforeEach(() => {
      cy.viewport(1920, 960);
      cy.visit(`http://172.18.11.245:8512/work_order/GE/SD/${orderId}`).wait(5000);
    });

    it('Plan 状态筛选分类测试', () => {
      cy.get('#flash-tab-' + flashName)
        .click()
        .wait(1000);

      // 测试Plan阶段列表遍历
      stageList.forEach((stage: string) => {
        cy.get(`span#${stage}`).wait(waitTime).click();
        ergodic(stage, '', '');

        // 测试人员列表遍历
        testPersons.forEach((person: string) => {
          cy.get(`span#${person}`).wait(waitTime).click();
          ergodic(stage, person, '');

          // plan状态列表遍历
          stateList.forEach((state: string) => {
            cy.get(`span#${state}`).wait(waitTime).click();
            ergodic(stage, person, state);
          });
          cy.get(`span#STOPPED`).wait(waitTime).click();
        });
        cy.get(`span#${testPersons[testPersons.length - 1]}`).click();
      });
      cy.get(`span#INTEGRITY`).click();
    });

    it('Plan 排序测试', () => {
      cy.get('#flash-tab-' + flashName)
        .click()
        .wait(1000);

      const lenList: number[] = [];
      sortNameList.forEach((name: string) => {
        cy.get(`span#${name}`).click().wait(1000);
        cy.get('div#plan-list-collapse-body')
          .then(($el) => {
            return $el[0].querySelectorAll('.plan-item-body').length;
          })
          .then((length) => {
            cy.log(`${name}: ${length}`);
            lenList.push(length);
          });
      });
      cy.then(() => {
        const setList = Array.from(new Set(lenList));
        cy.log(`lenList length: ` + setList.length);
        if (setList.length > 1) {
          cy.log('排序后plan列表长度不一致！');
          cy.debug();
        }
      });
    });
  });
});

const ergodic = function (stage: string, person: string, state: string) {
  cy.get('div#plan-list-collapse-body')
    .then(($el) => {
      return $el[0].querySelectorAll('.plan-item-body').length;
    })
    .then((length) => {
      cy.log('select length: ' + length);
      if (length > 0) {
        cy.get('div.plan-item-body').each((item, index, list) => {
          // Returns the elements from the cy.get command
          cy.wrap(item).then(($subj) => {
            const planMsg = $subj[0].dataset.planMsg || '';
            if (
              !planMsg.includes(stage) &&
              !planMsg.includes(person.replace('-', '.')) &&
              !planMsg.includes(state)
            ) {
              cy.log('stage: ' + stage);
              cy.log('person: ' + person.replace('-', '.'));
              cy.log('state: ' + state);
              cy.log('planMsg: ' + planMsg);
              cy.log('检测到错误信息！');
              cy.debug();
            }
          });

          // // .should('have.class', 'plan-item-body')
          // //     .and('have.attr', 'data-plan-msg')
        });
      }
    });
};
