package com.yeestor.work_order.controller.review;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.dingtalk.api.DingTalkFeignClient;
import com.yeestor.dingtalk.model.AuthParamsVO;
import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.entity.analysis.OrderFailAnalysisEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.req.BugConnectParams;
import com.yeestor.work_order.model.http.req.ReviewFinishParams;
import com.yeestor.work_order.model.http.req.ReviewStartRequestParams;
import com.yeestor.work_order.model.http.req.SaveFailAnalysisParams;
import com.yeestor.work_order.model.http.resp.OrderReviewDetailVO;
import com.yeestor.work_order.model.http.resp.order.BugInfo;
import com.yeestor.work_order.model.http.resp.review.ErrDiskInfo;
import com.yeestor.work_order.model.http.resp.review.OrderErrInfo;
import com.yeestor.work_order.model.http.resp.review.ReviewVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.repository.analysis.FailAnalysisRepository;
import com.yeestor.work_order.repository.OrderBugRepository;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.order.DocumentService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.review.ReviewService;
import com.yeestor.work_order.service.role.RoleService;
import com.yeestor.work_order.service.zentao.ZenTaoService;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.zentao.model.resp.BugInfoResp;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.transaction.Transactional;
import javax.validation.Valid;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/review/")
@Api(tags = {"Review"}, value = "Review 相关接口。包含在review过程中需要的数据。")
public class ReviewController {


    private final WorkOrderRepository orderRepository ;
    private final DingTalkFeignClient dingTalkFeignClient ;

    private final OrderService orderService ;

    private final DocumentService documentService ;
    private final FlashService flashService ;
    private final RoleService roleService ;
    private final NotificationService notificationService ;
    private final FailAnalysisRepository failAnalysisRepository ;
    private final OrderFlashRepository orderFlashRepository ;

    private final OrderBugRepository bugRepository ;

    private final ZenTaoService zenTaoService;
    private final DataChangeListener dataChangeListener ;

    private final ReviewService reviewService;


    @GetMapping("/auth/params")
    @ApiOperation(value = "获取Auth的参数", notes = "获取Auth的参数")
    public HandleResp<AuthParamsVO> fetchJsAuthParams(
            @ApiParam("当前页面的URL") @RequestParam(name = "url") String url,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        return dingTalkFeignClient.fetchJsAuthParams("WO",url);
    }

    @GetMapping("/{orderId}/{flash}/errdisk")
    @ApiOperation(value = "获取工单的ErrDisk 信息", notes = "获取工单的ErrDisk 信息")
    public HandleResp<OrderErrInfo> fetchErrDiskInfo(
            @ApiParam("工单id") @PathVariable("orderId") long orderId,
            @ApiParam("Flash 批次") @PathVariable("flash") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) throws JsonProcessingException {
        if ( !orderRepository.existsById(orderId) ){
            return HandleResp.failed("工单不存在");
        }

        // 解析 ErrDisk 文件
        OrderErrInfo orderErrInfo = documentService.parseOrderErrDisk(orderId);
        if (orderErrInfo == null){
            return HandleResp.failed("ErrDisk解析失败！");
        }

        List<OrderFlashEntity> flashEntityList = orderFlashRepository.findAllByOrderId(orderId);

        List<OrderFailAnalysisEntity> failAnalysisList = failAnalysisRepository.findAllByOrderIdAndFlashAndType(
                orderId,
                flash,
                0
        );
        if(!failAnalysisList.isEmpty()){
            ObjectMapper objectMapper = new ObjectMapper();
            OrderFailAnalysisEntity analysisEntity = failAnalysisList.get(0);
            String result = analysisEntity.getResult();

            HashMap<String,ErrDiskInfo> errDiskInfo = orderErrInfo.getErrDiskInfo();

            HashMap<String, HashMap<String, String> > data = objectMapper.readValue(result,
                    new TypeReference<HashMap<String, HashMap<String, String>>>() {
                    });
            for (OrderFlashEntity flashEntity : flashEntityList) {
                String f = flashEntity.getFlash();
                if(data.containsKey(f)){
                    HashMap<String, String> flashData = data.get(f);
                    ErrDiskInfo diskInfo = errDiskInfo.get(f);
                    for (String key : flashData.keySet()) {
                        diskInfo.getStatistics().stream().filter(i -> i.getKey().equalsIgnoreCase(key))
                                .findFirst()
                                .ifPresent(i -> i.setMsg(flashData.get(key)));
                    }
                }
            }
        }

        return HandleResp.ok(orderErrInfo, "获取错误磁盘信息成功");
    }



    @GetMapping("/{orderId}/detail")
    @ApiOperation(value = "获取工单的测试小结", notes = "获取工单的测试小结,Review 详情")
    @SneakyThrows
    public HandleResp<OrderReviewDetailVO> fetchOrderReviewDetail(
            @ApiParam("工单id") @PathVariable("orderId") long orderId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        if ( !orderRepository.existsById(orderId) ){
            return HandleResp.failed("工单不存在");
        }
        // 工单实体类
//        WorkOrderEntity orderEntity = orderRepository.findById(orderId).orElse(null);
//        assert orderEntity != null;
//
//        // 检查工单的状态是否正处于等待发起review的状态。
//        if(orderEntity.getStatus() != WorkOrderEntity.Status.WAITING_FOR_REVIEW){
//            return HandleResp.failed("工单不处于等待发起review的状态");
//        }

        // 封装返回结果
        OrderReviewDetailVO orderReviewDetailVO = new OrderReviewDetailVO();

        orderReviewDetailVO.setSummaryInfo(documentService.parseOrderSummary(orderId));

        return HandleResp.ok(orderReviewDetailVO, "获取小结成功");
    }

    @PostMapping("/fail/analysis/{orderId}/{flash}/start")
    @ApiOperation(value = "发起fail分析请求", notes = "由测试人员发起的fail 分析，会给到研发这边一个提示。")
    @Transactional
    public HandleResp<String> sendFailAnalysisRequest(
            @ApiParam("工单ID") @PathVariable("orderId") long orderId,
            @ApiParam("Flash") @PathVariable("flash") String flash,
            @ApiParam("研发人员钉钉ID") @RequestParam("uid") String uid,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "发起fail分析" ;
        LogUtils.setOrderAndFlashTracePoint(orderId,flash, operate);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(), operate,Permission.START_FAIL);


        flashService.startFailAnalysis(flashEntity,uid, userDetail);
        // 给研发人员发送通知。
        notificationService.sendFailAnalysisNotification(orderEntity,flashEntity, Collections.singletonList(uid));

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.FLASH_ANALYSIS_STARTED)
                        .orderId(orderId)
                        .flash(flash)
                .build());

        LogUtils.clearTracePoint();
        // 工单进入fail分析状态，等待研发人员进行fail分析。
        return HandleResp.ok(null ,"发送请求成功");
    }

    @Transactional
    @PostMapping("/fail/analysis/{orderId}/{flash}/finish")
    @ApiOperation(value = "完成或者跳过fail分析", notes = "完成fail分析或者跳过fail 分析，会给到测试人员一个提示。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", allowableValues = "0,1" , dataTypeClass = Integer.class, value = "完成fail 类型，0表示完成，1表示跳过", required = true ,paramType= "body"),
            @ApiImplicitParam(name = "msg", dataTypeClass = String.class, value = "原因 , 和data 二选一并填", paramType= "body"),
            @ApiImplicitParam(name = "data", dataTypeClass = HashMap.class, value = "原因和测试项目的键值对", paramType= "body"),
    })
    public HandleResp<String> saveFailAnalysis(
            @ApiParam("工单ID") @PathVariable("orderId") long orderId,
            @ApiParam("Flash") @PathVariable("flash") String flash,
            @RequestBody SaveFailAnalysisParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = params.getType() == 0 ? "完成fail分析" : "跳过fail分析" ;
        LogUtils.setOrderTracePoint(orderId, operate);
        log.debug("saveFailAnalysis orderId:{} flash:{} params:{} ",orderId,flash,params);

        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        // 跳过fail分析
        if(
                (params.getType() == 1 && flashEntity.getStatus() != OrderFlashEntity.Status.WAITING_FOR_FAIL_ANALYSIS)
                || (params.getType() == 0 && flashEntity.getStatus() != OrderFlashEntity.Status.FAIL_ANALYSIS_STARTED)
        ) {
            log.warn("用户：{} {}失败，该工单不是等待fail分析状态，当前状态为：{}", userDetail.getUsername(), operate, flashEntity.getStatus());
            return HandleResp.failed("工单的状态: " + flashEntity.getStatus() + " 不支持支持" +operate+ "操作！");
        }

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(), operate,Permission.FINISH_FAIL);

        OrderDetailEntity orderDetailEntity = orderService.findOrderDetailOrElseThrow(orderEntity.getId());
        reviewService.finishFail(orderDetailEntity, orderEntity, flashEntity, params, userDetail, operate);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.FLASH_WAIT_REVIEW)
                .orderId(orderId)
                .flash(flash)
                .build());

        LogUtils.clearTracePoint();
        // 保存用户的fail分析结果
        return   HandleResp.ok(null, "保存成功");
    }



    // 发起review 请求 ， 需要人员信息，时间等。
    @Transactional
    @PostMapping("/{orderId}/{flash}/start")
    @ApiOperation(value = "发起review 请求", notes = "发起review 或者不发起review")
    public HandleResp<String> startReviewRequest(
            @ApiParam("工单ID") @PathVariable("orderId") long orderId,
            @ApiParam("Flash") @PathVariable("flash") String flash,
            @RequestBody ReviewStartRequestParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        log.debug("startReviewRequest body:{}" ,params);

        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        String operate = params.getType() == 0 ? "发起review" : "跳过review" ;
        LogUtils.setOrderTracePoint(orderId, operate);

        log.info("用户：{} {}!", userDetail.getUsername(), operate);
        roleService.checkPermission(userDetail,orderEntity.getSubProduct(), operate,Permission.START_TEST);
        if( flashEntity.getStatus() != OrderFlashEntity.Status.WAITING_FOR_REVIEW) {
            log.warn("用户：{} {}失败，该工单不是待review状态，当前状态为：{}", userDetail.getUsername(), operate, flashEntity.getStatus());
            return HandleResp.failed("工单不处于可以发起Review的阶段！");
        }

        if( params.getType() == 0 ) {
            // 发起review。
            reviewService.startReview(orderEntity, flashEntity, params, userDetail);

            dataChangeListener.onDataChange(DataChangeEvent.builder()
                    .type(DataChangeEvent.Type.FLASH_REVIEW_STARTED)
                    .orderId(orderId)
                    .flash(flash)
                    .build());
            LogUtils.clearTracePoint();
            return HandleResp.ok(null, "处理完成！");
        }
        else if( params.getType() == 1 ) {
            // 不发起review。
            OrderDetailEntity orderDetailEntity = orderService.findOrderDetailOrElseThrow(orderId);
            reviewService.skipReview(orderDetailEntity, orderEntity, flashEntity, params, userDetail);

            dataChangeListener.onDataChange(DataChangeEvent.builder()
                    .type(DataChangeEvent.Type.FLASH_COMPLETED)
                    .orderId(orderId)
                    .flash(flash)
                    .build());
            LogUtils.clearTracePoint();
            //TODO 保存review 的详细信息。
            return HandleResp.ok(null, "处理完成！");
        }

        LogUtils.clearTracePoint();
        return HandleResp.failed("参数错误！");
    }

    // 完成review 请求，通过结果：通过，不通过 , 需要对应的备注信息。
    @Transactional
    @PostMapping("/{orderId}/{flash}/finish")
    @ApiOperation(value = "完成review 请求", notes = "完成Review")
    public HandleResp<String> completeReview(
            @ApiParam("工单ID") @PathVariable("orderId") long orderId,
            @ApiParam("Flash") @PathVariable("flash") String flash,
            @RequestBody ReviewFinishParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        log.info("completeReview body:{}" ,params);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId,flash);
        OrderDetailEntity orderDetailEntity = orderService.findOrderDetailOrElseThrow(orderEntity.getId());
        if(orderEntity.getStatus() == WorkOrderEntity.Status.REVOKED){
            return HandleResp.failed("工单已撤销，不允许进行Review！");
        }

        reviewService.finishReview(orderDetailEntity, orderEntity, flashEntity, params, userDetail);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.FLASH_COMPLETED)
                .orderId(orderId)
                .flash(flash)
                .build());

        return HandleResp.ok(null, "review completed!");
    }


    // 完成review 请求，通过结果：通过，不通过 , 需要对应的备注信息。
    @Transactional
    @PostMapping("/bugs/connect")
    @ApiOperation(value = "连接禅道bug到工单中的Flash批次中", notes = "关联禅道bug ,最小的粒度应该为样片")
    public HandleResp<String> connect(
            @Valid @RequestBody BugConnectParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        // 查询工单是否存在
        WorkOrderEntity orderEntity = orderRepository.findById(params.getOrderId())
                .orElseThrow(()-> new DataNotFoundException("id为"+params.getOrderId()+"的工单不存在"));
        String operate = "关联禅道bug" ;
        LogUtils.setOrderTracePoint(params.getOrderId(), operate);
        log.info("用户 {} 发起{}的请求",userDetail.getUsername(),operate);

        // 检查权限
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate,Permission.START_TEST,Permission.OPERATE_PLAN);

        BugInfoResp bugResp = zenTaoService.fetchBugInfo(params.getBugId());
        BugInfo bugInfo = BugInfo.toModel(bugResp);

        OrderBugEntity orderBugEntity = bugInfo.convert2Entity(params) ;
        orderBugEntity.setCreatedBy(userDetail.getUid());
        orderBugEntity.setCreatedPerson(userDetail.getUsername());
        bugRepository.save(orderBugEntity) ;

        log.info("成功将禅道bug: #{}[{}] 绑定至Flash批次:{}",bugInfo.getId(),bugInfo.getTitle(),params.getFlash());
        LogUtils.clearTracePoint();
        return HandleResp.ok(null,"");
    }

    // review 信息查询
    @Transactional
    @GetMapping("/{orderId}/{flash}/reviewinfo")
    @ApiOperation(value = "获取当前批次review记录信息")
    public HandleResp<ReviewVO> fetchReviewInfo(
            @ApiParam("工单ID") @PathVariable("orderId") long orderId,
            @ApiParam("Flash") @PathVariable("flash") String flash
    ){
        ReviewVO info = reviewService.fetchInfo(orderId, flash);
        return HandleResp.ok(info, "获取成功");
    }

    // review 人员增加
    @Transactional
    @PostMapping("/{orderId}/{flash}/review/person")
    @ApiOperation(value = "增加review会议人员")
    public HandleResp<ReviewVO> changeReview(
            @ApiParam("工单ID") @PathVariable("orderId") long orderId,
            @ApiParam("Flash") @PathVariable("flash") String flash,
            @ApiParam("修改后的人员名单") @RequestParam("personList") String personList
    ){
        String operate = "修改参会人员列表" ;
        LogUtils.setOrderAndFlashTracePoint(orderId, flash, operate);
        reviewService.changePersonList(orderId, flash, personList);
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "修改成功！");
    }

}
