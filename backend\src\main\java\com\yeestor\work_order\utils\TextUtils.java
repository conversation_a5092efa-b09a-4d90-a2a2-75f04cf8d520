package com.yeestor.work_order.utils;

import com.yeestor.work_order.exception.ErrorImportOrderException;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;

import java.util.Collections;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.yeestor.work_order.utils.Const.SUB_PRODUCT_EMMC;
import static com.yeestor.work_order.utils.Const.SUB_PRODUCT_UFS;
import static com.yeestor.work_order.utils.Const.SUB_PRODUCT_IND_EMMC;
import static com.yeestor.work_order.utils.Const.SUB_PRODUCT_IND_UFS;

@Slf4j
public class TextUtils {
    private TextUtils(){}

    public static String parseMpToolVersion(String fullVersion) {
        String regex = "^[^_|\\\\s]*[\\\\s_]([^_]*)_.*$";
        String regex1 = "^.*(V[^_]*).*$" ;
        Matcher matcher = Pattern.compile(regex, Pattern.MULTILINE).matcher(fullVersion);
        Matcher matcher1 = Pattern.compile(regex1, Pattern.MULTILINE).matcher(fullVersion);
        String v = null ;
        if(matcher.matches()){
            v = matcher.group(1);

            if(fullVersion.toUpperCase().contains("V") && !v.toUpperCase().contains("V") && matcher1.matches()) {
                v = matcher1.group(1);
            }
        }
        else if (matcher1.matches()) {
            v = matcher1.group(1);
        }
        if(fullVersion.contains(".") && !v.contains(".")){
            Matcher matcher2 = Pattern.compile("^.*(V\\d+(\\.\\d+)+)_.*$", Pattern.MULTILINE).matcher(fullVersion);
            if (matcher2.matches()) {
                v = matcher2.group(1);
            }
        }
        if(v != null ){
            v= v.replace("MPTools", "")
                    .replace("MPTool", "")
                    .trim();

            if(v.contains("(")){
                v = v.substring(0,v.indexOf("(")) ;
            }
        }
        return v;
    }


    /**
     * 工单号构成
     * @param prefix 前缀
     * @param chip 主控
     * @param toolType 量产工具类型
     * @param version 版本号
     * @param shortFlashName Flash型号
     * @param count 此前导入的版本数量
     * @param fwVersion fw号
     * @param productName 产品名称
     * @return 工单号
     */
    public static String buildOrderNo(
            String prefix,
            String chip,
            String toolType,
            String version,
            String shortFlashName,
            Integer count,
            String fwVersion,
            String productName
    ){
        if (version == null) {
            throw new ErrorImportOrderException("导入工单失败, 版本号信息异常:" + version + "！");
        }
        StringBuilder sb = new StringBuilder();
        if (prefix != null) {
            sb.append(prefix);
        }

        if (!(chip.toLowerCase().startsWith("ys"))) {
            chip = "YS" + chip;
        } else {
            chip = chip.toUpperCase();
        }
        // chip长度为8位，不足则在后面补# 到8位
        if (chip.length() < 8) {
            chip = chip + String.join("", Collections.nCopies(8 - chip.length(), "#"));
        }
        // chip长度超过8位，则截取前8位
        else if (chip.length() > 8) {
            // 如果长度超过8位，则抛出异常，并提示处理
            throw new ErrorImportOrderException("导入工单失败,版本主控超过8个字符,请检查主控:" + chip + "！");
        }
        sb.append(chip);

        if (toolType.length() < 2) {
            toolType = toolType + String.join("", Collections.nCopies(2 - toolType.length(), "#"));
        } else {
            toolType = toolType.substring(0, 2);
        }

        sb.append(toolType.toUpperCase());
        if (fwVersion.length() < 8) {
            fwVersion = fwVersion + String.join("", Collections.nCopies(8 - fwVersion.length(), "#"));
        } else {
            fwVersion = fwVersion.substring(0, 8);
        }

        // 是否是eMMC、UFS、工业级eMMC、工业级UFS
        boolean isEM = SUB_PRODUCT_EMMC.equalsIgnoreCase(productName)
                || SUB_PRODUCT_UFS.equals(productName)
                || SUB_PRODUCT_IND_EMMC.equalsIgnoreCase(productName)
                || SUB_PRODUCT_IND_UFS.equals(productName);
        sb.append(fwVersion.toUpperCase()).append("#");   // 使用#进行分隔
        version = Optional.of(version)
                .map(s -> {
                    if (isEM && !s.contains(".")) {
                        return s;
                    }
                    // 截取以点为分隔符的最后两个字符串
                    String[] split = s.split("\\.");
                    if (split.length >= 2) {
                        return split[split.length - 2] + split[split.length - 1];
                    }
                    return null;
                })
                .map(s -> {
                    // 版本为6位，不足则在后面补# 到6位
                    if (s.length() < 6) {
                        return s + String.join("", Collections.nCopies(6 - s.length(), "#"));
                    } else if (s.length() > 6) {
                        // 版本为6位，超过则截取前6位
                        return s.substring(0, 6);
                    }
                    return s;
                })
                .orElseThrow(() -> new ErrorImportOrderException("版本号格式存在问题，buildOrderNo失败!"));
        sb.append(version);

        // Flash信息，为12位，不足则在后面补# 到12位
        String flashStr = shortFlashName;
        if (flashStr.length() < 12) {
            flashStr = flashStr + String.join("", Collections.nCopies(12 - flashStr.length(), "#"));
        } else if (flashStr.length() > 12) {
            // Flash信息，超过则截取前12位
            flashStr = flashStr.substring(0, 12);
        }
        sb.append(flashStr);

        sb.append(DateTime.now().toString("yy"));
        if (count != null) {
            sb.append(String.format("%04d", count + 1));
        }
        return sb.toString();
    }

}
