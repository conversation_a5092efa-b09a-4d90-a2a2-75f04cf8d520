package com.yeestor.work_order.utils;

import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.model.rms.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.http.client.reactive.ClientHttpRequest;
import org.springframework.http.client.reactive.ClientHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.*;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.time.Duration;
import java.util.*;

@Slf4j
@Component
public class RMSApis {
    private final WebClient rmsWebClient;

    static class BufferingDecorator extends ClientHttpRequestDecorator {

        private BufferingDecorator(ClientHttpRequest delegate) {
            super(delegate);
        }

        @NotNull
        @Override
        public Mono<Void> writeWith(@NotNull Publisher<? extends DataBuffer> body) {
            return DataBufferUtils.join(body).flatMap(buffer -> {
                getHeaders().setContentLength(buffer.readableByteCount());
                return super.writeWith(Mono.just(buffer));
            });
        }

    }

    /**
     * 构造函数,用来初始化WebClient
     */
    @Autowired
    public RMSApis(Environment environment) {
        this(environment.getActiveProfiles()[0]) ;
    }

    public RMSApis(String profile){
        this.rmsWebClient = WebClient.builder()
                .baseUrl("http://ereport.yeestor.com")
                .defaultHeader("SERVER-TYPE", profile)
                .filter((request, next) -> {
                    if (MediaType.MULTIPART_FORM_DATA.includes(request.headers().getContentType())) {
                        return next.exchange(
                                ClientRequest.from(request)
                                        .body(
                                                (outputMessage, context) -> request.body().insert(new BufferingDecorator(outputMessage), context)
                                        )
                                        .build()
                        );
                    } else {
                        return next.exchange(request);
                    }
                })
                .build();
    }

    /**
     * 通过 产品线, 产品, 版本类型 来获取计划列表
     *
     * @param product     产品线
     * @param subProduct  产品
     * @param versionType 版本类型，
     * @return 计划列表
     */
    public List<PlanModel> getPlanList(String product, String subProduct, String versionType) {
        HandleResp<List<PlanModel>> rsp = rmsWebClient.get()
                .uri(builder ->
                        builder.path("/wo/plan/list")
                                .queryParam("p", product)
                                .queryParam("sp", subProduct)
                                .queryParam("v", Optional.ofNullable(versionType).map(String::toLowerCase).orElse(""))
                                .build()
                )

                .retrieve()
                .onStatus(HttpStatus::is5xxServerError, clientResponse ->
                        Mono.error(new RuntimeException("eReport服务器内部错误，状态码: " + clientResponse.statusCode())))

                .bodyToMono(new ParameterizedTypeReference<HandleResp<List<PlanModel>>>() {
                })
                .timeout(Duration.ofSeconds(10))
                .doOnError(e -> log.warn("getPlanList with {},{},{} got exception:{}", product, subProduct, versionType, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("getPlanList with %s,%s,%s failed", product, subProduct, versionType)))
                .block();
        log.info("getPlanList with {},{},{} got data :{}", product, subProduct, versionType, rsp);
        return Optional.ofNullable(rsp)
                .filter(HandleResp::isSuccess)
                .map(HandleResp::getData)
                .orElse(new ArrayList<>());
    }


    /**
     * 通过 产品线, 产品, 版本类型 来获取计划合集列表
     * @param product           产品线
     * @param subProduct        产品
     * @param versionType       版本类型，
     * @return 计划合集列表
     */
    public List<PlanGroupModel> getPlanGroups(String product, String subProduct,String versionType) {
        HandleResp<List<PlanGroupModel>> rsp = rmsWebClient.get()
                .uri(builder ->
                        builder.path("/wo/plan/group/list")
                                .queryParam("p", product)
                                .queryParam("sp", subProduct)
                                .queryParam("version", Optional.ofNullable(versionType).map(String::toLowerCase).orElse(""))
                                .build()
                )

                .retrieve()

                .bodyToMono(new ParameterizedTypeReference<HandleResp<List<PlanGroupModel>>>() {
                })
                .timeout(Duration.ofSeconds(10))
                .doOnError(e -> log.warn("getPlanGroups with {},{},{} got exception:{}", product, subProduct, versionType, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("getPlanGroups with %s,%s,%s failed", product, subProduct, versionType)))
                .block();
        log.info("getPlanGroups with {},{},{} got data :{}", product, subProduct, versionType, rsp);
        return Optional.ofNullable(rsp)
                .filter(HandleResp::isSuccess)
                .map(HandleResp::getData)
                .orElse(new ArrayList<>());
    }

    /**
     * 获取产品来获取设备列表
     *
     * @param subProduct 产品, 目前有 "U2" , "U3" , "SD"
     * @return 设备列表
     */
    public List<DeviceModel> getDeviceList(String subProduct) {
        Tuple2<List<DeviceModel>,List<DeviceModel>> tuple2 = getAllDeviceList(subProduct);
        return tuple2.getT1();
    }

    /**
     * 获取产品来获取设备列表
     *
     * @param subProduct 产品, 目前有 "U2" , "U3" , "SD"
     * @return 设备列表
     */
    public Tuple2<List<DeviceModel>,List<DeviceModel>> getAllDeviceList(String subProduct) {
        DeviceListResp rsp = rmsWebClient.get()
                .uri(builder ->
                        builder.path("/wo/device/list")
                                .queryParam("p", subProduct)
                                .build()
                )
                .retrieve()
                .bodyToMono(DeviceListResp.class)
                .doOnError(e -> log.warn("getAllDeviceList with [ {} ] got exception:{}", subProduct, e.getMessage()))
                .onErrorReturn(DeviceListResp.failed(String.format("getAllDeviceList with [ %s ] failed!", subProduct)))
                .block();

        log.info("getAllDeviceList with {} got data {}", subProduct, rsp);
        return Optional.ofNullable(rsp)
                .filter(DeviceListResp::isSuccess)
                .map(d -> Tuples.of(d.getData(),d.getWorkPcLst()))
                .orElse(Tuples.of(new ArrayList<>(),new ArrayList<>()));
    }


    /**
     * 通过 设备ip 以及工单号 来锁定设备。
     *
     * @param ipList 需要锁定的设备ip 列表
     * @param macList 需要锁定的设备mac 列表
     * @param no     工单号
     */
    public void lockDevice(
            List<String> ipList,
            List<String> macList,
            String no
    ) {
        HandleResp<String> rsp = rmsWebClient.post()
                .uri("/wo/device/lock")
                .body(BodyInserters.fromValue(OrderLockParams.builder()
                        .ipList(ipList)
                        .macList(macList)
                        .no(no)
                        .build()
                ))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<String>>() {
                })
                .doOnError(e -> log.warn("lockDevice with  {}-{},{} got exception:{}", ipList,macList, no, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("lockDevice with [ (%s-%s),%s ] failed!", String.join(",", ipList),String.join(",", macList), no)))
                .block();

        log.info("lockDevice with ipList:{} - macList:{},no:{} got data: {}", ipList, macList, no, rsp);
    }


    /**
     * 通过 设备ip 以及工单号 来解锁设备。
     *
     * @param ipList 需要解锁的设备ip 列表
     * @param macList 需要解锁的设备mac 列表
     * @param no     工单号
     */
    public void unlockDevice(
            List<String> ipList,
            List<String> macList,
            String no
    ) {
        HandleResp<String> rsp = rmsWebClient.post()
                .uri("/wo/device/unlock")
                .body(BodyInserters.fromValue(OrderLockParams.builder()
                        .ipList(ipList)
                        .macList(macList)
                        .no(no)
                        .build()
                ))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<String>>() {
                })
                .doOnError(e -> log.warn("unlockDevice with  {}-{},{} got exception:{}", ipList,macList, no, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("unlockDevice with [ (%s-%s),%s ] failed!", String.join(",", ipList),String.join(",", macList), no)))
                .block();

        log.info("unlockDevice with ipList:{} - macList:{},no:{} got data: {}", ipList, macList, no, rsp);
    }

    /**
     * 调用RMS的接口，开始测试对应的Plan以及对应的Device
     *
     * @param params 开始测试Plan的参数
     */
    public HandleResp<PlanTestRespModel> startTestPlan(PlanTestParams params) {
        log.info("startTestPlan with {}", params);

        if("SATA".equalsIgnoreCase(params.getProduct())){
            params.setProduct("SSD");
        }
        log.info(
                "Product:{} testPerson:{} order no:{} plan name:{}  macList:{}",
                params.getProduct(),
                params.getUsername(),
                params.getNo(),
                params.getPlanName(),
                params.getMacList()
        );
        HandleResp<PlanTestRespModel> rsp = rmsWebClient.post()
                .uri("/wo/plan/test")
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<PlanTestRespModel>>() {
                })
                .doOnError(e -> log.warn("startTestPlan with {} got exception:{}", params, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("startTestPlan with [%s ] failed!", params)))
                .block();

        log.info("startTestPlan with {}'s {} got data:{}", params.getNo(), params.getPlanName(), rsp);
        return rsp;
    }

    /**
     * 调用RMS的接口，停止测试对应的Plan以及对应的Device
     *
     * @param params 停止测试Plan的参数
     */
    public HandleResp<PlanTestRespModel> stopTestPlan(StopTestParams params) {
        HandleResp<PlanTestRespModel> rsp = rmsWebClient.post()
                .uri("/wo/plan/stop")
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<PlanTestRespModel>>() {
                })
                .doOnError(e -> log.warn("stopTestPlan with {} got exception:{}", params, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("stopTestPlan with [%s ] failed!", params)))
                .block();
        log.info("stopTestPlan with {} got data:{}", params, rsp);
        return rsp;
    }

    /**
     * 调用RMS的接口，创建工单。以一个FLash 批次为一个RMS工单
     *
     * @param params OrderCreateParams 创建工单的参数
     */
    public void createOrder(OrderCreateParams params) {

        HandleResp<String> resp = rmsWebClient.post()
                .uri("/wo/order/create")
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .body(BodyInserters.fromFormData(params.toFormData()))
                .retrieve()
                .onStatus(HttpStatus::is5xxServerError, clientResponse ->
                        Mono.error(new RuntimeException("eReport服务器内部错误，状态码: " + clientResponse.statusCode())))
                .bodyToMono(new ParameterizedTypeReference<HandleResp<String>>() {
                })
                .doOnError(e -> log.warn("createOrder with {} got exception:{}", params, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("createOrder with [%s ] failed!", params)))
                .block();

        log.info("createOrder with {} got data:{}", params, resp);
    }

    /**
     * 调用RMS的关闭工单接口。
     *
     * @param orderFlashNo RMS 工单号
     * @param reason       关闭原因
     */
    public void closeOrder(String orderFlashNo, String reason) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>() ;
        params.add("no", orderFlashNo);
        params.add("reason", reason);

        HandleResp<String> resp = rmsWebClient.post()
                .uri("/wo/order/close")
                .body(BodyInserters.fromFormData(params))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<String>>() {
                })
                .doOnError(e -> log.warn("closeOrder with {},{} got exception:{}", orderFlashNo, reason, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("closeOrder with [ %s, %s ] failed!", orderFlashNo, reason)))
                .block();
        log.info("closeOrder with {},{} got data:{}", orderFlashNo, reason, resp);
    }


    /**
     * 调用RMS的上传报告接口
     *
     * @param type         报告类型
     * @param orderFlashNo RMS 工单号
     * @param product      产品
     * @param plan         对应的Plan
     * @param user         当前登录用户
     * @param files        报告文件
     * @return 上传报告的结果
     */
    public Map<String, String> uploadReport(int type, String orderFlashNo, String product, String plan, String user, List<Resource> files) {
        MultipartBodyBuilder builder = new MultipartBodyBuilder();
        builder.part("type", type);
        builder.part("no", orderFlashNo);
        builder.part("p", product);
        if (type == 0) {
            builder.part("plan", plan);
        }
        builder.part("user", user);
        for (Resource file : files) {
            builder.part("file", file);
        }
        HandleResp<HashMap<String, String>> resp = rmsWebClient.post()
                .uri("/wo/report/upload")
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromMultipartData(builder.build()))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<HashMap<String, String>>>() {
                })
                .doOnError(e -> log.warn("uploadReport with {},{},{},{},{},{} got exception:{}", type, orderFlashNo, product, plan, user, files, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("uploadReport with [ %s, %s, %s, %s, %s, %s ] failed!", type, orderFlashNo, product, plan, user, files)))
                .block();

        log.info("uploadReport with {},{},{},{},{} got data:{}", type, orderFlashNo, plan, user, files, resp);
        return Optional.ofNullable(resp).map(HandleResp::getData).orElse(new HashMap<>());
    }


    /**
     * 合并RMS 工单中的报告。
     *
     * @param orderFlashNo RMS 中的工单号
     * @param subProduct   子产品 ,SD ,U2， U3 等。
     */
    public boolean mergeOrderReport(String orderFlashNo, String subProduct) {

        log.info("mergeOrderReport with {},{} ", orderFlashNo, subProduct);
        HandleResp<String> resp = rmsWebClient.get()
                .uri(builder -> builder.path("/wo/report/merge")
                                .queryParam("no", orderFlashNo)
                                .queryParam("p", subProduct)
                                .build())
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<String>>() {})
                .doOnError(e -> log.warn("mergeOrderReport with {},{}  got exception:{}", orderFlashNo, subProduct, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("mergeOrderReport with [ %s, %s ] failed!", orderFlashNo, subProduct)))
                .block();

        log.info("mergeOrderReport with {},{} got data: {}", orderFlashNo, subProduct, resp);
        return Optional.ofNullable(resp).map(HandleResp::isSuccess).orElse(false) ;
    }

    /**
     * 通过mac地址来获取设备的样片上盘情况
     * @param macList mac地址
     * @return 设备样片列表
     */
    public Map<String, List<DeviceInfo>> getDeviceDiskInfo(
            List<String> macList,
            String orderNo
    ) {
        HandleResp<Map<String, List<DeviceInfo>>> rsp = rmsWebClient.post()
                .uri("/wo/device/disk_new")
                .body(BodyInserters.fromValue(PlanDeviceDiskParams.builder()
                        .macLst(macList)
                        .orderNo(orderNo)
                        .build()
                ))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<Map<String, List<DeviceInfo>>>>() {
                })
                .doOnError(e -> log.warn("get device diskInfo with {} got exception:{}", macList, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("get device diskInfo with [ (%s) ] failed!", String.join(",", macList))))
                .block();

        log.info("getDeviceDiskInfo with macList {}, orderNo: {} got data: {}", macList, orderNo, rsp);
        return Optional.ofNullable(rsp).map(HandleResp::getData).orElse(new HashMap<>());
    }

    /**
     * 电脑开机
     * @param params 电脑开机参数
     * @return 结果信息
     */
    public HandleResp<DeviceControlResp> startupDevice(DeviceControlParams params) {
        HandleResp<DeviceControlResp> rsp = rmsWebClient.post()
                .uri("/wo/device/startup")
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<DeviceControlResp>>() {
                })
                .doOnError(e -> log.warn("startupDevice with {} got exception:{}", params, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("startupDevice with [%s ] failed!", params)))
                .block();
        log.info("startupDevice with {} got data:{}", params, rsp);
        return rsp;
    }

    /**
     * 电脑关机
     * @param params 电脑关机参数
     * @return 结果信息
     */
    public HandleResp<DeviceControlResp> shutdownDevice(DeviceControlParams params) {
        HandleResp<DeviceControlResp> rsp = rmsWebClient.post()
                .uri("/wo/device/shutdown")
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<DeviceControlResp>>() {
                })
                .doOnError(e -> log.warn("shutdownDevice with {} got exception:{}", params, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("shutdownDevice with [%s ] failed!", params)))
                .block();
        log.info("shutdownDevice with {} got data:{}", params, rsp);
        return rsp;
    }

    /**
     * 通过 产品线, 产品, 版本类型 来获取计划列表
     *
     * @param product     产品线
     * @param subProduct  产品
     * @return 计划列表
     */
    public List<CaseModel> getTerminalCaseList(String product, String subProduct) {
        HandleResp<List<CaseModel>> rsp = rmsWebClient.get()
                .uri(builder ->
                        builder.path("/wo/terminal/case/list")
                                .queryParam("p", product)
                                .queryParam("sp", subProduct)
                                .build()
                )
                .retrieve()
                .onStatus(
                        HttpStatus::is5xxServerError,
                        clientResponse -> Mono.error(new RuntimeException("eReport服务器内部错误，状态码: " + clientResponse.statusCode()))
                )
                .bodyToMono(new ParameterizedTypeReference<HandleResp<List<CaseModel>>>() {
                })
                .timeout(Duration.ofSeconds(10))
                .doOnError(e -> log.warn("getTerminalCaseList with {},{} got exception:{}", product, subProduct, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("getTerminalCaseList with %s,%s failed", product, subProduct)))
                .block();

        return Optional.ofNullable(rsp)
                .filter(HandleResp::isSuccess)
                .map(HandleResp::getData)
                .orElse(new ArrayList<>());
    }

    /**
     * 通过 产品线, 产品, 版本类型 来获取计划列表
     *
     * @param product     产品线
     * @param subProduct  产品
     * @param tool 工具名称，
     * @return 计划列表
     */
    public List<TerminalPlanModel> getTerminalPlanList(String product, String subProduct, String tool) {
        HandleResp<List<TerminalPlanModel>> rsp = rmsWebClient.get()
                .uri(builder ->
                        builder.path("/wo/terminal/plan/list")
                                .queryParam("p", product)
                                .queryParam("sp", subProduct)
                                .queryParam("tool", Optional.ofNullable(tool).map(String::toLowerCase).orElse(""))
                                .build()
                )
                .retrieve()
                .onStatus(
                        HttpStatus::is5xxServerError,
                        clientResponse -> Mono.error(new RuntimeException("eReport服务器内部错误，状态码: " + clientResponse.statusCode()))
                )
                .bodyToMono(new ParameterizedTypeReference<HandleResp<List<TerminalPlanModel>>>() {
                })
                .timeout(Duration.ofSeconds(10))
                .doOnError(e -> log.warn("getTerminalPlanList with {},{},{} got exception:{}", product, subProduct, tool, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("getTerminalPlanList with %s,%s,%s failed", product, subProduct, tool)))
                .block();

        return Optional.ofNullable(rsp)
                .filter(HandleResp::isSuccess)
                .map(HandleResp::getData)
                .orElse(new ArrayList<>());
    }

    /**
     * 通过 产品线, 产品, 版本类型 来获取计划合集列表
     *
     * @param product     产品线
     * @param subProduct  产品
     * @param tool 版本类型，
     * @return 计划合集列表
     */
    public List<TerminalPlanGroupModel> getTerminalPlanGroups(String product, String subProduct, String tool) {
        HandleResp<List<TerminalPlanGroupModel>> rsp = rmsWebClient.get()
                .uri(builder ->
                        builder.path("/wo/terminal/plan/group/list")
                                .queryParam("p", product)
                                .queryParam("sp", subProduct)
                                .queryParam("tool", Optional.ofNullable(tool).map(String::toLowerCase).orElse(""))
                                .build()
                )
                .retrieve()

                .bodyToMono(new ParameterizedTypeReference<HandleResp<List<TerminalPlanGroupModel>>>() {
                })
                .timeout(Duration.ofSeconds(10))
                .doOnError(e -> log.warn("getTerminalPlanGroups with {},{},{} got exception:{}", product, subProduct, tool, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("getTerminalPlanGroups with %s,%s,%s failed", product, subProduct, tool)))
                .block();

        return Optional.ofNullable(rsp)
                .filter(HandleResp::isSuccess)
                .map(HandleResp::getData)
                .orElse(new ArrayList<>());
    }

    /**
     * 获取产品来获取终端平台列表
     *
     * @param subProduct 产品, 目前有 "eMMC"
     * @return 设备列表
     */
    public List<TerminalDeviceModel> getAllPlatformList(String subProduct) {
        HandleResp<List<TerminalDeviceModel>> rsp = rmsWebClient.get()
                .uri(builder ->
                        builder.path("/wo/terminal/device/list")
                                .queryParam("p", subProduct)
                                .build()
                )
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<List<TerminalDeviceModel>>>() {
                })
                .doOnError(e -> log.warn("getAllPlatformList with [ {} ] got exception:{}", subProduct, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("getAllPlatformList with [ %s ] failed!", subProduct)))
                .block();

        log.info("getAllPlatformList with {} got data {}", subProduct, rsp);
        return Optional.ofNullable(rsp)
                .filter(HandleResp::isSuccess)
                .map(HandleResp::getData)
                .orElse(new ArrayList<>());
    }

    /**
     * 终端平台加锁
     * @param terminalList 需要加锁的终端平台设备
     * @param username 锁定人员
     * @param no 锁定工单
     */
    public void lockTerminalPlatform(
            List<TerminalModel> terminalList,
            String username,
            String no
    ) {
        HandleResp<String> rsp = rmsWebClient.post()
                .uri("/wo/terminal/lock")
                .body(BodyInserters.fromValue(TerminalOrderLockParams.builder()
                        .terminalList(terminalList)
                        .username(username)
                        .no(no)
                        .build()
                ))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<String>>() {
                })
                .doOnError(e -> log.warn("lockPlatform with {}-{} got exception:{}", no, terminalList, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("lockPlatform with [ %s ] failed!", no)))
                .block();

        log.info("lockPlatform with terminalList: {},no:{} got data: {}", terminalList, no, rsp);
    }

    /**
     * 终端平台解锁
     * @param terminalList 需要解锁的终端平台设备
     * @param username 执行解锁人员
     * @param no 锁定工单
     */
    public void unlockTerminalPlatform(
            List<TerminalModel> terminalList,
            String username,
            String no
    ) {
        HandleResp<String> rsp = rmsWebClient.post()
                .uri("/wo/terminal/unlock")
                .body(BodyInserters.fromValue(TerminalOrderLockParams.builder()
                        .terminalList(terminalList)
                        .username(username)
                        .no(no)
                        .build()
                ))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<String>>() {
                })
                .doOnError(e -> log.warn("unlockPlatform with {}-{} got exception:{}", no, terminalList, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("lockTerminal with [ %s ] failed!", no)))
                .block();

        log.info("unlockPlatform with terminalList: {},no:{} got data: {}", terminalList, no, rsp);
    }

    public HandleResp<TerminalPlatformStatusResp> stopTestTerminalPlan(TerminalPlanStopParams params) {
        log.info("stopTestTerminalPlan with {}", params);

        HandleResp<TerminalPlatformStatusResp> rsp = rmsWebClient.post()
                .uri("/wo/terminal/plan/stop")
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<TerminalPlatformStatusResp>>() {
                })
                .doOnError(e -> log.warn("stopTestTerminalPlan with {} got exception:{}", params, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("stopTestTerminalPlan with [%s ] failed!", params)))
                .block();

        log.info("stopTestTerminalPlan with {} got data:{}", params.getNo(), rsp);
        return rsp;
    }

    public HandleResp<TerminalPlatformStatusResp> startTestTerminalPlan(TerminalPlanTestParams params) {
        log.info("startTestTerminalPlan with {}", params);

        HandleResp<TerminalPlatformStatusResp> rsp = rmsWebClient.post()
                .uri("/wo/terminal/plan/test")
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<HandleResp<TerminalPlatformStatusResp>>() {
                })
                .doOnError(e -> log.warn("startTestTerminalPlan with {} got exception:{}", params, e.getMessage()))
                .onErrorReturn(HandleResp.failed(String.format("startTestTerminalPlan with [%s ] failed!", params)))
                .block();

        log.info("startTestTerminalPlan with {} got data:{}", params.getNo(), rsp);
        return rsp;
    }



}
