package com.yeestor.work_order.utils;

import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class TextUtilsTest {

    @Test
    void parseMpToolVersion() {
        String version = TextUtils.parseMpToolVersion("05.924_HQT3801A") ;
        assertEquals(null,version);
        String s = Optional.of("YS9085HQ_MPToolV8.00.01.05.924_HQT3801A(ALL_CAP_PSLC)_Release").map(TextUtils::parseMpToolVersion).orElse("123");
        assertEquals("V8.00.01.05.924",s);
        String s1 = Optional.of("05.924_HQT3801A").map(TextUtils::parseMpToolVersion).orElse(s);
        assertEquals(s1,s);
    }
}