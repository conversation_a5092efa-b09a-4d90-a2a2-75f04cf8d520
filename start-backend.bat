@echo off
echo ========================================
echo eSee 后端服务启动脚本
echo ========================================

cd /d "%~dp0backend"

echo 检查 Java 环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到 Java 环境，请确保已安装 JDK 11+
    pause
    exit /b 1
)

echo.
echo 检查 Maven 环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到 Maven 环境，请确保已安装 Maven 3.6+
    pause
    exit /b 1
)

echo.
echo 正在编译项目...
mvn clean compile -Dmaven.test.skip=true
if %errorlevel% neq 0 (
    echo 错误：项目编译失败，请检查依赖和网络连接
    pause
    exit /b 1
)

echo.
echo 正在启动后端服务...
echo 服务地址：http://localhost:8793
echo API 文档：http://localhost:8793/doc.html
echo 健康检查：http://localhost:8793/actuator/health
echo.
echo 按 Ctrl+C 停止服务
echo ========================================

@echo off
chcp 65001 >nul
echo ========================================
echo eSee Backend Service Startup (JDK 11)
echo ========================================

REM Find and set JDK 11 path
echo Looking for JDK 11 installation...

REM Common JDK 11 installation paths
if exist "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
    goto :found_jdk
)
if exist "C:\Program Files\Java\jdk-11.0.27\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\Java\jdk-11.0.27"
    goto :found_jdk
)
if exist "C:\Program Files\OpenJDK\jdk-11.0.27\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\OpenJDK\jdk-11.0.27"
    goto :found_jdk
)
if exist "C:\Program Files\AdoptOpenJDK\jdk-*********-hotspot\bin\java.exe" (
    set "JAVA_HOME=C:\Program Files\AdoptOpenJDK\jdk-*********-hotspot"
    goto :found_jdk
)

echo ERROR: JDK 11 not found, please set JAVA_HOME manually
pause
exit /b 1

:found_jdk
set "PATH=%JAVA_HOME%\bin;%PATH%"
echo Found JDK 11: %JAVA_HOME%
echo.
echo Current Java version:
java -version
echo.
echo JAVA_HOME: %JAVA_HOME%
echo.

echo Starting backend service...
echo Service URL: http://localhost:8793
echo API Docs: http://localhost:8793/doc.html
echo Health Check: http://localhost:8793/actuator/health
echo.
echo Press Ctrl+C to stop service
echo ========================================

echo Changing to backend directory...
cd /d "%~dp0backend"
if not exist "pom.xml" (
    echo ERROR: pom.xml not found in backend directory
    pause
    exit /b 1
)

echo Starting backend with debug support...
echo Debug port: 5005
echo.
mvn spring-boot:run "-Dspring.profiles.active=dev" "-Dspring-boot.run.jvmArguments=-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"

pause
