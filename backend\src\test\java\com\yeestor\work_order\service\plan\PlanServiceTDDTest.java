package com.yeestor.work_order.service.plan;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.device.DeviceTestHistoryEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.req.Person;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.repository.PlanDeviceRepository;
import com.yeestor.work_order.repository.device.DeviceTestHistoryRepository;
import com.yeestor.work_order.service.device.QueueService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import reactor.util.concurrent.Queues;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@SpringBootTest
@ActiveProfiles("prod")
class PlanServiceTDDTest {


    @Autowired
    PlanService planService;

    @Autowired
    OrderService orderService;

    @Autowired
    FlashService flashService;

    @Autowired
    QueueService queueService;

    @Autowired
    OrderPlanRepository planRepository;

    @Autowired
    PlanDeviceRepository planDeviceRepository;
    @Autowired
    DeviceTestHistoryRepository deviceTestHistoryRepository;

    @Autowired
    TestHistoryService testHistoryService;


    @Test
    void check(){
//        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(3375);
//        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(3375, "YS6297BB-WDx4-B_512GB");
//        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(68970);
//        planService.checkDeviceReleaseStatus(orderEntity, flashEntity, planEntity);

//        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(4028);
//        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(4028, "N38A_4X2_4P_1024GB");
//        log.info("orderEntity subProduct: {} ", orderEntity.getSubProduct());
//        List<Person> testerIdList = new ArrayList<>(flashEntity.getTesters());
//        // 查出这个批次下所有的plan列表。
//        List<OrderPlanEntity> allPlanEntityList = planService.fetchAllPlanByFlash(orderEntity.getId(), flashEntity.getFlash());
//
//        // 此次分配进程中涉及到的Flash批次下所有可分配Plan
//        List<OrderPlanEntity> canAssignList = queueService.fetchCanAssignPlanList(allPlanEntityList);
//
//        // Flash 剩余样片数量
//        final AtomicInteger leftFlashNum = new AtomicInteger(flashEntity.getLeftNum());
//        log.info("执行预分配前共有{}颗样片", leftFlashNum.get());
//        List<OrderPlanEntity> assignList = new ArrayList<>();
//
//        testerIdList.forEach(user -> {
//            // 此次分配的进程中，某个测试人员剩余的Plan可分配额度
//            int leftPlanNum = planService.leftPlanCountByUserId(allPlanEntityList, user.getId());
//            log.info("user: {} leftPlanNum: {}", user.getName(), leftPlanNum);
//            // 此次分配进程中，某一个测试人员所有可分配的Plan
//            List<OrderPlanEntity> userCanAssignList = canAssignList.stream().filter(p -> user.getId().equals(p.getBelongTo())).collect(Collectors.toList());
//            log.info("此次Plan分配的可分配额度为0！");
//
//        });
//        log.info("assignList: {}", assignList);


        // 查询测试人员历史测试Plan使用的设备情况
//        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(68697);
//        log.info("planEntity: {}", planEntity);


       // 检查设备与测试人的关系
        long endTime = 1730686283000L;
        long startTime = 1719885600000L;
        List<OrderPlanEntity> planList = planRepository.findAllByStatusAndBelongToNotNull(OrderPlanEntity.Status.COMPLETED).stream()
                .filter(p -> p.getCreatedAt() >= startTime && p.getCreatedAt() < endTime)
                .filter(p -> !p.getBelongTo().isEmpty()).collect(Collectors.toList());

        log.info("planListSize: {}", planList.size());
        final AtomicInteger num = new AtomicInteger();
        planList.forEach(p -> {
            WorkOrderEntity workOrderEntity = orderService.findOrderOrElseThrow(p.getOrderId());
            String subProduct = workOrderEntity.getSubProduct();
            List<PlanDeviceEntity> deviceEntityList = planDeviceRepository.findAllByPlanIdAndStatusInAndMacNotNull(p.getId(), PlanDeviceEntity.FINISHED_STATUS_LIST);
            num.addAndGet(deviceEntityList.size());
            log.info("deviceEntityList: {}", deviceEntityList);
            deviceEntityList.forEach(dev -> {
                if (deviceTestHistoryRepository.existsBySubProductAndPlanNameAndMacAndUserId(subProduct, p.getName(), dev.getMac(), p.getBelongTo())) {
                    DeviceTestHistoryEntity entity = deviceTestHistoryRepository.findBySubProductAndPlanNameAndMacAndUserId(subProduct, p.getName(), dev.getMac(), p.getBelongTo())
                            .orElseThrow(() -> new DataNotFoundException("未找到对应数据"));
                    log.info("exists dev no: {} in plan: {}", dev.getNo(), p.getName());
                    entity.setUseNum(entity.getUseNum() + 1);
                    deviceTestHistoryRepository.save(entity);
                } else {
                    DeviceTestHistoryEntity historyEntity = new DeviceTestHistoryEntity().toEntity(p, dev, workOrderEntity.getProduct(), subProduct);
                    log.info("not find dev no: {} in plan: {} historyEntity: {}", dev.getNo(), p.getName(), historyEntity);
                    deviceTestHistoryRepository.save(historyEntity);
                }
            });
        });
        log.info("sumail chen all device size: {} ", num.get());


//        testHistoryService.findDeviceTestHistoryByPlan(63570L);


    }
}
