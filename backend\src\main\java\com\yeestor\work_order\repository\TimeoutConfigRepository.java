package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.config.TimeoutConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface TimeoutConfigRepository extends JpaRepository<TimeoutConfigEntity, Long> {
    List<TimeoutConfigEntity> findAllByProductAndSubProduct(String product, String subProduct);

    Optional<TimeoutConfigEntity> findByProductAndSubProductAndTypeAndPhase(String product, String subProduct, TimeoutConfigEntity.Type type, String phase);
}
