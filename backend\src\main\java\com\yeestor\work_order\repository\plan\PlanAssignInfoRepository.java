package com.yeestor.work_order.repository.plan;

import com.yeestor.work_order.entity.plan.PlanAssignInfoEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface PlanAssignInfoRepository extends JpaRepository<PlanAssignInfoEntity, Long> {
    Optional<PlanAssignInfoEntity> findByPlanId(long planId);

    List<PlanAssignInfoEntity> findAllByPlanIdIn(List<Long> planI);

}

