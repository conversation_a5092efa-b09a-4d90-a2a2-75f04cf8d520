package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "TerminalCaseStartParams", description = "plan下case启动回调参数")
public class TerminalCaseStartParams {
    @NotBlank(message = "工单号不能为空")
    @ApiModelProperty(value = "工单号", required = true)
    private String orderNo;

    @NotBlank(message = "Plan名不能为空")
    @ApiModelProperty(value = "计划名称", required = true)
    private String plan;

    @NotBlank(message = "case名不能为空")
    @ApiModelProperty(value = "case名称", required = true)
    private String caseName;

    @JsonAlias("case_step")
    @Min(value = 0, message = "case在Plan中的索引不能小于0")
    @ApiModelProperty(value = "Case在Plan中的索引", required = true)
    private Integer caseStep;

    @ApiModelProperty(value = "设备MAC地址", required = true)
    private String mac;

    @ApiModelProperty(value = "测试终端平台序列号", required = true)
    private String number;
}
