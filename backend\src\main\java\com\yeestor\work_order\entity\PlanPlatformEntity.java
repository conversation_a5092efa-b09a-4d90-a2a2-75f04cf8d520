package com.yeestor.work_order.entity;

import com.yeestor.entity.BaseEntity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity(name = "PlanPlatform")
@Table(name = "wo_plan_platform")
public class PlanPlatformEntity extends BaseEntity {

    public enum Status {
        /**
         * 已占用
         */
        OCCUPIED,

        /**
         * 正在运行中。
         */
        RUNNING,

        /**
         * 已结束 , 成功
         */
        FINISHED_SUCCESS,
        /**
         * 已结束 ， 失败
         */
        FINISHED_FAILED,

        /**
         * 已取消
         */
        CANCELED
    }

    public static final List<Status> RUN_STATUS_LIST = List.of(Status.OCCUPIED, Status.RUNNING);
    public static final List<Status> COMPLETED_STATUS_LIST = List.of(Status.FINISHED_SUCCESS, Status.CANCELED);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "order_id")
    private long orderId;

    @Column(name = "plan_id")
    private Long planId;

    /**
     * plan 的名称。主要用于快速查询。
     */
    @Column(name = "plan_name")
    private String planName;

    @Column(name = "ip")
    private String ip;

    /**
     * 设备的mac 地址
     */
    @Column(name = "mac")
    private String mac;

    /**
     * 设备的pc 编号。
     */
    @Column(name = "no")
    private String no;

    /**
     * 设备的位置
     */
    @Column(name = "position")
    private String position;

    /**
     * 平台名称【编号】
     */
    @Column(name = "name")
    private String name;

    /**
     * 手机平台序列号
     */
    @Column(name = "number")
    private String number;

    /**
     * 样品容量
     */
    @Column(name = "sample")
    private String sample;

    /**
     * 样品容量
     */
    @Column(name = "volume")
    private String volume;

    /**
     * 平台添加人员钉钉ID
     */
    @Column(name = "added_by")
    private String addedBy;

    /**
     * 平台添加人员名称
     */
    @Column(name = "added_p")
    private String addedPerson;

    /**
     * 设备的状态 , 可以是已经占用，已经结束，已经取消。
     */
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private Status status;

    /**
     * 测试的开始时间
     */
    @Column(name = "start_at")
    private Long startAt;

    @Column(name = "start_by")
    private String startBy;

    @Column(name = "start_p")
    private String startPerson;

    /**
     * 测试的结束时间
     */
    @Column(name = "end_at")
    private Long endAt;

    /**
     * 测试失败的原因。当测试失败时，这个字段会有值。
     */
    @Column(name = "fail_reason")
    private String failReason;

    /**
     * 停止测试或者取消测试会存在记录。
     */
    @Column(name = "stop_reason")
    private String stopReason;

    /**
     * 设备是否已经释放， 如果没有释放，这个值为空， 如果释放了，就是 释放的时间。
     */
    @Column(name = "release_at")
    private Long releaseAt;

    /**
     * 设备由谁来释放的，如果是人为操作的话，这个值为其 钉钉ID
     */
    @Column(name = "release_by")
    private String releaseBy;

    /**
     * 释放人的名称。
     */
    @Column(name = "release_p")
    private String releasePerson;

    /**
     * 人为手动终止测试的时间。
     */
    @Column(name = "terminate_at")
    private Long terminateAt;

    /**
     * 终止测试的人员，钉钉ID
     */
    @Column(name = "terminate_by")
    private String terminateBy;

    /**
     * 终止测试的人员 名称
     */
    @Column(name = "terminate_p")
    private String terminatePerson;

    @Transient
    public boolean isOccupied() {
        return this.status == Status.OCCUPIED;
    }

    @Transient
    public boolean isFailed() {
        return this.status == Status.FINISHED_FAILED;
    }

    @Transient
    public boolean isRelease() {
        return this.releaseAt != null;
    }

    @Transient
    public boolean isSamePlatform(String mac, String number) {
        return this.mac.equalsIgnoreCase(mac) && this.number.equalsIgnoreCase(number);
    }

    public PlanPlatformEntity duplicate() {
        PlanPlatformEntity entity = new PlanPlatformEntity();
        entity.id = id;
        entity.orderId = orderId;
        entity.planId = planId;
        entity.planName = planName;
        entity.ip = ip;
        entity.mac = mac;
        entity.no = no;
        entity.position = position;
        entity.name = name;
        entity.number = number;
        entity.volume = volume;
        entity.sample = sample;
        entity.status = status;
        entity.setCreatedAt(getCreatedAt());
        entity.setUpdatedAt(getUpdatedAt());
        return entity;
    }

    public PlanPlatformEntity duplicateEmpty() {
        PlanPlatformEntity entity = duplicate();

        entity.setFailReason(null);
        entity.setEndAt(null);

        entity.setStartAt(null);
        entity.setStartBy(null);
        entity.setStartPerson(null);

        entity.setTerminateAt(null);
        entity.setTerminateBy(null);
        entity.setTerminatePerson(null);

        entity.setReleaseAt(null);
        entity.setReleaseBy(null);
        entity.setReleasePerson(null);

        entity.setUpdatedAt(System.currentTimeMillis());
        return entity;

    }

}
