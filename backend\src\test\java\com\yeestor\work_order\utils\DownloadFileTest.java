package com.yeestor.work_order.utils;

import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

@Slf4j
class DownloadFileTest {

    @Test
    @DisplayName("使用未encode的url")
    void getSizeWithOriginUrl() {

        String url = "http://rms.yeestor.com/report/file_download/?product=SD&testNo=020Alpha_旺宏X8AA_LRF-6285DA-X8AA-A&dir=FinalReport&file=LRF-6285DA-AGX8AA-A(V2.0.0.12.020)Full Test Reports_2022.03.22.xlsx";
        url = url.replace(" ", "%20");

        int size = DownloadUtils.getSizeWithWebclient(url);
        log.info("is.available() = {}", size);
        Assertions.assertThat(size).isPositive();

    }


    @Test
    @DisplayName("使用encode的url")
    void getSizeWithEncodeUrl(){

        String url = "http://rms.yeestor.com/report/file_download/?product=SD&testNo=V2.0.0.10.430_Release_XYJGSA&file=SD%E6%B6%88%E8%B4%B9%E7%BA%A7%E6%B1%87%E6%80%BB%E6%8A%A5%E5%91%8A.xlsx" ;
        int size = DownloadUtils.getSizeWithWebclient(url);
        log.info("is.available() = {}", size);
        Assertions.assertThat(size).isPositive() ;
    }

    @Test
    @DisplayName("下载html类型的文件")
    void downloadHtmlFile(){
        String url = "https://www.baidu.com/";
        int size = DownloadUtils.getSizeWithWebclient(url);
        log.info("is.available() = {}", size);
        Assertions.assertThat(size).isZero();

    }

}

