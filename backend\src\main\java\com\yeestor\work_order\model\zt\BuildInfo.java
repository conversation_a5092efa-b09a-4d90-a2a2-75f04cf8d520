package com.yeestor.work_order.model.zt;

import lombok.Data;
import lombok.ToString;

/**
 * 禅道的版本列表
 */
@Data
@ToString(exclude = "desc")
public class BuildInfo {

    private int id;
    private int project;
    private int product;
    private int branch;
    private int execution;
    private String name;
    private String scmPath;
    private String filePath;
    private String date;
    private String[] stories;
    private int[] bugs;
    private UserInfo builder;
    private String desc;
    private boolean deleted;
    private String executionName;
    private String productName;
    private String productType;
    private String branchName;
}
