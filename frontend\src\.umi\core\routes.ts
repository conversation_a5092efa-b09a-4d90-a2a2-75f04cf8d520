// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'F:/Git/eSee/frontend/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@ant-design/pro-layout/es/PageLoading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "component": dynamic({ loader: () => import(/* webpackChunkName: '.umi__plugin-layout__Layout' */'F:/Git/eSee/frontend/src/.umi/plugin-layout/Layout.tsx'), loading: LoadingComponent}),
    "routes": [
      {
        "path": "/~demos/:uuid",
        "layout": false,
        "wrappers": [dynamic({ loader: () => import(/* webpackChunkName: 'wrappers' */'../dumi/layout'), loading: LoadingComponent})],
        "component": ((props) => dynamic({
          loader: async () => {
            const React = await import('react');
            const { default: getDemoRenderArgs } = await import(/* webpackChunkName: 'dumi_demos' */ 'F:/Git/eSee/frontend/node_modules/@umijs/preset-dumi/lib/plugins/features/demo/getDemoRenderArgs');
            const { default: Previewer } = await import(/* webpackChunkName: 'dumi_demos' */ 'dumi-theme-default/es/builtins/Previewer.js');
            const { usePrefersColor, context } = await import(/* webpackChunkName: 'dumi_demos' */ 'dumi/theme');

            return props => {
              
      const { demos } = React.useContext(context);
      const [renderArgs, setRenderArgs] = React.useState([]);

      // update render args when props changed
      React.useLayoutEffect(() => {
        setRenderArgs(getDemoRenderArgs(props, demos));
      }, [props.match.params.uuid, props.location.query.wrapper, props.location.query.capture]);

      // for listen prefers-color-schema media change in demo single route
      usePrefersColor();

      switch (renderArgs.length) {
        case 1:
          // render demo directly
          return renderArgs[0];

        case 2:
          // render demo with previewer
          return React.createElement(
            Previewer,
            renderArgs[0],
            renderArgs[1],
          );

        default:
          return `Demo ${props.match.params.uuid} not found :(`;
      }
    
            }
          },
          loading: () => null,
        }))()
      },
      {
        "path": "/_demos/:uuid",
        "redirect": "/~demos/:uuid"
      },
      {
        "__dumiRoot": true,
        "layout": false,
        "path": "/~docs",
        "wrappers": [dynamic({ loader: () => import(/* webpackChunkName: 'wrappers' */'../dumi/layout'), loading: LoadingComponent}), dynamic({ loader: () => import(/* webpackChunkName: 'wrappers' */'F:/Git/eSee/frontend/node_modules/dumi-theme-default/es/layout.js'), loading: LoadingComponent})],
        "routes": [
          {
            "path": "/~docs",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'README.md' */'F:/Git/eSee/frontend/README.md'), loading: LoadingComponent}),
            "exact": true,
            "meta": {
              "locale": "en-US",
              "order": null,
              "filePath": "README.md",
              "updatedTime": 1632807209000,
              "slugs": [
                {
                  "depth": 1,
                  "value": "Ant Design Pro",
                  "heading": "ant-design-pro"
                },
                {
                  "depth": 2,
                  "value": "Environment Prepare",
                  "heading": "environment-prepare"
                },
                {
                  "depth": 2,
                  "value": "Provided Scripts",
                  "heading": "provided-scripts"
                },
                {
                  "depth": 3,
                  "value": "Start project",
                  "heading": "start-project"
                },
                {
                  "depth": 3,
                  "value": "Build project",
                  "heading": "build-project"
                },
                {
                  "depth": 3,
                  "value": "Check code style",
                  "heading": "check-code-style"
                },
                {
                  "depth": 3,
                  "value": "Test code",
                  "heading": "test-code"
                },
                {
                  "depth": 2,
                  "value": "More",
                  "heading": "more"
                }
              ],
              "title": "Ant Design Pro"
            },
            "title": "Ant Design Pro"
          },
          {
            "path": "/~docs/components",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'components__index.md' */'F:/Git/eSee/frontend/src/components/index.md'), loading: LoadingComponent}),
            "exact": true,
            "meta": {
              "filePath": "src/components/index.md",
              "updatedTime": 1657242071000,
              "title": "业务组件",
              "sidemenu": false,
              "slugs": [
                {
                  "depth": 1,
                  "value": "业务组件",
                  "heading": "业务组件"
                },
                {
                  "depth": 2,
                  "value": "Footer 页脚组件",
                  "heading": "footer-页脚组件"
                },
                {
                  "depth": 2,
                  "value": "HeaderDropdown 头部下拉列表",
                  "heading": "headerdropdown-头部下拉列表"
                }
              ],
              "hasPreviewer": true,
              "group": {
                "path": "/~docs/components",
                "title": "Components"
              }
            },
            "title": "业务组件 - isee"
          },
          {
            "path": "/~docs/pages/work_order/components/life/life-tab/guide",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__components__life__lifeTab__guide.md' */'F:/Git/eSee/frontend/src/pages/work_order/components/life/lifeTab/guide.md'), loading: LoadingComponent}),
            "exact": true,
            "meta": {
              "filePath": "src/pages/work_order/components/life/lifeTab/guide.md",
              "updatedTime": 1661324595000,
              "slugs": [
                {
                  "depth": 2,
                  "value": "index.tsx 获取当前 Flash 详情页面的 Tab 栏",
                  "heading": "indextsx-获取当前-flash-详情页面的-tab-栏"
                }
              ],
              "title": "index.tsx 获取当前 Flash 详情页面的 Tab 栏",
              "group": {
                "path": "/~docs/pages/work_order/components/life/life-tab",
                "title": "Pages"
              }
            },
            "title": "index.tsx 获取当前 Flash 详情页面的 Tab 栏 - isee"
          },
          {
            "path": "/~docs/pages/work_order/components/life/life-title/guide",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__components__life__lifeTitle__guide.md' */'F:/Git/eSee/frontend/src/pages/work_order/components/life/lifeTitle/guide.md'), loading: LoadingComponent}),
            "exact": true,
            "meta": {
              "filePath": "src/pages/work_order/components/life/lifeTitle/guide.md",
              "updatedTime": 1661324595000,
              "slugs": [
                {
                  "depth": 2,
                  "value": "index.tsx 返回当前工单的测试状态下的工单列表中",
                  "heading": "indextsx-返回当前工单的测试状态下的工单列表中"
                }
              ],
              "title": "index.tsx 返回当前工单的测试状态下的工单列表中",
              "group": {
                "path": "/~docs/pages/work_order/components/life/life-title",
                "title": "Pages"
              }
            },
            "title": "index.tsx 返回当前工单的测试状态下的工单列表中 - isee"
          },
          {
            "path": "/~docs/services",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'services__README.md' */'F:/Git/eSee/frontend/src/services/README.md'), loading: LoadingComponent}),
            "exact": true,
            "meta": {
              "filePath": "src/services/README.md",
              "updatedTime": 1742203698000,
              "slugs": [
                {
                  "depth": 1,
                  "value": "服务API文档",
                  "heading": "服务api文档"
                },
                {
                  "depth": 2,
                  "value": "服务架构",
                  "heading": "服务架构"
                },
                {
                  "depth": 2,
                  "value": "API响应格式",
                  "heading": "api响应格式"
                },
                {
                  "depth": 2,
                  "value": "API端点列表",
                  "heading": "api端点列表"
                },
                {
                  "depth": 3,
                  "value": "用户服务",
                  "heading": "用户服务"
                },
                {
                  "depth": 3,
                  "value": "工单服务",
                  "heading": "工单服务"
                },
                {
                  "depth": 4,
                  "value": "通用API",
                  "heading": "通用api"
                },
                {
                  "depth": 4,
                  "value": "Flash API",
                  "heading": "flash-api"
                },
                {
                  "depth": 3,
                  "value": "审核服务",
                  "heading": "审核服务"
                },
                {
                  "depth": 3,
                  "value": "角色服务",
                  "heading": "角色服务"
                },
                {
                  "depth": 3,
                  "value": "Ebuild服务",
                  "heading": "ebuild服务"
                },
                {
                  "depth": 2,
                  "value": "请求流程",
                  "heading": "请求流程"
                },
                {
                  "depth": 2,
                  "value": "数据转换流程",
                  "heading": "数据转换流程"
                },
                {
                  "depth": 2,
                  "value": "认证流程",
                  "heading": "认证流程"
                },
                {
                  "depth": 2,
                  "value": "错误处理",
                  "heading": "错误处理"
                },
                {
                  "depth": 2,
                  "value": "常见响应代码",
                  "heading": "常见响应代码"
                },
                {
                  "depth": 2,
                  "value": "使用示例",
                  "heading": "使用示例"
                }
              ],
              "title": "服务API文档",
              "group": {
                "path": "/~docs/services",
                "title": "Services"
              }
            },
            "title": "服务API文档 - isee"
          },
          {
            "path": "/~docs/pages/work_order/components/life/life-tab",
            "meta": {},
            "exact": true,
            "redirect": "/~docs/pages/work_order/components/life/life-tab/guide"
          },
          {
            "path": "/~docs/pages/work_order/components/life/life-title",
            "meta": {},
            "exact": true,
            "redirect": "/~docs/pages/work_order/components/life/life-title/guide"
          }
        ],
        "title": "isee",
        "component": (props) => props.children
      },
      {
        "path": "/user",
        "layout": false,
        "routes": [
          {
            "path": "/user",
            "routes": [
              {
                "name": "login",
                "path": "/user/login",
                "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__user__Login' */'F:/Git/eSee/frontend/src/pages/user/Login'), loading: LoadingComponent}),
                "exact": true
              },
              {
                "name": "login",
                "path": "/user/test/login",
                "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__user__TestLogin' */'F:/Git/eSee/frontend/src/pages/user/TestLogin'), loading: LoadingComponent}),
                "exact": true
              },
              {
                "name": "login",
                "path": "/user/auth",
                "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__user__LoginCallBack' */'F:/Git/eSee/frontend/src/pages/user/LoginCallBack'), loading: LoadingComponent}),
                "exact": true
              },
              {
                "name": "login",
                "path": "/user/ding",
                "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__user__ding' */'F:/Git/eSee/frontend/src/pages/user/ding'), loading: LoadingComponent}),
                "exact": true
              },
              {
                "name": "login",
                "path": "/user/account",
                "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__user__account' */'F:/Git/eSee/frontend/src/pages/user/account'), loading: LoadingComponent}),
                "exact": true
              },
              {
                "name": "login",
                "path": "/user/onload",
                "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__user__onload' */'F:/Git/eSee/frontend/src/pages/user/onload'), loading: LoadingComponent}),
                "exact": true
              },
              {
                "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__404' */'F:/Git/eSee/frontend/src/pages/404'), loading: LoadingComponent}),
                "exact": true
              }
            ]
          },
          {
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__404' */'F:/Git/eSee/frontend/src/pages/404'), loading: LoadingComponent}),
            "exact": true
          }
        ]
      },
      {
        "path": "/report",
        "name": "report",
        "icon": "barChart",
        "routes": [
          {
            "name": "order",
            "path": "/report/order",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__OrderReportPage' */'F:/Git/eSee/frontend/src/pages/report/OrderReportPage'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "name": "flash",
            "path": "/report/flash",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__FlashReportPage' */'F:/Git/eSee/frontend/src/pages/report/FlashReportPage'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "name": "plan",
            "path": "/report/plan",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__PlanReportPage' */'F:/Git/eSee/frontend/src/pages/report/PlanReportPage'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "name": "device",
            "path": "/report/device",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__DeviceReportPage' */'F:/Git/eSee/frontend/src/pages/report/DeviceReportPage'), loading: LoadingComponent}),
            "exact": true
          },
          {
            "name": "zentao",
            "path": "/report/zentao",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__report__ZenTaoReportPage' */'F:/Git/eSee/frontend/src/pages/report/ZenTaoReportPage'), loading: LoadingComponent}),
            "exact": true
          }
        ]
      },
      {
        "name": "role",
        "icon": "partition",
        "path": "/role",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__role' */'F:/Git/eSee/frontend/src/pages/role'), loading: LoadingComponent}),
        "access": "user_canVIEW_ROLE_LIST",
        "exact": true
      },
      {
        "name": "work_order",
        "icon": "experiment",
        "path": "/work_order",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layouts__BasicLayout' */'F:/Git/eSee/frontend/src/layouts/BasicLayout'), loading: LoadingComponent}),
        "routes": [
          {
            "path": "/work_order/GE/list",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__product' */'F:/Git/eSee/frontend/src/pages/work_order/product'), loading: LoadingComponent}),
            "icon": "smile",
            "name": "general",
            "exact": true
          },
          {
            "path": "/work_order/GE/:productLine/:id",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__product__life' */'F:/Git/eSee/frontend/src/pages/work_order/product/life'), loading: LoadingComponent}),
            "hideInMenu": true,
            "exact": true
          },
          {
            "path": "/work_order/flash/insert/:product/:productLine/:id",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__insertFlash' */'F:/Git/eSee/frontend/src/pages/work_order/insertFlash'), loading: LoadingComponent}),
            "hideInMenu": true,
            "exact": true
          },
          {
            "path": "/work_order/:productLine/:orderId/:flashName/:planId/:planName/:type/detail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__planDetail__content' */'F:/Git/eSee/frontend/src/pages/work_order/planDetail/content'), loading: LoadingComponent}),
            "hideInMenu": true,
            "exact": true
          },
          {
            "path": "/work_order/:subProduct/:orderId/:flashName/:planId/terminal/detail",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__terminalPlanDetail__content' */'F:/Git/eSee/frontend/src/pages/work_order/terminalPlanDetail/content'), loading: LoadingComponent}),
            "hideInMenu": true,
            "exact": true
          },
          {
            "path": "/work_order/SSD/list",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__product' */'F:/Git/eSee/frontend/src/pages/work_order/product'), loading: LoadingComponent}),
            "icon": "smile",
            "name": "ssd",
            "exact": true
          },
          {
            "path": "/work_order/SSD/:productLine/:id",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__product__life' */'F:/Git/eSee/frontend/src/pages/work_order/product/life'), loading: LoadingComponent}),
            "hideInMenu": true,
            "exact": true
          },
          {
            "path": "/work_order/EM/list",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__product' */'F:/Git/eSee/frontend/src/pages/work_order/product'), loading: LoadingComponent}),
            "icon": "smile",
            "name": "em",
            "exact": true
          },
          {
            "path": "/work_order/EM/:productLine/:id",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__product__life' */'F:/Git/eSee/frontend/src/pages/work_order/product/life'), loading: LoadingComponent}),
            "hideInMenu": true,
            "exact": true
          },
          {
            "path": "/work_order/IND/list",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__product' */'F:/Git/eSee/frontend/src/pages/work_order/product'), loading: LoadingComponent}),
            "icon": "smile",
            "name": "indus",
            "exact": true
          },
          {
            "path": "/work_order/IND/:productLine/:id",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__work_order__product__life' */'F:/Git/eSee/frontend/src/pages/work_order/product/life'), loading: LoadingComponent}),
            "hideInMenu": true,
            "exact": true
          }
        ]
      },
      {
        "name": "machine",
        "icon": "Desktop",
        "path": "/machines",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layouts__BasicLayout' */'F:/Git/eSee/frontend/src/layouts/BasicLayout'), loading: LoadingComponent}),
        "routes": [
          {
            "path": "/machines/order",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__machineList__order' */'F:/Git/eSee/frontend/src/pages/machineList/order'), loading: LoadingComponent}),
            "name": "order",
            "exact": true
          },
          {
            "path": "/machines/rms",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__machineList__rms' */'F:/Git/eSee/frontend/src/pages/machineList/rms'), loading: LoadingComponent}),
            "name": "rms",
            "exact": true
          }
        ]
      },
      {
        "name": "mobile",
        "path": "/mobile",
        "hideInMenu": true,
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'layouts__BasicLayout' */'F:/Git/eSee/frontend/src/layouts/BasicLayout'), loading: LoadingComponent}),
        "routes": [
          {
            "path": "/mobile/:productLine/:product/:id/addplan",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'pages_mobile__planPage__addPage' */'F:/Git/eSee/frontend/src/pages_mobile/planPage/addPage'), loading: LoadingComponent}),
            "hideInMenu": true,
            "exact": true
          }
        ]
      },
      {
        "path": "/device/:pcNo",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__machine' */'F:/Git/eSee/frontend/src/pages/machine'), loading: LoadingComponent}),
        "layout": {
          "hideMenu": true,
          "hideNav": true,
          "hideFooter": false
        },
        "exact": true
      },
      {
        "path": "/index.html",
        "redirect": "/work_order/GE/list",
        "exact": true
      },
      {
        "path": "/",
        "redirect": "/work_order/GE/list",
        "exact": true
      },
      {
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__404' */'F:/Git/eSee/frontend/src/pages/404'), loading: LoadingComponent}),
        "exact": true
      }
    ]
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
