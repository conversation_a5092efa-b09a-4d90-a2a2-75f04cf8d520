package com.yeestor.work_order.service.product.impl;

import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.model.rms.DeviceModel;
import com.yeestor.work_order.service.product.ProductContext;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class USBProductService extends GeneralProductService
{

    private final ProductContext productContext;

    @PostConstruct
    private void init(){
        productContext.registerProductService("U2",this);
        productContext.registerProductService("U3",this);
    }

    /**
     * 处理测试所有样片的Plan的设备分配.
     * @param orderId 工单ID
     * @param planEntity Plan 实体
     * @param devices 可用设备列表
     *
     * @return 需要分配的设备列表
     */
    @Override
    public List<DeviceModel> handleTestAllPlan(long orderId, OrderPlanEntity planEntity, List<DeviceModel> devices){

        // 如果不是测试所有样片的Plan,则直接返回空列表
        if(!planEntity.isTestAll()){
            return new ArrayList<>();
        }

        // 找到这个批次下 第一个已经测试完成且测试所有样片的Plan.
        OrderPlanEntity firstTestAllPlan = planService.findFirstCompletedAndTestAllPlan(orderId,planEntity.getFlash());
        if(firstTestAllPlan == null){
            // 如果没有找到,则返回空列表
            return devices;
        }
        // 如果找到了,就去查询这个Plan用到的设备
        List<PlanDeviceEntity> usedDevices = deviceService.findDevicesByPlanId(planEntity.getId());
        // 如果devices 中包含了usedDevices中所有的设备,则返回这些设备.
        if(!usedDevices.stream().allMatch(d -> devices.stream().anyMatch(d2 -> d2.getIp().equals(d.getIp())))){
            return new ArrayList<>();
        }
        return devices.stream()
                .filter(d -> usedDevices.stream().anyMatch(d2 -> d2.getIp().equals(d.getIp())))
                .collect(Collectors.toList());
    }




}
