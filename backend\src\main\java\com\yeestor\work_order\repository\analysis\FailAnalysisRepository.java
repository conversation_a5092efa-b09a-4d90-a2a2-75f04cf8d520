package com.yeestor.work_order.repository.analysis;

import com.yeestor.work_order.entity.analysis.OrderFailAnalysisEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface FailAnalysisRepository extends JpaRepository<OrderFailAnalysisEntity, Long> {

    List<OrderFailAnalysisEntity> findAllByOrderIdAndFlashAndType(
            long orderId,
            String flash,
            int type
    ) ;

    List<OrderFailAnalysisEntity> findAllByOrderId(long id);

    Optional<OrderFailAnalysisEntity> findFirstByOrderIdAndFlash(long orderId, String flash);
}
