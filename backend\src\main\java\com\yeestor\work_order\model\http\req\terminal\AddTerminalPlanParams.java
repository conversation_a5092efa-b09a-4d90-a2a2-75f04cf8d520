package com.yeestor.work_order.model.http.req.terminal;

import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.model.http.req.Person;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel(value="AddTerminalPlanParams",description = "添加临时Plan的参数")
public class AddTerminalPlanParams {
    @Size(min = 1, max = 20, message = "标题长度为1~20")
    @ApiModelProperty("plan标题")
    private String title;

    @NotBlank(message = "Plan描述不能为空")
    @ApiModelProperty("plan 描述")
    private String desc;

    @ApiModelProperty("脚本")
    private List<String> caseList;

    @ApiModelProperty("属性")
    private List<String> attrList;

    @ApiModelProperty("plan 测试阶段")
    private OrderPlanEntity.Phase phase;

    @ApiModelProperty("产品线")
    private String product;

    @ApiModelProperty("产品")
    private String subProduct;

    @ApiModelProperty("测试负责人")
    private Person owner;

    @Min(value = 1, message = "无效的工单ID")
    @ApiModelProperty("操作时的工单ID")
    private long orderId;

    @NotBlank(message = "flash 批次号不能为空")
    @ApiModelProperty("flash批次")
    private String flash;
}
