package com.yeestor.work_order.service.job;

import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.service.device.RMSDeviceService;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.utils.LogUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class TimedShutdownDeviceJob implements Job {
    public final static String JOB_GROUP_NAME = "TimedShutdownDeviceJob";
    public static final String KEY_SUB_PRODUCT = "subProduct";
    public static final String KEY_USER_NAME = "userName";
    public static final String KEY_FLASH = "flash";
    public static final String KEY_TITLE = "title";
    public static final String KEY_ORDER_ID = "orderId";
    public static final String KEY_PLAN_ID = "planId";
    public static final String KEY_MAC = "mac";
    private final RMSDeviceService rmsDeviceService;
    private final DeviceService deviceService;

    public static Map<String ,Object> buildJobDataMap(
            String subProduct,
            String userName,
            String flashName,
            String title,
            PlanDeviceEntity deviceEntity
    ) {
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put(KEY_SUB_PRODUCT, subProduct);
        dataMap.put(KEY_USER_NAME, userName);
        dataMap.put(KEY_FLASH, flashName);
        dataMap.put(KEY_TITLE, title);

        dataMap.put(KEY_ORDER_ID, deviceEntity.getOrderId());
        dataMap.put(KEY_PLAN_ID, deviceEntity.getPlanId());
        dataMap.put(KEY_MAC, deviceEntity.getMac());
        return dataMap;
    }

    public static String buildJobName(String subProduct ,String mac){
        return subProduct + "_" + mac + "_deviceShutdown";
    }

    @Override
    public void execute(JobExecutionContext context) {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();

        String subProduct = dataMap.getString(KEY_SUB_PRODUCT);
        String userName = dataMap.getString(KEY_USER_NAME);
        String flash = dataMap.getString(KEY_FLASH);
        String title = dataMap.getString(KEY_TITLE);
        if ( !dataMap.containsKey( KEY_ORDER_ID) ||  !dataMap.containsKey(KEY_PLAN_ID) || !dataMap.containsKey(KEY_MAC) ){
             log.info("{}定时器部分参数不存在！", JOB_GROUP_NAME);
             return;
        }
        long orderId = dataMap.getLong(KEY_ORDER_ID);
        long planId = dataMap.getLong(KEY_PLAN_ID);
        String mac = dataMap.getString(KEY_MAC);
        LogUtils.setOrderAndFlashTracePoint(orderId, flash, JOB_GROUP_NAME);

        log.info("execute:{}  map key:{} subProduct:{} userName:{} title:{} planId:{} mac:{}",
                context.getJobDetail().getKey().getName(),
                dataMap.getKeys(),
                subProduct,
                userName,
                title,
                 planId,
                mac
        );


        try {
            List<PlanDeviceEntity> devices = deviceService.findDevicesByPlanIdAndMacIn(planId, List.of(mac));
            if (devices.isEmpty()) {
                log.info("设备{}不在计划{}中，忽略", mac, planId);
                LogUtils.clearTracePoint();
                return;
            }
            // 取 最后创建的设备
            PlanDeviceEntity entity = devices.get(devices.size() - 1) ;
            log.info("倒计时结束，即将释放设备{}[{}]", entity.getNo(), entity.getMac());
            rmsDeviceService.shutdownDevice(subProduct, flash, title, userName, List.of(entity));
        } catch (DataNotFoundException e) {
            log.info("Device Not Found (No Matter): {}",e.getMessage());
        }
        LogUtils.clearTracePoint();
    }
}
