# WO数据库表结构分析文档

## 概述

本文档详细分析了数据库中所有以"wo_"开头的表，共发现35个表，主要涉及工单管理、设备控制、测试计划、Flash测试等业务领域。本文档基于实际代码库中的实体类定义和数据库结构，提供准确的字段注释和业务流程说明。

## 表概览

| 表名 | 记录数 | 主要用途 | 核心字段 | 中文说明 |
|------|--------|----------|----------|----------|
| wo_order | 5,764 | 工单主表 | id, no, product, sub_product, status, priority | 存储所有工单的基本信息，是整个系统的核心表 |
| wo_order_detail | 5,748 | 工单详情表 | order_id, cap, driver_version, requirement | 存储工单的技术详情和配置信息 |
| wo_order_plan | 66,355 | 测试计划表 | id, order_id, name, status, type | 管理每个工单下的具体测试计划 |
| wo_order_flash | - | Flash测试表 | id, order_id, flash, status, num | 管理Flash批次的测试状态和进度 |
| wo_plan_device | 112,167 | 计划设备分配表 | id, order_id, plan_id, no, ip, status | 管理测试计划与设备的分配关系 |
| wo_device_control | 13,492 | 设备控制记录表 | id, device_id, order_id, plan_id, operator | 记录设备的控制操作历史 |
| wo_device_lock_info | - | 设备锁定信息表 | id, device_id, locked, unlock_time | 管理设备的锁定状态，防止资源冲突 |
| wo_device_sample | - | 设备采样表 | id, device_id, samples, trace_id | 记录设备的采样数据和测试信息 |
| wo_device_disk | - | 设备磁盘表 | id, device_id, samples, test_num | 管理设备的磁盘相关信息和测试数据 |
| wo_review_info | - | 评审信息表 | id, order_id, description, persons | 存储测试结果的评审信息和参与人员 |

## 数据库表关系图

### 核心业务关系图

```mermaid
erDiagram
    wo_order {
        bigint id PK
        text no
        text product
        text sub_product
        text status
        bigint priority
        text version
        text build_by
        bigint start_at
        bigint end_at
    }

    wo_order_detail {
        bigint order_id FK
        text cap
        text driver_version
        text requirement
        text zt_product
        text zt_project
    }

    wo_order_flash {
        bigint id PK
        bigint order_id FK
        text order_flash_no
        text flash
        text status
        bigint num
        bigint left_num
        text test_by
        double_precision test_start_at
        double_precision test_end_at
    }

    wo_order_plan {
        bigint id PK
        bigint order_id FK
        text name
        text flash
        text status
        bigint type
        bigint test_num
        double_precision start_at
        double_precision end_at
    }

    wo_plan_device {
        bigint id PK
        bigint order_id FK
        bigint plan_id FK
        text no
        text ip
        text status
        bigint test_num
        text operator
    }

    wo_product_t {
        text product PK
        text name
    }

    wo_sub_product_t {
        text product FK
        text sub_product PK
    }

    %% 核心业务关系
    wo_order ||--|| wo_order_detail : "工单详情"
    wo_order ||--o{ wo_order_flash : "Flash批次"
    wo_order_flash ||--o{ wo_order_plan : "测试计划"
    wo_order_plan ||--o{ wo_plan_device : "设备分配"

    %% 产品分类关系
    wo_product_t ||--o{ wo_sub_product_t : "产品子类"
    wo_product_t ||--o{ wo_order : "产品工单"
```

### 设备管理关系图

```mermaid
erDiagram
    wo_order_flash {
        bigint id PK
        text order_flash_no
        text flash
        text status
    }

    wo_order_plan {
        bigint id PK
        bigint order_id FK
        text flash
        text name
        text status
    }

    wo_plan_device {
        bigint id PK
        bigint plan_id FK
        text no
        text ip
        text status
    }

    wo_device_control {
        bigint id PK
        bigint device_id
        bigint order_id FK
        bigint plan_id FK
        text flash
        text title
        text operator
    }

    wo_device_lock_info {
        bigint id PK
        bigint device_id
        bigint order_id FK
        bigint plan_id FK
        text locked_order_flash_no
        double_precision locked
        double_precision unlock_time
    }

    wo_device_sample {
        bigint id PK
        double_precision device_id
        text order_flash_no
        text plan_name
        double_precision is_success
    }

    wo_device_disk {
        bigint id PK
        bigint device_id
        bigint order_id FK
        bigint plan_id FK
        text flash
        text samples
    }

    %% Flash批次驱动的设备管理关系
    wo_order_flash ||--o{ wo_order_plan : "Flash下的计划"
    wo_order_plan ||--o{ wo_plan_device : "计划设备分配"
    wo_plan_device ||--o{ wo_device_control : "设备控制记录"
    wo_plan_device ||--o{ wo_device_lock_info : "设备锁定"
    wo_order_flash ||--o{ wo_device_sample : "Flash采样数据"
    wo_order_plan ||--o{ wo_device_disk : "计划磁盘数据"
```

### 评审和扩展业务关系图

```mermaid
erDiagram
    wo_order {
        bigint id PK
        text no
        text status
    }

    wo_order_flash {
        bigint id PK
        bigint order_id FK
        text flash
        text status
    }

    wo_review_info {
        bigint id PK
        bigint order_id FK
        text flash
        text description
        text persons
    }

    wo_review_item {
        bigint id PK
        bigint order_id FK
        text flash
        text item
        text result
    }

    wo_review_result {
        bigint id PK
        bigint order_id FK
        text flash
        bigint result
        text conclusion
    }

    wo_order_bug {
        bigint id PK
        bigint order_id FK
        text flash
        text bug_id
        text title
        bigint pri
    }

    wo_flash_analysis_history {
        bigint id PK
        bigint order_id FK
        text flash
        text detail_info
    }

    wo_order_reason {
        bigint id PK
        text belong_to
        text type
        text description
    }

    %% 评审和分析关系
    wo_order ||--o{ wo_review_info : "评审信息"
    wo_order_flash ||--o{ wo_review_item : "评审项目"
    wo_order_flash ||--o{ wo_review_result : "评审结果"
    wo_order_flash ||--o{ wo_order_bug : "测试Bug"
    wo_order_flash ||--o{ wo_flash_analysis_history : "分析历史"
    wo_order ||--o{ wo_order_reason : "工单原因"
    wo_order_flash ||--o{ wo_order_reason : "Flash原因"
```

### 表关系图说明

**关系图优化要点**：

1. **分层展示**：将复杂的表关系分为三个子图，提高可读性
   - 核心业务关系图：展示主要的业务流程
   - 设备管理关系图：专注于设备相关的操作
   - 评审和扩展业务关系图：展示质量控制和分析流程

2. **简化字段显示**：移除详细的中文注释，只保留核心字段，避免图表过于拥挤

3. **修正核心关系**：
   - **wo_order_flash 的核心地位**：Flash批次是实际的测试执行单元
   - **正确的层次关系**：工单 → Flash批次 → 测试计划 → 设备分配
   - **Flash驱动的业务流程**：大部分设备操作和测试活动都是基于Flash批次进行的

4. **业务数据验证**：
   - wo_order_flash 表有6,021条记录，覆盖4,874个不同工单
   - wo_plan_device 表有112,167条记录，是数据量最大的表
   - 设备控制、锁定等操作都通过 plan_id 和 flash 字段关联到具体的Flash批次

5. **实际业务流向**：
   - 工单创建后，会创建一个或多个Flash批次
   - 每个Flash批次下创建多个测试计划
   - 测试计划分配具体的设备资源
   - 所有的设备操作、采样、控制都基于计划和Flash批次进行

## 核心业务表详细分析

### 1. wo_order (工单主表)
**业务用途**: 存储所有工单的基本信息，是整个系统的核心表

**状态枚举** (基于WorkOrderEntity.Status):
- `CREATED` - 创建中：工单刚刚创建，等待测试主管确认信息
- `CONFIRMED_FLASH` - 待测试：测试主管已经确认了Flash信息，目前正在排队中
- `TESTING` - 测试中：工单已经开始测试
- `EVALUATING` - 评估中：工单评估中
- `COMPLETED` - 已完成：测试完成
- `REVOKED` - 已取消：工单已经被取消

**核心字段**:
- `id` (bigint): 主键，工单唯一标识
- `no` (text): 工单编号，格式如"YS6297##MP#########0067##0F26_QLC_1LU250395"
- `product` (text): 产品类型，如"GE"(通用存储)、"EM"(嵌入式存储)
- `sub_product` (text): 子产品类型，如"U2", "U3", "SD", "UFS"
- `status` (text): 工单状态，见上述状态枚举
- `priority` (bigint): 优先级，0-普通、1-中等、2-高
- `flash` (text): Flash型号信息
- `version` (text): 版本信息
- `version_type` (text): 版本类型
- `full_version` (text): 完整版本号

**人员和时间追踪字段**:
- 创建阶段: `created_at`, `build_by`, `build_p`, `build_at`
- 确认阶段: `confirm_at`, `confirm_by`, `confirm_p`
- 测试阶段: `start_at`, `ready_at`, `test_start_at`, `test_end_at`
- 结束阶段: `end_at`, `review_at`, `review_result`
- 撤销流程: `revoke_at`, `revoke_by`, `revoke_p`, `revoke_reason`
- 取消流程: `cancel_at`, `cancel_by`, `cancel_p`, `cancel_reason`
- 转换流程: `convert_at`, `convert_by`, `convert_p`, `convert_tool_path`

**路径和配置字段**:
- `mars_path` (text): Mars工具路径
- `mptool_path` (text): MPTool工具路径
- `plan_path` (text): 测试计划路径
- `scrcpy_path` (text): Scrcpy工具路径
- `scrcpy_plan` (text): Scrcpy计划配置

### 2. wo_order_detail (工单详情表)
**业务用途**: 存储工单的技术详情和配置信息，与工单主表一对一关系

**技术配置字段**:
- `order_id` (bigint): 关联工单ID，主键
- `cap` (text): 容量信息
- `driver_version` (text): 驱动版本
- `fw_svn_path` (text): 固件SVN路径
- `fw_svn_version` (text): 固件SVN版本
- `job_name` (text): 任务名称
- `mail_list` (text): 邮件列表
- `requirement` (text): 需求描述
- `test_point` (text): 测试点
- `version_log` (text): 版本日志
- `ci_json` (text): CI配置JSON

**禅道系统集成字段**:
- `zt_product` (text): 在禅道中的产品
- `zt_project` (text): 在禅道中的项目
- `zt_testtask` (text): 在禅道中的测试单
- `zt_build_id` (text): 禅道中的版本ID
- `zt_execution_id` (text): 禅道中的执行ID
- `generated_bug_num` (int): 当前禅道版本产生的bug数量
- `sv_bug_num` (int): 当前禅道版本测试验证产生的bug数量

### 3. wo_order_plan (测试计划表)
**业务用途**: 管理每个工单下的具体测试计划，支持多种测试阶段和类型

**状态枚举** (基于OrderPlanEntity.Status):
- `NEW` - 刚创建：Flash确认前
- `QUEUE` - 排队中：等待资源分配
- `READY` - 资源准备就绪：可以开始测试
- `CONFIRMED` - 资源分配完成：环境确认完毕
- `RUNNING` - 开始测试：正在执行中
- `COMPLETED` - 已完成：进入此状态后不能再改变
- `STOPPED` - 手动停止：被手动终止

**测试阶段枚举** (基于OrderPlanEntity.Phase):
- `ACCEPT` - 接收测试：验收阶段测试
- `VERIFY` - 验证测试：功能验证测试
- `INTEGRITY` - 完整测试：全面完整性测试

**计划类型**:
- `TYPE_AUTO` (0): 自动类型的Plan
- `TYPE_MANUAL` (1): 手动类型的Plan

**结束状态**:
- `END_STATUS_UNKNOWN` (0): 未知状态
- `END_STATUS_SUCCESS` (1): 成功，Plan下所有设备都成功测试完
- `END_STATUS_FAIL` (2): 失败，Plan下有某些设备测试失败
- `END_STATUS_TERMINATED` (3): 手动终止，Plan被手动停止

**核心字段**:
- `id` (bigint): 计划唯一标识
- `order_id` (bigint): 关联工单ID
- `name` (text): 计划名称，如"plan1"
- `feature` (text): 特性描述
- `status` (text): 计划状态，见上述状态枚举
- `phase` (text): 测试阶段，见上述阶段枚举
- `type` (bigint): 计划类型，0-自动、1-手动
- `priority` (int): 优先级
- `attrs` (text): 计划涉及的属性，以分号为分隔符
- `test_num` (bigint): 测试数量
- `test_all` (bigint): 总测试数量
- `end_status` (bigint): 结束状态，见上述结束状态定义

**执行控制字段**:
- `start_at/end_at` (double): 开始/结束时间
- `start_by/end_by` (text): 开始/结束操作人
- `start_p/end_p` (text): 开始/结束操作人部门
- `terminate_at/terminate_by` (double/text): 终止时间/操作人
- `terminate_p` (text): 终止操作人部门
- `confirmed_at/confirmed_by` (double/text): 确认时间/操作人
- `confirmed_p` (text): 确认操作人部门

### 4. wo_order_flash (Flash测试表)
**业务用途**: 管理Flash批次的测试状态和进度，每个工单可包含多个Flash批次

**状态枚举** (基于OrderFlashEntity.Status):
- `NEW` - 刚刚创建：等待资源
- `WAITING_FOR_START` - 等待开始测试：资源已分配
- `IN_PROGRESS` - 已经开始：正在测试中
- `WAITING_FOR_MERGE` - 测试完成：已经合并报告，等待报告状态变更回调
- `WAITING_FOR_REPORT` - 测试完成：等待上传报告
- `WAITING_FOR_FAIL_ANALYSIS` - 测试完成：等待进行fail分析
- `FAIL_ANALYSIS_STARTED` - FAIL分析开始：正在进行失败分析
- `WAITING_FOR_REVIEW` - 测试完成：等待review
- `REVIEW_STARTED` - Review已经开始：正在评审中
- `COMPLETED` - 已经完成：所有流程完成
- `CANCELLED` - 已经取消：批次被取消

**核心字段**:
- `id` (bigint): Flash批次唯一标识
- `order_id` (bigint): 关联工单ID
- `order_flash_no` (text): Flash批次+工单号组合的RMS工单号
- `flash` (text): Flash的批次信息，不是Flash ID
- `size` (text): Flash批次的容量
- `status` (text): Flash批次状态，见上述状态枚举
- `num` (int): 该批次Flash的数量
- `idx` (int): Flash在工单中的先后顺序，从小开始
- `left_num` (int): 剩余数量

**测试人员字段**:
- `test_by` (text): 测试人员ID
- `test_person` (text): 测试人员姓名
- `test_related_person` (text): 测试相关人员JSON格式

**时间控制字段**:
- `test_start_at/test_end_at` (double): 开始/结束测试时间
- `start_fail_at` (double): fail分析启动时间
- `complete_at` (double): flash批次结束时间，也是完成Review的时间

**失败处理字段**:
- `start_fail_by/start_fail_p` (text): 启动fail分析的人员/部门
- `fail_assign_to` (text): fail分析指定人，通常是构建人
- `fail_reason` (text): fail分析的原因，如果没有指定人则表示跳过

**特殊配置字段**:
- `auto_loc` (boolean): 这个flash批次是否需要自动定位
- `ce_count` (text): flash批次的CE数，当前仅eMMC需要填写

### 5. wo_plan_device (计划设备分配表)
**业务用途**: 管理测试计划与设备的分配关系，是设备资源管理的核心表

**状态枚举** (基于PlanDeviceEntity.Status):
- `OCCUPIED` - 已占用：设备已被分配但未确认
- `CONFIRMED` - 环境已确认：设备环境检查通过
- `RUNNING` - 正在运行中：设备正在执行测试
- `FINISHED_SUCCESS` - 已结束，成功：测试成功完成
- `FINISHED_FAILED` - 已结束，失败：测试失败结束
- `CANCELED` - 已取消：设备分配被取消

**核心字段**:
- `id` (bigint): 计划设备分配唯一标识
- `order_id` (bigint): 关联工单ID
- `plan_id` (bigint): 关联计划ID
- `plan_name` (text): 计划名称
- `no` (text): 设备编号
- `ip/mac` (text): 设备网络信息
- `position` (text): 设备位置
- `status` (text): 设备状态，见上述状态枚举
- `operator` (text): 操作员
- `owner` (text): 设备所有者

**测试数据字段**:
- `test_num` (bigint): 计划测试数量
- `actual_num` (double): 实际测试数量
- `score` (bigint): 设备评分
- `fail_reason` (text): 失败原因

**时间控制字段**:
- `start_at/end_at` (double): 开始/结束时间
- `release_at` (double): 释放时间
- `terminate_at` (double): 终止时间
- `confirm_at` (double): 确认时间

**操作人员字段**:
- `release_by/release_p` (text): 释放操作人员/部门
- `terminate_by/terminate_p` (text): 终止操作人员/部门
- `added_by/added_p` (text): 添加人员/部门

## 设备管理相关表

### wo_device_control (设备控制表)
**业务用途**: 记录设备的控制操作历史，包括开机、关机、释放等操作

**操作类型** (基于title字段的常见值):
1. Plan确认环境后电脑开机
2. Plan添加电脑后电脑开机
3. Plan停止测试后电脑关机
4. Flash取消测试后电脑关机
5. Plan暂缓分配后电脑关机
6. 手动释放设备后电脑关机
7. 重新选择电脑后电脑关机
8. 重新选择电脑后电脑开机
9. 电脑自动释放后电脑关机

**核心字段**:
- `id` (bigint): 设备控制记录唯一标识
- `created_at` (bigint): 创建时间戳
- `device_id` (bigint): 设备ID
- `order_id` (bigint): 关联工单ID
- `plan_id` (bigint): 关联计划ID
- `no/ip/mac` (text): 设备标识信息
- `flash` (text): Flash信息
- `title` (text): 操作标题，简单描述操作内容
- `operator` (text): 操作员
- `operate_by` (text): 操作执行人
- `reason` (text): 操作失败的原因

### wo_device_lock_info (设备锁定信息表)
**业务用途**: 管理设备的锁定状态，防止设备被多个任务同时使用，确保资源独占

**核心字段**:
- `id` (bigint): 设备锁定信息唯一标识
- `device_id` (bigint): 设备ID
- `order_id` (bigint): 关联工单ID
- `plan_id` (bigint): 关联计划ID
- `no/ip/mac` (text): 设备标识信息
- `position` (text): 设备位置
- `product/sub_product` (text): 产品类型信息
- `locked` (double): 锁定时间戳
- `unlock_time` (double): 解锁时间
- `locked_order_flash_no` (text): 锁定的工单Flash编号
- `plan_name` (text): 计划名称
- `plan_type` (bigint): 计划类型
- `attrbuites` (text): 设备属性信息
- `version` (bigint): 版本号

### wo_device_sample (设备采样表)
**业务用途**: 记录设备的采样数据和测试信息，用于测试结果分析

**核心字段**:
- `id` (bigint): 设备采样唯一标识
- `device_id` (double): 设备ID
- `order_id` (double): 关联工单ID
- `plan_id` (double): 关联计划ID
- `no` (text): 设备编号
- `device_ip` (text): 设备IP地址
- `order_flash_no` (text): 工单Flash编号
- `plan_name` (text): 计划名称
- `is_success` (double): 是否成功标识
- `path` (text): 采样数据路径
- `port` (double): 端口号
- `all_p_e` (text): 所有P/E周期信息
- `residual_p_e` (text): 剩余P/E周期信息
- `use_p_e` (text): 已使用P/E周期信息

### wo_device_disk (设备磁盘表)
**业务用途**: 管理设备的磁盘相关信息和测试数据，记录磁盘使用情况

**核心字段**:
- `id` (bigint): 设备磁盘唯一标识
- `device_id` (bigint): 设备ID
- `order_id` (bigint): 关联工单ID
- `plan_id` (bigint): 关联计划ID
- `no/ip/mac` (text): 设备标识信息
- `flash` (text): Flash信息
- `samples` (text): 采样信息
- `trace_id` (text): 跟踪ID
- `request_uuid` (text): 请求UUID
- `test_num` (double): 测试数量

## 扩展业务表详细分析

### wo_order_bug (工单Bug表)
**业务用途**: 记录工单测试过程中发现的Bug信息，与禅道系统集成

**核心字段**:
- `id` (bigint): Bug记录唯一标识
- `order_id` (bigint): 关联工单ID
- `bug_id` (text): 禅道中的bug ID
- `title` (text): Bug标题
- `flash` (text): Bug关联的flash批次
- `plan` (text): Bug关联的Plan名称
- `device` (text): Bug关联的设备
- `sample` (text): Bug关联的样片编号
- `pri` (bigint): 禅道中的优先级
- `severity` (bigint): 严重程度
- `status` (text): Bug状态
- `created_by/created_p` (text): 创建人员/部门

### wo_order_reason (工单原因表)
**业务用途**: 记录工单取消或撤销的原因，支持分类管理

**核心字段**:
- `id` (bigint): 原因记录唯一标识
- `belong_to` (text): 属于工单或批次 (Order/Flash)
- `type` (text): 原因类型 (CANCEL-取消/REVOKE-撤销)
- `description` (text): 原因描述

### wo_order_stat (工单统计表)
**业务用途**: 工单数据统计邮件的基础配置信息

**核心字段**:
- `id` (bigint): 统计配置唯一标识
- `product/sub_product` (text): 产品线/产品
- `to_email` (text): 接收邮箱
- `ccMail` (text): 抄送邮箱
- `hours` (int): 数据统计到当前第n小时之前
- `title` (text): 邮件标题
- `description` (text): 邮件主题

### wo_temp_plan (临时计划表)
**业务用途**: 存储临时创建的测试计划，用于快速测试场景

**核心字段**:
- `id` (bigint): 临时计划唯一标识
- `order_id` (bigint): 关联工单ID
- `name` (text): 计划名称
- `feature` (text): 特性描述
- `product/sub_product` (text): 产品信息
- `type` (bigint): 计划类型
- `priority` (bigint): 优先级
- `test_num` (bigint): 测试数量
- `version_type` (text): 版本类型
- `owner_id/owner_name` (text): 负责人ID/姓名
- `title` (text): 计划标题
- `scripts` (text): 测试脚本
- `added_by/added_p` (text): 添加人员/部门

### wo_timeout_config (超时配置表)
**业务用途**: 配置各种操作的超时时间，支持按产品和阶段分类

**核心字段**:
- `id` (bigint): 超时配置唯一标识
- `product/sub_product` (text): 产品信息
- `phase` (text): 测试阶段
- `type` (text): 配置类型
- `timeout` (bigint): 超时时间(秒)
- `listeners` (text): 监听器配置

### wo_plan_platform (计划平台表)
**业务用途**: 管理计划与测试平台的关系，支持平台级别的测试管理

**状态枚举** (基于PlanPlatformEntity.Status):
- `OCCUPIED` - 已占用：平台已被分配
- `RUNNING` - 正在运行：平台正在执行测试
- `FINISHED_SUCCESS` - 成功结束：测试成功完成
- `FINISHED_FAILED` - 失败结束：测试失败结束

**核心字段**:
- `id` (bigint): 计划平台唯一标识
- `order_id/plan_id` (bigint): 关联工单/计划ID
- `plan_name` (text): 计划名称
- `name` (text): 平台名称
- `no/ip/mac` (text): 平台标识信息
- `number` (text): 平台编号
- `position` (text): 平台位置
- `volume` (text): 容量信息
- `sample` (text): 样片信息
- `status` (text): 平台状态
- `fail_reason/stop_reason` (text): 失败/停止原因

## 产品管理表

### wo_product_t (产品表)
**业务用途**: 定义产品类型和名称的映射关系

**核心字段**:
- `product` (text): 产品代码 (如GE、EM)
- `name` (text): 产品名称

### wo_sub_product_t (子产品表)
**业务用途**: 定义产品下的子产品分类

**核心字段**:
- `product` (text): 产品代码
- `sub_product` (text): 子产品代码 (如U2、U3、SD、UFS)

## 业务流程分析

### 1. 工单生命周期流程图

```mermaid
flowchart TD
    A[创建工单<br/>CREATED] --> B[Flash确认<br/>CONFIRMED_FLASH]
    B --> C[开始测试<br/>TESTING]
    C --> D[创建测试计划<br/>NEW]
    D --> E[计划排队<br/>QUEUE]
    E --> F[资源准备<br/>READY]
    F --> G[环境确认<br/>CONFIRMED]
    G --> H[设备分配<br/>OCCUPIED]
    H --> I[环境确认<br/>CONFIRMED]
    I --> J[开始执行<br/>RUNNING]
    J --> K[测试执行中]
    K --> L[收集结果]
    L --> M[计划完成<br/>COMPLETED]
    M --> N[工单评估<br/>EVALUATING]
    N --> O{所有计划完成?}
    O -->|是| P[工单完成<br/>COMPLETED]
    O -->|否| Q[继续其他计划]
    Q --> D

    %% 异常流程
    A --> R[工单撤销<br/>REVOKED]
    B --> R
    C --> R
    J --> S[手动停止<br/>STOPPED]
    S --> T[设备释放<br/>CANCELED]

    %% Flash批次流程
    C --> U[Flash批次创建<br/>NEW]
    U --> V[等待开始<br/>WAITING_FOR_START]
    V --> W[测试进行中<br/>IN_PROGRESS]
    W --> X[等待报告<br/>WAITING_FOR_REPORT]
    X --> Y[失败分析<br/>WAITING_FOR_FAIL_ANALYSIS]
    Y --> Z[评审阶段<br/>WAITING_FOR_REVIEW]
    Z --> AA[批次完成<br/>COMPLETED]

    style A fill:#e1f5fe
    style P fill:#c8e6c9
    style R fill:#ffcdd2
    style S fill:#fff3e0
    style AA fill:#c8e6c9
```

### 2. 核心业务流程描述

**工单生命周期** (基于WorkOrderEntity.Status):
```
CREATED(创建中) → CONFIRMED_FLASH(待测试) → TESTING(测试中) → EVALUATING(评估中) → COMPLETED(已完成)
                                                                                    ↓
                                                                              REVOKED(已取消)
```

**测试计划执行流程** (基于OrderPlanEntity.Status):
```
NEW(刚创建) → QUEUE(排队中) → READY(资源准备就绪) → CONFIRMED(资源分配完成) → RUNNING(开始测试) → COMPLETED(已完成)
                                                                                                    ↓
                                                                                              STOPPED(手动停止)
```

**Flash批次测试流程** (基于OrderFlashEntity.Status):
```
NEW(刚创建) → WAITING_FOR_START(等待开始) → IN_PROGRESS(测试中) → WAITING_FOR_REPORT(等待报告)
    ↓
WAITING_FOR_FAIL_ANALYSIS(等待失败分析) → FAIL_ANALYSIS_STARTED(分析中) → WAITING_FOR_REVIEW(等待评审)
    ↓
REVIEW_STARTED(评审中) → COMPLETED(已完成) / CANCELLED(已取消)
```

**设备分配管理流程** (基于PlanDeviceEntity.Status):
```
OCCUPIED(已占用) → CONFIRMED(环境已确认) → RUNNING(正在运行) → FINISHED_SUCCESS(成功结束)
                                                            ↓
                                                      FINISHED_FAILED(失败结束)
                                                            ↓
                                                      CANCELED(已取消)
```

**设备控制操作流程**:
```
设备锁定 → 分配给计划 → 环境确认 → 开机启动 → 执行测试 → 采样数据 → 关机释放 → 解锁设备
```

## 关键业务规则推断

1. **工单唯一性**: 每个工单有唯一的编号和ID
2. **层次关系**: 工单 → 计划 → 设备，形成三层管理结构
3. **状态管理**: 各表都有完整的状态字段，支持流程控制
4. **人员追踪**: 记录各个环节的操作人员和时间
5. **失败处理**: 完整的失败原因记录和责任分配机制
6. **设备资源管理**: 通过锁定机制防止资源冲突

## 数据特点

- **时间戳使用**: 大量使用bigint存储时间戳
- **文本字段**: 大部分描述性字段使用text类型
- **允许NULL**: 所有字段都允许NULL，说明业务流程灵活
- **无强制约束**: 数据库层面没有外键约束，依赖应用层控制
- **版本控制**: 多个表包含version字段，支持数据版本管理

## 建议优化点

1. **添加索引**: 在order_id, plan_id, device_id等关联字段上添加索引
2. **数据约束**: 考虑添加必要的外键约束保证数据一致性  
3. **状态枚举**: 将status字段标准化为枚举值
4. **时间字段**: 统一时间字段的数据类型和格式
5. **数据归档**: 对历史数据制定归档策略

## 完整表列表 (共35个表)

### 核心业务表 (10个)
1. **wo_order** (51字段) - 工单主表：存储所有工单的基本信息，是整个系统的核心表
2. **wo_order_detail** (24字段) - 工单详情表：存储工单的技术详情和配置信息
3. **wo_order_plan** (46字段) - 测试计划表：管理每个工单下的具体测试计划
4. **wo_order_flash** (39字段) - Flash测试表：管理Flash批次的测试状态和进度
5. **wo_plan_device** (29字段) - 计划设备分配表：管理测试计划与设备的分配关系
6. **wo_device_control** (13字段) - 设备控制表：记录设备的控制操作历史
7. **wo_device_lock_info** (20字段) - 设备锁定信息表：管理设备的锁定状态，防止资源冲突
8. **wo_review_info** (11字段) - 评审信息表：存储测试结果的评审信息和参与人员
9. **wo_product_t** (2字段) - 产品表：定义产品类型和名称的映射关系
10. **wo_sub_product_t** (2字段) - 子产品表：定义产品下的子产品分类

### 设备管理表 (4个)
11. **wo_device_disk** (14字段) - 设备磁盘表：管理设备的磁盘相关信息和测试数据
12. **wo_device_sample** (16字段) - 设备采样表：记录设备的采样数据和测试信息
13. **wo_device_test_history** (14字段) - 设备测试历史表：记录设备的测试历史信息
14. **wo_plan_device_history** (7字段) - 计划设备历史表：记录计划设备分配的历史变更

### 计划管理表 (7个)
15. **wo_plan_assign_info** (15字段) - 计划分配信息表：管理计划的分配状态和队列信息
16. **wo_plan_history** (15字段) - 计划历史表：记录计划执行的历史信息
17. **wo_plan_note** (4字段) - 计划备注表：存储计划相关的备注信息
18. **wo_plan_platform** (29字段) - 计划平台表：管理计划与测试平台的关系
19. **wo_temp_plan** (18字段) - 临时计划表：存储临时创建的测试计划
20. **wo_timeout_config** (7字段) - 超时配置表：配置各种操作的超时时间
21. **wo_plan_device_history_t** (7字段) - 计划设备历史模板表：计划设备历史的模板定义

### 工单扩展表 (8个)
22. **wo_order_bug** (15字段) - 工单Bug表：记录工单测试过程中发现的Bug信息
23. **wo_order_fail_analysis** (9字段) - 工单失败分析表：存储工单失败的分析结果
24. **wo_order_flash_doc** (8字段) - 工单Flash文档表：管理Flash相关的文档信息
25. **wo_order_plan_doc** (5字段) - 工单计划文档表：管理计划相关的文档信息
26. **wo_order_plan_t** (37字段) - 工单计划模板表：存储计划的模板定义
27. **wo_order_reason** (4字段) - 工单原因表：记录工单取消或撤销的原因
28. **wo_order_stat** (8字段) - 工单统计表：存储工单数据统计的配置信息
29. **wo_order_t** (35字段) - 工单模板表：存储工单的模板定义

### Flash管理表 (2个)
30. **wo_flash_analysis_history** (6字段) - Flash分析历史表：记录Flash分析的历史信息
31. **wo_flash_notice** (6字段) - Flash通知表：管理Flash相关的通知信息

### 评审管理表 (2个)
32. **wo_review_item** (7字段) - 评审项目表：存储评审的具体项目和结果
33. **wo_review_result** (12字段) - 评审结果表：存储评审的最终结果和结论

### 文档和平台表 (2个)
34. **wo_doc** (8字段) - 文档表：管理系统中的文档信息
35. **wo_platform_case** (13字段) - 平台案例表：记录平台测试案例的执行情况

## 字段命名规范分析

### 通用字段模式
- **id**: 主键，bigint类型
- **created_at/updated_at**: 创建/更新时间戳
- **order_id**: 关联工单ID
- **plan_id**: 关联计划ID
- **device_id**: 关联设备ID
- **status**: 状态字段，text类型
- **no**: 编号字段，通常是设备编号或工单编号

### 人员相关字段模式
- **xxx_by**: 操作人员，如build_by, start_by, end_by
- **xxx_p**: 操作人员权限或部门，如build_p, start_p
- **xxx_at**: 操作时间，如build_at, start_at, end_at

### 设备相关字段模式
- **ip/mac**: 设备网络信息
- **flash**: Flash型号或路径
- **position**: 设备位置
- **operator**: 操作员

## 业务状态分析

基于实体类枚举定义和示例数据分析：

### wo_order.status 工单状态 (WorkOrderEntity.Status)
- **CREATED** (创建中): 工单刚刚创建，等待测试主管确认信息
- **CONFIRMED_FLASH** (待测试): 测试主管已经确认了Flash信息，目前正在排队中
- **TESTING** (测试中): 工单已经开始测试
- **EVALUATING** (评估中): 工单评估中
- **COMPLETED** (已完成): 测试完成
- **REVOKED** (已取消): 工单已经被取消

### wo_order_plan.status 计划状态 (OrderPlanEntity.Status)
- **NEW** (刚创建): Flash确认前
- **QUEUE** (排队中): 等待资源分配
- **READY** (资源准备就绪): 可以开始测试
- **CONFIRMED** (资源分配完成): 环境确认完毕
- **RUNNING** (开始测试): 正在执行中
- **COMPLETED** (已完成): 进入此状态后不能再改变
- **STOPPED** (手动停止): 被手动终止

### wo_order_flash.status Flash批次状态 (OrderFlashEntity.Status)
- **NEW** (刚创建): 等待资源
- **WAITING_FOR_START** (等待开始): 资源已分配
- **IN_PROGRESS** (测试中): 正在测试
- **WAITING_FOR_MERGE** (等待合并): 等待报告合并
- **WAITING_FOR_REPORT** (等待报告): 等待上传报告
- **WAITING_FOR_FAIL_ANALYSIS** (等待失败分析): 等待进行fail分析
- **FAIL_ANALYSIS_STARTED** (分析中): FAIL分析开始
- **WAITING_FOR_REVIEW** (等待评审): 等待review
- **REVIEW_STARTED** (评审中): Review已经开始
- **COMPLETED** (已完成): 所有流程完成
- **CANCELLED** (已取消): 批次被取消

### wo_plan_device.status 设备状态 (PlanDeviceEntity.Status)
- **OCCUPIED** (已占用): 设备已被分配但未确认
- **CONFIRMED** (环境已确认): 设备环境检查通过
- **RUNNING** (正在运行): 设备正在执行测试
- **FINISHED_SUCCESS** (成功结束): 测试成功完成
- **FINISHED_FAILED** (失败结束): 测试失败结束
- **CANCELED** (已取消): 设备分配被取消

### 优先级分析
- **0**: 普通优先级
- **1**: 中等优先级
- **2**: 高优先级

### 计划类型分析 (OrderPlanEntity)
- **TYPE_AUTO** (0): 自动类型的Plan
- **TYPE_MANUAL** (1): 手动类型的Plan

### 计划结束状态分析 (OrderPlanEntity)
- **END_STATUS_UNKNOWN** (0): 未知状态
- **END_STATUS_SUCCESS** (1): 成功，Plan下所有设备都成功测试完
- **END_STATUS_FAIL** (2): 失败，Plan下有某些设备测试失败
- **END_STATUS_TERMINATED** (3): 手动终止，Plan被手动停止

### 测试阶段分析 (OrderPlanEntity.Phase)
- **ACCEPT** (接收测试): 验收阶段测试
- **VERIFY** (验证测试): 功能验证测试
- **INTEGRITY** (完整测试): 全面完整性测试

## 数据量分析

| 表名 | 记录数 | 数据密度 |
|------|--------|----------|
| wo_plan_device | 112,167 | 最高 |
| wo_order_plan | 66,355 | 高 |
| wo_device_control | 13,492 | 中等 |
| wo_order | 5,764 | 中等 |
| wo_order_detail | 5,748 | 中等 |

**数据特点**:
- 计划设备分配表数据量最大，说明设备资源管理是核心业务
- 工单与工单详情数据量基本一致，说明数据完整性较好
- 设备控制记录较多，说明设备操作频繁

## 系统架构特点总结

### 数据库设计特点
1. **松耦合设计**: 数据库层面没有强制外键约束，依赖应用层控制数据一致性
2. **状态驱动**: 各表都有完整的状态字段，支持复杂的业务流程控制
3. **审计追踪**: 完整的人员和时间追踪，记录各个环节的操作人员和时间
4. **版本控制**: 多个表包含version字段，支持数据版本管理
5. **灵活扩展**: 所有字段都允许NULL，说明业务流程设计灵活

### 业务流程特点
1. **层次化管理**: 工单 → 计划 → 设备，形成三层管理结构
2. **并行处理**: 支持多个Flash批次并行测试
3. **资源管控**: 通过锁定机制防止设备资源冲突
4. **失败处理**: 完整的失败原因记录和责任分配机制
5. **质量保证**: 多阶段评审和分析流程

### 集成特点
1. **禅道集成**: 与禅道项目管理系统深度集成
2. **工具链集成**: 支持多种测试工具(Mars、MPTool、Scrcpy等)
3. **通知机制**: 完整的邮件通知和状态回调机制
4. **文档管理**: 支持测试文档的版本化管理

### 性能特点
1. **数据量大**: 计划设备分配表数据量最大(112,167条)，说明设备资源管理是核心业务
2. **高并发**: 支持大量设备并发测试
3. **历史追踪**: 完整的历史记录表，支持数据审计和分析

---
*文档更新时间: 2025-07-15*
*分析表数量: 35个*
*数据记录总数: 约20万条*
*核心业务表: 10个*
*设备管理表: 4个*
*扩展业务表: 21个*
*基于实际代码库和数据库结构分析*
