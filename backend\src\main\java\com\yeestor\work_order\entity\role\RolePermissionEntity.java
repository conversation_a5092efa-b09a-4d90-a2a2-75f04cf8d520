package com.yeestor.work_order.entity.role;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

/**
 * 将角色和产品线，权限关联起来。
 * 理论上，一个角色可以关联多个产品线，但是有的产品线可能会单独创建一个角色，所以这里也提供这样的扩展性以供使用。
 */
@Getter
@Setter
@ToString
@Entity(name = "RolePermission")
@Table(name = "wo_role_permission")
public class RolePermissionEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "role_id")
    private long roleId ;

    /**
     * 产品线
     */
    @Column(name= "product")
    private String product ;

    /**
     * 权限
     */
    @Lob
    @Column(name= "permission", length=5120)
    private String permission ;
}
