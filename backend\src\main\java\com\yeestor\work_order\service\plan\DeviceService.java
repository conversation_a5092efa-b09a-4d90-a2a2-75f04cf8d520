package com.yeestor.work_order.service.plan;

import cn.hutool.core.text.CharSequenceUtil;
import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.entity.device.DeviceDiskEntity;
import com.yeestor.work_order.entity.device.DeviceLockInfoEntity;
import com.yeestor.work_order.entity.history.PlanDeviceHistoryEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.req.ForceReleaseDeviceParams;
import com.yeestor.work_order.model.http.resp.order.DeviceDetailVO;
import com.yeestor.work_order.model.http.resp.order.DeviceSamplesVO;
import com.yeestor.work_order.model.rms.*;
import com.yeestor.work_order.repository.*;
import com.yeestor.work_order.repository.device.DeviceDiskRepository;
import com.yeestor.work_order.service.device.QueueService;
import com.yeestor.work_order.service.device.RMSDeviceService;
import com.yeestor.work_order.service.job.CheckPlanStartStatusJob;
import com.yeestor.work_order.service.job.JobService;
import com.yeestor.work_order.service.job.StartTestJob;
import com.yeestor.work_order.service.job.TimedShutdownDeviceJob;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.product.ProductContext;
import com.yeestor.work_order.utils.DingTalkUtils;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.quartz.DateBuilder;
import org.quartz.JobDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

import static com.yeestor.work_order.entity.PlanDeviceEntity.FIXED_STATUS_LIST;
import static com.yeestor.work_order.model.rms.DeviceModel.FIXED_ATTRS;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceService {

    private final PlanDeviceRepository planDeviceRepository ;
    private final DeviceHistoryRepository historyRepository ;
    private final RMSDeviceService rmsDeviceService ;
    private final DeviceSampleRepository deviceSampleRepository ;
    private final DeviceDiskRepository deviceDiskRepository ;

    private final JobService jobService ;

    private final DataChangeListener dataChangeListener ;

    private final ProductContext productContext ;

    private final DingTalkUtils dingTalkUtils;

    @Setter(onMethod_ = { @Autowired,@Lazy} )
    private PlanAssignService planAssignService ;

    @Setter(onMethod_ = { @Autowired,@Lazy} )
    private FlashService flashService;

    @Setter(onMethod_ = { @Autowired,@Lazy} )
    private PlanService planService;

    /**
     * 强行释放设备
     * @param orderEntity order实体类
     * @param flashEntity Flash实体类
     * @param planEntity Plan实体类
     * @param params 设备信息
     */
    public void forceReleaseDevice(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            ForceReleaseDeviceParams params
    ){
        String userName = DingTalkUtils.getCurrentUserName();
        String userId = DingTalkUtils.getCurrentUserDingTalkID();
        // 在这个工单中试图找到对应的设备
        List<PlanDeviceEntity> planDeviceEntities = planDeviceRepository.findAllByOrderIdAndMac(flashEntity.getOrderId(), params.getMac());
        if (planDeviceEntities.isEmpty()) {
            // 如果在设备列表中没有找到的话,说明设备已经到了历史状态.
            DeviceLockInfoEntity lockInfo = rmsDeviceService.findLockInfoInTest(
                    planEntity.getId(),
                    params.getMac()
            );

            rmsDeviceService.releaseDevices(
                    orderEntity.getSubProduct(),
                    flashEntity.getOrderFlashNo(),
                    planEntity,
                    Collections.singletonList(lockInfo)
            );
            log.info("设备{}已经到了历史状态,直接释放", params.getMac());
            return;

        }
        log.info("找到对应的设备信息,共{}个", planDeviceEntities.size());
        // 目前不考虑 取消，停止状态的设备
        PlanDeviceEntity deviceEntity = planDeviceEntities.stream()
                .filter(e -> params.getPlan() == null || e.getPlanName().equals(params.getPlan()))
                .findFirst()
                .orElseThrow(() -> new DataNotFoundException("找不到对应的运行中的设备!"));

        // 释放设备
        rmsDeviceService.releaseDevices(orderEntity.getSubProduct(), flashEntity.getOrderFlashNo(), planEntity, Collections.singletonList(deviceEntity));
        PlanDeviceEntity newDeviceEntity = deviceEntity.duplicate();
        // 强制停止的设备的都认为是 失败的结束.
        newDeviceEntity.setStatus(PlanDeviceEntity.Status.FINISHED_FAILED);
        newDeviceEntity.setFailReason("由" + userName + "强制停止");
        newDeviceEntity.setEndAt(System.currentTimeMillis());
        newDeviceEntity.setReleaseAt(System.currentTimeMillis());
        newDeviceEntity.setReleaseBy(userId);
        newDeviceEntity.setReleasePerson(userName);

        // TODO 在指定的状态下需要更新样片数量
        saveDeviceList(
                "强制释放设备",
                Collections.singletonList(deviceEntity),
                Collections.singletonList(newDeviceEntity)
        );

        if (orderEntity.getStatus() != WorkOrderEntity.Status.REVOKED) {
            planService.checkDeviceReleaseStatus(orderEntity,flashEntity, planEntity);
        }
    }

    /**
     * 停止运行中的设备
     *
     * @param orderEntity order实体类
     * @param flashEntity flash实体类
     * @param planEntity  Plan实体类
     * @param macList     需要停止运行的设备
     * @param reason      停止的原因
     */
    public void stopRunningDevice(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            List<String> macList,
            String reason
    ) {
        String userName = DingTalkUtils.getCurrentUserName();
        String userId = DingTalkUtils.getCurrentUserDingTalkID();
        List<PlanDeviceEntity> deviceEntityList = planDeviceRepository.findAllByPlanIdAndMacIn(planEntity.getId(), macList);
        List<String> deviceIpList = deviceEntityList.stream().map(PlanDeviceEntity::getIp).collect(Collectors.toList());

        List<PlanDeviceEntity> newDeviceList = deviceEntityList.stream().map(d -> {
            PlanDeviceEntity d1 = d.duplicate();
            log.debug("停止测试原因： " + reason);
            d1.setFailReason(reason);
            d1.setOperator(userId);
            d1.setTerminateAt(System.currentTimeMillis());
            d1.setTerminateBy(userId);
            d1.setTerminatePerson(userName);
            return d1;
        }).collect(Collectors.toList());

        saveDeviceList("停止指定的电脑测试", deviceEntityList, newDeviceList);

        log.info(
                "停止Flash:{}下{}的设备:{}",
                flashEntity.getFlash(),
                planEntity.getName(),
                deviceEntityList.stream().map(d -> String.format("%s(%s - %s)", d.getNo(), d.getIp(), d.getMac())).collect(Collectors.joining(","))
        );

        // 停止 plan 下的指定设备
        stopTestPlanAndCheck(
                planEntity,
                StopTestParams.builder()
                        .orderNo(flashEntity.getOrderFlashNo())
                        .planDeviceList(Collections.singletonList(new StopTestParams.PlanDeviceIpInfo(planEntity.getName(), macList, deviceIpList)))
                        .build()
        );
        // 检查Plan 下的设备情况。
        planService.checkDeviceReleaseStatus(orderEntity, flashEntity, planEntity);
    }

    /**
     * 将设备的数据结构拆分后，重写 getPlanDevices 方法，后续拆分工单detail接口时既可直接使用该接口
     * 获取plan 对应的设备列表， 从数据库中获取ip ，然后从redis 中取。
     * @param product 产品线
     * @param planEntity plan 实体类
     * @return 设备列表
     */
    public List<DeviceDetailVO> getPlanDevicesVO(String product, OrderPlanEntity planEntity){
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());

        return planDeviceRepository.findAllByPlanId(planEntity.getId())
                .stream()
                .map(planDeviceEntity -> {
                    DeviceDetailVO deviceInfo = new DeviceDetailVO();
                    deviceInfo.setInfo(planDeviceEntity);

                    List<DeviceSamplesVO> sampleNoteList = deviceSampleRepository.findSamplesNoteListByOrderIdAndPlan(
                            flashEntity.getOrderId(),
                            planDeviceEntity.getPlanId(),
                            planDeviceEntity.getId()
                    );
                    deviceInfo.setSamplesNoteList(sampleNoteList);

                    rmsDeviceService.fetchDeviceByMac(product, planDeviceEntity.getMac())
                            .ifPresent(deviceModel -> deviceInfo.setAttrModelList(deviceModel.getAttrModelList()));
                    return deviceInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 保存 旧设备的历史后，删除旧设备，然后保存新设备。
     * @param oldDeviceList 旧设备
     * @param newDeviceList 新设备
     */
    @Transactional
    public List<PlanDeviceEntity> saveDeviceList(String title ,List<PlanDeviceEntity> oldDeviceList, List<PlanDeviceEntity> newDeviceList){
        // 保存设备的历史。
        recordDeviceHistory(title, oldDeviceList);

        // 找到 新设备中不包含的旧设备
        List<PlanDeviceEntity> oldDeviceListNotInNew = oldDeviceList.stream()
                .filter(d-> newDeviceList.stream().noneMatch(d1-> d1.isSameDevice(d)))
                .collect(Collectors.toList());
        // 删除这些旧设备
        planDeviceRepository.deleteAll(oldDeviceListNotInNew);

        // 首先，将新设备中存在的旧设备，更新版本号，同时，将ID 设置为旧设备的ID。
        newDeviceList.stream()
                .filter(d-> oldDeviceList.stream().anyMatch(d1-> d1.isSameDevice(d)))
                .forEach(d-> oldDeviceList.stream()
                        .filter(d1-> d1.isSameDevice(d))
                        .findFirst()
                        .ifPresent(d1->{
                            d.setId(d1.getId());
                            d.setVersion(d1.getVersion() + 1);
                        })
                );

        // 保存新设备。
        return planDeviceRepository.saveAll(newDeviceList);
    }

    /**
     * 记录设备的历史。
     * @param title 设备历史变更简述
     * @param deviceEntityList 需要记录的设备实体类
     */
    public void recordDeviceHistory(String title, List<PlanDeviceEntity> deviceEntityList){
        // 记录设备历史实体类
        List<PlanDeviceHistoryEntity> historyEntityList = deviceEntityList.stream()
                .map(d-> {
                    PlanDeviceHistoryEntity deviceHistory = PlanDeviceHistoryEntity.device2History(d);
                    deviceHistory.setTitle(title);

                    return deviceHistory ;
                })
                .collect(Collectors.toList());

        historyRepository.saveAll(historyEntityList) ;
    }

    /**
     * 重新分配设备 . 将新设备的状态设置为占用中 ,不会对设备进行加解锁操作。
     * @param orderEntity 工单实体类
     * @param flashEntity flash 实体类
     * @param planEntity plan 实体类
     * @param oldDevice 旧设备 , 在数据库中.
     * @param newDevice 新设备的设备信息
     */
    public void reAllocDevice(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            PlanDeviceEntity oldDevice,
            DeviceModel newDevice
    ) {
        String orderFlashNo = flashEntity.getOrderFlashNo();

        PlanDeviceEntity newDeviceEntity = DeviceModel.toEntity(newDevice, planEntity.getId(), orderEntity.getId());
        newDeviceEntity.setStatus(PlanDeviceEntity.Status.OCCUPIED);
        newDeviceEntity.setCreatedAt(System.currentTimeMillis());
        newDeviceEntity.setUpdatedAt(System.currentTimeMillis());
        newDeviceEntity.setPlanName(planEntity.getName());
        newDeviceEntity.setTestNum(oldDevice.getTestNum());

        List<PlanDeviceEntity> holdDeviceList = saveDeviceList("重新选择设备", Collections.singletonList(oldDevice), Collections.singletonList(newDeviceEntity));

        // 释放旧设备
        rmsDeviceService.releaseDevices(orderEntity.getSubProduct(), orderFlashNo, planEntity, List.of(oldDevice));

        // 占用新设备
        rmsDeviceService.holdDevices(
                orderEntity.getSubProduct(),
                orderFlashNo,
                planEntity,
                holdDeviceList,
                PlanDeviceEntity.Status.OCCUPIED
        );
        // 将旧设备关机
        saveDeviceControlList(orderEntity.getSubProduct(), flashEntity, DingTalkUtils.getCurrentUserName(), "重新选择电脑后电脑关机", false, Collections.singletonList(oldDevice));

        // 将新设备开机
        saveDeviceControlList(orderEntity.getSubProduct(), flashEntity, DingTalkUtils.getCurrentUserName(), "重新选择电脑后电脑开机", true, Collections.singletonList(newDeviceEntity));
    }

    /**
     * 更新样片的信息
     * @param flashEntity flash 实体类
     * @param deviceEntity 设备实体类
     * @param samples 样片编号
     */
    @Transactional
    public void updateSamples(
            OrderFlashEntity flashEntity, PlanDeviceEntity deviceEntity,
            List<String> samples
    ){
        List<DeviceSampleEntity> sampleEntityList = samples.stream().map(no ->
            DeviceStatusChangeParams.convertToDeviceSampleEntity(
                    deviceEntity,flashEntity.getOrderFlashNo(),no
            ))
        .collect(Collectors.toList());

        deviceSampleRepository.saveAll(sampleEntityList);
    }

    @Transactional
    public void updateDetailSamples(
            OrderFlashEntity flashEntity,
            PlanDeviceEntity deviceEntity,
            List<SampleInfo> samples
    ){
        samples.stream().map(info ->
                        info.convertToDeviceSampleEntity(
                                deviceEntity,flashEntity.getOrderFlashNo()
                        )
                ).forEach(deviceSampleRepository::save);
    }


    /**
     * 更新设备的样片数量。
     * @param orderFlashEntity flash 批次
     * @param deviceInfoLst 设备信息列表
     */
    @Transactional
    public Map<Long,String> updateDeviceStatus(
            OrderFlashEntity orderFlashEntity,
            List<DeviceInfo> deviceInfoLst
    ) {
        String orderNo = orderFlashEntity.getOrderFlashNo() ;
        log.debug("[RMS 回调] 样片数量变更！ deviceInfoLst={}", deviceInfoLst);

        // 保存所有的Plan id, 用于后面的任务的查询,以及打印.
        HashMap<Long,String> planIDAndNames = new HashMap<>();

        // 循环更新设备中的实际样片数量
        List<PlanDeviceEntity> oldDeviceLst = new ArrayList<>() ;
        List<PlanDeviceEntity> newDeviceLst = new ArrayList<>() ;
        deviceInfoLst.forEach(deviceInfo -> {
            PlanDeviceEntity deviceEntity = planDeviceRepository.findByOrderIdAndIpAndStatusInAndReleaseAtIsNull(
                    orderFlashEntity.getOrderId(),
                    deviceInfo.getIp(),
                    Collections.singletonList(PlanDeviceEntity.Status.RUNNING)
            ).orElse(null);
            if(deviceEntity == null){
                List<PlanDeviceEntity> allDeviceList = planDeviceRepository.findAllByOrderId(orderFlashEntity.getOrderId());
                log.trace("allDeviceList:{}", allDeviceList);
                log.error("设备不存在,orderNo:{},ip:{}", orderNo, deviceInfo.getIp());
                return;
            }
            planIDAndNames.put(deviceEntity.getPlanId(), deviceEntity.getPlanName());
            PlanDeviceEntity newDeviceEntity = deviceEntity.duplicate();
            newDeviceEntity.setActualNum(deviceInfo.getNum());
            log.info("Flash:{} 下{}中的设备:{} 的实际样片数量更新至:{}",orderFlashEntity.getFlash(),deviceEntity.getPlanName(), deviceEntity.getNo()+"("+deviceEntity.getIp()+")",deviceInfo.getNum());
            newDeviceEntity.setUpdatedAt(System.currentTimeMillis());

            oldDeviceLst.add(deviceEntity) ;
            newDeviceLst.add(newDeviceEntity) ;


            List<String> samples = Optional.ofNullable(deviceInfo.getSampleNoList()).orElseGet(() -> deviceInfo.getSampleNoLst().stream().map(SampleInfo::getSampleNo).collect(Collectors.toList()));

            log.info("绑定样片:{}至{}中的设备{}",samples, deviceEntity.getPlanName(),deviceEntity.getNo()+"("+deviceEntity.getIp()+")");

            if(deviceInfo.getSampleNoList() != null && !deviceInfo.getSampleNoList().isEmpty()){
                updateSamples(orderFlashEntity, deviceEntity, deviceInfo.getSampleNoList());
            }
            else if(deviceInfo.getSampleNoLst() != null && !deviceInfo.getSampleNoLst().isEmpty()){
                updateDetailSamples(orderFlashEntity, deviceEntity, deviceInfo.getSampleNoLst());
            }

        });
        // 更新设备信息
        saveDeviceList("更新样片的数量",oldDeviceLst, newDeviceLst);

        return planIDAndNames;

    }

    /**
     * 更新指定Plan 中的分配信息以及 对应的任务状态
     * @param orderFlashEntity flash 批次
     * @param planId plan id
     * @param planName plan 名称
     */
    public void updatePlanAssignInfoAndJob(
            OrderFlashEntity orderFlashEntity,
            long planId, String planName){

        planAssignService.updateActualNum(planId);
        List<PlanDeviceEntity> deviceList = planDeviceRepository.findAllByPlanId(planId) ;
        long left = deviceList.stream().filter(s -> s.getActualNum() == null && s.getStatus() == PlanDeviceEntity.Status.RUNNING).count();

        log.info("{} 还有{}/{}台设备未启动!",planName,left,deviceList.size());
        if(left == 0) {
            log.info("{} 下的所有设备都已启动成功,发送启动成功通知!",planName);
            jobService.triggerJobIfExist(CheckPlanStartStatusJob.buildJobName(orderFlashEntity.getOrderFlashNo(),planName),CheckPlanStartStatusJob.JOB_GROUP_NAME);
        }
        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.DEVICE_SAMPLE_CHANGED)
                        .orderId(orderFlashEntity.getOrderId())
                        .flash(orderFlashEntity.getFlash())
                        .planId(planId)
                        .build()
        );
    }


    /**
     * 通过Plan来获取支持的设备列表 , 此方法会默认先从eReport中拉取最新的设备列表
     * @param orderEntity 工单
     * @param planEntity 需要检测的实体类
     * @param useOld 是否使用之前运行过的设备
     * @return 支持此plan的设备列表。
     */
    public List<DeviceModel> findSupportDevicesByPlan(WorkOrderEntity orderEntity, OrderPlanEntity planEntity, boolean useOld) {
        String subProduct = orderEntity.getSubProduct();
        List<DeviceModel> deviceList = rmsDeviceService.getAvailableDevices(orderEntity.getSubProduct());
        // 如果是eMMC产品，则区分电脑的所属公司
        if (subProduct.equals("eMMC")) {
            String location = dingTalkUtils.getLocationByDingId(planEntity.getBelongTo());   // 测试人员所在公司地址
            deviceList = deviceList.stream().filter(item -> item.getRegion().equalsIgnoreCase(location)).collect(Collectors.toList());
        }
        return findSupportDevicesByPlan(orderEntity, planEntity, useOld, deviceList);
    }


    public int getTestNum(DeviceModel model, WorkOrderEntity orderEntity, OrderPlanEntity planEntity) {
        List<String> attrs = new ArrayList<>(Arrays.asList(planEntity.getAttrs().split(";")));

        List<String> supportAttrs = model.getAttrModelList().stream()
                .map(AttrModel::getAttrName).collect(Collectors.toList());

        // 在SATA 产品线中, 如果是品质验证的工单, 则高温的Plan 可以用 品质高温 的设备来测试 , 具体参考: #165
        if ("SATA".equalsIgnoreCase(orderEntity.getSubProduct()) &&
                (orderEntity.getFeature() & WorkOrderEntity.FEATURE_VERIFY_FLASH) == WorkOrderEntity.FEATURE_VERIFY_FLASH &&
                (attrs.contains(DeviceModel.ATTR_HIGH_TEMP) && supportAttrs.contains(DeviceModel.ATTR_QUALITY_HIGH_TEMP))
        ) {
            model.getAttrModelList().stream().filter(s -> DeviceModel.ATTR_QUALITY_HIGH_TEMP.equals(s.getAttrName())).findFirst()
                    .ifPresent(a -> a.setAttrName(DeviceModel.ATTR_HIGH_TEMP));
        }
        return attrs.stream()
                .map(model::getTestNumByAttr)
                .min(Comparator.comparingInt(Integer::intValue))
                .orElse(0);
    }

    /**
     * 判断设备 是否可以用来测试指定工单下的指定Plan
     * @param model 设备
     * @param orderEntity 工单
     * @param planEntity plan
     * @param isIgnoreRare 是否忽略高低温等特殊属性的独占性
     * @return true: 可以用来测试, false: 不可以用来测试
     */
    public boolean canTest(DeviceModel model, WorkOrderEntity orderEntity, OrderPlanEntity planEntity, boolean isIgnoreRare) {
        List<String> attrs = new ArrayList<>(Arrays.asList(planEntity.getAttrs().split(";")));

        List<String> supportAttrs = model.getAttrModelList().stream()
                .map(AttrModel::getAttrName).collect(Collectors.toList());
//        log.info("plan attrs: {} pcNo: {} supportAttrs: {} isIgnoreRare: {}", attrs, model.getPcNo(), supportAttrs, isIgnoreRare);

        // 在SATA 产品线中, 如果是品质验证的工单, 则高温的Plan 可以用 品质高温 的设备来测试 , 具体参考: #165
        if ("SATA".equalsIgnoreCase(orderEntity.getSubProduct()) &&
                (orderEntity.getFeature() & WorkOrderEntity.FEATURE_VERIFY_FLASH) == WorkOrderEntity.FEATURE_VERIFY_FLASH &&
                (attrs.contains(DeviceModel.ATTR_HIGH_TEMP) && supportAttrs.contains(DeviceModel.ATTR_QUALITY_HIGH_TEMP))
        ) {
            // 如果Plan 中包含高温属性,并且设备支持品质高温, 则直接抛开这两个属性,看看Plan以及设备的其他属性是否匹配
            attrs.remove(DeviceModel.ATTR_HIGH_TEMP);
            return new HashSet<>(supportAttrs).containsAll(attrs);
        }

        // 如果忽略高低温属性的独占性，则直接判断Plan属性是否被设备直接包含，是则可以用来测试，否则不可以
        if (isIgnoreRare) {
            return new HashSet<>(supportAttrs).containsAll(attrs);
        }

        // 找出attrs 与 FIXED_ATTRS 的交集
        HashSet<String> intersection = new HashSet<>(attrs);
        intersection.retainAll(FIXED_ATTRS);

        // 如果交集为空,则表明attrs 中没有高温或者低温属性.
        // 如果此时supportAttrs 中包含高温或者低温属性,则表明这个设备不能测试attrs 中的属性,因为高温和低温属性是属于独占的.
        // 即 Plan 中不含高温,但是设备支持高温.
        if (intersection.isEmpty() && supportAttrs.stream().anyMatch(FIXED_ATTRS::contains)) {
            return false;
        }
        return new HashSet<>(supportAttrs).containsAll(attrs);
    }

    /**
     * 获取工单中
     * @param orderId 工单id
     * @param availableDevices 可用设备列表
     * @return 可用的设备列表 , 如果工单都没有跑过，则返回空列表。
     */
    public List<DeviceModel> fetchRunCompletedDevice(long orderId, List<DeviceModel> availableDevices){

        if(planDeviceRepository.countByOrderIdAndStatusIn(orderId, PlanDeviceEntity.FINISHED_STATUS_LIST) == 0){
            return new ArrayList<>() ;
        }
        List<PlanDeviceEntity> planDeviceEntityList = planDeviceRepository.findAllByOrderIdAndStatusIn(orderId, PlanDeviceEntity.FINISHED_STATUS_LIST);
        // 将planDeviceEntityList 转换成 mac 列表, 并去重
        List<String> macList = planDeviceEntityList.stream().map(PlanDeviceEntity::getMac).distinct().collect(Collectors.toList());
        // 将可用的设备的ip 和 planDeviceEntityList 的mac 做比较，如果有交集，则返回可用的设备。
        return availableDevices.stream().filter(d -> macList.contains(d.getMac())).collect(Collectors.toList());
    }

    /**
     * 通过Plan来获取支持的设备列表【从设备列表中检索出符合测试Plan的设备】
     * @param orderEntity 工单
     * @param planEntity 需要检测的实体类
     * @param useOld 是否使用之前运行过的设备
     * @param deviceList 需要检查的设备列表 【空闲列表或者占用列表】
     * @return 支持此plan的设备列表。
     */
    public List<DeviceModel> findSupportDevicesByPlan(
            WorkOrderEntity orderEntity,
            OrderPlanEntity planEntity,
            boolean useOld,
            List<DeviceModel> deviceList
    ) {
        List<DeviceModel> tempDeviceList = new ArrayList<>(deviceList);
        if (useOld) {
            tempDeviceList = fetchRunCompletedDevice(planEntity.getOrderId(), deviceList);
        }
        // 如果是SATA的工单，直接忽略特殊属性进行匹配
        return matchPlanAttrsDevices(orderEntity, planEntity, tempDeviceList, orderEntity.getSubProduct().equals("SATA"));
    }

    /**
     * 从 deviceList 中筛选出支持此plan的设备列表.
     * 如果是手动Plan的话,则返回所有的设备列表
     *
     * @param orderEntity 需要检测的工单
     * @param planEntity 需要检测的plan
     * @param deviceList 需要检查的设备列表
     * @param isIgnoreRare 是否忽略稀有属性
     * @return 支持此plan的设备列表。
     */
    public List<DeviceModel> matchPlanAttrsDevices(
            WorkOrderEntity orderEntity,
            OrderPlanEntity planEntity,
            List<DeviceModel> deviceList,
            boolean isIgnoreRare
    ) {
        if (planEntity.isManualPlan()) {
            // 如果手动计划，则返回所有的设备。
            return deviceList;
        }

        // 与设备的测试属性以及数量进行比较。
        // 只有包含 这个plan下的所有属性的设备才会认为是可用
        // 找到最合适的设备。
        return deviceList.stream()
                .filter(d -> canTest(d, orderEntity, planEntity, isIgnoreRare)) // 过滤掉不支持的设备
                .sorted(productContext.deviceComparator(planEntity.getOrderId()))
                .collect(Collectors.toList());
    }

    /**
     * 获取工单正在占用以及正在运行的设备总数
     * @param orderId 工单ID
     * @return 设备总数
     */
    public int getOccupiedDeviceCount(long orderId) {
        return planDeviceRepository.countByOrderIdAndStatusIn(orderId,
                Arrays.asList(
                        PlanDeviceEntity.Status.RUNNING,
                        PlanDeviceEntity.Status.OCCUPIED,
                        PlanDeviceEntity.Status.CONFIRMED
                )
        );
    }


    /**
     * 释放plan下的设备列表
     * 使用地方：
     *      1、单个设备释放
     *      2、手动Plan完成释放锁定设备
     * @param oldDeviceList 需要释放的设备列表
     * @param flashEntity  flash 实体
     * @param uid 操作的用户id
     * @param username 操作的用户名
     */
    @Transactional
    public void releaseDevices(
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            List<PlanDeviceEntity> oldDeviceList,
            String uid ,
            String username
    ){
        // 如果设备列表为空，则直接返回
        if (oldDeviceList == null || oldDeviceList.isEmpty()) {
            return;
        }

        // 释放设备
        // 如果停止测试plan不会释放对应的设备，则需要调用之前的释放设备接口、
        // 理想情况是 只需要释放对应的redis 中的值即可。
        rmsDeviceService.releaseDevices(
                productContext.getOrderSubProduct(planEntity.getOrderId()),
                flashEntity.getOrderFlashNo(),
                planEntity,
                oldDeviceList
        );

        List<PlanDeviceEntity> newDeviceList = oldDeviceList.stream().map(PlanDeviceEntity::duplicate).collect(Collectors.toList());
        // 更新plan下 的 设备的释放状态
        newDeviceList.forEach(planDeviceEntity -> {
            PlanDeviceEntity.Status status = planDeviceEntity.getStatus();
            // 如果设备还在锁定状态，则可以认为该设备被取消了。
            if(planDeviceEntity.isOccupied()) {
                planDeviceEntity.setStatus(PlanDeviceEntity.Status.CANCELED);
            } else if(status == PlanDeviceEntity.Status.FINISHED_SUCCESS) {
                planDeviceEntity.setStatus(PlanDeviceEntity.Status.FINISHED_SUCCESS);
            } else {
                planDeviceEntity.setStatus(PlanDeviceEntity.Status.FINISHED_FAILED);
            }
            planDeviceEntity.setUpdatedAt(System.currentTimeMillis());
            planDeviceEntity.setReleaseAt(System.currentTimeMillis());
            planDeviceEntity.setOperator(uid);
            planDeviceEntity.setReleaseBy(uid);
            planDeviceEntity.setReleasePerson(username);
        });
        // 此处只需要保存变更后的信息即可
        saveDeviceList("释放Plan的设备",oldDeviceList, newDeviceList);
        // 将测试设备关机
        // 对于测试失败的设备并且自动释放, 不会关机
        List<PlanDeviceEntity> shutdownDeviceList = newDeviceList.stream()
                        .filter(entity-> entity.getStatus() != PlanDeviceEntity.Status.FINISHED_FAILED)
                        .collect(Collectors.toList());

        saveDeviceControlList(
                productContext.getOrderSubProduct(planEntity.getOrderId()),
                flashEntity,
                username,
                "手动释放设备后电脑关机",
                false,
                uid == null ? shutdownDeviceList : newDeviceList // 当uid为空的时候表示为自动释放, 这种情况下,不需要处理FINISHED_FAILED 的设备.
        );
        int sumCount = oldDeviceList.stream().mapToInt(PlanDeviceEntity::getTestNum).sum();
        flashService.updateLeftSampleNum(flashEntity, sumCount);
    }

    /**
     * 启动Plan下的指定的设备列表
     * @param planEntity 需要启动的Plan
     * @param deviceMacList 指定的Plan 下的设备的mac列表.
     */
    public void startTest(
            OrderPlanEntity planEntity,
            List<String> deviceMacList
    ) {
        String username = DingTalkUtils.getCurrentUserName();

        List<PlanDeviceEntity> needTestDeviceList = planDeviceRepository.findAllByPlanIdAndMacIn(
                planEntity.getId(),
                deviceMacList
        );

        needTestDeviceList.forEach(d -> d.setOperator(DingTalkUtils.getCurrentUserDingTalkID()));

        List<PlanDeviceEntity> successNewDeviceList = needTestDeviceList.stream()
                .map(p -> {
                    PlanDeviceEntity newDeviceEntity = p.duplicateEmpty();
                    newDeviceEntity.setFailReason(null);
                    newDeviceEntity.setActualNum(null);
                    newDeviceEntity.setStartAt(System.currentTimeMillis());
                    newDeviceEntity.setStatus(PlanDeviceEntity.Status.RUNNING);
                    return newDeviceEntity;
                }).collect(Collectors.toList());

        saveDeviceList("启动测试", needTestDeviceList, successNewDeviceList);

        // 使用延时任务开始测试,防止出现由于 样片数量回调过快导致的样片数量错误的问题.
        String jobName = StartTestJob.buildJobName(planEntity.getOrderId(), planEntity.getId());
        JobDetail jobDetail = StartTestJob.buildJobDetail(
                jobName,
                planEntity.getOrderId(),
                planEntity.getFlash(),
                planEntity.getId(),
                username,
                needTestDeviceList
        );
        jobService.startJob(
                jobDetail,
                jobName,
                StartTestJob.GROUP_NAME,
                DateBuilder.futureDate(5, DateBuilder.IntervalUnit.SECOND)
        );
        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_STARTED)
                .orderId(planEntity.getOrderId())
                .flash(planEntity.getFlash())
                .planId(planEntity.getId())
                .build());
    }

    /**
     * 启动一个10分钟后的定时检测Plan启动状态的任务。
     * @param orderId 工单id
     * @param orderFlashNo RMS中完整的工单号
     * @param planEntity plan 实体类
     * @param deviceMacList 需要检测设备列表
     */
    public void startCheckPlanStartStatusJob(
            long orderId,
            String orderFlashNo,
            OrderPlanEntity planEntity,
            List<String> deviceMacList
    ){
        String jobName = orderFlashNo + "_" + planEntity.getName() + "_checkStartStatus" ;
        jobService.startJobAfter(
                jobName,
                CheckPlanStartStatusJob.JOB_GROUP_NAME,
                CheckPlanStartStatusJob.buildJobDataMap(
                        orderId,
                        orderFlashNo,
                        planEntity.getId(),
                        deviceMacList
                ),
                CheckPlanStartStatusJob.class,
                600
        );
    }


    /**
     * 停止测试。
     * @param stopTestParams 停止测试参数
     */
    public void stopTestPlanAndCheck(
            OrderPlanEntity orderPlanEntity,
            StopTestParams stopTestParams
    ) {
        if (stopTestParams.getPlanDeviceList().isEmpty()) {
            return;
        }

        HandleResp<PlanTestRespModel> testResultResp = rmsDeviceService.stopTestPlan(stopTestParams) ;

        if(testResultResp.getCode() == 3) {
            PlanTestRespModel r = testResultResp.getData();
            List<PlanTestRespModel.DeviceTestResult> failLst = r.getFailLst() ;
            if(!failLst.isEmpty()){
                // 未开始测试的设备，只需要直接释放即可
                List<String> ipList = failLst.stream()
                        .filter(d->d.getReason().contains("未测试无法停止"))
                        .map(PlanTestRespModel.DeviceTestResult::getIp)
                        .collect(Collectors.toList());

                //TODO: eReport 中的停止尚未返回mac 地址，所以暂时使用ip地址来进行匹配。
                List<PlanDeviceEntity> planDeviceList = planDeviceRepository.findAllByPlanIdAndIpIn(orderPlanEntity.getId(),ipList);
                int sumSampleCount = planDeviceList.stream().mapToInt(PlanDeviceEntity::getTestNum).sum();
                List<PlanDeviceEntity> newDeviceList = new ArrayList<>() ;
                planDeviceList.forEach(p -> {
                    p.setStatus(PlanDeviceEntity.Status.FINISHED_FAILED);
                    p.setReleaseAt(System.currentTimeMillis());
                    p.setReleasePerson(DingTalkUtils.getCurrentUserName());
                    p.setReleaseBy(DingTalkUtils.getCurrentUserDingTalkID());
                    newDeviceList.add(p) ;
                });

                flashService.updateLeftSampleNum(orderPlanEntity.getOrderId(),orderPlanEntity.getFlash(), sumSampleCount);

                saveDeviceList("结束测试",planDeviceList,newDeviceList);
                rmsDeviceService.releaseDevices(
                        productContext.getOrderSubProduct(orderPlanEntity.getOrderId()),
                        stopTestParams.getOrderNo(),
                        orderPlanEntity,
                        newDeviceList
                );
            }
        }
    }

    /**
     * 保存电脑开关机信息
     *
     * @param subProduct 所属产品
     * @param flashEntity flash实体类
     * @param userName      执行用户
     * @param isOpen     是否为开机操作
     * @param title      描述
     * @param deviceList 设备列表
     */
    @Async
    public void saveDeviceControlList(
            String subProduct,
            OrderFlashEntity flashEntity,
            String userName,
            String title,
            boolean isOpen,
            List<PlanDeviceEntity> deviceList
    ) {
        // Support Position
        Set<String>  SUPPORT_POSITION = Set.of("GE",
                "SSD2_2","SSD2_5","SSD2_6",
                "SSD3_1","SSD3_2","SSD3_3","SSD3_4",
                "SSD4_1","SSD4_2","SSD4_3","SSD4_4",
                "SSD5_1","SSD5_2","SSD5_3",
                "SSD6", "SSD7", "SSD8","SSD9_1",
                "EM4_1", "EM4_2", "EM4_3", "EM4_4", "EM4_5", "EM4_7","EM4_8", "EM4_9", "EM4_10",
                "EM6_1","EM6_2","EM6_3","EM6_4","EM6_5",
                "EM7_1","EM7_3","EM7_4",
                "AE_HL"
        );
        // 如果产品是PCIE类，则不需要处理位置
        List<PlanDeviceEntity> needHandleList;
        if (subProduct.toLowerCase().contains("pcie")) {
            needHandleList = deviceList;
        } else {
            needHandleList = deviceList.stream()
                    .filter(d -> { String pos = d.getPosition(); return pos != null && SUPPORT_POSITION.stream().anyMatch(pos::contains); })
                    .collect(Collectors.toList());
        }

        log.info("产品{}下用户{}执行了{}, 即将对如下电脑进行操作: {}",
                subProduct,
                userName,
                title,
                needHandleList.stream().map(PlanDeviceEntity::getNo).collect(Collectors.toList())
        );
        if (needHandleList.isEmpty()) {
            return;
        }
        String flash = flashEntity.getFlash();

        for (PlanDeviceEntity deviceEntity : needHandleList) {
            if (isOpen) {
                cancelShutDownDeviceJob(subProduct, deviceEntity);
            } else {
                startTimedShutDownDeviceJob(subProduct, flash, title, userName, deviceEntity);
            }
        }

        if (isOpen) {
            // 如果产品是PCIE类，则不需要处理开机
            if (subProduct.toLowerCase().contains("pcie")) {
                return;
            }
            rmsDeviceService.startupDevice(subProduct, flash, title, userName, needHandleList);
        }
    }

    /**
     * 开始关机定时任务
     * @param subProduct 产品
     * @param userName 发起人
     * @param flashName Flash批次
     * @param title 引发操作
     * @param deviceEntity 设备实体类
     */
    public void startTimedShutDownDeviceJob(
            String subProduct,
            String flashName,
            String title,
            String userName,
            PlanDeviceEntity deviceEntity
    ){
        String jobName = TimedShutdownDeviceJob.buildJobName(subProduct, deviceEntity.getMac());
        log.info("添加定时关机任务:{}", jobName);
        jobService.startJobAfter(
                jobName,
                TimedShutdownDeviceJob.JOB_GROUP_NAME,
                TimedShutdownDeviceJob.buildJobDataMap(
                        subProduct,
                        userName,
                        flashName,
                        title,
                        deviceEntity
                ),
                TimedShutdownDeviceJob.class,
                600
        );
    }

    /**
     * 检查当前设备是否存在定时关机任务
     * @param subProduct 产品
     * @param deviceEntity device实体类
     */
    public void cancelShutDownDeviceJob(String subProduct, PlanDeviceEntity deviceEntity){
        log.info("取消产品{}下设备{}({})的关机任务!", subProduct, deviceEntity.getNo(), deviceEntity.getMac());
        String jobName = TimedShutdownDeviceJob.buildJobName(subProduct, deviceEntity.getMac());
        log.info("检查是否存在关机任务 {}", jobName);
        // 取消关机任务
        jobService.cancelJobIfExist(jobName, TimedShutdownDeviceJob.JOB_GROUP_NAME);
    }



    /**
     * 更新设备样片列表信息。
     * @param deviceEntity 设备实体类
     * @param failList 失败样片列表
     */
    @Transactional
    public void updateSampleInfo(PlanDeviceEntity deviceEntity, List<String> failList) {
        log.info("回调信息中的设备测试样品信息即将更新更新");
        List<DeviceSampleEntity> sampleEntities = deviceSampleRepository.findByDeviceId(deviceEntity.getId());

        log.info("需要更新的样品信息: {}", sampleEntities.stream()
                .map(d -> String.format("%s(%s)", d.getPlanName(), d.getNo()))
                .collect(Collectors.joining(",")));
        sampleEntities.forEach(sampleEntity -> {
            // 如果样片在失败列表中，则更新样片状态为失败， 否则更新为成功。
            sampleEntity.setSuccess(!failList.contains(sampleEntity.getNo()));
            sampleEntity.setUpdatedAt(System.currentTimeMillis());
            deviceSampleRepository.save(sampleEntity);
        });

    }


    public List<PlanDeviceEntity> findDevicesByPlanId(long id) {
        return planDeviceRepository.findAllByPlanId(id);
    }

    /**
     * 根据PlanId和Mac地址列表查询设备列表
     *
     * @param id      PlanId
     * @param macList Mac地址列表
     * @return 设备列表
     */
    public List<PlanDeviceEntity> findDevicesByPlanIdAndMacIn(long id, List<String> macList) {
        return planDeviceRepository.findAllByPlanIdAndMacIn(id, macList);
    }


    /**
     * 将 Plan 下的指定的设备
     * @param planEntity
     * @param devices
     * @param requestUUID
     * @param traceId
     */
    public void prepareForDeviceDisk(
            OrderPlanEntity planEntity ,
            List<PlanDeviceEntity> devices ,
            String requestUUID,
            String traceId

    ) {
        // 获取当前请求的Span, 并拿到其中的TraceId
        List<DeviceDiskEntity> deviceDisk = devices.stream().map(d -> d.convert2DiskEntity(planEntity.getFlash(), requestUUID, traceId)).collect(Collectors.toList());
        deviceDiskRepository.saveAll(deviceDisk);
    }

    /**
     * 获取已经分配了的设备,然后从里面找出适合指定Plan的设备
     * @param orderEntity 工单
     * @param planEntity Plan
     * @return 在占用状态的设备列表中找出适合指定Plan的设备
     */
    public List<DeviceModel> findSupportDevicesInOccupied(WorkOrderEntity orderEntity, OrderPlanEntity planEntity) {
        // 获取已分配，但是尚未开始测试的设备信息
        List<DeviceModel> deviceModels = rmsDeviceService.getOccupiedDevices(orderEntity.getSubProduct());
        return matchPlanAttrsDevices(orderEntity, planEntity, deviceModels, false);
    }

    /**
     * 检查指定的这些设备是否已确认或者正在运行中,如果已确认或者正在运行中,则抛出异常.
     * @param macList 需要检查的设备列表.
     */
    public void checkDevicesNotConfirmedOrRunning(long orderId, List<String> macList) {
        List<PlanDeviceEntity> deviceList = planDeviceRepository.findAllByOrderIdAndMacInAndStatusIn(
                orderId,
                macList,
                FIXED_STATUS_LIST
        );

        log.info("检查下列设备是否已确认或者正在运行中,需要检查的设备:{} 结果:{} ",macList,deviceList);
        if(!deviceList.isEmpty()){
            log.warn("发现设备: {} 还在运行中!",deviceList);
            String msg = "设备: " + deviceList.stream().map(PlanDeviceEntity::getNo).collect(Collectors.joining(",")) + " 还在运行中! 无法分配!";
            throw new IllegalArgumentException(msg);
        }
        List<PlanDeviceEntity> failedDeviceList = planDeviceRepository.findAllByOrderIdAndMacInAndStatusIn(
                orderId,
                macList,
                List.of(PlanDeviceEntity.Status.FINISHED_FAILED)
        );
        // 检查这些失败的设备是否有被释放,如果没有被释放,则不允许使用
        if(failedDeviceList.stream().anyMatch(d -> d.getReleaseAt() == null)) {
            log.warn("发现设备: {} 处于失败状态,请先释放!",failedDeviceList);
            throw new IllegalArgumentException("发现设备: " + failedDeviceList.stream().map(PlanDeviceEntity::getNo).collect(Collectors.joining(",")) + " 处于失败状态,请先释放!");
        }

    }

    public PlanDeviceEntity findPlanDeviceByPcNo(String pcNo){
        return planDeviceRepository.findFirstByNoOrderByIdDesc(pcNo)
                .orElseThrow(() -> new DataNotFoundException("pcNo为"+pcNo+"的Plan设备信息不存在"));
    }

    /**
     * 通过设备id查询
     * @param deviceId 设备id
     * @return 设备实体类
     */
    public PlanDeviceEntity findPlanDeviceById(long deviceId){
        return planDeviceRepository.findById(deviceId).orElseThrow(() -> new DataNotFoundException("deviceId为" + deviceId + "的设备信息不存在"));
    }

    public int findDeviceByOrderIdAndMac(long orderId, String mac){
        List<PlanDeviceEntity> deviceList = planDeviceRepository.findAllByOrderIdAndMacAndStatusIn(orderId, mac, PlanDeviceEntity.FIXED_STATUS_LIST);
        return deviceList.size();
    }

}
