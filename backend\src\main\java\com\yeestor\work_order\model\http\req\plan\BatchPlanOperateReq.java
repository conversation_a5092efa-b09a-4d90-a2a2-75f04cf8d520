package com.yeestor.work_order.model.http.req.plan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel
public class BatchPlanOperateReq {

    @Min(value = 1, message = "无效的工单id")
    @ApiModelProperty(value = "工单ID")
    private long orderId ;

    @NotBlank(message = "Flash批次不能为空")
    @ApiModelProperty(value = "Flash批次")
    private String flashName ;


    @NotEmpty(message = "Plan列表不能为空")
    @ApiModelProperty(value = "Plan Id 列表")
    private List<Long> planIdList ;

}
