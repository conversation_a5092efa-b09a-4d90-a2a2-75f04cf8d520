package com.yeestor.work_order.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.resp.order.BugInfo;
import com.yeestor.work_order.model.zt.BuildInfo;
import com.yeestor.work_order.model.zt.TestTaskInfo;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class ZentaoAPITest {

    @Autowired
    ZentaoAPI zentaoAPI ;


    @Test
    void parseZentaoResp_data_IllegalStateException(){
        ObjectMapper mapper = new ObjectMapper();
        ZentaoAPI.ZentaoResp zentaoResp = new ZentaoAPI.ZentaoResp();
        zentaoResp.setData("");
        assertThrows(IllegalStateException.class, () -> zentaoResp.dataMap(mapper));
    }
    @Test
    void parseZentaoResp_IllegalStateException(){
        assertThrows(IllegalStateException.class, () -> zentaoAPI.parseZentaoResp(""));
    }


    @Test
    void getTaskInfo(){
        TestTaskInfo taskInfo =  zentaoAPI.getTestTaskInfo(2750);
        Assertions.assertThat(taskInfo)
                .isNotNull()
                .hasFieldOrPropertyWithValue("id", 2750)
                .hasFieldOrPropertyWithValue("build", 3797)
        ;

        log.info("taskInfo:{}", taskInfo);
    }

    @Test
    void getBuildInfo() {
        BuildInfo info = zentaoAPI.getBuildInfoById(3736);
        assertNotNull(info);
        log.info("info:{}", info);
        log.info("------------------------------");

    }
    @Test
    void getBuildGeneratedBugs(){
        zentaoAPI.getBuildGeneratedBugs("3736") ;
        List<BugInfo> bugs = zentaoAPI.getBuildGeneratedBugs("3774") ;
        assertNotNull(bugs);
        bugs.forEach(bug -> {
            log.info("------------------------------");
            log.info("{}",bug);
        });
    }

    @Test
    void getBugInfo() {
        BugInfo bugInfo = zentaoAPI.getBugInfo("10761");
        assertNotNull(bugInfo);
    }

    @Test
    void getBuildGeneratedBugs_not_exist() {
        List<BugInfo> bugInfos = zentaoAPI.getBuildGeneratedBugs("10761ab");
        assertNotNull(bugInfos);
        assertEquals(0,bugInfos.size());
    }
}