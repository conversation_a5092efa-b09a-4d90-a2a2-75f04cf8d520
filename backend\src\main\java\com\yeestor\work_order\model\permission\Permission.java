package com.yeestor.work_order.model.permission;


/**
 * 权限相关内容
 * 这些权限还需要与产品线进行管理。
 *
 * 直接使用权限枚举来控制， 而不是采用sql的形式来插入数据库。
 * 主要的原因是目前的阶段暂时不需要这么多的权限。
 *
 * <h1>工单相关权限</h1>
 * <ul>
 *     <li>确认Flash</li>
 *     <li>确认Flash通知</li>
 *     <li>更改优先级</li>
 *     <li>撤销工单</li>
 *     <li>停止工单</li>
 *     <li>确认环境</li>
 *     <li>确认环境通知</li>
 *     <li>开始测试</li>
 *     <li>重新测试</li>
 *     <li>上传报告</li>
 *     <li>发起review</li>
 *     <li>操作plan</li>
 *     <li>强行释放设备</li>
 * </ul>
 * <hr/>
 *
 * <h1>角色相关权限</h1>
 * <ul>
 *     <li>创建角色</li>
 *     <li>删除角色</li>
 *     <li>更改角色</li>
 *     <li>查看角色</li>
 *     <li>查看角色列表</li>
 * </ul>
 * <hr/>
 */

public enum Permission {

    // 确认Flash
    CONFIRM_FLASH(PermissionGroup.WORK_ORDER, "确认Flash"),
    // 修改工单Mars信息
    UPDATE_MARS(PermissionGroup.WORK_ORDER, "修改Mars信息"),
    // 确认Flash通知
    CONFIRM_FLASH_NOTIFICATION(PermissionGroup.WORK_ORDER, "确认Flash通知"),
    // 更改优先级
    UPDATE_PRIORITY(PermissionGroup.WORK_ORDER, "更改优先级"),
    // 撤销工单
    REVOKE_ORDER(PermissionGroup.WORK_ORDER, "撤销工单"),
    // 强行释放设备
    FORCE_RELEASE_DEVICE(PermissionGroup.WORK_ORDER, "强行释放设备"),
    // 释放RMS异常锁定设备
    RELEASE_LOCKED_DEVICE(PermissionGroup.WORK_ORDER, "释放RMS设备"),
    // 导入历史工单
    IMPORT_HISTORY_ORDER(PermissionGroup.WORK_ORDER, "导入历史版本"),
    // 取消工单
    CANCEL_ORDER(PermissionGroup.WORK_ORDER, "取消工单"),
    // 更新禅道版本信息
    UPDATE_ZENTAO(PermissionGroup.WORK_ORDER, "更新禅道绑定信息"),


    /* ------------------------ flash相关 --------------------*/
    // 更新Flash信息
    UPDATE_FLASH_INFO(PermissionGroup.FLASH, "更新Flash信息"),
    // 增加新的Flash批次
    INSERT_FLASH(PermissionGroup.FLASH, "增加Flash批次"),
    // 确认环境通知
    CONFIRM_ENVIRONMENT_NOTIFICATION(PermissionGroup.FLASH, "确认环境通知"),
    // 所有Plan开始分配
    RESUME_FLASH(PermissionGroup.FLASH, "Flash开始分配"),
    // 所有Plan暂缓分配
    PAUSE_FLASH(PermissionGroup.FLASH, "Flash暂缓分配"),
    // 绑定禅道bug
    BIND_BUG_ZT(PermissionGroup.FLASH, "绑定禅道bug"),
    // 取消Flash
    CANCEL_FLASH(PermissionGroup.FLASH, "Flash取消测试"),
    // 上传报告
    UPLOAD_REPORT(PermissionGroup.FLASH, "上传报告"),
    // 删除Flash报告
    DELETE_REPORT(PermissionGroup.FLASH, "Flash报告删除"),
    // 不发起Fail分析与review，直接结束
    ENDING_FLASH(PermissionGroup.FLASH, "Flash流程结束"),
    // 发起fail分析
    START_FAIL(PermissionGroup.FLASH, "fail分析发起"),
    // 完成fail分析
    FINISH_FAIL(PermissionGroup.FLASH, "fail分析完成"),
    // Review
    REVIEW_FLASH(PermissionGroup.FLASH, "Review"),
    // Flash状态回退到测试中
    FALLBACK_STATUS_FLASH(PermissionGroup.FLASH, "Flash回退测试中"),
    // Flash 复测release版本
    RELEASE_RETEST_FLASH(PermissionGroup.FLASH, "Release版本复测"),
    // Flash状态校准
    STATUS_CHECK(PermissionGroup.FLASH, "状态校准"),
    // Flash样片校准
    SAMPLE_CHECK(PermissionGroup.FLASH, "样片校准")


    /* ------------------------ plan相关 --------------------*/
    // 增加Plan
    ,INSERT_PLAN(PermissionGroup.PLAN, "增加Plan"),
    // 修改Plan优先级
    UPDATE_PRIORITY_PLAN(PermissionGroup.PLAN, "修改优先级"),
    // 取消Plan测试
    CANCEL_PLAN(PermissionGroup.PLAN, "取消测试"),
    // 操作plan
    OPERATE_PLAN(PermissionGroup.PLAN, "操作plan"),
    // 重新测试
    REPEAT_TEST(PermissionGroup.PLAN, "重新测试"),
    // 开始测试
    START_TEST(PermissionGroup.PLAN, "开始测试"),
    // 确认环境
    CONFIRM_ENVIRONMENT(PermissionGroup.PLAN, "确认环境"),
    HISTORY_TEST_DEV(PermissionGroup.PLAN, "历史设备"),
    // 手动解锁设备 并重新分配设备
    MANUAL_UNLOCK(PermissionGroup.PLAN, "重新分配")


    /* ------------------------ 通知相关 --------------------*/
    // 创建配置
    ,CREATE_TIMEOUT_CONFIG(PermissionGroup.NOTIFICATION, "配置超时通知")


    /* ----------------------角色相关---------------------------- */
    // 创建角色
    ,CREATE_ROLE(PermissionGroup.ROLE, "创建角色")
    // 删除角色
    ,DELETE_ROLE(PermissionGroup.ROLE, "删除角色")
    // 更改角色
    ,UPDATE_ROLE(PermissionGroup.ROLE, "更改角色")
    // 查看角色
    ,VIEW_ROLE(PermissionGroup.ROLE, "查看角色")
    // 查看角色列表
    ,VIEW_ROLE_LIST(PermissionGroup.ROLE, "查看角色列表")

    ;





    private final PermissionGroup group ;
    private final String locale ;

    Permission(PermissionGroup group, String locale) {
        this.group = group ;
        this.locale = locale ;
    }

    public PermissionGroup getGroup() {
        return group;
    }

    public String getLocale() {
        return locale;
    }
}
