{"version": "0.2.0", "configurations": [{"type": "java", "name": "Debug e<PERSON><PERSON>", "request": "launch", "mainClass": "com.yeestor.work_order.WorkOrderApplication", "projectName": "system.work_order", "args": "--spring.profiles.active=dev", "vmArgs": "-Dspring.profiles.active=dev -Dspring.cloud.client.ip-address=***********", "console": "internalConsole", "stopOnEntry": false, "cwd": "${workspaceFolder}"}, {"type": "java", "name": "Attach to JVM", "request": "attach", "hostName": "localhost", "port": 5005}]}