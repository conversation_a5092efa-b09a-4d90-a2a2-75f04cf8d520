package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("启动终端plan测试返回的格式")
public class TerminalPlanTestRespModel {

    @ApiModel(value = "PlatformTestResult", description = "成功或者设备信息。")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class PlatformTestResult {
        @ApiModelProperty("电脑IP")
        private String ip;  // 电脑IP

        @ApiModelProperty("电脑mac地址")
        private String mac;  // 电脑mac地址

        @ApiModelProperty("平台编号")
        private String number; // 平台编号

        @ApiModelProperty("出错原因")
        private String reason;  // 出错原因
    }

    @ApiModelProperty("成功的信息列表")
    private List<PlatformTestResult> successLst;

    @ApiModelProperty("失败的信息列表")
    private List<PlatformTestResult> failLst;
}
