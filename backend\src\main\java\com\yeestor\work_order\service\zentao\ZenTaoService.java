package com.yeestor.work_order.service.zentao;

import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.zentao.api.ZentaoFeignClient;
import com.yeestor.zentao.model.ZtResolvedBug;
import com.yeestor.zentao.model.resp.BugInfoResp;
import com.yeestor.zentao.model.resp.build.ZtBuildBugResp;
import com.yeestor.zentao.model.resp.build.ZtBuildResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ZenTaoService {

    private final ZentaoFeignClient zentaoFeignClient;

    /**
     * 通过禅道API获取产品线下所有bug
     * @param ztProductId 禅道产品id
     * @return 返回bug信息
     */
    public HashMap<String, String> fetchBugList(String ztProductId){
        HandleResp<HashMap<String, String>> resp = zentaoFeignClient.fetchBugListByProductId("release", ztProductId);
        if(resp.getCode() == 0){
            return resp.getData();
        }
        throw new DataNotFoundException("获取数据失败！");
    }

    /**
     * 修改bug信息
     * @param bugId bugId禅道bugID
     * @param ztBugInfo 变更参数 comment、status等
     * @return 执行结果
     */
    public boolean updateBugInfo(String bugId, ZtResolvedBug ztBugInfo){
        HandleResp<String> resp = zentaoFeignClient.updateBug("release", bugId, ztBugInfo);
        return resp.getCode() == 0;
    }

    /**
     * 获取bug信息
     * @param bugId bug id
     * @return bug信息
     */
    public BugInfoResp fetchBugInfo(String bugId){
        HandleResp<BugInfoResp> resp = zentaoFeignClient.fetchBugInfoById("release", bugId);
        return resp.getData();
    }

    /**
     * 更新测试单信息
     * @param taskId 测试单id
     * @param params 需要修改参数，例如comment、status等
     * @return 返回执行结果
     */
    public boolean updateTestTask(String taskId, HashMap<String, String> params){
        HandleResp<String> resp = zentaoFeignClient.updateTestTask("release", taskId, params);
        return resp.getCode() == 0;
    }

    /**
     * 更新测试单状态
     * @param taskId 测试单id
     * @param status 测试单修改状态【done、doing、wait】
     * @return 返回执行结果
     */
    public boolean updateTestTaskStatus(String taskId, String status){
        log.info("update test task: {} status to {}", taskId, status);
        HandleResp<Object> resp = zentaoFeignClient.updateTaskStatus("release", taskId, status);
        return resp.getCode() == 0;
    }

    /**
     * 通过禅道项目id禅道构建版本信息
     * @param projectId 禅道项目id
     * @return 返回查询信息
     */
    public List<ZtBuildResp> fetchBuildsByProjectId(String projectId){
        HandleResp<List<ZtBuildResp>> resp = zentaoFeignClient.fetchBuildsByProjectId("release", projectId);
        return resp.getData();
    }

    /**
     * 获取禅道版本信息
     * @param buildId 禅道版本id
     * @return 禅道版本信息
     */
    public ZtBuildBugResp fetchBuildByBuildId(String buildId){
        HandleResp<ZtBuildBugResp> resp = zentaoFeignClient.fetchBuildInfo("release", buildId);
        log.info("fetchBuildByBuildId: {}", resp.getData());
        return resp.getData();
    }

    /**
     * 获取禅道执行下的所有版本
     * @param buildId 禅道版本id
     * @return 禅道版本列表
     */
    public List<ZtBuildResp> fetchBuildsByExecutionId(String buildId){
        HandleResp<List<ZtBuildResp>> resp = zentaoFeignClient.fetchBuildsByExecutionId("release", buildId);
        return resp.getData();
    }

    /**
     * 获取禅道版本的基础信息
     * @param buildId 禅道版本id
     * @return 版本信息
     */
    public ZtBuildBugResp fetchBuildInfoById(String buildId){
        HandleResp<ZtBuildBugResp> resp = zentaoFeignClient.fetchBuildInfo("release", buildId);
        return resp.getData();
    }

}
