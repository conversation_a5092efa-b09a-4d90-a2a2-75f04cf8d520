package com.yeestor.work_order.controller;

import com.yeestor.model.http.HandleResp;
import com.yeestor.model.http.ResultCode;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.exception.ErrorImportOrderException;
import com.yeestor.work_order.exception.PermissionDeniedAccessException;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.persistence.NonUniqueResultException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@ControllerAdvice
public class ExceptionAdvice {
    public final NotificationService notificationService;

    public ExceptionAdvice(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

    @ResponseBody
    @ExceptionHandler(DataNotFoundException.class)
    public HandleResp<String> dataNotFoundException(DataNotFoundException e) {
        LogUtils.clearTracePoint();
        return HandleResp.failed(e.getMessage());
    }
    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    public HandleResp<String> illegalArgumentException(IllegalArgumentException e) {
        log.warn("Found illegalArgument!",e);
        LogUtils.clearTracePoint();
        return HandleResp.failed(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(PermissionDeniedAccessException.class)
    public HandleResp<String> permissionDeniedException(PermissionDeniedAccessException e) {
        LogUtils.clearTracePoint();
        return HandleResp.failed(e.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public HandleResp<Map<String, String>> handleValidationExceptions(
            MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        LogUtils.clearTracePoint();
        return HandleResp.custom(ResultCode.INVALID_PARAM, errors, "参数错误");
    }

    @ResponseBody
    @ExceptionHandler(ErrorImportOrderException.class)
    public HandleResp<String> errorNoticeException(ErrorImportOrderException e) {
        notificationService.sendErrorNotification(e.getMessage());
        LogUtils.clearTracePoint();
        return HandleResp.failed(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(NonUniqueResultException.class)
    public HandleResp<String> nonUniqueResultException(NonUniqueResultException e) {
        log.warn("Found nonUniqueResultException!",e);
        notificationService.sendErrorNotification(e.getMessage());
        LogUtils.clearTracePoint();
        return HandleResp.failed(e.getMessage());
    }
}
