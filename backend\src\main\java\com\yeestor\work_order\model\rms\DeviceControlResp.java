package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("电脑开关机返回的格式")
public class DeviceControlResp {
    @ApiModel(value = "DeviceControlResult", description = "成功或者失败的设备信息。")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DeviceControlResult {
        private String ip;
        private String mac;
        private String pc_no;
        private String reason;
    }

    @ApiModelProperty("操作成功的mac列表")
    public List<DeviceControlResult> successLst;

    @ApiModelProperty("操作失败的mac列表")
    public List<DeviceControlResult> failLst;
}
