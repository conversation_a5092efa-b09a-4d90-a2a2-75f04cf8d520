﻿// const { NODE_ENV } = process.env;
// import defaultSettings from './defaultSettings';
const routes = [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        path: '/user',
        routes: [
          {
            name: 'login',
            path: '/user/login',
            component: './user/Login',
          },
          {
            name: 'login',
            path: '/user/test/login',
            component: './user/TestLogin',
          },
          {
            name: 'login',
            path: '/user/auth',
            component: './user/LoginCallBack',
          },
          {
            name: 'login',
            path: '/user/ding',
            component: './user/ding',
          },
          {
            name: 'login',
            path: '/user/account',
            component: './user/account',
          },
          {
            name: 'login',
            path: '/user/onload',
            component: './user/onload',
          },
          {
            component: './404',
          },
        ],
      },
      {
        component: './404',
      },
    ],
  },
  {
    path: '/report',
    name: 'report',
    icon: 'barChart',
    routes: [
      {
        name: 'order',
        path: 'order',
        component: './report/OrderReportPage',
      },
      {
        name: 'flash',
        path: 'flash',
        component: './report/FlashReportPage',
      },
      {
        name: 'plan',
        path: 'plan',
        component: './report/PlanReportPage',
      },
      {
        name: 'device',
        path: 'device',
        component: './report/DeviceReportPage',
      },
      {
        name: 'zentao',
        path: 'zentao',
        component: './report/ZenTaoReportPage',
      },
    ],
  },
  {
    name: 'role',
    icon: 'partition',
    path: '/role',
    component: './role',
    access: 'user_canVIEW_ROLE_LIST',
  },
  {
    name: 'work_order',
    icon: 'experiment',
    path: '/work_order',
    component: '../layouts/BasicLayout',
    routes: [
      {
        path: 'GE/list',
        component: './work_order/product',
        icon: 'smile',
        name: 'general',
      },
      {
        path: 'GE/:productLine/:id',
        component: './work_order/product/life',
        hideInMenu: true,
        exact: true,
      },
      {
        path: 'flash/insert/:product/:productLine/:id',
        component: './work_order/insertFlash',
        hideInMenu: true,
      },
      {
        path: ':productLine/:orderId/:flashName/:planId/:planName/:type/detail',
        component: './work_order/planDetail/content',
        hideInMenu: true,
        exact: true,
      },
      {
        path: ':subProduct/:orderId/:flashName/:planId/terminal/detail',
        component: './work_order/terminalPlanDetail/content',
        hideInMenu: true,
        exact: true,
      },
      {
        path: 'SSD/list',
        component: './work_order/product',
        icon: 'smile',
        name: 'ssd',
      },
      {
        path: 'SSD/:productLine/:id',
        component: './work_order/product/life',
        hideInMenu: true,
        exact: true,
      },
      {
        path: 'EM/list',
        component: './work_order/product',
        icon: 'smile',
        name: 'em',
      },
      {
        path: 'EM/:productLine/:id',
        component: './work_order/product/life',
        hideInMenu: true,
        exact: true,
      },
      {
        path: 'IND/list',
        component: './work_order/product',
        icon: 'smile',
        name: 'indus',
      },
      {
        path: 'IND/:productLine/:id',
        component: './work_order/product/life',
        hideInMenu: true,
        exact: true,
      }
    ],
  },
  {
    name: 'machine',
    icon: 'Desktop',
    path: '/machines',
    component: '../layouts/BasicLayout',
    routes: [
      {
        path: '/machines/order',
        component: './machineList/order',
        name: 'order',
      },
      {
        path: '/machines/rms',
        component: './machineList/rms',
        name: 'rms',
      },
    ],
  },
  {
    name: 'mobile',
    path: '/mobile',
    hideInMenu: true,
    component: '../layouts/BasicLayout',
    routes: [
      {
        path: ':productLine/:product/:id/addplan',
        component: '../pages_mobile/planPage/addPage',
        hideInMenu: true,
        exact: true,
      },
    ],
  },
  {
    path: '/device/:pcNo',
    component: './machine',
    layout: {
      hideMenu: true,
      hideNav: true,
      hideFooter: false,
    },
  },
  {
    path: '/',
    redirect: '/work_order/GE/list',
  },
  {
    component: './404',
  },
];
export default routes;
