package com.yeestor.work_order.model.http.req.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ZenTaoImportParams", description = "禅道导入工单时需要的参数")
public class ZenTaoImportParams {
    @Min(value = 0,message = "无效的测试单id")
    @ApiModelProperty(value = "禅道ID", required = true)
    private int taskId ;

    @NotBlank(message = "Flash不能为空")
    @ApiModelProperty(value = "Flash,目前仅支持一个", required = true)
    private String flash ;

    @NotBlank(message = "产品线不能为空")
    @ApiModelProperty(value = "所属产品线", required = true)
    private String product ;

    @NotBlank(message = "产品不能为空")
    @ApiModelProperty(value = "产品", required = true)
    private String subProduct ;
}
