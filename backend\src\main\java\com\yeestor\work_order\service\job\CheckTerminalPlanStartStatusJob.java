package com.yeestor.work_order.service.job;

import com.yeestor.work_order.entity.PlanPlatformEntity;
import com.yeestor.work_order.model.rms.TerminalModel;
import com.yeestor.work_order.repository.PlanPlatformRepository;
import com.yeestor.work_order.service.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class CheckTerminalPlanStartStatusJob implements Job {
    public final static String JOB_GROUP_NAME = "CheckTerminalPlanStartStatusJob";
    private final PlanPlatformRepository planPlatformRepository;
    private final NotificationService notificationService;

    public static String buildJobName(String orderFlashNo, String plan) {
        return orderFlashNo + "_" + plan + "_" + JOB_GROUP_NAME;
    }

    public static Map<String, Object> buildJobDataMap(
            long orderId,
            String orderFlashNo,
            long planId,
            List<TerminalModel> terminalList
    ) {
        List<String> macList = new ArrayList<>();

        for(TerminalModel model : terminalList){
            List<String> macNumbers = new ArrayList<>();
            for(String number : model.getNumberList()){
                macNumbers.add(model.getMac() + ";" + number);
            }
            macList.addAll(macNumbers);
        }
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("orderId", orderId);
        dataMap.put("orderFlashNo", orderFlashNo);
        dataMap.put("planId", planId);
        dataMap.put("platformsStr", String.join("#", macList));
        return dataMap;
    }

    @Override
    public void execute(JobExecutionContext context) {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        log.debug("execute:{}  map key:{} value:{} ", context.getJobDetail().getKey().getName(), Arrays.toString(dataMap.getKeys()), dataMap.values());
        if (
                !dataMap.containsKey("orderId") ||
                        !dataMap.containsKey("orderFlashNo") ||
                        !dataMap.containsKey("planId") ||
                        !dataMap.containsKey("deviceMacList")
        ) {
            log.warn(" execute:{}  map key:{} value:{} ", context.getJobDetail().getKey().getName(), Arrays.toString(dataMap.getKeys()), dataMap.values());
            return;
        }
        long orderId = dataMap.getLong("orderId");
        String orderFlashNo = dataMap.getString("orderFlashNo");
        long planId = dataMap.getLong("planId");
        String platformNumberStr = dataMap.getString("platformNumbers");
        List<String> macToNumbersList = Arrays.asList(platformNumberStr.split("#"));
        log.info("CheckTerminalPlanStartStatusJob macToNumbersList: {}", macToNumbersList);

        List<PlanPlatformEntity> resultList = new ArrayList<>();

        Map<String, List<String>> macToNumbersMap = macToNumbersList.stream()
                .collect(Collectors.groupingBy(
                        s -> s.split(";")[0], // 根据MAC地址分组
                        Collectors.mapping(
                                s -> s.split(";")[1], // 提取number部分
                                Collectors.toList() // 收集到列表中
                        )
                ));
        log.info("macToNumbersMap: {}", macToNumbersMap);
        for (Map.Entry<String, List<String>> entry : macToNumbersMap.entrySet()) {
            String mac = entry.getKey();
            List<String> numberList = entry.getValue();
            List<PlanPlatformEntity> entityList = planPlatformRepository.findAllByPlanIdAndMacAndNumberIn(planId, mac, numberList);
            resultList.addAll(entityList);
        }
        log.info("CheckTerminalPlanStartStatusJob resultList: {}", resultList);
        notificationService.sendTerminalPlanStartStatusNotification(orderId, orderFlashNo, planId, resultList);

    }

}
