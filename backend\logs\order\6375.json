{"@timestamp":"2025-07-23T11:00:00.2+08:00","@version":"1","message":"于2025-07-22 20:38:56发起的 Plan21 自动完成任务已到达触发时间！","logger_name":"com.yeestor.work_order.service.job.AutoCompletePlanJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动完成Plan"}
{"@timestamp":"2025-07-23T11:00:00.209+08:00","@version":"1","message":"Plan21 自动释放设备!","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.221+08:00","@version":"1","message":"解锁 [YS5083##MP#########40519#3DV8_HY_TLC_250145_Alpha_JGT-3DV8-A_64GB] 工单下 Plan21 的设备:[*************, *************, ************, ************, *************, ************] ","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.244+08:00","@version":"1","message":"[6375] - Plan21 移除redis: U3_6375_*************_A4-0C-66-12-95-BD 结果为:0 ","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.327+08:00","@version":"1","message":"[6375] - Plan21 移除redis: U3_6375_*************_A4-0C-66-12-94-52 结果为:0 ","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.329+08:00","@version":"1","message":"[6375] - Plan21 移除redis: U3_6375_************_A4-0C-66-12-95-6E 结果为:0 ","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.33+08:00","@version":"1","message":"[6375] - Plan21 移除redis: U3_6375_************_A4-0C-66-12-95-A2 结果为:0 ","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.332+08:00","@version":"1","message":"[6375] - Plan21 移除redis: U3_6375_*************_A4-0C-66-12-95-BB 结果为:0 ","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.334+08:00","@version":"1","message":"[6375] - Plan21 移除redis: U3_6375_************_A4-0C-66-12-96-95 结果为:0 ","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.338+08:00","@version":"1","message":"[6583eb44] HTTP POST http://ereport.yeestor.com/wo/device/unlock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.504+08:00","@version":"1","message":"unlockDevice with ipList:[*************, *************, ************, ************, *************, ************] - macList:[A4-0C-66-12-95-BD, A4-0C-66-12-94-52, A4-0C-66-12-95-6E, A4-0C-66-12-95-A2, A4-0C-66-12-95-BB, A4-0C-66-12-96-95],no:YS5083##MP#########40519#3DV8_HY_TLC_250145_Alpha_JGT-3DV8-A_64GB got data: HandleResp(code=0, data=, msg=解锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.55+08:00","@version":"1","message":"产品U3下用户null执行了手动释放设备后电脑关机, 即将对如下电脑进行操作: []","logger_name":"com.yeestor.work_order.service.plan.DeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.553+08:00","@version":"1","message":"更新Flash:JGT-3DV8-A_64GB的样片数量从80变更为96","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.584+08:00","@version":"1","message":"Plan21下的10台设备都已释放,将状态更新至已完成","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.584+08:00","@version":"1","message":"取消JGT-3DV8-A_64GB下的Plan21的所有任务!","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.599+08:00","@version":"1","message":"delete job :AutoCompletePlanJob.YS5083##MP#########40519#3DV8_HY_TLC_250145_Alpha_JGT-3DV8-A_64GB_Plan21","logger_name":"com.yeestor.work_order.service.job.JobService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.602+08:00","@version":"1","message":"更新：Plan21的状态至完成","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.614+08:00","@version":"1","message":"Flash:JGT-3DV8-A_64GB 下所有的Plan都已完成,调用合并报告并等待.","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.615+08:00","@version":"1","message":"mergeOrderReport with YS5083##MP#########40519#3DV8_HY_TLC_250145_Alpha_JGT-3DV8-A_64GB,U3 ","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.617+08:00","@version":"1","message":"[22bd1703] HTTP GET http://ereport.yeestor.com/wo/report/merge?no=YS5083%23%23MP%23%23%23%23%23%23%23%23%2340519%233DV8_HY_TLC_250145_Alpha_JGT-3DV8-A_64GB&p=U3","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.783+08:00","@version":"1","message":"mergeOrderReport with YS5083##MP#########40519#3DV8_HY_TLC_250145_Alpha_JGT-3DV8-A_64GB,U3 got data: HandleResp(code=0, data=, msg=报告合并成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.831+08:00","@version":"1","message":"update Flash: JGT-3DV8-A_64GB，uuid: f419461e-0713-4c35-bea6-6a4ceaf026b5","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:00.833+08:00","@version":"1","message":"updateDingInteractiveMsg: InteractiveCardParams(containName=iSee, receiverUserIdList=null, outTrackId=f419461e-0713-4c35-bea6-6a4ceaf026b5, cardTemplateId=fc9dbba1-d13e-4530-9cee-64c524a60125.schema, callbackRouteKey=null, cardDataParamMap={title=等待上传报告, version=V6.10.00.40.519, versionType=Alpha, builder=brandon.yi, buildAt=2025-07-22 14:37:01, flash=3DV8_HY_TLC_1LUN, product=GE - U3, detailUrl=dingtalk://dingtalkclient/action/openapp?corpid=ding31d78c632c4953fa35c2f4657eb6378f&container_type=work_platform&app_id=0_1368250694&redirect_type=jump&redirect_url=http%3A%2F%2F172.18.8.126%3A8512%2Fwork_order%2FGE%2FU3%2F6375%3Fflash%3DJGT-3DV8-A_64GB, sys_full_json_obj={\"flashBatch\":[{\"flash\":\"JGT-3DV8-A_64GB\",\"num\":96,\"planNum\":1,\"startAt\":1753171100496,\"spendTime\":\"19时1分40秒\",\"status\":\"等待合并报告\",\"testProgress\":\"1/1\",\"autoLoc\":\"样片重新编号\",\"testPerson\":\"Milly.Huang\"}]}}, cardMediaIdMap=null, conversationType=0, userIdType=1, privateCardDataMap=null, privateCardMediaIdMap=null)","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
{"@timestamp":"2025-07-23T11:00:01.16+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eab2a40249c04fa","spanId":"4eab2a40249c04fa","no":"6375","traceType":"自动释放设备"}
