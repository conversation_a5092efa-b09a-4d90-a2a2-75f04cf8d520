package com.yeestor.work_order.controller.order;

import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.OrderDetailEntity;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.http.req.UpdateFlashInfoReq;
import com.yeestor.work_order.model.http.req.order.FlashInfo;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.role.RoleService;
import com.yeestor.work_order.utils.LogUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/flash/")
@Api(tags = {"Flash"}, value = "用户处理Flash批次相关信息的接口")
public class FlashController {

    private final FlashService flashService ;
    private final OrderService orderService ;
    private final RoleService roleService ;

    @PutMapping("/{orderId}/{flash}/update")
    @ApiOperation(value = "更新Flash批次的信息", notes = "上传指定Plan的报告文件")
    public HandleResp<String> updateFlashInfo(
            @ApiParam(value= "工单 ID") @PathVariable("orderId") long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @Valid @RequestBody UpdateFlashInfoReq body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    )
    {
        String operate = "更新Flash批次信息" ;
        LogUtils.setOrderAndFlashTracePoint(orderId,flash, operate);

        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId) ;
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        log.info("用户{} 发起{}请求 -- 操作对象：{} ！", userDetail.getUsername(),operate, flash);
        log.debug("updateFlashInfo use body: {} ", body);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.UPDATE_FLASH_INFO);

        flashService.updateFlashInfo(flashEntity, body);

        log.debug("updateFlashInfo success!");
        LogUtils.clearTracePoint();
        return HandleResp.ok(null ,"更新Flash批次信息成功");
    }


    @PostMapping("/create")
    @ApiOperation(value = "新增Flash批次", notes = "给指定的Flash批次添加批次，需要重新指定Plan")
    public HandleResp<String> addFlash(
            @ApiParam(value= "工单 ID") @RequestParam("orderId") long orderId,
            @Valid @RequestBody FlashInfo body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "增加Flash批次" ;
        LogUtils.setOrderTracePoint(orderId, operate);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId) ;

        if (orderEntity.getStatus() != WorkOrderEntity.Status.CONFIRMED_FLASH && orderEntity.getStatus() != WorkOrderEntity.Status.TESTING) {
            return HandleResp.failed("当前工单状态无法增加Flash批次！");
        }

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.CONFIRM_FLASH);

        if (flashService.isFlashExist(orderId, body.getFlash())) {
            return HandleResp.failed("Flash批次已存在");
        }

        OrderDetailEntity orderDetailEntity = orderService.findOrderDetailOrElseThrow(orderId);
        orderService.handleFlashData(body,orderEntity, orderDetailEntity);

        LogUtils.clearTracePoint();
        return HandleResp.ok(null ,"Flash批次添加成功");
    }

    @ApiOperation(value = "取消flash批次测试")
    @PostMapping("/{orderId}/{flash}/cancel")
    public HandleResp<String> cancelFlash(
            @ApiParam(value= "工单 ID") @PathVariable("orderId") long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @NotBlank(message = "取消原因不能为空") @RequestParam("reason") @ApiParam("撤销批次的原因") String reason,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "取消Flash批次测试" ;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId) ;
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        if (flashEntity.getStatus().ordinal() > OrderFlashEntity.Status.IN_PROGRESS.ordinal()) {
            return HandleResp.failed("当前Flash批次测试已结束，不允许取消测试！");
        }
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.CANCEL_FLASH);

        LogUtils.setOrderAndFlashTracePoint(orderEntity.getId(), flash, operate);

        flashService.cancelFlash(orderEntity, flashEntity, reason);

        LogUtils.clearTracePoint();
        return HandleResp.ok(null ,"Flash批次取消测试完成！");
    }

    @GetMapping("/{orderId}/{flash}/unbind/bugs")
    @ApiOperation(value = "获取flash批次未绑定的禅道bug（产品线下所有bug）", notes = "获取flash批次未绑定的禅道bug（产品线下所有bug）")
    public HandleResp<HashMap<String, String>> fetchUnboundBugs(
            @ApiParam(value= "工单 ID") @PathVariable("orderId") long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        if (!flashService.isFlashExist(orderId, flash)) {
            return HandleResp.failed("Flash批次不存在");
        }
        OrderDetailEntity orderDetailEntity = orderService.findOrderDetailOrElseThrow(orderId);

        return HandleResp.ok(flashService.fetchFlashUnboundBugs(orderId, flash, orderDetailEntity.getZentaoProduct()),"获取未绑定禅道bug成功！");
    }

    @ApiOperation(value = "结束Flash流程", notes = "直接跳过fail分析与review")
    @PostMapping("/{orderId}/{flash}/ending")
    public HandleResp<String> endingFlash(
            @ApiParam(value= "工单 ID") @PathVariable("orderId") long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "结束Flash批次测试" ;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId) ;
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        if (flashEntity.getStatus().ordinal() < OrderFlashEntity.Status.WAITING_FOR_REPORT.ordinal()) {
            return HandleResp.failed("当前Flash批次不能直接结束测试！");
        }
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.ENDING_FLASH);

        LogUtils.setOrderAndFlashTracePoint(orderEntity.getId(), flash, operate);

        flashService.endingFlashFlow(orderEntity, flashEntity);

        LogUtils.clearTracePoint();
        return HandleResp.ok(null ,"Flash流程已经结束！");
    }

    @ApiOperation(value = "Flash状态回退到测试中", notes = "等待上传报告状态之前的Flash状态回退")
    @PostMapping("/{orderId}/{flash}/status/fallback")
    public HandleResp<String> fallbackFlashStatus(
            @ApiParam(value= "工单 ID") @PathVariable("orderId") long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "Flash批次回退测试中";
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        OrderFlashEntity.Status status = flashEntity.getStatus();
        if (status != OrderFlashEntity.Status.WAITING_FOR_MERGE && status != OrderFlashEntity.Status.WAITING_FOR_REPORT) {
            return HandleResp.failed("当前Flash批次不支持回退测试状态！");
        }
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.FALLBACK_STATUS_FLASH);

        LogUtils.setOrderAndFlashTracePoint(orderEntity.getId(), flash, operate);

        flashService.fallbackFlashToInProgress(flashEntity);

        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "Flash测试状态成功！");

    }

    @ApiOperation(value = "Flash批次样片数据校验", notes = "Flash批次样片数据校验")
    @PostMapping("/{orderId}/{flash}/leftNum/check")
    public HandleResp<String> flashLeftNumCheck(
            @ApiParam(value= "工单 ID") @PathVariable("orderId") long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "Flash批次样片数据校验";
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.SAMPLE_CHECK);
        flashService.checkFlashSampleNum(orderId, flashEntity);
        return HandleResp.ok(null, "Flash批次样片数据校验成功！");

    }

    @ApiOperation(value = "Flash批次复测Release版本", notes = "Flash批次复测Release版本")
    @PostMapping("/{orderId}/{flash}/retest/release")
    public HandleResp<String> flashRetestRelease(
            @ApiParam(value= "工单 ID") @PathVariable("orderId") long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "Flash批次复测Release版本";
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);

        if (!flashEntity.getMarkVersion().equals("Alpha")) {
            return HandleResp.failed("当前Flash批次不支持复测Release版本！");
        }

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.RELEASE_RETEST_FLASH);
        flashService.retestReleaseFlash(orderEntity, flashEntity, userDetail);
        return HandleResp.ok(null, "Flash批次已开始Release版本测试！");

    }

    @ApiOperation(value = "Flash批次状态校验", notes = "Flash批次状态校验")
    @PostMapping("/{orderId}/{flash}/status/check")
    public HandleResp<String> flashStatusCheck(
            @ApiParam(value= "工单 ID") @PathVariable("orderId") long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "Flash批次状态校验";
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.STATUS_CHECK);
        flashService.checkWaitMerge(orderEntity, flashEntity);
        return HandleResp.ok(null, "Flash批次状态校验成功！");

    }

}
