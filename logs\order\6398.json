{"@timestamp":"2025-07-24T14:06:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.644+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.646+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.678+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.696+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.714+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.731+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.732+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.732+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.732+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.732+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.733+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:10:02.626+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.626+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.653+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.672+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.689+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.704+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.704+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.704+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.705+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.705+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.705+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.639+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.639+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.682+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.709+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.738+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.766+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.766+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.766+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.767+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.767+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.767+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:22:02.615+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.615+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.633+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.647+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.677+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.677+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.677+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.678+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.678+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.652+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.656+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.656+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.686+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.71+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.739+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.773+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.773+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.774+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.775+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.775+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.775+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.715+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:02.768+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.776+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.897+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.954+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.977+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.994+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.997+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:02.998+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.008+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.015+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.015+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.66+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.666+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.666+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.711+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.739+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.765+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.792+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.793+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.793+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.793+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.793+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.793+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.614+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:02.621+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.621+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.646+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.684+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.699+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.714+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.715+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.721+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.737+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.789+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.643+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.646+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.646+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.669+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.7+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.719+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.752+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.755+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.757+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.76+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.76+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.761+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:10:02.616+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.616+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.647+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.661+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.676+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.69+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.69+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.69+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.691+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.691+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.691+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.632+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.635+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.635+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.665+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.694+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.71+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.725+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.725+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.725+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.725+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.726+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.726+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:02.614+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.614+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.646+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.691+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.692+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.692+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.692+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.693+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.693+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:02.887+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.003+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6633, orderId=6398, flash=29038_BICS6-QLC_1024GB, orderFlashNo=YS9205##MP2XB5722A#17629#29038_BICS6#250147_29038_BICS6-QLC_1024GB, num=10, leftNum=7)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.05+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.265+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.302+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.349+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.381+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.383+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113777, orderId=6398, name=Plan601, status=QUEUE, priority=90), OrderPlanEntity(id=113778, orderId=6398, name=Plan602, status=QUEUE, priority=90), OrderPlanEntity(id=113781, orderId=6398, name=Plan605, status=QUEUE, priority=85), OrderPlanEntity(id=113783, orderId=6398, name=Plan607, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.384+08:00","@version":"1","message":"执行预分配前共有7颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.389+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.395+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.397+08:00","@version":"1","message":"[6398] - [29038_BICS6-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6398","traceType":"分配设备"}
