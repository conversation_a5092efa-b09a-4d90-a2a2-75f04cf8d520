package com.yeestor.work_order.entity.review;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity(name = "ReviewResult")
@Table(name="wo_review_result")
public class ReviewResultEntity {

    public static final int TYPE_REVIEW_FINISH = 0 ;
    public static final int TYPE_SKIP_REVIEW = 1 ;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "order_id")
    private long orderId ;

    /**
     * 评审时的工单版本
     * 如果alpha转成release之后再评审，则版本记录为release
     */
    @Column(name = "version_type")
    private String versionType;

    @Column(name = "flash")
    private String flash ;

    /**
     * Review 通过或者不通过.true 通过,false 不通过
     */
    @Column(name = "result")
    private Boolean result ;

    /**
     *  跳过的情况
     */
    @Column(name = "path")
    private String path ;

    /**
     * 测试小结， 跳过review时产生，后端限制输入一千个字，前端已做八百字限制
     */
    @Column(name = "remark", length=2048)
    private String remark ;

    /**
     * review产生的评审结论，协商预留八百字，后端限制输入一千个字，前端已做八百字限制
     */
    @Column(name = "conclusion", length=2048)
    private String conclusion ;

    /**
     * 结果类型 ,0: 正常进行review, 1: 跳过review,但是也会有结果
     */
    @Column(name = "type")
    private int type ;

    @Column(name = "created_at")
    private long createdAt ;

    @Column(name = "created_by")
    private String createdBy ;

    @Column(name = "created_p")
    private String createdPerson ;

}
