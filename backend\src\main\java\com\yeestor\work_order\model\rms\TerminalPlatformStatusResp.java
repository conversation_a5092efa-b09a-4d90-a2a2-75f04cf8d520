package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "TerminalPlatformStatusResp", description = "启动Plan下的测试平台，需要回调测试平台的启动命令执行结果")
public class TerminalPlatformStatusResp {

    @ApiModel(value = "PlatformTestResult", description = "成功或者设备信息。")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class PlatformStatusResult {
        @ApiModelProperty(value = "电脑mac地址", required = true)
        private String mac;  // 电脑mac地址

        @ApiModelProperty(value = "平台编号", required = true)
        private String number; // 平台编号

        @ApiModelProperty(value = "测试平台状态 [0 -> 失败, 1 -> 成功]", required = true)
        private int status; // 平台编号

        @ApiModelProperty("出错原因")
        private String reason;  // 出错原因
    }

    @NotBlank(message = "工单号不能为空")
    @ApiModelProperty(value = "RMS中的工单号", required = true)
    private String orderNo;

    @ApiModelProperty(value = "执行Plan", required = true)
    private String plan;

    @ApiModelProperty("成功的测试平台列表")
    private List<PlatformStatusResult> successLst;

    @ApiModelProperty("失败的测试平台列表")
    private List<PlatformStatusResult> failLst;
}
