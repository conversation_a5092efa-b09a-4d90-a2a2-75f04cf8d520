package com.yeestor.work_order.model.http.resp.order;

import com.yeestor.work_order.entity.PlatformCaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "终端测试Plan的case测试信息")
public class PlanCaseItemVO {
    private long id;
    private long orderId;

    private Long planId;

    private Long platformId;

    @ApiModelProperty("脚本名称")
    private String caseName;

    @ApiModelProperty("平台名称")
    private String platformName;

    @ApiModelProperty("脚本测试状态")
    private PlatformCaseEntity.Status status;

    @ApiModelProperty("测试开始时间")
    private Long startAt;

    @ApiModelProperty("测试结束时间")
    private Long endAt;

    @ApiModelProperty("测试结束原因")
    private String failReason;

    public void setCaseInfo(PlatformCaseEntity caseEntity){
        this.id = caseEntity.getId();
        this.orderId = caseEntity.getOrderId();
        this.planId = caseEntity.getPlanId();
        this.platformId = caseEntity.getPlatformId();
        this.caseName = caseEntity.getCaseName();
        this.platformName = caseEntity.getPlatformName();
        this.status = caseEntity.getStatus();
        this.startAt = caseEntity.getStartAt();
        this.endAt = caseEntity.getEndAt();
        this.failReason = caseEntity.getFailReason();
    }
}
