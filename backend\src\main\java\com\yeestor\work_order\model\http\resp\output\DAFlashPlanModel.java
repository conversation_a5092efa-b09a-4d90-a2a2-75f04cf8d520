package com.yeestor.work_order.model.http.resp.output;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "DAFlashPlanModel", description = "Flash 批次统计Plan数据列表返回类型")
public class DAFlashPlanModel {
    private long id;
    private long orderId;
    private String orderNo;
    private String flashModel;
    private String size;
    private String subProduct;

    private String name;
    private String feature;
    private OrderPlanEntity.Status status;
    private int priority;
    private String attrs;
    private String flash;
    private int testNum;
    private String parentPlan;
    private Long createAt;
    private String belongToPerson;
    private Long startAt;
    private Long endAt;
    private int type;

    public DAFlashPlanModel(WorkOrderEntity workOrderEntity, OrderFlashEntity flashEntity, OrderPlanEntity planEntity) {
        this.id = planEntity.getId();
        this.orderId = workOrderEntity.getId();
        this.orderNo = workOrderEntity.getNo();
        this.flashModel = workOrderEntity.getFlash();
        this.size = flashEntity.getSize();
        this.subProduct = workOrderEntity.getSubProduct();

        this.name = planEntity.getName();
        this.feature = planEntity.getFeature();
        this.status = planEntity.getStatus();
        this.priority = planEntity.getPriority();
        this.attrs = planEntity.getAttrs();
        this.flash = planEntity.getFlash();
        this.testNum = planEntity.getTestNum();
        this.parentPlan = planEntity.getParentPlan();
        this.createAt = planEntity.getCreatedAt();
        this.belongToPerson = planEntity.getBelongToPerson();
        this.startAt = planEntity.getStartAt();
        this.endAt = planEntity.getEndAt();
        this.type = planEntity.getType();
    }
}
