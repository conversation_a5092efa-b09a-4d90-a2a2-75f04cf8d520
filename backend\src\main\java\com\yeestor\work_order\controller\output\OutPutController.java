package com.yeestor.work_order.controller.output;

import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.resp.output.DAFlashModel;
import com.yeestor.work_order.model.http.resp.output.DAFlashPlanModel;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.output.DAFlashService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/output/")
@Api(tags = {"output"}, value = "处理对外输出的接口信息")
public class OutPutController {
    private final WorkOrderRepository orderRepository;
    private final OrderFlashRepository flashRepository;
    private final OrderService orderService;
    private final DAFlashService daFlashService;

    @ApiOperation(value = "获取指定产品的工单列表.")
    @GetMapping("/orderNo/list")
    public HandleResp<List<String>> fetchOrderList(
            @ApiParam("产品线") @RequestParam(value = "product") String product,
            @ApiParam("产品") @RequestParam(value = "subProduct") String subProduct
    ){
        List<WorkOrderEntity.Status> orderStatusList = Arrays.asList(WorkOrderEntity.Status.CONFIRMED_FLASH, WorkOrderEntity.Status.TESTING);
        List<String> orderNoList = orderRepository.findOrderNoByProductAndSubProductAndStatus(product, subProduct, orderStatusList);

        return HandleResp.ok(orderNoList, "成功获取工单列表.");
    }

    @ApiOperation(value = "获取Flash批次测试单号.")
    @GetMapping("/flash/flashNo")
    public HandleResp<String> fetchFlashNo(
            @ApiParam("工单号") @RequestParam(value = "orderNo") String orderNo,
            @ApiParam("容量") @RequestParam(value = "size") String size
    ){
        List<String> flashNoList = flashRepository.findFlashNoByOrderNoAndSize(orderNo, size);
        String flashNo = flashNoList.stream().findFirst().orElseThrow(() -> new DataNotFoundException("工单号为" + orderNo + "的工单未找到指定容量的Flash！"));
        return HandleResp.ok(flashNo, "成功获取Flash批次测试单号.");
    }

    @GetMapping("/flashModel/{subProduct}")
    @ApiOperation(value = "获取产品下所有Flash型号", notes = "用于统计页查询Flash型号")
    public HandleResp<List<String>> findAllFlashList(
            @PathVariable("subProduct") @ApiParam("产品线") String subProduct
    ) {
        return HandleResp.ok(orderRepository.findFlashModelBySubProduct(subProduct), "Flash批次信息获取成功！");
    }

    @GetMapping("/version/{subProduct}")
    @ApiOperation(value = "获取产品下所有版本信息", notes = "用于统计页面获取所有版本信息")
    public HandleResp<List<String>> findAllVersionList(
            @PathVariable("subProduct") @ApiParam("产品线") String subProduct
    ) {
        return HandleResp.ok(orderRepository.findFullVersionBySubProduct(subProduct), "版本信息获取成功！");
    }

    @GetMapping("/chip/{product}/{subProduct}")
    @ApiOperation(value = "获取产品下所有主控", notes = "用于统计页面查询主控列表")
    public HandleResp<List<String>> findAllChipList(
            @PathVariable("product") @ApiParam("产品") String product,
            @PathVariable("subProduct") @ApiParam("产品线") String subProduct
    ) {
        return HandleResp.ok(orderService.findChipNameByProductAndSubProduct(product, subProduct), "主控信息获取成功！");
    }

    @GetMapping("/da/orderFlash/list")
    @ApiOperation(value = "通过筛选条件检索对应的Flash批次信息", notes = "用于统计页面查询Flash列表")
    public HandleResp<List<DAFlashModel>> findFlashInfoList(
            @ApiParam("所属产品") @RequestParam(value = "subProduct") String subProduct,
            @ApiParam("工单所属主控") @RequestParam(value = "chip") String chip,
            @ApiParam("工单所属版本") @RequestParam(value = "fullVersion") String fullVersion,
            @ApiParam("工单所属Flash型号") @RequestParam(value = "flash") String flash,
            @ApiParam("Flash最早创建时间") @RequestParam(value = "afterAt") Long afterAt,
            @ApiParam("Flash最晚创建时间") @RequestParam(value = "beforeAt") Long beforeAt
    ) {
        return HandleResp.ok(daFlashService.retrievalDAFlashList(subProduct, chip, fullVersion, flash, afterAt, beforeAt), "Flash信息获取成功！");
    }
}
