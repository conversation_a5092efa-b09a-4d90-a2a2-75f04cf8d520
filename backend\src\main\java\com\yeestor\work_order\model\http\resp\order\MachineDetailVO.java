package com.yeestor.work_order.model.http.resp.order;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.model.http.req.Person;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Slf4j
@ApiModel(value = "用于测试机房中查询当前设备的测试信息")
public class MachineDetailVO {

    @Data
    public static class OrderDetail {
        private String orderNo;
        private String product;
        private String subProduct;
        private String versionType;
        private String fullVersion;
        private long createAt;
        private String mpToolPath;
        private String marsPath;
        private String planPath;
        private String buildPerson;
        private long buildEndAt;
        private String mpFilePath;
        private String flash;
        private String requirement;
    }

    @Data
    public static class FlashDetail {
        private String name;
        private String size;
        private int num;
        private Boolean autoLoc;
        private OrderFlashEntity.Status status;
        private Long testStartAt;
        private String testPerson;
        private List<Person> testRelatedPerson;
    }

    @Data
    public static class PlanDetail {
        private String name;
        private String feature;
        private OrderPlanEntity.Status status;
        private int priority;
        private List<String> attrs;
        private int testNum;
        private String parentPlan;
        private Boolean needReport;
        private Long createAt;
        private Long readyAt;
        private Long confirmedAt;
        private String confirmedPerson;
        private String belongToPerson;
        private Long startAt;
        private String startPerson;
        private long createdAt;
    }

    @Data
    public static class DeviceDetail {
        private String ip;
        private String mac;
        private String no;
        private String position;
        private int score;
        private int testNum;
        private Integer actualNum;
        private PlanDeviceEntity.Status status;
    }

    public OrderDetail orderDetail;
    public FlashDetail flashDetail;
    public PlanDetail planDetail;
    public List<DeviceDetail> deviceList;

    public static OrderDetail toOrderModel(OrderDetailEntity orderDetailEntity, WorkOrderEntity workOrderEntity) {
        OrderDetail model = new OrderDetail();
        model.orderNo = workOrderEntity.getNo();
        model.product = workOrderEntity.getProduct();
        model.subProduct = workOrderEntity.getSubProduct();
        model.versionType = workOrderEntity.getVersionType();
        model.fullVersion = workOrderEntity.getFullVersion();
        model.createAt = workOrderEntity.getCreatedAt();
        model.mpToolPath = workOrderEntity.getMpToolPath();
        model.marsPath = workOrderEntity.getMarsPath();
        model.planPath = workOrderEntity.getPlanPath();
        model.buildPerson = workOrderEntity.getBuildPerson();
        model.buildEndAt = workOrderEntity.getBuildEndAt();
        model.mpFilePath = orderDetailEntity.getMpFilePath();
        model.flash = workOrderEntity.getFlash();
        model.requirement = orderDetailEntity.getRequirement();
        return model;
    }

    public static FlashDetail toFlashModel(OrderFlashEntity flashEntity) {
        ObjectMapper mapper = new ObjectMapper();
        FlashDetail model = new FlashDetail();
        model.name = flashEntity.getFlash();
        model.size = flashEntity.getSize();
        model.num = flashEntity.getNum();
        model.autoLoc = flashEntity.getAutoLoc();
        model.status = flashEntity.getStatus();
        model.testPerson = flashEntity.getTestPerson();
        model.testStartAt = flashEntity.getTestStartAt();
        String personStr = flashEntity.getTestRelatedPerson();
        try {
            if(personStr != null){
                model.testRelatedPerson = mapper.readValue(personStr, new TypeReference<List<Person>>() {});
            }
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return model;
    }

    public static PlanDetail toPlanModel(OrderPlanEntity planEntity) {
        PlanDetail model = new PlanDetail();
        model.name = planEntity.getName();
        model.feature = planEntity.getFeature();
        model.status = planEntity.getStatus();
        model.priority = planEntity.getPriority();
        String[] arr = planEntity.getAttrs().split(";");
        model.attrs = new ArrayList<>(Arrays.asList(arr));
        model.testNum = planEntity.getTestNum();
        model.parentPlan = planEntity.getParentPlan();
        model.needReport = planEntity.getNeedReport();
        model.createAt = planEntity.getCreatedAt();
        model.readyAt = planEntity.getReadyAt();
        model.confirmedAt = planEntity.getConfirmedAt();
        model.confirmedPerson = planEntity.getConfirmedPerson();
        model.belongToPerson = planEntity.getBelongToPerson();
        model.startAt = planEntity.getStartAt();
        model.startPerson = planEntity.getStartPerson();
        model.createdAt = planEntity.getCreatedAt();
        return model;
    }

    public static DeviceDetail toDeviceModel(PlanDeviceEntity deviceEntity){
        DeviceDetail model = new DeviceDetail();
        model.ip = deviceEntity.getIp();
        model.mac = deviceEntity.getMac();
        model.no = deviceEntity.getNo();
        model.position = deviceEntity.getPosition();
        model.score = deviceEntity.getScore();
        model.testNum = deviceEntity.getTestNum();
        model.actualNum = deviceEntity.getActualNum();
        model.status = deviceEntity.getStatus();
        return model;
    }

    public static MachineDetailVO convertToMachineDetail(
            OrderDetailEntity orderDetailEntity,
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            List<PlanDeviceEntity> deviceList
    ){
        MachineDetailVO machineDetailVO = new MachineDetailVO();
        machineDetailVO.setPlanDetail(toPlanModel(planEntity));
        machineDetailVO.setFlashDetail(toFlashModel(flashEntity));
        machineDetailVO.setOrderDetail(toOrderModel(orderDetailEntity, orderEntity));
        List<MachineDetailVO.DeviceDetail> deviceDetails = deviceList.stream().map(d -> toDeviceModel(d)).collect(Collectors.toList());
        machineDetailVO.setDeviceList(deviceDetails);
        return machineDetailVO;
    }
}
