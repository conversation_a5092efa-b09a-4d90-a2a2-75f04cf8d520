package com.yeestor.work_order.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SendEmailService {
    private final JavaMailSender mailSender;

    @Async
    public void sendHtmlMail(String subject, String text, boolean isHtml, String to, List<String> ccList) {
        MimeMessage mimeMessage = mailSender.createMimeMessage();

        try {
            MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage, true);


            messageHelper.setFrom("<EMAIL>");
            messageHelper.setTo(to);

            if (ccList.size() > 0) {
                for (int i = 0; i < ccList.size(); i++) {
                    messageHelper.addCc(ccList.get(i));
                }
            }

            messageHelper.setSubject(subject);
            messageHelper.setText(text, isHtml);
            mailSender.send(mimeMessage);

        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }
}
