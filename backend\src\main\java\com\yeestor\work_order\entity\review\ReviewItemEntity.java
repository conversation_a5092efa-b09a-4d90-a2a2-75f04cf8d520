package com.yeestor.work_order.entity.review;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity(name = "ReviewItem")
@Table(name="wo_review_item")
public class ReviewItemEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "order_id")
    private long orderId ;

    @Column(name = "flash")
    private String flash ;

    @Column(name = "result_id")
    private long resultId ;

    @Column(name="item")
    private String item;

    @Column(name="result")
    private String result ;

    @Lob
    @Column(name= "remark", length=512)
    private String remark ;



}
