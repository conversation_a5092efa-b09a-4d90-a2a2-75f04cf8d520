package com.yeestor.work_order.entity;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.entity.BaseEntity;
import com.yeestor.work_order.model.http.req.Person;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@Setter
@ToString( of = {"id", "orderId", "flash", "num", "leftNum", "orderFlashNo"} )
@Entity(name = "OrderFlash")
@Table(name = "wo_order_flash")
public class OrderFlashEntity extends BaseEntity {

    public enum Status {
        /**
         * 刚刚创建，等待资源
         */
        NEW,
        /**
         * 等待开始测试
         */
        WAITING_FOR_START,
        /**
         * 已经开始
         */
        IN_PROGRESS,
        /**
         * 测试完成，已经合并报告，等待报告状态变更回调。
         */
        WAITING_FOR_MERGE,
        /**
         * 测试完成，等待上传报告。
         */
        WAITING_FOR_REPORT,

        /**
         * 测试完成，等待进行fail 分析。
         */
        WAITING_FOR_FAIL_ANALYSIS,

        /**
         * FAIL 分析开始
         */
        FAIL_ANALYSIS_STARTED,
        /**
         * 测试完成，等待 review
         */
        WAITING_FOR_REVIEW,

        /**
         * Review 已经开始。
         */
        REVIEW_STARTED,
        /**
         * 已经完成
         */
        COMPLETED,
        /**
         * 已经取消
         */
        CANCELLED ;

        public String getDesc(){
            switch (this){
                case NEW:
                    return "等待资源分配";
                case IN_PROGRESS:
                    return "测试中";
                case WAITING_FOR_MERGE:
                    return "等待合并报告";
                case WAITING_FOR_REPORT:
                    return "等待上传报告";
                case WAITING_FOR_START:
                    return "等待开始测试";
                case WAITING_FOR_FAIL_ANALYSIS:
                    return "测试完成";
                case FAIL_ANALYSIS_STARTED:
                    return "fail分析中";
                case WAITING_FOR_REVIEW:
                    return "等待review";
                case REVIEW_STARTED:
                    return "等待完成review";
                case COMPLETED:
                    return "已完成";
                case CANCELLED:
                    return "已取消";
                default:
                    return "未知";
            }
        }
        public boolean greaterThan( Status status ) {
            return this.ordinal() >= status.ordinal() ;
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    /**
     * 工单id 。这里不采用外键。
     */
    @Column(name = "order_id")
    private long orderId ;

    /**
     * Flash 的批次信息。不是Flash id。
     */
    @Column(name = "flash")
    private String flash ;

    /**
     * Flash 批次的容量.
     */
    @Column(name = "size")
    private String size ;

    /**
     * Flash 批次 + 工单号组合的 RMS工单号。
     */
    @Column(name = "order_flash_no")
    private String orderFlashNo  ;

    /**
     * 该批次Flash的数量。
     */
    @Column(name= "num")
    private int num ;

    /**
     * Flash 在工单中先后顺序, 顺序从小的开始。
     */
    @Column(name = "idx")
    private int idx;

    /**
     * Flash 批次的状态。
     * @see Status
     */
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private Status status ;

    /**
     * 开始测试时间。
     */
    @Column(name = "test_start_at")
    private Long testStartAt ;

    /**
     * 测试负责人， 钉钉ID
     */
    @Column(name = "test_by")
    private String testBy ;

    /**
     * 测试负责人， 名称（英文名）
     */
    @Column(name = "test_p")
    private String testPerson ;


    @Lob
    @Column(name= "test_releated_p", length=1024)
    private String testRelatedPerson ;

    /**
     * 测试结束时间, 也是开始合并报告的时间
     */
    @Column(name = "test_end_at")
    private Long testEndAt ;


    /**
     * Flash批次撤销时间。
     */
    @Column(name = "revoke_at")
    private Long revokeAt ;


    /**
     * 撤销工单的人员， 钉钉ID
     */
    @Column(name = "revoke_by")
    private String revokeBy ;

    /**
     * 撤销工单的原因、
     */
    @Column(name = "revoke_reason")
    private String revokeReason ;

    /**
     * 剩余的可用Flash 的数量。只在状态为{@link Status#IN_PROGRESS}时有用。
     */
    @Column(name = "left_num")
    private int leftNum ;

    /**
     * 合并报告完成的时间
     */
    @Column(name = "merge_complete_at")
    private Long mergeCompleteAt ;

    /**
     * fail 分析启动时间
     */
    @Column(name = "start_fail_at")
    private Long startFailAt ;

    /**
     * 启动fail 分析的人员
     */
    @Column(name = "start_fail_by")
    private String startFailBy ;

    /**
     * 启动Fail 分析的人员的名称
     */
    @Column(name = "start_fail_p")
    private String startFailPerson ;

    /**
     * fail 分析指定人，通常是构建人。
     */
    @Column(name = "fail_assign_to")
    private String failAssignTo ;


    /**
     * fail 分析的原因，如果没有指定人，则表示跳过。
     */
    @Column(name = "fail_reason")
    private String failReason ;

    /**
     * flash 批次结束的时间 ,也是完成Review的时间
     */
    @Column(name = "complete_at")
    private Long completeAt ;

    /**
     * 这个flash 批次是否需要自动定位
     */
    @Column(name = "auto_loc")
    private Boolean autoLoc ;

    /**
     * flash 批次的CE数，当前仅eMMC需要填写
     */
    @Column(name = "ce_count")
    private String ceCount ;

    @ColumnDefault("0")
    @Column(name = "disabled")
    private Boolean disabled ;

    /**
     * Flash批次取消测试时间。
     */
    @Column(name = "cancel_at")
    private Long cancelAt ;


    /**
     * 取消测试的人员， 钉钉ID
     */
    @Column(name = "cancel_by")
    private String cancelBy ;

    /**
     * 取消测试的原因、
     */
    @Column(name = "cancel_reason")
    private String cancelReason ;

    /**
     * 取消测试的人员， 名称（英文名）
     */
    @Column(name = "cancel_p")
    private String cancelPerson;

    /**
     * 结束Flash流程的人员钉钉ID
     */
    @Column(name = "ending_by")
    private String endingBy;

    /**
     * alpha转release的复测时间
     */
    @Column(name = "retest_at")
    private Long retestAt;

    /**
     * 版本转换人员钉钉id
     */
    @Column(name = "retest_by")
    private String retestBy;

    /**
     * 复测人员
     */
    @Column(name = "retest_p")
    private String retestPerson;

    /**
     * flash批次被标记的版本 alpha， release， alphaToRelease
     */
    @Column(name = "mark_version")
    private String markVersion;

    /**
     * 如果是通过alpha转的release版本，需要记录是否已经测试
     * 如果在fail分析或者review状态，则可以通过复测Release版本重新回到测试中
     * 如果转换时Flash的状态正好在测试中，则需要把这个状态标记为已经测试
     * true 表示已经测试
     * false表示未测试
     */
    @Column(name = "has_test_convert")
    private Boolean hasTestConvert;


    public boolean isDisabled() {
        return disabled != null && disabled;
    }

    public void setLeftNum(int leftNum) {
        this.leftNum = Math.max(0,Math.min(leftNum, this.num));
    }

    public String getTestRelatedPerson() {
        return Optional.ofNullable(testRelatedPerson).orElse("[]");
    }

    /**
     * 获取Flash批次的所有测试干系人
     * @return 干系人信息
     */
    @Transient
    public Set<String> getTestPersonIds(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String testUser = this.testBy;
        String testRelatedPerson = this.testRelatedPerson;
        List<Person> persons;
        try {
            persons = mapper.readValue(testRelatedPerson,
                    new TypeReference<>() {
                    });
        } catch (JsonProcessingException e) {
            persons = new ArrayList<>();
        }
        Set<String> userIdSet = persons.stream().map(Person::getId).collect(Collectors.toSet());
        userIdSet.add(testUser);
        return userIdSet;
    }

    /**
     * 获取Flash批次的所有测试干系人和负责人
     * @return 人员信息
     */
    @Transient
    public List<Person> getTesters(){
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        String testRelatedPerson = this.testRelatedPerson;
        List<Person> persons;
        try {
            persons = mapper.readValue(testRelatedPerson,
                    new TypeReference<>() {
                    });
        } catch (JsonProcessingException e) {
            persons = new ArrayList<>();
        }
        persons.add(new Person(this.testBy, this.testPerson));
        return persons.stream().collect(Collectors.groupingBy(Person::getId))
                .values().stream()
                .map(list -> list.get(0))
                .collect(Collectors.toList());
    }

    /**
     * 清除当前Flash的状态及记录时间
     */
    public void clearFlashStatus() {
        this.endingBy = null;
        this.completeAt = null;
        this.failReason = null;
        this.failAssignTo = null;
        this.startFailPerson = null;
        this.startFailBy = null;
        this.startFailAt = null;
        this.mergeCompleteAt = null;
        this.testEndAt = null;
    }
}
