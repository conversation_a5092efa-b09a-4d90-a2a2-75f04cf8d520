package com.yeestor.work_order.service.job;

import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.rms.PlanTestParams;
import com.yeestor.work_order.model.rms.PlanTestRespModel;
import com.yeestor.work_order.repository.PlanDeviceRepository;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.work_order.utils.RMSApis;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class StartTestJob implements Job {

    public static final String GROUP_NAME = "StartTestJob" ;
    private static final String ORDER_ID = "order_id" ;
    private static final String FLASH = "flash" ;
    private static final String PLAN_ID = "plan_id" ;
    private static final String USER_NAME = "user_name" ;
    private static final String DEVICE_IPS = "ip_list" ;
    private static final String DEVICE_MAC_LIST = "mac_list" ;
    private static final String OPERATE = "启动测试" ;

    private final DeviceService deviceService ;
    private final RMSApis rmsApis ;
    private final PlanService planService ;
    private final PlanDeviceRepository planDeviceRepository;
    private final Environment environment ;
    private final OrderService orderService ;
    private final FlashService flashService ;

    public static String buildJobName(
            long orderId,
            long planId
    ){
        return "StartTestJob_"+orderId+"_"+planId+"_"+ UUID.randomUUID();
    }

    public static JobDetail buildJobDetail(
            String jobName,
            long orderId,
            String flash,
            long planId,
            String userName,
            List<PlanDeviceEntity> devices
    ){
        List<String> deviceIps = devices.stream().map(PlanDeviceEntity::getIp).collect(Collectors.toList());
        List<String> deviceMacList = devices.stream().map(PlanDeviceEntity::getMac).collect(Collectors.toList());

        return JobBuilder.newJob(StartTestJob.class)
                .withIdentity(jobName, GROUP_NAME)
                .usingJobData(ORDER_ID, orderId)
                .usingJobData(FLASH, flash)
                .usingJobData(PLAN_ID, planId)
                .usingJobData(USER_NAME, userName)
                .usingJobData(DEVICE_IPS, String.join(",", deviceIps))
                .usingJobData(DEVICE_MAC_LIST, String.join(",", deviceMacList))
                .build() ;
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        long orderId = dataMap.getLong(ORDER_ID);
        String flash = dataMap.getString(FLASH);
        long planId = dataMap.getLong(PLAN_ID);
        LogUtils.setOrderAndFlashTracePoint(orderId, flash,OPERATE);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash) ;
        log.info("Flash:{}下的{}", flash, planEntity.getName());

        String marsPath = Optional.ofNullable(orderEntity.getMarsPath()).orElse("");
        String mpPath = Optional.ofNullable(orderEntity.getMpToolPath()).orElse("");
        String planPath = Optional.ofNullable(orderEntity.getPlanPath()).orElse("");
        String username = dataMap.getString(USER_NAME);
        String startType = Optional.ofNullable(planEntity.getStartType()).orElse("");
        boolean autoLoc;
        // 如果启动时调整了编号策略，则以调整后的为准
        if (startType.equals("renumber")) {
            autoLoc = false;
        } else {
            autoLoc = Optional.ofNullable(flashEntity.getAutoLoc()).orElse(false);
        }
        List<String> deviceIps = Arrays.asList(dataMap.getString(DEVICE_IPS).split(","));
        List<String> deviceMacList = Arrays.asList(dataMap.getString(DEVICE_MAC_LIST).split(","));

        log.info("Flash:{}下的{}使用设备:{} -{} 开始测试!", flash, planEntity.getName(),deviceIps ,deviceMacList );
        if (deviceMacList.isEmpty()) {
            log.error("Flash:{}下的{}使用设备:{} -{} 开始测试失败: MacList为空!", flash, planEntity.getName(),deviceIps ,deviceMacList );
            return ;
        }

        PlanTestParams params = PlanTestParams.builder()
                .planName(planEntity.getName())
                .no(flashEntity.getOrderFlashNo())
                .group(Arrays.asList(environment.getActiveProfiles()).contains("prod") ? orderEntity.getRmsGroup(): "")
                .bAutoLoc(autoLoc)
                .ipList(deviceIps)
                .macList(deviceMacList)
                .product(orderEntity.getSubProduct())
                .marsPath(marsPath)
                .mpPath(mpPath)
                .planPath(planPath)
                .username(username)
                .build() ;

        HandleResp<PlanTestRespModel> testResp = rmsApis.startTestPlan(params);

        if(testResp.isSuccess()){

            List<PlanDeviceEntity> needTestDeviceList = planDeviceRepository.findAllByPlanIdAndMacIn(
                    planEntity.getId(),
                    deviceMacList
            );
            log.info("{} 启动测试成功, resp: {}", planEntity.getName(), testResp.getData());
            PlanTestRespModel respModel = testResp.getData();
            List<PlanTestRespModel.DeviceTestResult> failLst = Optional.ofNullable(respModel.getFailLst()).orElse(new ArrayList<>());

            List<PlanDeviceEntity> failedOriginDeviceList = needTestDeviceList.stream()
                    .filter(p-> failLst.stream().anyMatch(d-> d.getIp().equals(p.getIp())))
                    .collect(Collectors.toList());

            if(failedOriginDeviceList.isEmpty()){
                return ;
            }

            List<PlanDeviceEntity> failedNewDeviceList = failedOriginDeviceList.stream()
                    .map(p-> {
                        PlanTestRespModel.DeviceTestResult failInfo = failLst.stream().filter(d-> d.getIp().equals(p.getIp())).findFirst().orElse(null);

                        PlanDeviceEntity newDeviceEntity = p.duplicateEmpty() ;
                        newDeviceEntity.setActualNum(null);

                        newDeviceEntity.setEndAt(System.currentTimeMillis());
                        newDeviceEntity.setStatus(PlanDeviceEntity.Status.FINISHED_FAILED);
                        if(failInfo != null){
                            newDeviceEntity.setFailReason(failInfo.getReason());
                        }
                        return newDeviceEntity;
                    }).collect(Collectors.toList());

            deviceService.saveDeviceList("部分启动失败",failedOriginDeviceList, failedNewDeviceList);

            deviceService.startCheckPlanStartStatusJob(
                    orderId,
                    params.getNo(),
                    planEntity,
                    deviceMacList
            );


        }
        else{
            log.warn("{}启动测试失败，{}",planEntity.getName(),testResp.getMsg());
        }
        LogUtils.clearTracePoint();

    }
}
