{"@timestamp":"2025-07-23T10:34:02.669+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"69a3ba6105219f11","spanId":"69a3ba6105219f11","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:34:02.678+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"69a3ba6105219f11","spanId":"69a3ba6105219f11","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.684+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"69a3ba6105219f11","spanId":"69a3ba6105219f11","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.684+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"69a3ba6105219f11","spanId":"69a3ba6105219f11","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.684+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"69a3ba6105219f11","spanId":"69a3ba6105219f11","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.643+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c48dd9472665cc9","spanId":"9c48dd9472665cc9","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:38:02.645+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c48dd9472665cc9","spanId":"9c48dd9472665cc9","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.649+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c48dd9472665cc9","spanId":"9c48dd9472665cc9","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.65+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c48dd9472665cc9","spanId":"9c48dd9472665cc9","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.65+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c48dd9472665cc9","spanId":"9c48dd9472665cc9","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.664+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a4856be6f93807","spanId":"40a4856be6f93807","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:42:02.668+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a4856be6f93807","spanId":"40a4856be6f93807","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a4856be6f93807","spanId":"40a4856be6f93807","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.672+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a4856be6f93807","spanId":"40a4856be6f93807","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.672+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a4856be6f93807","spanId":"40a4856be6f93807","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.651+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ec13535314c5054","spanId":"7ec13535314c5054","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:46:02.653+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ec13535314c5054","spanId":"7ec13535314c5054","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.657+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ec13535314c5054","spanId":"7ec13535314c5054","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.657+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ec13535314c5054","spanId":"7ec13535314c5054","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.657+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ec13535314c5054","spanId":"7ec13535314c5054","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.642+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28a1741d34c58ac5","spanId":"28a1741d34c58ac5","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:50:02.643+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28a1741d34c58ac5","spanId":"28a1741d34c58ac5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28a1741d34c58ac5","spanId":"28a1741d34c58ac5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.647+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28a1741d34c58ac5","spanId":"28a1741d34c58ac5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.647+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28a1741d34c58ac5","spanId":"28a1741d34c58ac5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.695+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c77365d1f2e631e","spanId":"8c77365d1f2e631e","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:02.697+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c77365d1f2e631e","spanId":"8c77365d1f2e631e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.704+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c77365d1f2e631e","spanId":"8c77365d1f2e631e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.704+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c77365d1f2e631e","spanId":"8c77365d1f2e631e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.704+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c77365d1f2e631e","spanId":"8c77365d1f2e631e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.643+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e3205ad48dc6c36a","spanId":"e3205ad48dc6c36a","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:58:02.644+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e3205ad48dc6c36a","spanId":"e3205ad48dc6c36a","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e3205ad48dc6c36a","spanId":"e3205ad48dc6c36a","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.648+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e3205ad48dc6c36a","spanId":"e3205ad48dc6c36a","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.648+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e3205ad48dc6c36a","spanId":"e3205ad48dc6c36a","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.642+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2156b623c2aaa3ff","spanId":"2156b623c2aaa3ff","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:02:02.643+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2156b623c2aaa3ff","spanId":"2156b623c2aaa3ff","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2156b623c2aaa3ff","spanId":"2156b623c2aaa3ff","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.648+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2156b623c2aaa3ff","spanId":"2156b623c2aaa3ff","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.648+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2156b623c2aaa3ff","spanId":"2156b623c2aaa3ff","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.667+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b5bcb48495905de","spanId":"3b5bcb48495905de","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:06:02.667+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b5bcb48495905de","spanId":"3b5bcb48495905de","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.67+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b5bcb48495905de","spanId":"3b5bcb48495905de","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.67+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b5bcb48495905de","spanId":"3b5bcb48495905de","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.67+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b5bcb48495905de","spanId":"3b5bcb48495905de","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.64+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"31360faca853d8e5","spanId":"31360faca853d8e5","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:10:02.641+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"31360faca853d8e5","spanId":"31360faca853d8e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"31360faca853d8e5","spanId":"31360faca853d8e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.645+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"31360faca853d8e5","spanId":"31360faca853d8e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.645+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"31360faca853d8e5","spanId":"31360faca853d8e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.652+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4620df724bc9d835","spanId":"4620df724bc9d835","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:14:02.653+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4620df724bc9d835","spanId":"4620df724bc9d835","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.656+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4620df724bc9d835","spanId":"4620df724bc9d835","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.656+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4620df724bc9d835","spanId":"4620df724bc9d835","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.656+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4620df724bc9d835","spanId":"4620df724bc9d835","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.648+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3f9f9024dd77ae34","spanId":"3f9f9024dd77ae34","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:18:02.651+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3f9f9024dd77ae34","spanId":"3f9f9024dd77ae34","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.656+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3f9f9024dd77ae34","spanId":"3f9f9024dd77ae34","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.656+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3f9f9024dd77ae34","spanId":"3f9f9024dd77ae34","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.656+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3f9f9024dd77ae34","spanId":"3f9f9024dd77ae34","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.751+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b273db4babad06e4","spanId":"b273db4babad06e4","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:34:02.767+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b273db4babad06e4","spanId":"b273db4babad06e4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.776+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b273db4babad06e4","spanId":"b273db4babad06e4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.776+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b273db4babad06e4","spanId":"b273db4babad06e4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.776+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b273db4babad06e4","spanId":"b273db4babad06e4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.76+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b27ab80c965b9d32","spanId":"b27ab80c965b9d32","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:38:02.767+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b27ab80c965b9d32","spanId":"b27ab80c965b9d32","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.774+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b27ab80c965b9d32","spanId":"b27ab80c965b9d32","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.774+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b27ab80c965b9d32","spanId":"b27ab80c965b9d32","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.774+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b27ab80c965b9d32","spanId":"b27ab80c965b9d32","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.67+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8ac280115a38e6d7","spanId":"8ac280115a38e6d7","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:02.671+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8ac280115a38e6d7","spanId":"8ac280115a38e6d7","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.674+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8ac280115a38e6d7","spanId":"8ac280115a38e6d7","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.674+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8ac280115a38e6d7","spanId":"8ac280115a38e6d7","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.674+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8ac280115a38e6d7","spanId":"8ac280115a38e6d7","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.647+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4135df9c9fb02ba","spanId":"d4135df9c9fb02ba","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:46:02.648+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4135df9c9fb02ba","spanId":"d4135df9c9fb02ba","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.651+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4135df9c9fb02ba","spanId":"d4135df9c9fb02ba","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.651+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4135df9c9fb02ba","spanId":"d4135df9c9fb02ba","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.651+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4135df9c9fb02ba","spanId":"d4135df9c9fb02ba","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fb4b5a0cbbba4b6","spanId":"7fb4b5a0cbbba4b6","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:50:02.627+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fb4b5a0cbbba4b6","spanId":"7fb4b5a0cbbba4b6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fb4b5a0cbbba4b6","spanId":"7fb4b5a0cbbba4b6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.63+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fb4b5a0cbbba4b6","spanId":"7fb4b5a0cbbba4b6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.63+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fb4b5a0cbbba4b6","spanId":"7fb4b5a0cbbba4b6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.647+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a4035e1581534434","spanId":"a4035e1581534434","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:54:02.648+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a4035e1581534434","spanId":"a4035e1581534434","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.651+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a4035e1581534434","spanId":"a4035e1581534434","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.651+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a4035e1581534434","spanId":"a4035e1581534434","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.651+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a4035e1581534434","spanId":"a4035e1581534434","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.641+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0786aa489e8fd97c","spanId":"0786aa489e8fd97c","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:58:02.641+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0786aa489e8fd97c","spanId":"0786aa489e8fd97c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0786aa489e8fd97c","spanId":"0786aa489e8fd97c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.644+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0786aa489e8fd97c","spanId":"0786aa489e8fd97c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.644+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0786aa489e8fd97c","spanId":"0786aa489e8fd97c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6915f9e6464ede46","spanId":"6915f9e6464ede46","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:02:02.632+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6915f9e6464ede46","spanId":"6915f9e6464ede46","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6915f9e6464ede46","spanId":"6915f9e6464ede46","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.634+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6915f9e6464ede46","spanId":"6915f9e6464ede46","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.634+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6915f9e6464ede46","spanId":"6915f9e6464ede46","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65aa3398895d683d","spanId":"65aa3398895d683d","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:06:02.637+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65aa3398895d683d","spanId":"65aa3398895d683d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.641+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65aa3398895d683d","spanId":"65aa3398895d683d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.642+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65aa3398895d683d","spanId":"65aa3398895d683d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.642+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65aa3398895d683d","spanId":"65aa3398895d683d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.633+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ffbd192a50f9e91","spanId":"9ffbd192a50f9e91","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:10:02.635+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ffbd192a50f9e91","spanId":"9ffbd192a50f9e91","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ffbd192a50f9e91","spanId":"9ffbd192a50f9e91","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.638+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ffbd192a50f9e91","spanId":"9ffbd192a50f9e91","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.638+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9ffbd192a50f9e91","spanId":"9ffbd192a50f9e91","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.638+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"869aaa3122fa8ac6","spanId":"869aaa3122fa8ac6","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:14:02.639+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"869aaa3122fa8ac6","spanId":"869aaa3122fa8ac6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.641+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"869aaa3122fa8ac6","spanId":"869aaa3122fa8ac6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.642+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"869aaa3122fa8ac6","spanId":"869aaa3122fa8ac6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.642+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"869aaa3122fa8ac6","spanId":"869aaa3122fa8ac6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.679+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b44e52f75ae50d8","spanId":"7b44e52f75ae50d8","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:18:02.68+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b44e52f75ae50d8","spanId":"7b44e52f75ae50d8","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.683+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b44e52f75ae50d8","spanId":"7b44e52f75ae50d8","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.683+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b44e52f75ae50d8","spanId":"7b44e52f75ae50d8","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.683+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b44e52f75ae50d8","spanId":"7b44e52f75ae50d8","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.665+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0f8d49af9d1ac9","spanId":"0b0f8d49af9d1ac9","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:22:02.666+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0f8d49af9d1ac9","spanId":"0b0f8d49af9d1ac9","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0f8d49af9d1ac9","spanId":"0b0f8d49af9d1ac9","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.668+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0f8d49af9d1ac9","spanId":"0b0f8d49af9d1ac9","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.668+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0f8d49af9d1ac9","spanId":"0b0f8d49af9d1ac9","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.633+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66e0022f142ceef4","spanId":"66e0022f142ceef4","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:26:02.634+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66e0022f142ceef4","spanId":"66e0022f142ceef4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66e0022f142ceef4","spanId":"66e0022f142ceef4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.636+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66e0022f142ceef4","spanId":"66e0022f142ceef4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.636+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66e0022f142ceef4","spanId":"66e0022f142ceef4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.651+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"169803a8d0d9209d","spanId":"169803a8d0d9209d","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:02.653+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"169803a8d0d9209d","spanId":"169803a8d0d9209d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.661+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"169803a8d0d9209d","spanId":"169803a8d0d9209d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.661+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"169803a8d0d9209d","spanId":"169803a8d0d9209d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.661+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"169803a8d0d9209d","spanId":"169803a8d0d9209d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.637+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d453ad3745c4dc6","spanId":"0d453ad3745c4dc6","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:34:02.638+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d453ad3745c4dc6","spanId":"0d453ad3745c4dc6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d453ad3745c4dc6","spanId":"0d453ad3745c4dc6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.64+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d453ad3745c4dc6","spanId":"0d453ad3745c4dc6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.64+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d453ad3745c4dc6","spanId":"0d453ad3745c4dc6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.628+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1487d4f064fd1a69","spanId":"1487d4f064fd1a69","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:38:02.629+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1487d4f064fd1a69","spanId":"1487d4f064fd1a69","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1487d4f064fd1a69","spanId":"1487d4f064fd1a69","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.632+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1487d4f064fd1a69","spanId":"1487d4f064fd1a69","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.632+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1487d4f064fd1a69","spanId":"1487d4f064fd1a69","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.665+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cad18470005d35be","spanId":"cad18470005d35be","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:02.666+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cad18470005d35be","spanId":"cad18470005d35be","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cad18470005d35be","spanId":"cad18470005d35be","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.668+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cad18470005d35be","spanId":"cad18470005d35be","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.668+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cad18470005d35be","spanId":"cad18470005d35be","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.628+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ade496e0c140e14d","spanId":"ade496e0c140e14d","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:46:02.629+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ade496e0c140e14d","spanId":"ade496e0c140e14d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ade496e0c140e14d","spanId":"ade496e0c140e14d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.631+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ade496e0c140e14d","spanId":"ade496e0c140e14d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.631+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ade496e0c140e14d","spanId":"ade496e0c140e14d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b324d776fdb33d0","spanId":"8b324d776fdb33d0","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:50:02.636+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b324d776fdb33d0","spanId":"8b324d776fdb33d0","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b324d776fdb33d0","spanId":"8b324d776fdb33d0","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.639+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b324d776fdb33d0","spanId":"8b324d776fdb33d0","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.639+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b324d776fdb33d0","spanId":"8b324d776fdb33d0","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.643+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5564b65144896e44","spanId":"5564b65144896e44","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:54:02.643+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5564b65144896e44","spanId":"5564b65144896e44","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.646+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5564b65144896e44","spanId":"5564b65144896e44","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.646+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5564b65144896e44","spanId":"5564b65144896e44","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.646+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5564b65144896e44","spanId":"5564b65144896e44","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.641+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6a85649c15cbaa4","spanId":"f6a85649c15cbaa4","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:58:02.642+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6a85649c15cbaa4","spanId":"f6a85649c15cbaa4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6a85649c15cbaa4","spanId":"f6a85649c15cbaa4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.644+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6a85649c15cbaa4","spanId":"f6a85649c15cbaa4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.644+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f6a85649c15cbaa4","spanId":"f6a85649c15cbaa4","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.633+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581594f689757b43","spanId":"581594f689757b43","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:02:02.633+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581594f689757b43","spanId":"581594f689757b43","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581594f689757b43","spanId":"581594f689757b43","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.636+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581594f689757b43","spanId":"581594f689757b43","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.636+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581594f689757b43","spanId":"581594f689757b43","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.657+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de72fda8b9d9372b","spanId":"de72fda8b9d9372b","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:06:02.659+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de72fda8b9d9372b","spanId":"de72fda8b9d9372b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.664+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de72fda8b9d9372b","spanId":"de72fda8b9d9372b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.664+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de72fda8b9d9372b","spanId":"de72fda8b9d9372b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.664+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de72fda8b9d9372b","spanId":"de72fda8b9d9372b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.651+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9fb4f0e08128e2cb","spanId":"9fb4f0e08128e2cb","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:10:02.652+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9fb4f0e08128e2cb","spanId":"9fb4f0e08128e2cb","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9fb4f0e08128e2cb","spanId":"9fb4f0e08128e2cb","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.654+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9fb4f0e08128e2cb","spanId":"9fb4f0e08128e2cb","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.654+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9fb4f0e08128e2cb","spanId":"9fb4f0e08128e2cb","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.646+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5978ae67b6ab0371","spanId":"5978ae67b6ab0371","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:14:02.647+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5978ae67b6ab0371","spanId":"5978ae67b6ab0371","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.65+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5978ae67b6ab0371","spanId":"5978ae67b6ab0371","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.65+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5978ae67b6ab0371","spanId":"5978ae67b6ab0371","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.65+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5978ae67b6ab0371","spanId":"5978ae67b6ab0371","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.661+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30779071f6f8a89e","spanId":"30779071f6f8a89e","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:02.662+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30779071f6f8a89e","spanId":"30779071f6f8a89e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30779071f6f8a89e","spanId":"30779071f6f8a89e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.665+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30779071f6f8a89e","spanId":"30779071f6f8a89e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.665+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30779071f6f8a89e","spanId":"30779071f6f8a89e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.633+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a668378471dc6e5","spanId":"2a668378471dc6e5","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:22:02.634+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a668378471dc6e5","spanId":"2a668378471dc6e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a668378471dc6e5","spanId":"2a668378471dc6e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.637+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a668378471dc6e5","spanId":"2a668378471dc6e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.637+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a668378471dc6e5","spanId":"2a668378471dc6e5","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.633+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2625e1420bbd8e2e","spanId":"2625e1420bbd8e2e","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:26:02.634+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2625e1420bbd8e2e","spanId":"2625e1420bbd8e2e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2625e1420bbd8e2e","spanId":"2625e1420bbd8e2e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.636+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2625e1420bbd8e2e","spanId":"2625e1420bbd8e2e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.636+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2625e1420bbd8e2e","spanId":"2625e1420bbd8e2e","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.65+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c32c723fac62e609","spanId":"c32c723fac62e609","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:02.651+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c32c723fac62e609","spanId":"c32c723fac62e609","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c32c723fac62e609","spanId":"c32c723fac62e609","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.654+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c32c723fac62e609","spanId":"c32c723fac62e609","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.654+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c32c723fac62e609","spanId":"c32c723fac62e609","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.642+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"76da564f17ba2c7d","spanId":"76da564f17ba2c7d","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:34:02.643+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"76da564f17ba2c7d","spanId":"76da564f17ba2c7d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"76da564f17ba2c7d","spanId":"76da564f17ba2c7d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.647+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"76da564f17ba2c7d","spanId":"76da564f17ba2c7d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.647+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"76da564f17ba2c7d","spanId":"76da564f17ba2c7d","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.643+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58ec08b9e859f22b","spanId":"58ec08b9e859f22b","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:38:02.644+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58ec08b9e859f22b","spanId":"58ec08b9e859f22b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58ec08b9e859f22b","spanId":"58ec08b9e859f22b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.648+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58ec08b9e859f22b","spanId":"58ec08b9e859f22b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.648+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58ec08b9e859f22b","spanId":"58ec08b9e859f22b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.647+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fde8e3e50d5e0c46","spanId":"fde8e3e50d5e0c46","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:02.648+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fde8e3e50d5e0c46","spanId":"fde8e3e50d5e0c46","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.651+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fde8e3e50d5e0c46","spanId":"fde8e3e50d5e0c46","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.651+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fde8e3e50d5e0c46","spanId":"fde8e3e50d5e0c46","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.651+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fde8e3e50d5e0c46","spanId":"fde8e3e50d5e0c46","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0e2f133cc3b3aab","spanId":"d0e2f133cc3b3aab","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:46:02.627+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0e2f133cc3b3aab","spanId":"d0e2f133cc3b3aab","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0e2f133cc3b3aab","spanId":"d0e2f133cc3b3aab","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.63+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0e2f133cc3b3aab","spanId":"d0e2f133cc3b3aab","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.63+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0e2f133cc3b3aab","spanId":"d0e2f133cc3b3aab","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.649+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e0471b63d091439","spanId":"5e0471b63d091439","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:50:02.65+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e0471b63d091439","spanId":"5e0471b63d091439","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e0471b63d091439","spanId":"5e0471b63d091439","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.652+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e0471b63d091439","spanId":"5e0471b63d091439","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.652+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e0471b63d091439","spanId":"5e0471b63d091439","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.649+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cdfbe38f3f1afc24","spanId":"cdfbe38f3f1afc24","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:54:02.649+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cdfbe38f3f1afc24","spanId":"cdfbe38f3f1afc24","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cdfbe38f3f1afc24","spanId":"cdfbe38f3f1afc24","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.652+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cdfbe38f3f1afc24","spanId":"cdfbe38f3f1afc24","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.652+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cdfbe38f3f1afc24","spanId":"cdfbe38f3f1afc24","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d75112a2f9093cf","spanId":"9d75112a2f9093cf","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:58:02.636+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d75112a2f9093cf","spanId":"9d75112a2f9093cf","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d75112a2f9093cf","spanId":"9d75112a2f9093cf","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.638+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d75112a2f9093cf","spanId":"9d75112a2f9093cf","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.639+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d75112a2f9093cf","spanId":"9d75112a2f9093cf","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.639+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8affc231b2af2efd","spanId":"8affc231b2af2efd","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:02:02.64+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8affc231b2af2efd","spanId":"8affc231b2af2efd","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8affc231b2af2efd","spanId":"8affc231b2af2efd","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.652+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8affc231b2af2efd","spanId":"8affc231b2af2efd","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.652+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8affc231b2af2efd","spanId":"8affc231b2af2efd","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.661+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72f3111499c1e1b6","spanId":"72f3111499c1e1b6","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:02.662+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72f3111499c1e1b6","spanId":"72f3111499c1e1b6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72f3111499c1e1b6","spanId":"72f3111499c1e1b6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.665+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72f3111499c1e1b6","spanId":"72f3111499c1e1b6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.665+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72f3111499c1e1b6","spanId":"72f3111499c1e1b6","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.649+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7577d4403eaf445c","spanId":"7577d4403eaf445c","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:10:02.65+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7577d4403eaf445c","spanId":"7577d4403eaf445c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7577d4403eaf445c","spanId":"7577d4403eaf445c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.654+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7577d4403eaf445c","spanId":"7577d4403eaf445c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.654+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7577d4403eaf445c","spanId":"7577d4403eaf445c","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.641+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4e2dacdf50e5932f","spanId":"4e2dacdf50e5932f","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:14:02.642+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4e2dacdf50e5932f","spanId":"4e2dacdf50e5932f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.646+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4e2dacdf50e5932f","spanId":"4e2dacdf50e5932f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.646+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4e2dacdf50e5932f","spanId":"4e2dacdf50e5932f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.646+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4e2dacdf50e5932f","spanId":"4e2dacdf50e5932f","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.649+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af299e7d18ac6a0b","spanId":"af299e7d18ac6a0b","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:18:02.65+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af299e7d18ac6a0b","spanId":"af299e7d18ac6a0b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af299e7d18ac6a0b","spanId":"af299e7d18ac6a0b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.654+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af299e7d18ac6a0b","spanId":"af299e7d18ac6a0b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.654+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af299e7d18ac6a0b","spanId":"af299e7d18ac6a0b","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf159e791b6434ed","spanId":"cf159e791b6434ed","context":"QueueService","no":"6263","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:22:02.628+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6503, orderId=6263, flash=97_E09T_PSLC_010001_8GB, orderFlashNo=YS8297##MP010001###0530##3D_TLC_E09T#250134_97_E09T_PSLC_010001_8GB, num=200, leftNum=166)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf159e791b6434ed","spanId":"cf159e791b6434ed","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf159e791b6434ed","spanId":"cf159e791b6434ed","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.63+08:00","@version":"1","message":"[6263] - [97_E09T_PSLC_010001_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf159e791b6434ed","spanId":"cf159e791b6434ed","context":"QueueService","no":"6263","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.63+08:00","@version":"1","message":"[eMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cf159e791b6434ed","spanId":"cf159e791b6434ed","context":"QueueService","no":"6263","traceType":"分配设备"}
