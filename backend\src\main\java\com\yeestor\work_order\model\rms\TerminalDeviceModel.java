package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.yeestor.work_order.entity.PlanPlatformEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 终端设备信息
 */
@Slf4j
@Data
@JsonAutoDetect
@ApiModel(value = "TerminalDeviceModel", description = "RMS中终端下的设备【手机平台】，手机挂载在电脑终端，需要补充电脑终端信息")
@NoArgsConstructor
@AllArgsConstructor
public class TerminalDeviceModel {

    public final static List<String> FREE_STATUS = Arrays.asList("空闲", "离线");
    /**
     * 产品线, SD , U2 , U3 等等, 对应着系统中的subProduct
     */
    @ApiModelProperty("产品线")
    private String product;

    @ApiModelProperty("平台序号")
    private String no;

    @ApiModelProperty("平台名称【编号】")
    private String name;

    @ApiModelProperty("样品编号")
    private String sample;

    @ApiModelProperty("样品容量")
    private String volume;

    /**
     * 平台所包含的属性列表
     */
    @ApiModelProperty("平台所包含的属性列表")
    private List<String> attrModelList;

    /**
     * 平台的状态
     */
    @ApiModelProperty("平台的状态")
    private String status;

    /**
     * 平台接入的终端编号
     */
    @ApiModelProperty("平台接入的终端编号")
    private String pcNo;

    /**
     * 平台接入的终端的IP地址
     */
    @ApiModelProperty("平台接入的终端IP地址")
    private String ip;

    /**
     * 平台接入的测试机的mac 地址
     */
    @ApiModelProperty("平台接入的终端MAC地址")
    private String mac;

    @ApiModelProperty("平台接入的终端位置")
    private String position;

    public static PlanPlatformEntity toEntity(TerminalDeviceModel model) {
        PlanPlatformEntity entity = new PlanPlatformEntity();
        entity.setIp(model.getIp());
        entity.setMac(model.getMac());
        entity.setNo(model.getPcNo());
        entity.setPosition(model.getPosition());
        entity.setNumber(model.getNo());
        entity.setName(model.getName());
        entity.setVolume(model.getVolume());
        entity.setSample(model.getSample());
        return entity;
    }

}
