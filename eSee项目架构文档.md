# eSee 项目架构文档

## 1. 项目整体架构概述

### 1.1 项目简介
eSee 是一个前后端分离的工单管理系统，主要用于存储设备的测试工单管理、设备控制、测试计划执行等业务场景。系统支持多产品线（GE通用、SSD、EM嵌入式、IND工业级）的工单生命周期管理。

### 1.2 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │    │   后端服务       │    │   外部系统       │
│   (React/Umi)   │◄──►│  (Spring Boot)  │◄──►│  (RMS/禅道)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx         │    │   MariaDB       │    │   Redis         │
│   (静态资源)     │    │   (业务数据)     │    │   (缓存/会话)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.3 部署架构
- **容器化部署**: 使用 Docker 容器化部署
- **微服务架构**: 基于 Spring Cloud 的微服务体系
- **负载均衡**: Nginx 作为反向代理和负载均衡
- **服务发现**: Eureka 服务注册与发现
- **配置管理**: Spring Cloud Config 配置中心

## 2. 后端架构分析

### 2.1 技术栈和框架
- **核心框架**: Spring Boot 2.x + Spring Cloud
- **数据访问**: Spring Data JPA + Hibernate
- **数据库**: MariaDB (主) + Redis (缓存)
- **安全认证**: Spring Security + OAuth2 + JWT
- **API文档**: Knife4j (Swagger增强版)
- **任务调度**: Spring Quartz
- **监控管理**: Spring Boot Admin + Actuator
- **日志管理**: Logback + ELK Stack
- **工具库**: Hutool、Lombok、Apache POI

### 2.2 目录结构和模块组织
```
backend/src/main/java/com/yeestor/work_order/
├── WorkOrderApplication.java          # 应用启动类
├── config/                           # 配置类
├── controller/                       # 控制器层
│   ├── order/                       # 工单相关接口
│   ├── review/                      # 评审相关接口
│   ├── role/                        # 权限角色接口
│   ├── tool/                        # 工具类接口(RMS/Scrcpy)
│   └── jobs/                        # 定时任务接口
├── entity/                          # 实体类
│   ├── order/                       # 工单实体
│   ├── device/                      # 设备实体
│   ├── plan/                        # 计划实体
│   ├── review/                      # 评审实体
│   └── role/                        # 角色权限实体
├── repository/                      # 数据访问层
├── service/                         # 业务逻辑层
│   ├── order/                       # 工单服务
│   ├── device/                      # 设备管理服务
│   ├── plan/                        # 计划管理服务
│   └── zentao/                      # 禅道集成服务
├── model/                           # 数据传输对象
├── utils/                           # 工具类
└── exception/                       # 异常处理
```

### 2.3 数据库设计
#### 核心业务表结构
- **wo_order** (5,764条记录): 工单主表，存储工单基本信息
- **wo_order_detail** (5,748条记录): 工单详情表，技术配置信息
- **wo_order_plan** (66,355条记录): 测试计划表，管理具体测试计划
- **wo_order_flash** : Flash测试表，管理Flash批次测试
- **wo_plan_device** (112,167条记录): 计划设备分配表
- **wo_device_control** (13,492条记录): 设备控制记录表
- **wo_review_info**: 评审信息表
- **wo_role_***: 角色权限相关表

#### 数据库关系图
```mermaid
erDiagram
    wo_order ||--|| wo_order_detail : "工单详情"
    wo_order ||--o{ wo_order_flash : "Flash批次"
    wo_order_flash ||--o{ wo_order_plan : "测试计划"
    wo_order_plan ||--o{ wo_plan_device : "设备分配"
    wo_plan_device ||--o{ wo_device_control : "设备控制"
```

### 2.4 API 接口设计
#### RESTful API 规范
- **工单管理**: `/api/order/*` - 工单CRUD操作
- **设备管理**: `/api/device/*` - 设备控制和监控
- **计划管理**: `/api/plan/*` - 测试计划管理
- **评审管理**: `/api/review/*` - 测试结果评审
- **权限管理**: `/api/role/*` - 用户角色权限
- **工具集成**: `/api/tool/*` - RMS/禅道集成

#### 接口认证
- JWT Token 认证机制
- OAuth2 授权框架
- 基于角色的访问控制(RBAC)

### 2.5 核心业务逻辑模块
#### 工单生命周期管理
1. **CREATED** - 创建中：工单刚创建，等待确认
2. **CONFIRMED_FLASH** - 待测试：Flash信息已确认，排队中
3. **TESTING** - 测试中：工单正在执行测试
4. **EVALUATING** - 评估中：测试完成，评估结果
5. **COMPLETED** - 已完成：测试完成
6. **REVOKED** - 已取消：工单被取消

#### 设备管理模块
- 设备状态监控和控制
- 设备锁定机制防止资源冲突
- 自动化设备开关机管理
- 设备测试历史记录

#### 测试计划模块
- 多产品线测试计划管理
- 设备资源自动分配
- 测试进度跟踪
- 测试结果收集

## 3. 前端架构分析

### 3.1 技术栈和框架
- **核心框架**: React 17.0.0 + TypeScript 4.2.2
- **构建工具**: Umi 3.5.0 (企业级React应用框架)
- **UI组件库**: Ant Design 4.14.0 + Ant Design Pro
- **移动端**: Ant Design Mobile 5.0.0
- **状态管理**: Dva (Redux + Redux-saga)
- **HTTP客户端**: umi-request
- **样式方案**: Less + CSS Modules
- **图表库**: AntV G2/G6
- **富文本**: Toast UI Editor, React Quill
- **其他工具**: 钉钉JSAPI、Excel处理(xlsx)

### 3.2 目录结构和组件组织
```
frontend/src/
├── app.tsx                          # 应用入口配置
├── access.ts                        # 权限配置
├── global.tsx                       # 全局配置
├── components/                      # 全局通用组件
│   ├── Authorized/                  # 权限组件
│   ├── RightContent/               # 右侧内容区
│   └── unit/                       # 业务组件单元
├── layouts/                         # 布局组件
│   └── BasicLayout.tsx             # 基础布局
├── pages/                          # PC端页面组件
│   ├── work_order/                 # 工单管理页面
│   ├── machine/                    # 设备管理页面
│   ├── report/                     # 报告页面
│   ├── role/                       # 角色管理页面
│   └── user/                       # 用户相关页面
├── pages_mobile/                   # 移动端页面组件
├── models/                         # Dva数据模型
│   ├── workorder/                  # 工单相关模型
│   ├── login/                      # 登录模型
│   └── role/                       # 角色模型
├── services/                       # API服务层
│   ├── order/                      # 工单API
│   ├── review/                     # 评审API
│   ├── role/                       # 角色API
│   └── zentao/                     # 禅道API
├── utils/                          # 工具函数
├── locales/                        # 国际化文件
└── css/                           # 全局样式
```

### 3.3 路由设计
#### 路由配置结构
```typescript
const routes = [
  {
    path: '/work_order',           # 工单管理模块
    routes: [
      { path: 'GE/list' },        # 通用产品线
      { path: 'SSD/list' },       # SSD产品线
      { path: 'EM/list' },        # eMMC产品线
      { path: 'IND/list' },       # 工业级产品线
    ]
  },
  {
    path: '/machines',             # 设备管理模块
    routes: [
      { path: '/machines/order' }, # 工单设备
      { path: '/machines/rms' },   # RMS设备
    ]
  },
  {
    path: '/report',               # 报告模块
    routes: [
      { path: 'order' },          # 工单报告
      { path: 'flash' },          # Flash报告
      { path: 'plan' },           # 计划报告
      { path: 'device' },         # 设备报告
    ]
  }
];
```

### 3.4 状态管理方案
#### Dva Model 架构
```typescript
export default {
  namespace: 'workorder',
  state: {
    orderList: [],
    currentOrder: null,
    loading: false,
  },
  effects: {
    *fetchOrderList({ payload }, { call, put }) {
      const response = yield call(orderAPI.getOrderList, payload);
      yield put({ type: 'saveOrderList', payload: response.data });
    },
  },
  reducers: {
    saveOrderList(state, { payload }) {
      return { ...state, orderList: payload };
    },
  },
};
```

### 3.5 UI 组件库使用情况
- **Ant Design Pro Table**: 高级表格组件，支持搜索、排序、分页
- **Ant Design Pro Form**: 高级表单组件，支持复杂表单验证
- **Ant Design Pro Layout**: 企业级布局组件
- **Ant Design Mobile**: 移动端组件库
- **AntV 图表**: G2数据可视化、G6图分析

## 4. 前后端交互方式

### 4.1 API 通信协议
- **协议**: HTTP/HTTPS RESTful API
- **数据格式**: JSON
- **认证方式**: JWT Token + Cookie
- **错误处理**: 统一错误码和错误信息格式

### 4.2 请求响应格式
```typescript
// 统一响应格式
interface HandleResp<T> {
  code: number;        // 状态码：0-成功，其他-失败
  data: T;            // 响应数据
  message: string;    // 响应消息
}

// 分页响应格式
interface PageResp<T> {
  list: T[];          // 数据列表
  total: number;      // 总数
  pageSize: number;   // 页大小
  current: number;    // 当前页
}
```

### 4.3 前端请求封装
```typescript
// 基于umi-request的统一请求封装
import { request } from 'umi';

export async function getOrderList(params: OrderListParams) {
  return request<HandleResp<PageResp<OrderItem>>>('/api/order/list', {
    method: 'GET',
    params,
  });
}
```

## 5. 部署和构建流程

### 5.1 后端构建部署
```dockerfile
# 多阶段构建
FROM maven:3.6.3-jdk-11 AS maven
COPY ./src ./src
COPY ./pom.xml ./pom.xml
RUN mvn clean install -Dmaven.test.skip=true

FROM openjdk:11-jre-slim
COPY --from=maven target/system.work_order-1.0.0-SNAPSHOT.jar ./work_order.jar
CMD java -jar work_order.jar
```

### 5.2 前端构建部署
```dockerfile
# Node.js构建阶段
FROM node:14.19.2 AS BUILD
WORKDIR /usr/src/app
RUN npm install -g pnpm@7.5.0
COPY . .
RUN pnpm install && pnpm run build

# Nginx部署阶段
FROM nginx:1.20.1
COPY --from=BUILD /usr/src/app/dist /usr/share/nginx/html
COPY default.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
```

### 5.3 Kubernetes 部署配置
- **开发环境**: wo-dev.yaml
- **测试环境**: wo-alpha.yaml  
- **生产环境**: wo-prod.yaml

## 6. 开发环境配置说明

### 6.1 后端开发环境
```yaml
# application-dev.yml
server:
  port: 8793

spring:
  datasource:
    url: *******************************************************
    username: root
    password: Reliable123!
  
  redis:
    host: ***********
    port: 6379
    database: 1
```

### 6.2 前端开发环境
```json
{
  "scripts": {
    "start": "cross-env UMI_ENV=dev PORT=8512 umi dev",
    "build": "umi build",
    "test": "umi test"
  }
}
```

### 6.3 开发工具配置
- **IDE**: IntelliJ IDEA / VS Code
- **Java版本**: JDK 11
- **Node版本**: Node.js 14.19.2
- **包管理器**: Maven (后端) + pnpm (前端)

## 7. 关键依赖项和版本信息

### 7.1 后端关键依赖
```xml
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
    </dependency>
    <dependency>
        <groupId>org.mariadb.jdbc</groupId>
        <artifactId>mariadb-java-client</artifactId>
    </dependency>
</dependencies>
```

### 7.2 前端关键依赖
```json
{
  "dependencies": {
    "react": "^17.0.0",
    "umi": "^3.5.0",
    "antd": "^4.14.0",
    "@ant-design/pro-table": "^2.30.8",
    "@ant-design/pro-form": "^1.18.3",
    "typescript": "^4.2.2"
  }
}
```

## 8. 系统特色功能

### 8.1 多产品线支持
- GE (通用存储)
- SSD (固态硬盘)  
- EM (嵌入式存储)
- IND (工业级存储)

### 8.2 设备自动化管理
- 设备状态实时监控
- 自动开关机控制
- 设备资源锁定机制
- 测试任务自动分配

### 8.3 外部系统集成
- **RMS系统**: 设备管理和测试执行
- **禅道系统**: 项目管理和Bug跟踪
- **钉钉集成**: 消息通知和身份认证

### 8.4 移动端支持
- 响应式设计
- 移动端专用页面
- 钉钉小程序集成

---

*本文档基于 eSee 项目当前版本生成，如有更新请及时同步修改。*
