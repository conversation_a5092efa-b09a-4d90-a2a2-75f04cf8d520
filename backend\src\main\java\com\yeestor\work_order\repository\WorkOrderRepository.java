package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.http.resp.output.DAFlashModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface WorkOrderRepository extends JpaRepository<WorkOrderEntity, Long> {

    Page<WorkOrderEntity> findAllByProductAndSubProductAndStatusIn(String product, String subProduct, List<WorkOrderEntity.Status> statusList, Pageable pageable);

    Page<WorkOrderEntity> findAllByProductAndSubProductAndChipContainingAndVersionContainingAndBuildByContainingAndStatusInAndFeatureIn(
            String product,
            String subProduct,
            String chip,
            String version,
            String builderBy,
            List<WorkOrderEntity.Status> statusList,
            List<Integer> featureList,
            Pageable pageable
    );

    int countByProductAndSubProduct(String product, String subProduct);
    int countByProductAndSubProductAndCreatedAtAfter(String product, String subProduct, long beforeCreatedAt);


    @Query(value = "select o from WorkOrder o where o.subProduct = :subProduct and o.status in :statusList and " +
            "(select count(1) from OrderPlan p where p.orderId = o.id and p.status in :planStatusList) > 0 ")
    Page<WorkOrderEntity> fetchOrdersBySpecifyStatusPlanCount(String subProduct,
                                                              List<WorkOrderEntity.Status> statusList,
                                                              List<OrderPlanEntity.Status> planStatusList,
                                                              Pageable pageable);

    Page<WorkOrderEntity> findAllByProductAndSubProductAndChipContainingAndVersionContainingAndBuildByContainingAndStatusInAndIdInAndFeatureIn(
            String product,
            String subProduct,
            String chip,
            String version,
            String builderBy,
            List<WorkOrderEntity.Status> statusList,
            List<Long> orderIdList,
            List<Integer> featureList,
            Pageable pageable
    );

    boolean existsByNo(String no);

    @Query("select o.subProduct from WorkOrder o where o.id = :orderId")
    Optional<String> findSubProductById(long orderId);

    @Query("select a.fullVersion from WorkOrder a where a.fullVersion is not null ")
    List<String> findAllFullVersion();

    int countBySubProductAndStatusIn(String subProduct,List<WorkOrderEntity.Status> statusList );

    Optional<WorkOrderEntity> findByNo(String orderNo);

    List<WorkOrderEntity> findByProductAndSubProductAndChipAndFlashAndVersionTypeAndFullVersionContaining(
            String product,
            String subProduct,
            String chip,
            String flash,
            String versionType,
            String fullVersion
    );

    List<WorkOrderEntity> findAllByStatusNotIn(List<WorkOrderEntity.Status> statusList);

    List<WorkOrderEntity> findAllBySubProductAndChipAndFeatureNotAndStatusIn(String subProduct, String chip, int feature, List<WorkOrderEntity.Status> statusList);

    @Query("select o from WorkOrder o where o.subProduct = :subProduct and o.chip = :chip and o.feature != :feature and o.status in :statusList and o.endAt > :startTime")
    List<WorkOrderEntity> findBySubProductAndChipAndFeatureNotAndStatusInAndEndAt(String subProduct, String chip, int feature, List<WorkOrderEntity.Status> statusList, long startTime);

    @Query("select o from WorkOrder o where o.buildId = :buildId and o.id != :orderId")
    List<WorkOrderEntity> findAllOrderByBuildId(long buildId, long orderId);

    @Query("select DISTINCT f.flash from WorkOrder o, OrderFlash f where o.product = :product and o.subProduct = :subProduct and o.id = f.orderId")
    List<String> findAllFlashByProductAndBySubProduct(String product, String subProduct);

    @Query("select o from WorkOrder o where o.product = :product and o.subProduct = :subProduct")
    List<WorkOrderEntity> findAllByProductAndBySubProduct(String product, String subProduct);

    @Query("select o.chip from WorkOrder o where o.product = :product and o.subProduct = :subProduct")
    List<String> findChipNameByProductAndBySubProduct(String product, String subProduct);

    @Query("select o.no from WorkOrder o where o.product = :product and o.subProduct = :subProduct and o.status in :statusList order by o.id desc")
    List<String> findOrderNoByProductAndSubProductAndStatus(String product, String subProduct, List<WorkOrderEntity.Status> statusList);

    List<WorkOrderEntity> findAllOrderByProductAndFeature(String product, int feature);

    @Query("select DISTINCT a.fullVersion from WorkOrder a where a.subProduct = :subProduct and a.fullVersion is not null ")
    List<String> findFullVersionBySubProduct(String subProduct);

    @Query("select DISTINCT a.flash from WorkOrder a where a.subProduct = :subProduct and a.flash is not null ")
    List<String> findFlashModelBySubProduct(String subProduct);

    @Query("select new com.yeestor.work_order.model.http.resp.output.DAFlashModel(o, f) from WorkOrder o, OrderFlash f " +
            "where o.subProduct = :subProduct and o.chip like %:chip% and o.flash like %:flash% and o.fullVersion like %:fullVersion% " +
            "and o.id = f.orderId and f.createdAt >= :afterAt and f.createdAt <= :beforeAt ")
    List<DAFlashModel> findOrderFlashBySubProductAndChipAndFlashAndTime(
            String subProduct,
            String chip,
            String flash,
            String fullVersion,
            long afterAt,
            long beforeAt
    );
}
