{"@timestamp":"2025-07-23T16:27:02.6+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04d54c0f1809f372","spanId":"04d54c0f1809f372","context":"QueueService","no":"6371","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:27:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6623, orderId=6371, flash=TJ-1583-TAS-C_64GB, orderFlashNo=YS1583##MP#########3777##TAS-9BC4_TLC250023_TJ-1583-TAS-C_64GB, num=80, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04d54c0f1809f372","spanId":"04d54c0f1809f372","context":"QueueService","no":"6371","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.615+08:00","@version":"1","message":"[6371] - [TJ-1583-TAS-C_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04d54c0f1809f372","spanId":"04d54c0f1809f372","context":"QueueService","no":"6371","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.615+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04d54c0f1809f372","spanId":"04d54c0f1809f372","context":"QueueService","no":"6371","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.596+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b82581b8e3d99c55","spanId":"b82581b8e3d99c55","context":"QueueService","no":"6371","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:02.597+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6623, orderId=6371, flash=TJ-1583-TAS-C_64GB, orderFlashNo=YS1583##MP#########3777##TAS-9BC4_TLC250023_TJ-1583-TAS-C_64GB, num=80, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b82581b8e3d99c55","spanId":"b82581b8e3d99c55","context":"QueueService","no":"6371","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.598+08:00","@version":"1","message":"[6371] - [TJ-1583-TAS-C_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b82581b8e3d99c55","spanId":"b82581b8e3d99c55","context":"QueueService","no":"6371","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.599+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b82581b8e3d99c55","spanId":"b82581b8e3d99c55","context":"QueueService","no":"6371","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.598+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f717298121a3cf82","spanId":"f717298121a3cf82","context":"QueueService","no":"6371","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:45:02.599+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6623, orderId=6371, flash=TJ-1583-TAS-C_64GB, orderFlashNo=YS1583##MP#########3777##TAS-9BC4_TLC250023_TJ-1583-TAS-C_64GB, num=80, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f717298121a3cf82","spanId":"f717298121a3cf82","context":"QueueService","no":"6371","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.6+08:00","@version":"1","message":"[6371] - [TJ-1583-TAS-C_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f717298121a3cf82","spanId":"f717298121a3cf82","context":"QueueService","no":"6371","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.601+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f717298121a3cf82","spanId":"f717298121a3cf82","context":"QueueService","no":"6371","traceType":"分配设备"}
