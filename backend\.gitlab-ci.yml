#image: docker
#services:
#  - docker
#variables:
#  DOCKER_HOST: tcp://************:2375/
variables:
  CI_REGISTRY: gateway.yeestor.com:5000
  CI_REGISTRY_IMAGE: gateway.yeestor.com:5000/workorder
  CI_REGISTRY_USER: bugs.wan
  CI_REGISTRY_PASSWORD: bugs1234

stages:
#  - SAST
  - check
  - build
  - coverage
  - deploy

# 代码检测
# FIXME 移除代码检测
#sonarqube-check:
#  stage: SAST
#  image: maven:3.6.3-jdk-11
#  variables:
#    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
#    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
#  cache:
#    key: "${CI_JOB_NAME}"
#    paths:
#      - .sonar/cache
#  script:
#    - mvn verify sonar:sonar -DskipTests=true -Dsonar.projectKey=${SONAR_PROJECT_KEY}  -Dhttps.proxyHost=************* -Dhttps.proxyPort=7890
#  allow_failure: true
#  tags:
#    - coverage
#  rules:
#    - changes:
#        - src/**/*
#        - pom.xml
#        - Dockerfile

# 用于检查tag格式是否正确, 正确格式为: v1.0613.01-alpha, v1.0613.01-beta, v1.0613.01-rc, v1.0613.01
check-tag-format:
  stage: check
  image: busybox:1.35.0
  script:
    - if ! echo "$CI_COMMIT_TAG" | grep -q -E '^v[0-9]+\.[0-9]{4}\.[0-9]{2}(-alpha|-beta|-rc)$'; then echo "Invalid tag format!"; exit 1; fi
  only:
    - tags
  tags:
    - docker

# 构建镜像并推送到镜像仓库
build:
  stage: build
  image: docker:24.0.2
  services:
    - docker:24.0.2-dind
  before_script:
    - env
    - echo "Using ${CI_REGISTRY_USER}:$CI_REGISTRY_PASSWORD to login to $CI_REGISTRY"
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build --tag $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG --tag $CI_REGISTRY_IMAGE:latest .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
    - docker push $CI_REGISTRY_IMAGE:latest
  tags:
    - build
    - dnd
  rules:
    - if: '$CI_COMMIT_TAG'
      changes:
        - src/**/*
        - pom.xml
        - Dockerfile
  dependencies:
    - check-tag-format

# 部署到k8s 的模板
.deploy_template: &deploy_template
  stage: deploy
  tags:
    - docker
  image:
    name: bitnami/kubectl:1.24.4
    entrypoint: [""]
  dependencies:
    - check-tag-format
    - build

# 只要是正确的tag, 就部署到alpha环境
deploy-alpha:
  <<: *deploy_template
  script:
    - kubectl -n yeestor set image deployment/wo-alpha-deployment wo-alpha=${CI_REGISTRY_IMAGE}:${CI_COMMIT_TAG}
    - kubectl -n yeestor rollout status deployment/wo-alpha-deployment --timeout=20s
  only:
    - tags

# 只有beta, rc, 正式版才部署到prod环境
deploy-prod:
    <<: *deploy_template
    script:
      - kubectl -n yeestor set image deployment/wo-prod-deployment wo-prod=${CI_REGISTRY_IMAGE}:${CI_COMMIT_TAG}
      - kubectl -n yeestor rollout status deployment/wo-prod-deployment --timeout=20s
    rules:
      - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d{4}\.\d{2}(|-beta|-rc)$/'
        when: always

