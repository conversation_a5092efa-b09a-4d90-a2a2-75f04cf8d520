package com.yeestor.work_order.model.http.resp.order;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.entity.OrderBugEntity;
import com.yeestor.work_order.model.http.req.BugConnectParams;
import com.yeestor.zentao.model.resp.BugInfoResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BugInfo", description = "禅道中的Bug信息")
public class BugInfo {

    @ApiModelProperty("工单ID")
    private Long orderId;

    @ApiModelProperty("Flash批次")
    private String flash;

    @ApiModelProperty("Plan的名称,不是标题")
    private String planName ;

    @ApiModelProperty("设备的唯一标识,Mac/IP")
    private String device ;
    @ApiModelProperty("样片编号")
    private String sample ;

    @ApiModelProperty("Bug ID")
    private String id;
    @ApiModelProperty("Bug 标题")
    private String title;
    @ApiModelProperty("Bug 标题")
    private String status;

    @ApiModelProperty("Bug 复现步骤")
    private String steps;

    @ApiModelProperty("严重程度")
    private int severity;

    @ApiModelProperty("优先级")
    @JsonAlias("pri")
    private int priority;

    @SneakyThrows
    public static BugInfo of(Object obj) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        String jsonResult = mapper.writeValueAsString(obj);
        return mapper.readValue(jsonResult, BugInfo.class);
    }

    public OrderBugEntity convert2Entity(BugConnectParams params){
        OrderBugEntity bugEntity = convertOrder(params.getOrderId());

        bugEntity.setFlash(params.getFlash());
        bugEntity.setPlan(params.getPlan());
        bugEntity.setDevice(params.getDevice());
        bugEntity.setSample(params.getSample());
        return bugEntity ;
    }

    public OrderBugEntity convertOrder(long orderId){
        OrderBugEntity bugEntity = new OrderBugEntity();

        bugEntity.setOrderId(orderId);
        bugEntity.setBugId(id);
        bugEntity.setTitle(title);
        bugEntity.setPri(priority);
        bugEntity.setSeverity(severity);
        bugEntity.setStatus(status);
        // 绑定日期
        bugEntity.setCreatedAt(System.currentTimeMillis());
        bugEntity.setUpdatedAt(System.currentTimeMillis());
        return bugEntity ;
    }

    public void applyConnectInfo(OrderBugEntity bugEntity) {
        if(bugEntity == null){
            return;
        }
        this.setOrderId(bugEntity.getOrderId());
        this.setFlash(bugEntity.getFlash());
        this.setPlanName(bugEntity.getPlan());
        this.setDevice(bugEntity.getDevice());
        this.setSample(bugEntity.getSample());
    }

    public static BugInfo toModel(OrderBugEntity bugEntity) {
        BugInfo model = new BugInfo();
        model.setId(bugEntity.getBugId());
        model.setTitle(bugEntity.getTitle());
        model.setSeverity(bugEntity.getSeverity());
        model.setPriority(bugEntity.getPri());
        model.setOrderId(bugEntity.getOrderId());
        model.setFlash(bugEntity.getFlash());
        model.setPlanName(bugEntity.getPlan());
        model.setDevice(bugEntity.getDevice());
        model.setSample(bugEntity.getSample());
        return model;
    }

    public static BugInfo toModel(BugInfoResp bugInfoResp){
        BugInfo model = new BugInfo();
        model.setId(bugInfoResp.getId());
        model.setTitle(bugInfoResp.getTitle());
        model.setSteps(bugInfoResp.getSteps());
        model.setStatus(bugInfoResp.getStatus());
        model.setSeverity(bugInfoResp.getSeverity());
        model.setPriority(bugInfoResp.getPri());
        return model;
    }

}