{"@timestamp":"2025-07-24T14:00:02.95+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:00:03.086+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.281+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.286+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.634+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:03:02.636+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.639+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.699+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.704+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.709+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.709+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.655+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:09:02.658+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.663+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.664+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.648+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:12:02.65+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.655+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.655+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.645+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:15:02.65+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.663+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.663+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.686+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.688+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.692+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.692+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.656+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:21:02.658+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.662+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.663+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:24:02.63+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.634+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.634+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:27:02.635+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.639+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.76+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.761+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.764+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.764+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:33:02.632+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.635+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.633+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:36:02.635+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.638+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.649+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:51:02.67+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.678+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.762+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.764+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.775+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.782+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:02.893+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:00:02.947+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.124+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.125+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:03:02.633+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.636+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.697+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.698+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.703+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.703+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:09:02.637+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.64+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.964+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:12:02.966+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.97+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.97+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:15:02.631+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.635+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.686+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.691+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.695+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.695+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:21:02.63+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.633+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.637+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:24:02.64+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.643+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:27:02.633+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.637+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:02.887+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6391","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:02.919+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6631, orderId=6391, flash=WTX1_64GB, orderFlashNo=YS8297##MP010106###0530##3D_TLC_E09T#250087_WTX1_64GB, num=200, leftNum=200)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.083+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6391","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.111+08:00","@version":"1","message":"[6391] - [WTX1_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6391","traceType":"分配设备"}
