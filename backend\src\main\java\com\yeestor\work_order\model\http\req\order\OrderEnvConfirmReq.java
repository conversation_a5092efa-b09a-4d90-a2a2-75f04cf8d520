package com.yeestor.work_order.model.http.req.order;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@JsonAutoDetect
@ApiModel(value = "OrderEnvConfirmReq", description = "环境确认请求参数")
public class OrderEnvConfirmReq {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlanDeviceInfo {
        @ApiModelProperty(value = "Plan的ID")
        private long planId;
        @ApiModelProperty(value = "设备的列表，传设备的IP即可!")
        private List<String> nodes;

        @NotEmpty(message = "Plan的设备列表不能为空！")
        @ApiModelProperty(value = "设备的Mac列表,传设备的Mac即可!")
        private List<String> macList;
    }

    @Min(value = 1, message = "无效工单ID！")
    @ApiModelProperty(value = "工单ID")
    private long orderId;

    @NotBlank(message = "Flash 批次不能为空！")
    @ApiModelProperty(value = "Flash 批次",required = true)
    private String flash ;

    @NotEmpty(message = "Plan设备列表不能为空！")
    @ApiModelProperty(value = "Plan对应的测试环境列表", required = true)
    private List<PlanDeviceInfo> planDeviceInfoList;


}
