<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单系统数据分析报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .section-header p {
            color: #6c757d;
            font-size: 1.1em;
        }

        .section-content {
            padding: 20px;
        }

        .chart-container {
            margin-bottom: 30px;
            text-align: center;
        }

        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #495057;
        }

        .insights {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .insights h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .insights ul {
            list-style-type: none;
            padding-left: 0;
        }

        .insights li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .insights li:before {
            content: "📊";
            position: absolute;
            left: 0;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .kpi-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .kpi-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .kpi-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .recommendations {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .recommendations h3 {
            color: #f57c00;
            margin-bottom: 10px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .kpi-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 工单系统数据分析报告</h1>
            <p>基于实际数据的全面分析与可视化展示</p>
            <p>分析时间：2025年7月 | 数据范围：2024-2025年</p>
        </div>

        <!-- 关键指标概览 -->
        <div class="section">
            <div class="section-header">
                <h2>📈 关键指标概览</h2>
                <p>系统整体运行状况的核心指标</p>
            </div>
            <div class="section-content">
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-value">5,764</div>
                        <div class="kpi-label">总工单数</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">80.0%</div>
                        <div class="kpi-label">工单完成率</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">69.7%</div>
                        <div class="kpi-label">设备测试成功率</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">92.5%</div>
                        <div class="kpi-label">Flash批次完成率</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">71.6%</div>
                        <div class="kpi-label">测试计划完成率</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工单维度分析 -->
        <div class="section">
            <div class="section-header">
                <h2>📋 工单维度分析</h2>
                <p>工单状态、优先级、产品线分布及趋势分析</p>
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <div class="chart-title">工单状态分布</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/40TWTZsNrOUAAAAAR5AAAAgAoEACAQFr/original" alt="工单状态分布">
                </div>

                <div class="chart-container">
                    <div class="chart-title">不同优先级工单数量和完成情况对比</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/M9ZPRbX6Au0AAAAARaAAAAgAoEACAQFr/original" alt="优先级分析">
                </div>

                <div class="chart-container">
                    <div class="chart-title">各产品线工单分布和完成情况</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/nPFUQbrdJ4wAAAAARXAAAAgAoEACAQFr/original" alt="产品线分析">
                </div>

                <div class="chart-container">
                    <div class="chart-title">工单创建趋势（2024-2025年）</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/0ZH5Q7Y9HDgAAAAASKAAAAgAoEACAQFr/original" alt="创建趋势">
                </div>

                <div class="insights">
                    <h3>🔍 关键洞察</h3>
                    <ul>
                        <li>系统整体执行效率良好，80%的工单已完成，17.4%被撤销</li>
                        <li>高优先级工单占比最大(32.4%)，但完成率相对较低(84.2%)</li>
                        <li>GE-SD产品线工单量最大(1904个)，EM-UFS产品线需要特别关注</li>
                        <li>工单创建呈现季节性波动，12月和3月是高峰期</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 测试人员维度分析 -->
        <div class="section">
            <div class="section-header">
                <h2>👥 测试人员维度分析</h2>
                <p>测试人员工作负载、效率和能力分析</p>
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <div class="chart-title">测试人员工作负载分布（TOP15）</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/j6uuQ62TlnwAAAAATjAAAAgAoEACAQFr/original" alt="人员负载">
                </div>

                <div class="chart-container">
                    <div class="chart-title">测试人员工作量与完成率关系</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/AbvsT7sML08AAAAAUUAAAAgAoEACAQFr/original" alt="效率分析">
                </div>

                <div class="insights">
                    <h3>🔍 关键洞察</h3>
                    <ul>
                        <li>TOP15测试人员中，最高负载410个工单，最低130个，工作负载分布不均</li>
                        <li>工作负载与完成率没有明显负相关，高负载人员仍能保持较好完成率</li>
                        <li>完成率在75%-94%之间波动，存在明显的个人能力差异</li>
                        <li>建议实施工单分配算法，平衡测试人员工作负载</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 设备资源维度分析 -->
        <div class="section">
            <div class="section-header">
                <h2>🖥️ 设备资源维度分析</h2>
                <p>设备使用情况、成功率和资源利用分析</p>
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <div class="chart-title">设备状态分布</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/M9ZPRbX6Au0AAAAARaAAAAgAoEACAQFr/original" alt="设备状态">
                </div>

                <div class="chart-container">
                    <div class="chart-title">设备测试成功率趋势</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/nPFUQbrdJ4wAAAAARXAAAAgAoEACAQFr/original" alt="成功率趋势">
                </div>

                <div class="chart-container">
                    <div class="chart-title">设备使用频率排行榜（TOP20）</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/AbvsT7sML08AAAAAUUAAAAgAoEACAQFr/original" alt="设备使用频率">
                </div>

                <div class="chart-container">
                    <div class="chart-title">设备使用频率与成功率关系分析</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/nPFUQbrdJ4wAAAAARXAAAAgAoEACAQFr/original" alt="使用频率与成功率">
                </div>

                <div class="chart-container">
                    <div class="chart-title">设备使用频率分布统计</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/0ZH5Q7Y9HDgAAAAASKAAAAgAoEACAQFr/original" alt="使用频率分布">
                </div>

                <div class="insights">
                    <h3>🔍 关键洞察</h3>
                    <ul>
                        <li>63.5%的设备测试成功完成，27.5%失败，资源利用充分</li>
                        <li>设备使用存在明显差异：SS-PC-MB0643使用683次，而66台设备仅使用1次</li>
                        <li>高频使用设备(500次以上)仅14台，但承担了大量测试任务</li>
                        <li>设备成功率在54%-90%之间波动，GE2_2_02设备成功率仅54.71%需要检修</li>
                        <li>建议优化设备分配策略，提高低使用率设备的利用率</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Flash批次维度分析 -->
        <div class="section">
            <div class="section-header">
                <h2>💾 Flash批次维度分析</h2>
                <p>Flash批次状态分布和测试时长分析</p>
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <div class="chart-title">Flash批次状态分布</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/40TWTZsNrOUAAAAAR5AAAAgAoEACAQFr/original" alt="Flash状态">
                </div>

                <div class="chart-container">
                    <div class="chart-title">Flash批次测试时长分布</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/0ZH5Q7Y9HDgAAAAASKAAAAgAoEACAQFr/original" alt="测试时长">
                </div>

                <div class="insights">
                    <h3>🔍 关键洞察</h3>
                    <ul>
                        <li>92.5%的Flash批次已完成，流程执行效率很高</li>
                        <li>大部分Flash批次在1-4周内完成测试，符合预期</li>
                        <li>3.4%的批次测试超过4周，需要分析长周期原因</li>
                        <li>建立预警机制，对超过4周的长周期Flash批次进行监控</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 系统运营维度分析 -->
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 系统运营维度分析</h2>
                <p>系统整体吞吐量、计划执行效率和失败原因分析</p>
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <div class="chart-title">测试计划状态分布</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/40TWTZsNrOUAAAAAR5AAAAgAoEACAQFr/original" alt="计划状态">
                </div>

                <div class="chart-container">
                    <div class="chart-title">系统吞吐量趋势：计划创建数量与平均复杂度</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/j6uuQ62TlnwAAAAATjAAAAgAoEACAQFr/original" alt="吞吐量趋势">
                </div>

                <div class="chart-container">
                    <div class="chart-title">设备测试失败原因分析</div>
                    <img src="https://mdn.alipayobjects.com/one_clip/afts/img/M9ZPRbX6Au0AAAAARaAAAAgAoEACAQFr/original" alt="失败原因">
                </div>

                <div class="insights">
                    <h3>🔍 关键洞察</h3>
                    <ul>
                        <li>71.6%的测试计划已完成，12.6%被停止，整体执行良好</li>
                        <li>平均每个工单包含8-15个计划，系统复杂度较高</li>
                        <li>74.3%的设备失败没有明确原因，需要改进故障诊断机制</li>
                        <li>系统吞吐量呈现季节性波动，需要合理安排资源</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 业务改进建议 -->
        <div class="section">
            <div class="section-header">
                <h2>🚀 业务改进建议</h2>
                <p>基于数据分析的具体改进措施和建议</p>
            </div>
            <div class="section-content">
                <div class="recommendations">
                    <h3>📋 优先级管理优化</h3>
                    <ul>
                        <li>对高优先级工单分配更多资源，提高完成率</li>
                        <li>建立优先级动态调整机制，根据业务需求灵活调整</li>
                    </ul>
                </div>

                <div class="recommendations">
                    <h3>👥 人员负载均衡</h3>
                    <ul>
                        <li>实施工单分配算法，平衡测试人员工作负载</li>
                        <li>为低效率人员提供培训和支持，提升整体团队能力</li>
                    </ul>
                </div>

                <div class="recommendations">
                    <h3>🖥️ 设备管理改进</h3>
                    <ul>
                        <li>建立详细的设备故障诊断和记录机制</li>
                        <li>分析设备失败模式，制定预防性维护策略</li>
                        <li>提高设备测试成功率目标至75%以上</li>
                        <li>优化设备分配策略，提高低使用率设备的利用率</li>
                    </ul>
                </div>

                <div class="recommendations">
                    <h3>⚡ 流程监控强化</h3>
                    <ul>
                        <li>对超过4周的长周期Flash批次建立预警机制</li>
                        <li>优化测试流程，减少不必要的等待时间</li>
                    </ul>
                </div>

                <div class="recommendations">
                    <h3>📊 数据质量提升</h3>
                    <ul>
                        <li>强制要求填写失败原因，提高数据完整性</li>
                        <li>建立标准化的故障分类体系，便于分析和改进</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 关键性能指标建议 -->
        <div class="section">
            <div class="section-header">
                <h2>📈 关键性能指标(KPI)建议</h2>
                <p>建议监控的核心指标和目标值</p>
            </div>
            <div class="section-content">
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-value">> 85%</div>
                        <div class="kpi-label">工单完成率目标</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">> 75%</div>
                        <div class="kpi-label">设备测试成功率目标</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">> 95%</div>
                        <div class="kpi-label">Flash批次4周内完成率目标</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">< 50</div>
                        <div class="kpi-label">测试人员负载标准差目标</div>
                    </div>
                    <div class="kpi-card">
                        <div class="kpi-value">> 90%</div>
                        <div class="kpi-label">故障原因记录完整率目标</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>📊 本报告基于工单系统实际数据生成，分析时间：2025年7月</p>
            <p>💡 数据来源：wo_order、wo_plan_device、wo_order_flash、wo_order_plan等35个数据表</p>
            <p>🔄 建议定期更新此报告，持续监控系统运行状况</p>
        </div>
    </div>
</body>
</html>