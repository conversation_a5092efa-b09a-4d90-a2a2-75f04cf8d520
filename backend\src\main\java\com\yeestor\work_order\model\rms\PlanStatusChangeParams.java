package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yeestor.work_order.model.base.DeviceBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "PlanDeviceChangeParams", description = "Plan设备变更参数")
public class PlanStatusChangeParams {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(value = "PlanDeviceChangeParams-DeviceInfo", description = "成功或者设备信息。")
    public static class PlanDeviceInfo implements DeviceBaseInfo {

        @ApiModelProperty(value = "设备IP", required = true)
        private String ip ;

        // 过渡阶段，暂定为false
        @ApiModelProperty(value = "设备MAC地址")
        private String mac ;

        @ApiModelProperty(value = "设备编号", required = true)
        private String no ;

        @ApiModelProperty(value = "设备测试信息", required = true)
        private String msg ;

        @ApiModelProperty(value = "错误类型")
        @JsonAlias("error_type")
        private String errorType ;

        @ApiModelProperty(value = "错误的样片设备")
        @JsonAlias("failLst")
        private List<String> failList ;

        public List<String> getFailList() {
            if (failList == null) {
                return List.of();
            }
            return failList;
        }
    }

    @NotBlank(message = "工单号不能为空")
    @ApiModelProperty(value = "工单号", required = true)
    private String orderNo ;

    @NotBlank(message = "Plan名不能为空")
    @ApiModelProperty(value = "计划名称", required = true)
    private String plan ;
    @NotBlank(message = "状态不能为空！")
    @ApiModelProperty(value = "plan状态", required = true)
    private String status ;


    @NotEmpty(message = "设备列表不能为空")
    @ApiModelProperty(value = "设备列表", required = true)
    private List<PlanDeviceInfo> deviceList ;

    @JsonIgnore
    public boolean isSuccess(){
        return "PASS".equalsIgnoreCase(status);
    }

}

