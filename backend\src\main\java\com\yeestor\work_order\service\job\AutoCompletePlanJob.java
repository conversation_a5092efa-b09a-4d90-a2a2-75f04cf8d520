package com.yeestor.work_order.service.job;

import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.utils.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * plan的自动完成任务，如果plan 已经完成了，则不再执行
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AutoCompletePlanJob implements Job {

    public static final String JOB_GROUP_NAME = "AutoCompletePlanJob";

    private final PlanService planService ;
    private final OrderPlanRepository planRepository ;

    @Override
    public void execute(JobExecutionContext context) {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        log.debug("execute:{}  map key:{} value:{} ", context.getJobDetail().getKey().getName(), Arrays.toString(dataMap.getKeys()),dataMap.values());

        if(!dataMap.containsKey("orderFlashNo") || !dataMap.containsKey("planId") ){
            return;
        }
        long planId = dataMap.getLong("planId");
        String orderFlashNo = dataMap.getString("orderFlashNo");
        long timestamp = dataMap.getLong("timestamp");
        planRepository.findById(planId).ifPresent(planEntity -> {
            LogUtils.setOrderTracePoint(planEntity.getOrderId(), "自动完成Plan");
            log.info("于{}发起的 {} 自动完成任务已到达触发时间！", TimeUtils.formatTimestamp(timestamp) ,planEntity.getName());
            // 自动释放plan 下的所有需要释放的设备。
            planService.releasePlanDevices(orderFlashNo, planEntity);
            LogUtils.clearTracePoint();
        });


    }
}