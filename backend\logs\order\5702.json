{"@timestamp":"2025-07-23T10:34:04.203+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:34:04.231+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:04.237+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:04.237+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:03.332+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:38:03.333+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:03.335+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:03.336+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.359+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:42:03.36+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.362+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.362+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:03.439+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:46:03.44+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:03.442+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:03.442+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:03.352+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:50:03.353+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:03.354+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:03.355+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:03.58+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:03.581+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:03.583+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:03.584+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:03.288+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:58:03.289+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:03.291+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:03.291+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:03.497+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:02:03.499+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:03.501+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:03.502+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:03.331+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:06:03.332+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:03.333+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:03.334+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:03.324+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:10:03.324+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:03.326+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:03.326+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:03.52+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:14:03.521+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:03.523+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:03.524+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:03.294+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:18:03.294+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:03.296+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:03.296+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:05+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:34:05.037+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:05.043+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:05.043+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:03.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:38:03.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:03.614+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:03.614+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:03.327+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:03.328+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:03.331+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:03.331+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:03.321+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:46:03.322+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:03.324+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:03.324+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:03.273+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:50:03.274+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:03.276+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:03.276+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:03.293+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:54:03.293+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:03.295+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:03.295+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:03.311+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:58:03.312+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:03.314+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:03.314+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:03.293+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:02:03.293+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:03.295+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:03.295+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.291+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:06:03.291+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.292+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.292+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:03.351+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:10:03.351+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:03.353+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:03.353+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:03.304+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:14:03.305+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:03.307+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:03.307+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:03.379+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:18:03.379+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:03.381+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:03.381+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:03.314+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:22:03.315+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:03.317+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:03.317+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:03.304+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:26:03.305+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:03.306+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:03.306+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:03.272+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:03.273+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:03.275+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:03.275+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:03.267+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:34:03.268+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:03.269+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:03.27+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:03.323+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:38:03.325+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:03.326+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:03.326+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:03.352+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:03.353+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:03.355+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:03.355+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:03.328+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:46:03.329+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:03.33+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:03.33+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:03.32+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:50:03.321+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:03.323+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:03.323+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:03.343+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:54:03.344+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:03.346+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:03.346+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:03.342+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:58:03.342+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:03.344+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:03.344+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:03.306+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:02:03.307+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:03.308+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:03.308+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:03.333+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:06:03.333+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:03.336+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:03.336+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:03.374+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:10:03.374+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:03.375+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:03.375+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:03.37+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:14:03.371+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:03.373+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:03.373+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:03.36+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:03.361+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:03.363+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:03.363+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:03.378+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:22:03.378+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:03.38+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:03.38+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:04.341+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:26:04.341+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:04.343+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:04.343+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:03.405+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:03.406+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:03.409+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:03.409+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:03.414+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:34:03.415+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:03.417+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:03.417+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:03.372+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:38:03.373+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:03.375+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:03.375+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:03.349+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:03.349+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:03.352+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:03.352+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:03.307+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:46:03.308+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:03.31+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:03.31+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:03.289+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:50:03.29+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:03.291+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:03.292+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:03.301+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:54:03.302+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:03.304+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:03.304+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:03.286+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:58:03.287+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:03.288+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:03.288+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:03.382+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:02:03.383+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:03.385+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:03.386+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:03.38+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:03.381+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:03.383+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:03.383+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:03.493+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:10:03.494+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:03.497+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:03.498+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:03.438+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:14:03.44+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:03.442+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:03.442+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:03.359+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:18:03.36+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:03.399+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:03.399+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:03.337+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"5702","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:22:03.338+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5904, orderId=5702, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP010605###0010##3D_TLC_E09T#250015_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:03.339+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"5702","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:03.339+08:00","@version":"1","message":"[5702] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"5702","traceType":"分配设备"}
