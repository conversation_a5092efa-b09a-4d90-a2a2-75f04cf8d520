package com.yeestor.work_order.model.http.resp.config;

import com.yeestor.work_order.entity.config.TimeoutConfigEntity;
import lombok.Data;

import java.util.List;

@Data
public class TimeoutConfigVO {

    /**
     * 由于这部分的配置比较简单, 所以这里直接返回 Entity 列表.
     */
    private List<TimeoutConfigEntity> configs ;


    /**
     * 可选的类型
     */
    private List<TimeoutConfigEntity.Type> types ;


    /**
     * 可选的阶段
     */
    private List<String> phases ;



}
