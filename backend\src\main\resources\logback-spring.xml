<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/file-appender.xml"/>

    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <springProperty scope="context" name="orderPrefix" source="order.prefix"/>
    <!-- Example for logging into the build folder of your project -->
    <property name="LOG_FILE" value="${BUILD_FOLDER:-logs}/${springAppName}.log"/>

    <!-- You can override this to have a custom pattern -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([%X{no:-unknown}]){yellow}  %clr(${PID:- }){magenta} %clr(---){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m %n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx} "/>
    <property name="STDOUT_PATTERN"
              value="IMPORT-LOG %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>



    <!-- Appender to log to file -->
    <appender name="flat" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE:-logs/unknown}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>
    <logger name="org.springframework.web.HttpLogging" level="trace" additivity="false">
        <appender-ref ref="flat"/>
    </logger>

    <logger name="com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver" level="trace" additivity="false">
        <appender-ref ref="flat"/>
    </logger>

    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="trace" additivity="false">
        <appender-ref ref="flat"/>
    </logger>
    <logger name="org.hibernate.SQL" level="trace" additivity="false">
        <appender-ref ref="flat"/>
    </logger>

    <logger name="org.springframework.security" level="debug" additivity="false">
        <appender-ref ref="flat"/>
    </logger>

    <logger name="com.yeestor" level="trace"/>
    <logger name="org.apache.poi.openxml4j.opc.PackageRelationshipCollection" level="OFF" />

    <springProfile name="alpha">
        <logger name="org.springframework.boot.autoconfigure" level="DEBUG" additivity="false"/>
        <!-- Appender to log to console -->
        <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                <charset>utf8</charset>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="console"/>
        </root>
    </springProfile>

    <springProfile name="dev">
        <logger name="org.springframework.boot.autoconfigure" level="DEBUG" additivity="false"/>
        <!-- Appender to log to console -->
        <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                <charset>utf8</charset>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="console"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <!-- Appender to log to console -->
        <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
            <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
                <evaluator> <!-- defaults to type ch.qos.logback.classic.boolex.JaninoEventEvaluator -->
                    <expression>
                        return
                        message.contains("WorkContext: QueueService") ||
                        (marker != null &amp;&amp; marker.contains("QueueService")) ||
                        (formattedMessage.contains("eureka/apps/delta")) ||
                        "QueueService".equals((String) mdc.get("context")) ;
                    </expression>
                </evaluator>
                <onMismatch>ACCEPT</onMismatch>
                <onMatch>DENY</onMatch>
            </filter>
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                <charset>utf8</charset>
            </encoder>
        </appender>


        <appender name="SIFT_JSON" class="ch.qos.logback.classic.sift.SiftingAppender">
            <discriminator>
                <key>no</key>
                <defaultValue>unknown</defaultValue>
            </discriminator>
            <sift>
                <appender name="FILE-${no}" class="ch.qos.logback.core.FileAppender">
                    <file>logs/${orderPrefix}/${no}.json</file>
                    <append>true</append>
                    <encoder charset="UTF-8"
                             class="net.logstash.logback.encoder.LogstashEncoder">
                    </encoder>
                </appender>
            </sift>
        </appender>

        <appender name="logstash"
                  class="net.logstash.logback.appender.LogstashTcpSocketAppender">
            <destination>172.18.8.192:5044</destination>
            <!-- encoder必须配置,有多种可选 -->
            <encoder charset="UTF-8"
                     class="net.logstash.logback.encoder.LogstashEncoder">
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="logstash"/>
            <appender-ref ref="flat"/>
            <appender-ref ref="SIFT_JSON"/>
        </root>
    </springProfile>
</configuration>
