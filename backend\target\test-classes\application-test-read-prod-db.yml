
spring:
  application:
    name: system-workorder
    title: 工单系统-test

  main:
    allow-bean-definition-overriding: true

  datasource:
    url: ************************************************************************
    username: root
    password: Reliable123!

  jpa:
    open-in-view: false
    hibernate:
      ddl-auto: 'none'
      show-sql: true

  quartz:
    enable: false
    auto-startup: false
    job-store-type: memory


eureka:
  enabled: false
  client:
    serviceUrl:
      defaultZone: ${EUREKA_SERVER_ADDRESS:http://***********:8788/eureka/}

  instance:
    preferIpAddress: true
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}

order:
  prefix: test

logging:
  #  config: classpath:log4j2.yml
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.jdbc.core.JdbcTemplate: DEBUG
    org.springframework.jdbc.core.StatementCreatorUtils: TRACE
    org.springframework.web: DEBUG
    org.springframework.web.HttpLogging: DEBUG
    org.springframework.security: DEBUG
    org.springframework.security.oauth2: DEBUG
