package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "ReportStatusChangeParams", description = "报告状态变更回调")
public class ReportStatusChangeParams {


    @ApiModel(value = "ReportStatusChangeParams-ReportInfo", description = "报告信息。")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ReportInfo {

        @ApiModelProperty("文件名称，可以和文件名不同")
        private String name ;

        @ApiModelProperty("文件下载的地址，可以是smb地址或者http地址")
        private String url ;

        @ApiModelProperty(value = "文件类型，报告或者错误日志。",example = "report,error")
        private String type ;
    }


    @ApiModelProperty("工单号")
    @NotBlank(message = "工单号不能为空")
    private String orderNo ;

    @ApiModelProperty("报告信息列表")
    @NotEmpty(message = "报告信息列表不能为空")
    private List<ReportInfo> reportInfoList ;

}
