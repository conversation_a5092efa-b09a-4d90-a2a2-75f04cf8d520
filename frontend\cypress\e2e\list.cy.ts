/// <reference types="cypress" />

describe('Test Login', () => {
  beforeEach(() => {
    cy.viewport(1280, 720);
    cy.clearCookies();

    cy.intercept('http://************:8789/auth/oauth/token').as('login');

    cy.visit(
      'http://localhost:8512/user/test/login?uname=0653290119972081&pwd=yeestor_0653290119972081',
    );

    cy.wait('@login');
  });

  it('without login', () => {
    cy.clearCookies();
    cy.wait(1000);
    cy.visit('http://localhost:8512');
    cy.get('#rc-tabs-0-tab-qrcode').should('exist').contains('扫码登陆');
  }),
    it('with login', () => {
      cy.contains('access_token').should('exist');
      cy.wait(1000);
      cy.intercept('**/api/order/list**').as('getOrderList');
      cy.visit('http://localhost:8512');
      cy.wait('@getOrderList');

      cy.get('.ant-table').contains('工单号');
    });
});
context('with list page', () => {
  beforeEach(() => {
    cy.viewport(1280, 720);
    cy.clearCookies();

    cy.intercept('http://************:8789/auth/oauth/token').as('login');

    cy.visit(
      'http://localhost:8512/user/test/login?uname=0653290119972081&pwd=yeestor_0653290119972081',
    );

    cy.wait('@login');
    cy.intercept('**/api/order/list**').as('getOrderList');
    cy.visit('http://localhost:8512/work_order/GE/list');
    cy.wait('@getOrderList');
  });

  it('待测试', () => {
    cy.get('.ant-radio-group > :nth-child(1)').contains('待测试').click();
    cy.get('.ant-table-row')
      .contains('创建')
      .siblings(':nth-child(9)')
      //不包含‘详情’
      .should('not.contain', '详情');

    cy.get('.ant-table-row')
      .contains('Flash确认完成')
      .siblings(':nth-child(9)')
      //包含‘详情’
      .contains('详情');
  });
  it('测试中', () => {
    cy.intercept('**/api/order/list**').as('getOrderList');
    cy.get('.ant-radio-group > :nth-child(2)').contains('测试中').click();
    cy.wait('@getOrderList');
  });
  it('测试完成', () => {
    cy.get('.ant-radio-group > :nth-child(3)').contains('测试完成').click();
  });
  it('完成', () => {
    cy.get('.ant-radio-group > :nth-child(4)').contains('完成').click();
  });
  it('撤销', () => {
    cy.get('.ant-radio-group > :nth-child(5)').contains('撤销').click();
  });
});
