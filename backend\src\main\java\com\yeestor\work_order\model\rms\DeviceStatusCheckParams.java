package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DeviceStatusCheckParams",description = "设备状态检查参数")
public class DeviceStatusCheckParams {

    @NotBlank(message = "设备所属产品不能为空")
    @ApiModelProperty(value = "设备所属产品", required = true)
    private String product;

    @NotEmpty(message = "设备列表不能为空")
    @ApiModelProperty(value = "设备列表", required = true)
    private List<String> macList;
}
