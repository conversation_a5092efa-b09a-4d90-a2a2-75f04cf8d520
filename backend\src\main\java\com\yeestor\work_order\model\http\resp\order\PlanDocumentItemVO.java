package com.yeestor.work_order.model.http.resp.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yeestor.work_order.entity.document.DocumentEntity;
import com.yeestor.work_order.entity.document.PlanDocumentEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel(value = "PlanDocumentItemVO", description = "Plan文档项")
public class PlanDocumentItemVO {


    @ApiModelProperty(value = "文档Id")
    private long id ;

    @ApiModelProperty(value = "order_plan_doc 表的主键id")
    private long docId ;

    @ApiModelProperty(value = "工单id")
    private long orderId ;

    @ApiModelProperty(value = "Plan id")
    private long planId ;

    @ApiModelProperty(value = "文档的url")
    private String url ;

    @ApiModelProperty(value = "文档的名称")
    private String name ;

    @ApiModelProperty(value = "文件的ContentType")
    private String contentType ;

    @ApiModelProperty(value = "文档的大小, 单位为KB。")
    private long size ;

    @ApiModelProperty(value = "文档的创建时间")
    private long createdAt ;

    @ApiModelProperty(value = "文档的创建人的名称")
    private String createdPerson ;

    @ApiModelProperty(hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PlanDocumentEntity planDocumentEntity ;

    @ApiModelProperty(hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DocumentEntity documentEntity ;


    /**
     * 给到 JpaRepository 中使用的构造方法。
     * @param planDocumentEntity PlanDocumentEntity
     * @param documentEntity DocumentEntity
     */
    public PlanDocumentItemVO(PlanDocumentEntity planDocumentEntity, DocumentEntity documentEntity) {
        this.setDocId(planDocumentEntity.getId());
        this.setId(documentEntity.getId());
        this.setOrderId(planDocumentEntity.getOrderId());
        this.setPlanId(planDocumentEntity.getPlanId());
        this.setUrl(documentEntity.getUrl());
        this.setName(documentEntity.getName());
        this.setContentType(documentEntity.getContentType());
        this.setSize(documentEntity.getSize());
        this.setCreatedAt(documentEntity.getCreatedAt());
        this.setCreatedPerson(documentEntity.getCreatedPerson());
    }

}
