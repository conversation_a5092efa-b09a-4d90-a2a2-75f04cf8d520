{"@timestamp":"2025-07-23T15:09:02.696+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4db18ab602b4873","spanId":"b4db18ab602b4873","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:09:02.787+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4db18ab602b4873","spanId":"b4db18ab602b4873","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:02.928+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4db18ab602b4873","spanId":"b4db18ab602b4873","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:02.932+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4db18ab602b4873","spanId":"b4db18ab602b4873","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:02.942+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4db18ab602b4873","spanId":"b4db18ab602b4873","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:02.951+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4db18ab602b4873","spanId":"b4db18ab602b4873","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:02.962+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4db18ab602b4873","spanId":"b4db18ab602b4873","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:02.963+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4db18ab602b4873","spanId":"b4db18ab602b4873","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:02.963+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4db18ab602b4873","spanId":"b4db18ab602b4873","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2d59e1daa9797f4","spanId":"b2d59e1daa9797f4","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:12:02.629+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2d59e1daa9797f4","spanId":"b2d59e1daa9797f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2d59e1daa9797f4","spanId":"b2d59e1daa9797f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.633+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2d59e1daa9797f4","spanId":"b2d59e1daa9797f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2d59e1daa9797f4","spanId":"b2d59e1daa9797f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.636+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2d59e1daa9797f4","spanId":"b2d59e1daa9797f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2d59e1daa9797f4","spanId":"b2d59e1daa9797f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.64+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2d59e1daa9797f4","spanId":"b2d59e1daa9797f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.64+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2d59e1daa9797f4","spanId":"b2d59e1daa9797f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921dd42b5e9a989","spanId":"c921dd42b5e9a989","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:15:02.633+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921dd42b5e9a989","spanId":"c921dd42b5e9a989","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921dd42b5e9a989","spanId":"c921dd42b5e9a989","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.636+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921dd42b5e9a989","spanId":"c921dd42b5e9a989","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921dd42b5e9a989","spanId":"c921dd42b5e9a989","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.639+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921dd42b5e9a989","spanId":"c921dd42b5e9a989","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.641+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921dd42b5e9a989","spanId":"c921dd42b5e9a989","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.642+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921dd42b5e9a989","spanId":"c921dd42b5e9a989","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.642+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921dd42b5e9a989","spanId":"c921dd42b5e9a989","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.674+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d779d59a0bc4ae0","spanId":"5d779d59a0bc4ae0","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.676+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d779d59a0bc4ae0","spanId":"5d779d59a0bc4ae0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.679+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d779d59a0bc4ae0","spanId":"5d779d59a0bc4ae0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.679+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d779d59a0bc4ae0","spanId":"5d779d59a0bc4ae0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.685+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d779d59a0bc4ae0","spanId":"5d779d59a0bc4ae0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.685+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d779d59a0bc4ae0","spanId":"5d779d59a0bc4ae0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.689+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d779d59a0bc4ae0","spanId":"5d779d59a0bc4ae0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.69+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d779d59a0bc4ae0","spanId":"5d779d59a0bc4ae0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.69+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d779d59a0bc4ae0","spanId":"5d779d59a0bc4ae0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:21:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb8a1bf0d06bd792","spanId":"cb8a1bf0d06bd792","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:21:02.626+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb8a1bf0d06bd792","spanId":"cb8a1bf0d06bd792","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:21:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb8a1bf0d06bd792","spanId":"cb8a1bf0d06bd792","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:21:02.63+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb8a1bf0d06bd792","spanId":"cb8a1bf0d06bd792","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:21:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb8a1bf0d06bd792","spanId":"cb8a1bf0d06bd792","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:21:02.633+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb8a1bf0d06bd792","spanId":"cb8a1bf0d06bd792","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:21:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb8a1bf0d06bd792","spanId":"cb8a1bf0d06bd792","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:21:02.637+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb8a1bf0d06bd792","spanId":"cb8a1bf0d06bd792","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:21:02.637+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb8a1bf0d06bd792","spanId":"cb8a1bf0d06bd792","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.632+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3944f3185377dfa5","spanId":"3944f3185377dfa5","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:24:02.635+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3944f3185377dfa5","spanId":"3944f3185377dfa5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3944f3185377dfa5","spanId":"3944f3185377dfa5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.639+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3944f3185377dfa5","spanId":"3944f3185377dfa5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3944f3185377dfa5","spanId":"3944f3185377dfa5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.643+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3944f3185377dfa5","spanId":"3944f3185377dfa5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3944f3185377dfa5","spanId":"3944f3185377dfa5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.648+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3944f3185377dfa5","spanId":"3944f3185377dfa5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.648+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3944f3185377dfa5","spanId":"3944f3185377dfa5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:27:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4394cceb23eab9c","spanId":"c4394cceb23eab9c","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:27:02.627+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4394cceb23eab9c","spanId":"c4394cceb23eab9c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:27:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4394cceb23eab9c","spanId":"c4394cceb23eab9c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:27:02.631+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4394cceb23eab9c","spanId":"c4394cceb23eab9c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:27:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4394cceb23eab9c","spanId":"c4394cceb23eab9c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:27:02.635+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4394cceb23eab9c","spanId":"c4394cceb23eab9c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:27:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4394cceb23eab9c","spanId":"c4394cceb23eab9c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:27:02.638+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4394cceb23eab9c","spanId":"c4394cceb23eab9c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:27:02.638+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4394cceb23eab9c","spanId":"c4394cceb23eab9c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.654+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f18b836517783631","spanId":"f18b836517783631","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:51:02.663+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f18b836517783631","spanId":"f18b836517783631","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.67+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f18b836517783631","spanId":"f18b836517783631","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.67+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f18b836517783631","spanId":"f18b836517783631","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.676+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f18b836517783631","spanId":"f18b836517783631","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.676+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f18b836517783631","spanId":"f18b836517783631","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.683+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f18b836517783631","spanId":"f18b836517783631","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.768+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f18b836517783631","spanId":"f18b836517783631","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.768+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f18b836517783631","spanId":"f18b836517783631","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.685+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e603cf50053bf605","spanId":"e603cf50053bf605","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:02.686+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e603cf50053bf605","spanId":"e603cf50053bf605","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.691+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e603cf50053bf605","spanId":"e603cf50053bf605","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.691+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e603cf50053bf605","spanId":"e603cf50053bf605","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.696+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e603cf50053bf605","spanId":"e603cf50053bf605","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.696+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e603cf50053bf605","spanId":"e603cf50053bf605","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.7+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e603cf50053bf605","spanId":"e603cf50053bf605","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.7+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e603cf50053bf605","spanId":"e603cf50053bf605","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.701+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e603cf50053bf605","spanId":"e603cf50053bf605","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.638+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3260a560dd8b9448","spanId":"3260a560dd8b9448","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:57:02.639+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3260a560dd8b9448","spanId":"3260a560dd8b9448","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3260a560dd8b9448","spanId":"3260a560dd8b9448","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.648+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3260a560dd8b9448","spanId":"3260a560dd8b9448","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3260a560dd8b9448","spanId":"3260a560dd8b9448","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.653+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3260a560dd8b9448","spanId":"3260a560dd8b9448","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.657+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3260a560dd8b9448","spanId":"3260a560dd8b9448","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.657+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3260a560dd8b9448","spanId":"3260a560dd8b9448","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.657+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3260a560dd8b9448","spanId":"3260a560dd8b9448","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f02f9bcad12aa6e1","spanId":"f02f9bcad12aa6e1","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:00:02.631+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f02f9bcad12aa6e1","spanId":"f02f9bcad12aa6e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f02f9bcad12aa6e1","spanId":"f02f9bcad12aa6e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.634+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f02f9bcad12aa6e1","spanId":"f02f9bcad12aa6e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f02f9bcad12aa6e1","spanId":"f02f9bcad12aa6e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.638+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f02f9bcad12aa6e1","spanId":"f02f9bcad12aa6e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f02f9bcad12aa6e1","spanId":"f02f9bcad12aa6e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.644+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f02f9bcad12aa6e1","spanId":"f02f9bcad12aa6e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.644+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f02f9bcad12aa6e1","spanId":"f02f9bcad12aa6e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.633+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87ee65fb33982ce9","spanId":"87ee65fb33982ce9","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:03:02.635+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87ee65fb33982ce9","spanId":"87ee65fb33982ce9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.641+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87ee65fb33982ce9","spanId":"87ee65fb33982ce9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.641+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87ee65fb33982ce9","spanId":"87ee65fb33982ce9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87ee65fb33982ce9","spanId":"87ee65fb33982ce9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.646+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87ee65fb33982ce9","spanId":"87ee65fb33982ce9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.65+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87ee65fb33982ce9","spanId":"87ee65fb33982ce9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.65+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87ee65fb33982ce9","spanId":"87ee65fb33982ce9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.65+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87ee65fb33982ce9","spanId":"87ee65fb33982ce9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.676+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1cc11d10b2c7146","spanId":"d1cc11d10b2c7146","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:02.678+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1cc11d10b2c7146","spanId":"d1cc11d10b2c7146","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.68+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1cc11d10b2c7146","spanId":"d1cc11d10b2c7146","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.681+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1cc11d10b2c7146","spanId":"d1cc11d10b2c7146","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.684+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1cc11d10b2c7146","spanId":"d1cc11d10b2c7146","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.684+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1cc11d10b2c7146","spanId":"d1cc11d10b2c7146","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.687+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1cc11d10b2c7146","spanId":"d1cc11d10b2c7146","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.687+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1cc11d10b2c7146","spanId":"d1cc11d10b2c7146","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.687+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1cc11d10b2c7146","spanId":"d1cc11d10b2c7146","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f19258b3cd2864ba","spanId":"f19258b3cd2864ba","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:09:02.633+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f19258b3cd2864ba","spanId":"f19258b3cd2864ba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f19258b3cd2864ba","spanId":"f19258b3cd2864ba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.637+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f19258b3cd2864ba","spanId":"f19258b3cd2864ba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f19258b3cd2864ba","spanId":"f19258b3cd2864ba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.64+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f19258b3cd2864ba","spanId":"f19258b3cd2864ba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f19258b3cd2864ba","spanId":"f19258b3cd2864ba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.643+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f19258b3cd2864ba","spanId":"f19258b3cd2864ba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.643+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f19258b3cd2864ba","spanId":"f19258b3cd2864ba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f790073095d18b26","spanId":"f790073095d18b26","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:12:02.628+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f790073095d18b26","spanId":"f790073095d18b26","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f790073095d18b26","spanId":"f790073095d18b26","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.632+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f790073095d18b26","spanId":"f790073095d18b26","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f790073095d18b26","spanId":"f790073095d18b26","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.635+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f790073095d18b26","spanId":"f790073095d18b26","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f790073095d18b26","spanId":"f790073095d18b26","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.638+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f790073095d18b26","spanId":"f790073095d18b26","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.638+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f790073095d18b26","spanId":"f790073095d18b26","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:21:02.632+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61d8952878a0776a","spanId":"61d8952878a0776a","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:21:02.638+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61d8952878a0776a","spanId":"61d8952878a0776a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:21:02.641+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61d8952878a0776a","spanId":"61d8952878a0776a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:21:02.642+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61d8952878a0776a","spanId":"61d8952878a0776a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:21:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61d8952878a0776a","spanId":"61d8952878a0776a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:21:02.645+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61d8952878a0776a","spanId":"61d8952878a0776a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:21:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61d8952878a0776a","spanId":"61d8952878a0776a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:21:02.647+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61d8952878a0776a","spanId":"61d8952878a0776a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:21:02.647+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61d8952878a0776a","spanId":"61d8952878a0776a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.637+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49544550fd2d4e07","spanId":"49544550fd2d4e07","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:24:02.64+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49544550fd2d4e07","spanId":"49544550fd2d4e07","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.646+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49544550fd2d4e07","spanId":"49544550fd2d4e07","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.646+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49544550fd2d4e07","spanId":"49544550fd2d4e07","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.649+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49544550fd2d4e07","spanId":"49544550fd2d4e07","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.652+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49544550fd2d4e07","spanId":"49544550fd2d4e07","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49544550fd2d4e07","spanId":"49544550fd2d4e07","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.66+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49544550fd2d4e07","spanId":"49544550fd2d4e07","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.661+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49544550fd2d4e07","spanId":"49544550fd2d4e07","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.641+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"399c25a12241a5d3","spanId":"399c25a12241a5d3","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:27:02.642+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"399c25a12241a5d3","spanId":"399c25a12241a5d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"399c25a12241a5d3","spanId":"399c25a12241a5d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.647+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"399c25a12241a5d3","spanId":"399c25a12241a5d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"399c25a12241a5d3","spanId":"399c25a12241a5d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.652+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"399c25a12241a5d3","spanId":"399c25a12241a5d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.656+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"399c25a12241a5d3","spanId":"399c25a12241a5d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.656+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"399c25a12241a5d3","spanId":"399c25a12241a5d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:27:02.656+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"399c25a12241a5d3","spanId":"399c25a12241a5d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.68+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"222688a4437a1601","spanId":"222688a4437a1601","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.682+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"222688a4437a1601","spanId":"222688a4437a1601","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.685+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"222688a4437a1601","spanId":"222688a4437a1601","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.685+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"222688a4437a1601","spanId":"222688a4437a1601","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.689+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"222688a4437a1601","spanId":"222688a4437a1601","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.689+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"222688a4437a1601","spanId":"222688a4437a1601","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.692+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"222688a4437a1601","spanId":"222688a4437a1601","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.692+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"222688a4437a1601","spanId":"222688a4437a1601","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.692+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"222688a4437a1601","spanId":"222688a4437a1601","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.645+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b5f8035318e187b","spanId":"2b5f8035318e187b","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:33:02.648+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b5f8035318e187b","spanId":"2b5f8035318e187b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b5f8035318e187b","spanId":"2b5f8035318e187b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.653+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b5f8035318e187b","spanId":"2b5f8035318e187b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.657+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b5f8035318e187b","spanId":"2b5f8035318e187b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.657+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b5f8035318e187b","spanId":"2b5f8035318e187b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.661+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b5f8035318e187b","spanId":"2b5f8035318e187b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.661+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b5f8035318e187b","spanId":"2b5f8035318e187b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.661+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b5f8035318e187b","spanId":"2b5f8035318e187b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15b7ea04e4705afa","spanId":"15b7ea04e4705afa","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:36:02.627+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15b7ea04e4705afa","spanId":"15b7ea04e4705afa","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15b7ea04e4705afa","spanId":"15b7ea04e4705afa","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.63+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15b7ea04e4705afa","spanId":"15b7ea04e4705afa","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15b7ea04e4705afa","spanId":"15b7ea04e4705afa","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.632+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15b7ea04e4705afa","spanId":"15b7ea04e4705afa","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15b7ea04e4705afa","spanId":"15b7ea04e4705afa","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15b7ea04e4705afa","spanId":"15b7ea04e4705afa","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15b7ea04e4705afa","spanId":"15b7ea04e4705afa","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eec423d5aca01bdc","spanId":"eec423d5aca01bdc","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:39:02.629+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eec423d5aca01bdc","spanId":"eec423d5aca01bdc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eec423d5aca01bdc","spanId":"eec423d5aca01bdc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.632+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eec423d5aca01bdc","spanId":"eec423d5aca01bdc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eec423d5aca01bdc","spanId":"eec423d5aca01bdc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.635+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eec423d5aca01bdc","spanId":"eec423d5aca01bdc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eec423d5aca01bdc","spanId":"eec423d5aca01bdc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.638+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eec423d5aca01bdc","spanId":"eec423d5aca01bdc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.638+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eec423d5aca01bdc","spanId":"eec423d5aca01bdc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.675+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ebeabaeb58b7759","spanId":"2ebeabaeb58b7759","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:02.676+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ebeabaeb58b7759","spanId":"2ebeabaeb58b7759","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.679+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ebeabaeb58b7759","spanId":"2ebeabaeb58b7759","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.68+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ebeabaeb58b7759","spanId":"2ebeabaeb58b7759","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.682+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ebeabaeb58b7759","spanId":"2ebeabaeb58b7759","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.682+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ebeabaeb58b7759","spanId":"2ebeabaeb58b7759","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.685+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ebeabaeb58b7759","spanId":"2ebeabaeb58b7759","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.685+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ebeabaeb58b7759","spanId":"2ebeabaeb58b7759","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.685+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ebeabaeb58b7759","spanId":"2ebeabaeb58b7759","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"96f2d4e53903b76b","spanId":"96f2d4e53903b76b","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:45:02.628+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"96f2d4e53903b76b","spanId":"96f2d4e53903b76b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"96f2d4e53903b76b","spanId":"96f2d4e53903b76b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.631+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"96f2d4e53903b76b","spanId":"96f2d4e53903b76b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"96f2d4e53903b76b","spanId":"96f2d4e53903b76b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.634+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"96f2d4e53903b76b","spanId":"96f2d4e53903b76b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"96f2d4e53903b76b","spanId":"96f2d4e53903b76b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.637+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"96f2d4e53903b76b","spanId":"96f2d4e53903b76b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:45:02.638+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"96f2d4e53903b76b","spanId":"96f2d4e53903b76b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0707defe767af8d0","spanId":"0707defe767af8d0","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:48:02.636+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0707defe767af8d0","spanId":"0707defe767af8d0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0707defe767af8d0","spanId":"0707defe767af8d0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.64+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0707defe767af8d0","spanId":"0707defe767af8d0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0707defe767af8d0","spanId":"0707defe767af8d0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.643+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0707defe767af8d0","spanId":"0707defe767af8d0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0707defe767af8d0","spanId":"0707defe767af8d0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.647+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0707defe767af8d0","spanId":"0707defe767af8d0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.647+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0707defe767af8d0","spanId":"0707defe767af8d0","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"98d69f9d468d7417","spanId":"98d69f9d468d7417","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:51:02.628+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"98d69f9d468d7417","spanId":"98d69f9d468d7417","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"98d69f9d468d7417","spanId":"98d69f9d468d7417","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.63+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"98d69f9d468d7417","spanId":"98d69f9d468d7417","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"98d69f9d468d7417","spanId":"98d69f9d468d7417","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.632+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"98d69f9d468d7417","spanId":"98d69f9d468d7417","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"98d69f9d468d7417","spanId":"98d69f9d468d7417","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.635+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"98d69f9d468d7417","spanId":"98d69f9d468d7417","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.635+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"98d69f9d468d7417","spanId":"98d69f9d468d7417","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.286+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:00:03.323+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.368+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.369+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.383+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.383+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.417+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.418+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.419+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"517b7032ed4f94e1","spanId":"517b7032ed4f94e1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.639+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:03:02.641+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.645+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.65+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.65+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.655+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.656+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.656+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff23947308cf52cd","spanId":"ff23947308cf52cd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.709+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.712+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.717+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.718+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.724+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.724+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.73+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.73+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.73+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39e830ba22d6f080","spanId":"39e830ba22d6f080","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.664+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:09:02.667+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.678+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.686+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.686+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.693+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.693+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.693+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"669a796f83047ec9","spanId":"669a796f83047ec9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.655+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:12:02.657+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.663+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.663+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.669+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.669+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.674+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.674+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.674+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1be3ab99db2fbf2","spanId":"d1be3ab99db2fbf2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.663+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:15:02.665+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.669+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.669+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.677+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.678+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.685+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.685+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.685+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"04ec004d43bf5509","spanId":"04ec004d43bf5509","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.692+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.693+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.697+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.697+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.701+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.701+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.705+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.705+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.705+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82dd8dea9e66662e","spanId":"82dd8dea9e66662e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.663+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:21:02.665+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.668+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.672+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.672+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.677+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.677+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.677+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"507a8b7482277a0f","spanId":"507a8b7482277a0f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.634+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:24:02.639+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.649+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.652+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.657+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.657+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.657+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a3dd9211e1689a7","spanId":"3a3dd9211e1689a7","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.639+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:27:02.641+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.646+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.65+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.65+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.655+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.655+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.655+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d013f955f2299b4b","spanId":"d013f955f2299b4b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.765+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.766+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.771+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.771+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.775+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.776+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.78+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.78+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.78+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5ad6cdbca15169fc","spanId":"5ad6cdbca15169fc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:33:02.637+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.641+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.641+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.644+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.648+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.648+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d7cdf44ad7907e1a","spanId":"d7cdf44ad7907e1a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.638+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:36:02.639+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.643+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.648+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.651+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.652+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.652+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa1603953645188b","spanId":"fa1603953645188b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.679+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:51:02.691+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.7+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.7+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.709+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.709+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.72+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.72+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.72+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89bd1c87b2e09429","spanId":"89bd1c87b2e09429","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.782+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.786+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.794+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.795+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.809+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.809+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.816+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.816+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.816+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47fdf1ee800e1406","spanId":"47fdf1ee800e1406","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.127+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:00:03.155+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.177+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.179+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.201+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.202+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.226+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.226+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.227+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc890e6c99fc8192","spanId":"dc890e6c99fc8192","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:03:02.638+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.642+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.642+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.646+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.646+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.65+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.65+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.65+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"474ed13cbdcec243","spanId":"474ed13cbdcec243","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.703+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.706+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.717+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.717+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.724+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.726+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.732+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.733+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.733+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"15fbe7bbf264fd39","spanId":"15fbe7bbf264fd39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.64+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:09:02.641+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.645+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.649+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.652+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.652+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"89ff217593a6daa8","spanId":"89ff217593a6daa8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.971+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:12:02.973+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.977+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.977+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.981+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.981+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.985+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.986+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.986+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f0407a4dbf8090c5","spanId":"f0407a4dbf8090c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:15:02.636+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.641+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.645+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.649+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.649+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"84b872afd5b59f83","spanId":"84b872afd5b59f83","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.695+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.696+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.7+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.7+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.704+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.705+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.708+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.708+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.709+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"14c063c4e2e716a6","spanId":"14c063c4e2e716a6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.633+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:21:02.634+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.638+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.641+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.641+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.644+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.644+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62ec963c003321b8","spanId":"62ec963c003321b8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.643+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:24:02.645+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.649+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.65+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.656+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.656+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.66+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.66+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e885c55cc2675d75","spanId":"e885c55cc2675d75","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.637+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:27:02.639+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.643+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.648+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.652+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.652+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfea368a3533da68","spanId":"dfea368a3533da68","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.112+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.133+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=196)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.17+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.17+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.188+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.188+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.199+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.2+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.2+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"36ce550efdfe46c5","spanId":"36ce550efdfe46c5","context":"QueueService","no":"6292","traceType":"分配设备"}
