package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface PlanDeviceRepository extends JpaRepository<PlanDeviceEntity, Long> {

    List<PlanDeviceEntity> findAllByPlanId(long planId);


    int countByPlanIdAndStatusIn(long planId, List<PlanDeviceEntity.Status> statusList);

    PlanDeviceEntity findByPlanIdAndIp(long planId, String ip);
    PlanDeviceEntity findByPlanIdAndMac(long planId, String mac);
    List<PlanDeviceEntity> findAllByPlanIdAndIpIn(long planId, List<String> ipLst);
    List<PlanDeviceEntity> findAllByPlanIdIn(List<Long> planIds);
    List<PlanDeviceEntity> findByPlanIdInAndStatusAndReleaseAtIsNull(List<Long> planIds, PlanDeviceEntity.Status status);

    int countByPlanIdAndReleaseAtNotNull(long planId);

    boolean existsByPlanIdAndMac(long planId, String mac);
    int countByPlanIdAndMacIn(long planId, List<String> macList);

    int countByPlanId(long id);

    Optional<PlanDeviceEntity> findByOrderIdAndIpAndStatusInAndReleaseAtIsNull(long orderId, String ip , List<PlanDeviceEntity.Status> statusList);

    List<PlanDeviceEntity> findAllByOrderIdAndStatusIn(long orderId, List<PlanDeviceEntity.Status> asList);

    int countByOrderIdAndStatusIn(long orderId, List<PlanDeviceEntity.Status> asList);

    @Query("select p.planId from PlanDevice p where p.orderId = :orderId and p.status = :status group by p.planId")
    List<Long> findAllPlanIDByOrderIdAndStatus(long orderId, PlanDeviceEntity.Status status);

    List<PlanDeviceEntity> findAllByOrderIdAndIp(long orderId, String ip);
    List<PlanDeviceEntity> findAllByOrderIdAndMac(long orderId, String mac);
    List<PlanDeviceEntity> findAllByOrderIdAndMacAndStatusIn(long orderId, String mac, List<PlanDeviceEntity.Status> statusList);
    List<PlanDeviceEntity> findAllByOrderId(long orderId);


    List<PlanDeviceEntity> findAllByOrderIdAndMacInAndStatusIn(
            long orderId,
            List<String> macList,
            List<PlanDeviceEntity.Status> fixedStatusList
    );

    /**
     * 获取 指子产品下的所有手动类型的Plan 所绑定的设备
     * @param subProduct            子产品
     * @param planStatusList        Plan 状态列表
     * @param deviceStatusList      设备状态列表
     * @return                     设备列表
     */
    @Query("select d from PlanDevice d,OrderPlan p, WorkOrder o " +
            "where o.id = p.orderId and o.subProduct = :subProduct " +
            "and p.id = d.planId and p.type = 1 " +// 手动类型的Plan
            "and p.status in  :planStatusList " +
            "and d.status in :deviceStatusList " +
            "order by d.id asc"
    )
    List<PlanDeviceEntity> fetchAllInManualPlanBySubProduct(
            String subProduct,
            List<OrderPlanEntity.Status> planStatusList,
            List<PlanDeviceEntity.Status> deviceStatusList
    );

    List<PlanDeviceEntity> findAllByPlanIdAndMacIn(long planId, List<String> macList);

    /**
     * 获取 指PC编号下的plan 设备信息
     * @param pcNo   电脑编号
     * @return  设备信息
     */
    Optional<PlanDeviceEntity> findFirstByNoOrderByIdDesc(String pcNo);

    void deleteAllByPlanId(long planId);

    List<PlanDeviceEntity> findAllByPlanIdInAndStatusIn(List<Long> idList, List<PlanDeviceEntity.Status> statusList);

    List<PlanDeviceEntity> findAllByPlanIdAndStatusInAndMacNotNull(long planId, List<PlanDeviceEntity.Status> statusList);

    @Query("select d.position from PlanDevice d, OrderPlan p " +
            "where p.orderId = :orderId " +
            "and p.flash = :flash " +
            "and p.belongTo = :belongTo "+
            "and p.id = d.planId "
    )
    List<String> findAllByOrderIdAndFlashAndBelongTo(long orderId, String flash, String belongTo);
}
