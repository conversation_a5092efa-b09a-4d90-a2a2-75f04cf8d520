package com.yeestor.work_order.model.ci;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.yeestor.work_order.entity.WorkOrderEntity;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;

@Data
@Slf4j
@JsonAutoDetect
public class FaeModel {
    @ApiParam("flash 列表")
    private List<String> flashName;


    @ApiParam("bug列表")
    private List<BugLink> bugLink;

    @ApiParam("测试描述")
    private String htmlText;

    @ApiParam("产品ID")
    private String productId;

    @ApiParam("执行ID")
    private String executionId;

    @ApiParam("执行人邮件")
    private String userEmail;

    @ApiParam("执行人邮件")
    private String buildEmail;

    @ApiParam("构建人 ID")
    private String builder;

    @ApiModelProperty(value = "工单测试类型, ")
    private Integer feature = WorkOrderEntity.FEATURE_VERIFY_FLASH;

    @ApiParam("包含目的、测试流程、测试样片等，这部分存在变动情况")
    private HashMap<String, String> testInfo;

    @ApiParam("量产工具版本")
    private String mpVersion;

    @ApiParam("固件版本")
    private String fwVersion ;

    @ApiParam("主控")
    private String chip;

    @ApiParam("版本id， 在通用产品线Flash品质验证中是CI的版本id，在MPTOOL LLF中是禅道版本id")
    private Long buildId ;

    @ApiParam("版本路径, 竞品版本")
    private String versionPath ;

    @ApiParam("优先级")
    private Integer level;

    @Data
    @JsonAutoDetect
    public static class BugLink {
        private String bugId;
        private String solved;
        private String note;
        private long time;
    }
}
