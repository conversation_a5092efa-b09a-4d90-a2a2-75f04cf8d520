{"@timestamp":"2025-07-23T10:34:02.611+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:34:02.617+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.617+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.651+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.666+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.68+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.681+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.681+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.684+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.686+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.686+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:38:02.611+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.612+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.632+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.647+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.663+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.663+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.663+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.663+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.663+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.619+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:42:02.62+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.62+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.658+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.687+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.707+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.707+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.707+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.707+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.707+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.707+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.608+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:46:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.643+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.676+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.676+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.676+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.676+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.676+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.676+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:50:02.607+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.608+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.629+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.653+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.669+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.669+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.669+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.67+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.67+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:02.629+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.63+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.65+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.684+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.701+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.702+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.702+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.703+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.703+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.703+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:58:02.608+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.608+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.63+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.647+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.662+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.662+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.662+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.662+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.662+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.616+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:06:02.617+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.617+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.643+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.708+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.725+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.725+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.725+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.725+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.725+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.725+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:10:02.607+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.607+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.643+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.657+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.671+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.671+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.671+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.671+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.671+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.671+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.617+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:18:02.618+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.618+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.642+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.681+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.708+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.708+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.709+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.709+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.71+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.71+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.656+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:38:02.665+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.686+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.744+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.787+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.802+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.803+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.803+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.805+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.806+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.806+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.628+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.65+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.689+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.689+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.689+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.69+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.69+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.69+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.605+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:50:02.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.606+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.629+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.664+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.664+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.664+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.664+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.664+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:54:02.615+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.615+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.661+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.676+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.698+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.698+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.699+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.699+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.699+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.699+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:58:02.614+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.614+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.629+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.644+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.658+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.658+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.658+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.658+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.658+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.658+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:02:02.616+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.637+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.651+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.665+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.665+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.665+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.665+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.665+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.665+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.608+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:06:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.629+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.692+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.692+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.692+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.692+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.693+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.693+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:10:02.614+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6616, orderId=6379, flash=29173_BICS8-QLC_1024GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29173_BICS8#250195_29173_BICS8-QLC_1024GB, num=30, leftNum=10)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.614+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.637+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.651+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.667+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.667+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113611, orderId=6379, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113607, orderId=6379, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113609, orderId=6379, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.667+08:00","@version":"1","message":"执行预分配前共有10颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.667+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.668+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.668+08:00","@version":"1","message":"[6379] - [29173_BICS8-QLC_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6379","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:20:52.742+08:00","@version":"1","message":"execute:SATA_18-C0-4D-81-4B-2D_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SATA userName:Julie.zhu title:手动释放设备后电脑关机 planId:113603 mac:18-C0-4D-81-4B-2D","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2e0e54a680804209","spanId":"2e0e54a680804209","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:52.744+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0438[18-C0-4D-81-4B-2D]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2e0e54a680804209","spanId":"2e0e54a680804209","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:52.744+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0438]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2e0e54a680804209","spanId":"2e0e54a680804209","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:52.744+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=Julie.zhu, product=SATA, macList=[18-C0-4D-81-4B-2D])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2e0e54a680804209","spanId":"2e0e54a680804209","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:52.744+08:00","@version":"1","message":"[77fd82b7] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2e0e54a680804209","spanId":"2e0e54a680804209","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:52.888+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=Julie.zhu, product=SATA, macList=[18-C0-4D-81-4B-2D]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=***********, mac=18-C0-4D-81-4B-2D, pc_no=MB0438, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2e0e54a680804209","spanId":"2e0e54a680804209","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:52.889+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2e0e54a680804209","spanId":"2e0e54a680804209","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:56.485+08:00","@version":"1","message":"execute:SATA_18-C0-4D-81-A6-F6_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SATA userName:Julie.zhu title:手动释放设备后电脑关机 planId:113604 mac:18-C0-4D-81-A6-F6","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6744d12cd52629c9","spanId":"6744d12cd52629c9","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:56.488+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0429[18-C0-4D-81-A6-F6]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6744d12cd52629c9","spanId":"6744d12cd52629c9","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:56.489+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0429]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6744d12cd52629c9","spanId":"6744d12cd52629c9","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:56.489+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=Julie.zhu, product=SATA, macList=[18-C0-4D-81-A6-F6])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6744d12cd52629c9","spanId":"6744d12cd52629c9","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:56.491+08:00","@version":"1","message":"[1f1f16c2] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6744d12cd52629c9","spanId":"6744d12cd52629c9","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:56.622+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=Julie.zhu, product=SATA, macList=[18-C0-4D-81-A6-F6]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=18-C0-4D-81-A6-F6, pc_no=MB0429, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6744d12cd52629c9","spanId":"6744d12cd52629c9","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:56.623+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6744d12cd52629c9","spanId":"6744d12cd52629c9","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:58.642+08:00","@version":"1","message":"execute:SATA_18-C0-4D-80-15-FB_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SATA userName:Julie.zhu title:手动释放设备后电脑关机 planId:113604 mac:18-C0-4D-80-15-FB","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c553bd15e097e8a4","spanId":"c553bd15e097e8a4","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:58.647+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0430[18-C0-4D-80-15-FB]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c553bd15e097e8a4","spanId":"c553bd15e097e8a4","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:58.647+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0430]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c553bd15e097e8a4","spanId":"c553bd15e097e8a4","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:58.647+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=Julie.zhu, product=SATA, macList=[18-C0-4D-80-15-FB])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c553bd15e097e8a4","spanId":"c553bd15e097e8a4","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:58.648+08:00","@version":"1","message":"[387c4de7] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c553bd15e097e8a4","spanId":"c553bd15e097e8a4","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:58.778+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=Julie.zhu, product=SATA, macList=[18-C0-4D-80-15-FB]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=18-C0-4D-80-15-FB, pc_no=MB0430, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c553bd15e097e8a4","spanId":"c553bd15e097e8a4","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:20:58.778+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c553bd15e097e8a4","spanId":"c553bd15e097e8a4","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:01.378+08:00","@version":"1","message":"execute:SATA_B4-2E-99-E7-60-DA_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SATA userName:Julie.zhu title:手动释放设备后电脑关机 planId:113610 mac:B4-2E-99-E7-60-DA","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72447381c0abd034","spanId":"72447381c0abd034","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:01.379+08:00","@version":"1","message":"倒计时结束，即将释放设备SG00469[B4-2E-99-E7-60-DA]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72447381c0abd034","spanId":"72447381c0abd034","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:01.379+08:00","@version":"1","message":"需要关机的设备编号有: [SG00469]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72447381c0abd034","spanId":"72447381c0abd034","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:01.379+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=Julie.zhu, product=SATA, macList=[B4-2E-99-E7-60-DA])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72447381c0abd034","spanId":"72447381c0abd034","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:01.38+08:00","@version":"1","message":"[73a73b73] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72447381c0abd034","spanId":"72447381c0abd034","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:01.519+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=Julie.zhu, product=SATA, macList=[B4-2E-99-E7-60-DA]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=B4-2E-99-E7-60-DA, pc_no=SG00469, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72447381c0abd034","spanId":"72447381c0abd034","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:01.519+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72447381c0abd034","spanId":"72447381c0abd034","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:03.271+08:00","@version":"1","message":"execute:SATA_B4-2E-99-E7-59-0B_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SATA userName:Julie.zhu title:手动释放设备后电脑关机 planId:113610 mac:B4-2E-99-E7-59-0B","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9afc225b221b4560","spanId":"9afc225b221b4560","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:03.273+08:00","@version":"1","message":"倒计时结束，即将释放设备SG00747[B4-2E-99-E7-59-0B]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9afc225b221b4560","spanId":"9afc225b221b4560","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:03.273+08:00","@version":"1","message":"需要关机的设备编号有: [SG00747]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9afc225b221b4560","spanId":"9afc225b221b4560","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:03.273+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=Julie.zhu, product=SATA, macList=[B4-2E-99-E7-59-0B])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9afc225b221b4560","spanId":"9afc225b221b4560","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:03.273+08:00","@version":"1","message":"[41fbc723] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9afc225b221b4560","spanId":"9afc225b221b4560","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:03.403+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=Julie.zhu, product=SATA, macList=[B4-2E-99-E7-59-0B]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=B4-2E-99-E7-59-0B, pc_no=SG00747, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9afc225b221b4560","spanId":"9afc225b221b4560","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:03.403+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9afc225b221b4560","spanId":"9afc225b221b4560","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:14.694+08:00","@version":"1","message":"execute:SATA_18-C0-4D-81-A6-FA_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SATA userName:Julie.zhu title:Plan停止测试后电脑关机 planId:113608 mac:18-C0-4D-81-A6-FA","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b073f2747977baa","spanId":"2b073f2747977baa","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:14.695+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0435[18-C0-4D-81-A6-FA]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b073f2747977baa","spanId":"2b073f2747977baa","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:14.695+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0435]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b073f2747977baa","spanId":"2b073f2747977baa","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:14.695+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=Julie.zhu, product=SATA, macList=[18-C0-4D-81-A6-FA])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b073f2747977baa","spanId":"2b073f2747977baa","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:14.696+08:00","@version":"1","message":"[791c3332] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b073f2747977baa","spanId":"2b073f2747977baa","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:24.78+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=Julie.zhu, product=SATA, macList=[18-C0-4D-81-A6-FA]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=18-C0-4D-81-A6-FA, pc_no=MB0435, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b073f2747977baa","spanId":"2b073f2747977baa","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
{"@timestamp":"2025-07-23T12:21:24.78+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b073f2747977baa","spanId":"2b073f2747977baa","no":"6379","traceType":"TimedShutdownDeviceJob","flash":"29173_BICS8-QLC_1024GB"}
