FROM maven:3.6.3-jdk-11 AS maven
ENV TZ=Asia/Shanghai
RUN mkdir -p /root/.m2 \
    && mkdir /root/.m2/repository
# Copy maven settings, containing repository configurations
COPY ./src ./src
COPY ./pom.xml ./pom.xml
RUN --mount=type=cache,target=/root/.m2 \
    mvn clean -U install -Dmaven.test.skip=true  -Dhttps.proxyHost=********** -Dhttps.proxyPort=808


# Copy the jar file to the container
FROM openjdk:11-jre-slim
WORKDIR /project/workspace

COPY --from=maven target/system.work_order-1.0.0-SNAPSHOT.jar ./work_order.jar
CMD java -jar work_order.jar -XX:MaxDirectMemorySize=256m
