package com.yeestor.work_order.model.http.resp.order;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "查询plan 详细给前端返回的信息，包括flash批次、工单id、产品线等")
public class PlanDetailResp {
    @ApiModelProperty(value = "工单号")
    private String no;

    @ApiModelProperty(value = "工单id")
    private long orderId;

    @ApiModelProperty(value = "flash 批次名称")
    private String flash;

    @ApiModelProperty(value = "flash 批次的状态")
    private OrderFlashEntity.Status flashStatus;

    @ApiModelProperty(value = "产品线名称")
    private String product;

    @ApiModelProperty(value = "产品名称")
    private String subProduct;

    @ApiModelProperty(value = "简单的Plan信息列表")
    private PlanDetailVO planDetail;

    public static PlanDetailResp convertToSimple(WorkOrderEntity workOrder, PlanDetailVO planEntity) {
        PlanDetailResp planDetail = new PlanDetailResp();
        planDetail.setNo(workOrder.getNo());
        planDetail.setProduct(workOrder.getProduct());
        planDetail.setSubProduct(workOrder.getSubProduct());
        planDetail.setOrderId(workOrder.getId());
        planDetail.setFlash(planEntity.getFlash());
        planDetail.setPlanDetail(planEntity);

        return planDetail;
    }

}
