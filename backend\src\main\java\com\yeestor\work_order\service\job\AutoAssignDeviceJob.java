package com.yeestor.work_order.service.job;

import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.service.device.QueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.Arrays;

import static org.quartz.JobBuilder.newJob;

@Slf4j
@Component
@RequiredArgsConstructor
public class AutoAssignDeviceJob implements Job {

    public static final String GROUP_NAME = "AutoAssignDeviceJob";

    private static final String JOB_DATA_KEY_PRODUCT = "product";
    private static final String JOB_DATA_KEY_SUB_PRODUCT = "subProduct";


    private final WorkOrderRepository orderRepository;

    private final QueueService queueService ;


    public static JobDetail buildJobDetail(String p ,String sp){
        String jobName = "AutoAssignDevice_" + p + "_" + sp;
        return newJob(AutoAssignDeviceJob.class)
                .withIdentity(jobName, GROUP_NAME)
                .usingJobData(JOB_DATA_KEY_PRODUCT,p)
                .usingJobData(JOB_DATA_KEY_SUB_PRODUCT,sp)
                .build() ;
    }

    @Override
    public void execute(JobExecutionContext context) {
        MDC.put("context", QueueService.class.getSimpleName());
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        log.debug("execute:{}  map key:{} value:{} ", context.getJobDetail().getKey().getName(), Arrays.toString(dataMap.getKeys()),dataMap.values());

        String product = dataMap.getString(JOB_DATA_KEY_PRODUCT);
        String subProduct = dataMap.getString(JOB_DATA_KEY_SUB_PRODUCT);

        if (product == null || subProduct == null) {
            return;
        }

        int orderCount = orderRepository.countBySubProductAndStatusIn(
                subProduct,
                Arrays.asList(
                        WorkOrderEntity.Status.CONFIRMED_FLASH,
                        WorkOrderEntity.Status.TESTING
                )
        );
        if(orderCount == 0) {
            // TODO 如果没有工单，则可以考虑将此Job 暂停，等待有工单的时候，再重新恢复。
            return;
        }

        log.info("AutoAssignDeviceJob execute product:{} subProduct:{} orderCount:{}", product, subProduct, orderCount);

        queueService.assignDevicesByProduct(subProduct);
        MDC.remove("context");
    }

}
