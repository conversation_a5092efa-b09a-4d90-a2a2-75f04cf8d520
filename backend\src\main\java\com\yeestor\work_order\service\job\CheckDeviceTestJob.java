package com.yeestor.work_order.service.job;

import com.yeestor.dingtalk.api.DingTalkFeignClient;
import com.yeestor.dingtalk.utils.ConversationMessageBuilder;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.device.DeviceTestHistoryEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.repository.PlanDeviceRepository;
import com.yeestor.work_order.repository.device.DeviceTestHistoryRepository;
import com.yeestor.work_order.service.order.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static org.quartz.JobBuilder.newJob;

/**
 * 定时同步更新测试关系数据表，其中包含Plan、设备与测试人的关系
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class CheckDeviceTestJob implements Job {
    public static final String GROUP_NAME = "CheckDeviceTestJob";
    private final OrderPlanRepository planRepository;
    private final PlanDeviceRepository planDeviceRepository;
    private final OrderService orderService;
    private final DeviceTestHistoryRepository deviceTestHistoryRepository;
    // todo 等日常更新稳定后需要去掉通知
    private final DingTalkFeignClient dingTalkFeignClient;


    public static JobDetail buildJobDetail() {
        return newJob(CheckDeviceTestJob.class)
                .withIdentity(GROUP_NAME, GROUP_NAME)
                .build();
    }

    @Override
    public void execute(JobExecutionContext context) {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 60 * 60 * 24 * 7 * 1000;
        Date date = new Date(endTime * 1000);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = sdf.format(date);

        List<OrderPlanEntity> planList = planRepository.findAllByStatusAndBelongToNotNull(OrderPlanEntity.Status.COMPLETED).stream()
                .filter(p -> p.getCreatedAt() >= startTime && p.getCreatedAt() < endTime)
                .filter(p -> !(p.getBelongToPerson() == null || p.getBelongToPerson().isEmpty() || p.getBelongTo().isEmpty())).collect(Collectors.toList());

        planList.forEach(p -> {
            WorkOrderEntity workOrderEntity = orderService.findOrderOrElseThrow(p.getOrderId());
            String subProduct = workOrderEntity.getSubProduct();
            List<PlanDeviceEntity> deviceEntityList = planDeviceRepository.findAllByPlanIdAndStatusInAndMacNotNull(p.getId(), PlanDeviceEntity.FINISHED_STATUS_LIST);
            deviceEntityList.forEach(dev -> {
                if (deviceTestHistoryRepository.existsBySubProductAndPlanNameAndMacAndUserId(subProduct, p.getName(), dev.getMac(), p.getBelongTo())) {
                    DeviceTestHistoryEntity entity = deviceTestHistoryRepository.findBySubProductAndPlanNameAndMacAndUserId(subProduct, p.getName(), dev.getMac(), p.getBelongTo())
                            .orElseThrow(() -> new DataNotFoundException("未找到对应数据"));
                    entity.setUseNum(entity.getUseNum() + 1);
                    entity.setUpdatedAt(endTime);
                    deviceTestHistoryRepository.save(entity);
                } else {
                    DeviceTestHistoryEntity historyEntity = new DeviceTestHistoryEntity().toEntity(p, dev, workOrderEntity.getProduct(), subProduct);
                    deviceTestHistoryRepository.save(historyEntity);
                }
            });
        });
        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        List.of("16322748657306344"),
                        "测试人员与设备关系表更新-" + formattedDateTime,
                        "数据已更新！"
                )
        );
    }
}
