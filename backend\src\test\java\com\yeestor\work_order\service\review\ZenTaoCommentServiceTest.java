package com.yeestor.work_order.service.review;

import com.yeestor.work_order.entity.OrderBugEntity;
import com.yeestor.work_order.entity.OrderDetailEntity;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.review.ReviewItemEntity;
import com.yeestor.work_order.entity.review.ReviewResultEntity;
import com.yeestor.work_order.repository.OrderBugRepository;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;


@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
class ZenTaoCommentServiceTest {
    @Autowired
    ZenTaoCommentService zenTaoCommentService;

    @Autowired
    OrderService orderService;

    @Autowired
    FlashService flashService;

    @Autowired
    ReviewService reviewService;

    @Autowired
    OrderBugRepository orderBugRepository;

    @Test
    void check(){
        long orderId = 35L;
        String flashName = "test001_2GB";
        OrderDetailEntity orderDetailEntity = orderService.findOrderDetailOrElseThrow(orderId);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flashName);
        ReviewResultEntity reviewResultEntity = reviewService.findReviewInfoByOrderIdAndFlash(orderId, flashName);
        List<ReviewItemEntity>  list = reviewService.findReviewItem(orderId, flashName);
        List<OrderBugEntity> bindList = orderBugRepository.findAllByOrderIdAndFlash(orderId, flashName);

        zenTaoCommentService.saveReviewComment(
                orderDetailEntity,
                orderEntity,
                flashEntity,
                reviewResultEntity,
                list,
                bindList);

    }
}