package com.yeestor.work_order.model.http.resp.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@ApiModel(value = "OrderDocumentVO", description = "Flash 批次文档项")
public class OrderDocumentVO {

    @ApiModelProperty(value = "上传的手动plan的报告文件")
    List<PlanDocumentItemVO> planDocuments ;

    @ApiModelProperty(value = "上传的Flash批次报告以及合并的报告文件")
    List<FlashDocumentItemVO> flashDocuments ;
}
