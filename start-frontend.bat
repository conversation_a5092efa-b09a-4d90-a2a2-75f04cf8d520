@echo off
echo ========================================
echo eSee 前端服务启动脚本
echo ========================================

cd /d "%~dp0frontend"

echo 检查 Node.js 环境...
node -v
if %errorlevel% neq 0 (
    echo 错误：未找到 Node.js 环境，请确保已安装 Node.js 14.19.2+
    pause
    exit /b 1
)

echo.
echo 检查 npm 环境...
npm -v
if %errorlevel% neq 0 (
    echo 错误：未找到 npm 环境
    pause
    exit /b 1
)

echo.
echo 检查项目依赖...
if not exist "node_modules" (
    echo 正在安装项目依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 错误：依赖安装失败，尝试使用 legacy-peer-deps
        npm install --legacy-peer-deps
        if %errorlevel% neq 0 (
            echo 错误：依赖安装失败，请检查网络连接
            pause
            exit /b 1
        )
    )
) else (
    echo 依赖已存在，跳过安装
)

echo.
echo 正在启动前端服务...
echo 服务地址：http://localhost:8512
echo.
echo 按 Ctrl+C 停止服务
echo ========================================

set NODE_OPTIONS=--openssl-legacy-provider
npm run start

pause
