package com.yeestor.work_order.service.role;


import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.role.RoleEntity;
import com.yeestor.work_order.entity.role.RolePermissionEntity;
import com.yeestor.work_order.entity.role.RoleUserEntity;
import com.yeestor.work_order.exception.PermissionDeniedAccessException;
import com.yeestor.work_order.model.http.req.RoleParams;
import com.yeestor.work_order.model.http.resp.RoleVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.model.permission.PermissionGroup;
import com.yeestor.work_order.repository.role.RolePermissionRepository;
import com.yeestor.work_order.repository.role.RoleRepository;
import com.yeestor.work_order.repository.role.RoleUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 目前在service中暂时不考虑权限相关的问题。先放到Controller 中进行处理。
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RoleService {

    private final RoleRepository roleRepository;
    private final RoleUserRepository roleUserRepository ;
    private final RolePermissionRepository rolePermissionRepository ;

    /**
     * 创建角色
     * @param roleParams 角色相关的参数。
     */
    @Transactional
    public void createRole(RoleParams roleParams){
        log.info("roleParams: {}" ,roleParams);
        RoleEntity roleEntity = new RoleEntity();
        roleEntity.setDesc(Optional.ofNullable(roleParams.getDesc()).orElse(""));
        roleEntity.setName(roleParams.getName());

        RoleEntity result = roleRepository.save(roleEntity);

        // 处理用户与角色的关系
        roleParams.getUsers().forEach(u -> {
            RoleUserEntity userEntity = new RoleUserEntity();
            userEntity.setRoleId(result.getId());
            userEntity.setUserId(u);
            roleUserRepository.save(userEntity);
        });

        // 处理 角色与权限的关系
        roleParams.getProducts().forEach(
                p -> {
                    RolePermissionEntity rolePermissionEntity = new RolePermissionEntity();
                    String permission = roleParams.getPermissions().stream().map(s -> " "+s+" ").collect(Collectors.joining(";"));
                    rolePermissionEntity.setPermission(permission);
                    rolePermissionEntity.setRoleId(roleEntity.getId());
                    rolePermissionEntity.setProduct(p);
                    rolePermissionRepository.save(rolePermissionEntity) ;
                }
        );



    }
    // 查询所有的角色
    public List<RoleVO> findAllRole(String name, String desc){
        return roleRepository.findAllByNameContainingAndDescContaining(name, desc).stream().map(item -> {
//            log.info(item.toString());
            RoleVO o_role = new RoleVO();
            o_role.setId(item.getId());
            o_role.setName(item.getName());
            o_role.setDesc(item.getDesc());
            o_role.setCreatedBy(item.getCreatedBy());
            o_role.setUpdatedBy(item.getUpdatedBy());
            // 查询角色相关的用户
            List<String> users = roleUserRepository.findAllByRoleId(item.getId()).stream().map(RoleUserEntity::getUserId).collect(Collectors.toList());
            o_role.setUsers(users);
            List<RolePermissionEntity> permissionEntityList = rolePermissionRepository.findAllByRoleId(item.getId());
            // 查询角色相关的权限
            List<String> permissionList = permissionEntityList.stream()
                    .map(RolePermissionEntity::getPermission)
                    .map(s -> Arrays.stream(s.split(";")).map(String::trim).collect(Collectors.toList()))
                    .reduce(new ArrayList<>(), (a, b) -> {
                        a.addAll(b);
                        return a;
                    })
                    .stream()
                    .distinct()
                    .collect(Collectors.toList());
            o_role.setPermissions(permissionList);

            // 查询角色相关的产品
            List<String> products = permissionEntityList.stream().map(RolePermissionEntity::getProduct).collect(Collectors.toList());
            o_role.setProducts(products);

            return o_role;
        }).collect(Collectors.toList());
    }

    @Transactional
    public void updateRole(RoleParams roleParams, long roleId){
        log.debug("updateRole roleParams: {}" ,roleParams);
        roleRepository.findById(roleId).ifPresent(
                roleEntity -> {
                    roleEntity.setDesc(Optional.ofNullable(roleParams.getDesc()).orElse(""));
                    roleEntity.setName(roleParams.getName());
                    roleRepository.save(roleEntity);
                }
        );
        // 删除 角色相关的用户。
        roleUserRepository.deleteAllByRoleId(roleId);
        // 删除 角色相关的权限。
        rolePermissionRepository.deleteAllByRoleId(roleId);


        // 处理用户与角色的关系
        roleParams.getUsers().forEach(u -> {
            RoleUserEntity userEntity = new RoleUserEntity();
            userEntity.setRoleId(roleId);
            userEntity.setUserId(u);
            roleUserRepository.save(userEntity);
        });

        // 处理 角色与权限的关系
        roleParams.getProducts().forEach(
                p -> {
                    RolePermissionEntity rolePermissionEntity = new RolePermissionEntity();
                    String permission = roleParams.getPermissions().stream().map(s -> " "+s+" ").collect(Collectors.joining(";"));
                    rolePermissionEntity.setPermission(permission);
                    rolePermissionEntity.setRoleId(roleId);
                    rolePermissionEntity.setProduct(p);
                    rolePermissionRepository.save(rolePermissionEntity) ;
                }
        );

    }

    // 删除一个角色
    @Transactional
    public void deleteRole(long roleId){
        roleRepository.deleteById(roleId);
        roleUserRepository.deleteAllByRoleId(roleId);
        rolePermissionRepository.deleteAllByRoleId(roleId);
    }


    /**
     * 通过产品 和 指定的权限来获取用户列表
     * @param product   产品
     * @param permission 权限
     * @return 用户钉钉ID 列表
     */
    public List<String> getUserListByProductAndPermission(String product, Permission permission){
        String permissionStr = permission.name() ;
        // 查询包含这个权限的角色
        List<Long> roleId = rolePermissionRepository.findAllByProductAndPermissionContaining(product, permissionStr).stream().map(RolePermissionEntity::getRoleId).collect(Collectors.toList());
        // 查询这些角色的用户列表, 并且对用户钉钉ID进行去重
        return roleUserRepository.findAllByRoleIdIn(roleId).stream().map(RoleUserEntity::getUserId).distinct().collect(Collectors.toList());
    }

    /**
     * 匹配这个用户id 在这个产品线下 是否拥有此权限
     * @param userId        需要匹配的用户id
     * @param product       产品 ， 如果需要查询的是 角色权限组的话，这个参数可以为空
     * @param permissions    需要匹配的权限
     * @return true 拥有此权限， false 不拥有此权限
     */
    public boolean matchPermission(String userId,String product, Permission... permissions){
//        String permissionStr = permission.name() ;
        List<Long> userRoleIdList = roleUserRepository.findAllByUserId(userId).stream().map(RoleUserEntity::getRoleId).collect(Collectors.toList());
        List<RolePermissionEntity> permissionEntityList = rolePermissionRepository.findAllByRoleIdIn(userRoleIdList);

        if(product == null){
            // 与产品线无关的属性, 只匹配role相关的权限
            List<String> permissionList =  Arrays.stream(permissions).filter(p -> p.getGroup() == PermissionGroup.ROLE).map(Enum::name).collect(Collectors.toList());

            // 角色组 下的权限 实际上与产品线无关。所以只要判断是否有权限即可
            // 将permissionEntityList 中的permission按照; 分割， 并且去除空格,然后判断在permissionList中是否存在

            return permissionEntityList.stream()
                    .anyMatch(p -> Arrays.stream(
                            p.getPermission()
                                    .split(";")
                            )
                            .map(String::trim)
                            .anyMatch(permissionList::contains)
                    );
        }

        else {

            List<String> permissionList =  Arrays.stream(permissions).map(Enum::name).collect(Collectors.toList());
            // 与产品线有关的权限
            // 首先判断product ，然后再将permissionEntityList 中的permission按照; 分割， 并且去除空格,然后判断在permissionList中是否存在
            return permissionEntityList.stream()
                    .anyMatch(p -> p.getProduct().equals(product) && Arrays.stream(
                                    p.getPermission()
                                            .split(";")
                            )
                            .map(String::trim)
                            .anyMatch(permissionList::contains));
        }
    }


    public void checkPermission(
            OAuthUserDetail userDetail, String subProduct, String operate, Permission... permissions){
        if(userDetail == null){
            throw new PermissionDeniedAccessException("用户尚未登录!");
        }
        log.warn("用户{}发起 {}的请求！", userDetail.getUsername(), operate);
        if(!matchPermission(userDetail.getUid(), subProduct, permissions)){
            log.warn("用户{}没有权限操作{}！", userDetail.getUsername(), operate);
            throw new PermissionDeniedAccessException(
                    String.format("用户%s没有确认%s产品下%s的权限",userDetail.getUsername(),subProduct,operate)
            );
        }
    }

}
