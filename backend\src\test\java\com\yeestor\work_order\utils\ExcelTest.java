package com.yeestor.work_order.utils;

import com.yeestor.work_order.model.http.resp.review.ErrDiskInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

@Slf4j
class ExcelTest {


    @Test
    void readExcel() throws Exception{
        File file =  new File("C:\\Users\\<USER>\\Downloads\\ErrDisk.xlsx");
        Assertions.assertTrue(file.exists(), "文件不存在");
        FileInputStream fileInputStream = new FileInputStream(file);


        Workbook workbook =  WorkbookFactory.create(fileInputStream);
        assert workbook != null;

        DataFormatter dataFormatter = new DataFormatter();
        ErrDiskInfo errDiskInfo = new ErrDiskInfo() ;
        // 汇总统计
        Sheet sheet = workbook.getSheetAt(0);
        List<ErrDiskInfo.TestCountItem> statistics = new ArrayList<>() ;
        log.info("sheet.getLastRowNum() = {}", sheet.getLastRowNum());
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }
            Cell keyCell = row.getCell(0);
            String key = dataFormatter.formatCellValue(keyCell);
            String value = dataFormatter.formatCellValue(row.getCell(1));
            String address = Optional.ofNullable(keyCell.getHyperlink()).map(Hyperlink::getAddress).orElse(null);

            log.info("row {} key:{} value:{} address:{}", i, key, value, address);
            log.info("readExcel: address ={}", address);
            statistics.add(new ErrDiskInfo.TestCountItem(key,value, i - 1, address));
        }

        errDiskInfo.setStatistics(statistics);

        int sheetCount = workbook.getNumberOfSheets();
        for (int i = 1; i < sheetCount ; i++) {
            sheet = workbook.getSheetAt(i);
            String sheetName = sheet.getSheetName();
            List<ErrDiskInfo.ErrDiskLogItem> errDiskList = new ArrayList<>() ;
            for (int j = 1; j <= sheet.getLastRowNum(); j++) {
                Row row = sheet.getRow(j);
                ErrDiskInfo.ErrDiskLogItem errDiskLogItem = new ErrDiskInfo.ErrDiskLogItem() ;
                errDiskLogItem.setNo(dataFormatter.formatCellValue(row.getCell(0)));
                errDiskLogItem.setFailType(dataFormatter.formatCellValue(row.getCell(1)));
                errDiskLogItem.setPcNo(dataFormatter.formatCellValue(row.getCell(2)));
                errDiskLogItem.setLogPath(dataFormatter.formatCellValue(row.getCell(3)));

                if(StringUtils.hasText(errDiskLogItem.getNo()) && StringUtils.hasText(errDiskLogItem.getLogPath())){
                    errDiskList.add(errDiskLogItem);
                }
            }
            if(errDiskList.size() > 0){
                errDiskInfo.getLogSheets().put(sheetName,errDiskList);
            }
        }
        try {
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
