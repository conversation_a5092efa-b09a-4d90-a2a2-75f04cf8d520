package com.yeestor.work_order.service.plan;


import com.yeestor.work_order.entity.DeviceSampleEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.plan.PlanAssignInfoEntity;
import com.yeestor.work_order.repository.DeviceSampleRepository;
import com.yeestor.work_order.repository.PlanDeviceRepository;
import com.yeestor.work_order.repository.plan.PlanAssignInfoRepository;
import com.yeestor.work_order.service.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * PlanAssignService 是用来进行Plan中wo_plan_assign_info表（plan分配情况记录表）的更新
 * 以及预分配过程中对设备进行筛选等方案
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlanAssignService {

    private final PlanAssignInfoRepository planAssignInfoRepository;
    private final DeviceSampleRepository deviceSampleRepository ;
    private final PlanDeviceRepository planDeviceRepository;
    private final NotificationService notificationService;

    @Transactional
    public void preparePlanAssignInfo(OrderPlanEntity planEntity) {
        PlanAssignInfoEntity planAssignInfoEntity = PlanAssignInfoEntity.createAssignInfo(planEntity);
        planAssignInfoRepository.save(planAssignInfoEntity);
    }

    @Transactional
    public void preparePlanAssignInfo(List<OrderPlanEntity> planEntities) {
        List<PlanAssignInfoEntity> planAssignInfoEntities = PlanAssignInfoEntity.createAssignInfo(planEntities);
        planAssignInfoRepository.saveAll(planAssignInfoEntities);
    }

    public PlanAssignInfoEntity findPlanAssignInfo(long planId) {
        return planAssignInfoRepository.findByPlanId(planId).orElse(null);
    }

    public List<PlanAssignInfoEntity> findPlanAssignInfo(List<Long> planIds) {
        return planAssignInfoRepository.findAllByPlanIdIn(planIds);
    }

    /**
     * Plan分配状态改为排队中
     *
     * @param planId planId
     */
    public void updatePlanStatusToWaiting(long planId) {
        PlanAssignInfoEntity assignInfoEntity = findPlanAssignInfo(planId);
        assignInfoEntity.reset();
        planAssignInfoRepository.save(assignInfoEntity);
    }

    /**
     * 检查planEntityList中的Plan,如果这些Plan有 WAITING 状态的话,则将其分配状态更新至 QUEUE 中.
     *
     * @param planEntityList 需要检查的Plan列表
     */
    public void updatePlanStatusToQueue(List<OrderPlanEntity> planEntityList) {
        log.debug("updatePlanStatusToQueue: {} !", planEntityList);
        List<PlanAssignInfoEntity> assignInfoList = findPlanAssignInfo(planEntityList.stream().map(OrderPlanEntity::getId).collect(Collectors.toList()));
        if (assignInfoList.stream().anyMatch(a -> a.getStatus() == PlanAssignInfoEntity.Status.WAITING)) {
            // 如果有正在等待中的设备, 则将状态更新为队列中
            List<PlanAssignInfoEntity> waitingList = assignInfoList.stream().filter(PlanAssignInfoEntity::isWaiting).collect(Collectors.toList());
            waitingList.forEach(PlanAssignInfoEntity::startQueue);
            planAssignInfoRepository.saveAll(waitingList);
        }
    }

    public void terminatePlan(OrderPlanEntity planEntity) {
        log.info("terminatePlan: {} !", planEntity);
        planAssignInfoRepository.findByPlanId(planEntity.getId())
                .ifPresent(planAssignInfoEntity -> {
                    // 当且仅当 planAssignInfoEntity 已经开始排队了, 才更新结束时间,
                    if(planAssignInfoEntity.getStatus().ordinal() > PlanAssignInfoEntity.Status.QUEUE.ordinal() ){
                        return;
                    }

                    planAssignInfoEntity.cancelQueue();
                    if (planAssignInfoEntity.getStatus() == PlanAssignInfoEntity.Status.QUEUE && planAssignInfoEntity.getEndQueueTime() == null) {
                        planAssignInfoEntity.setEndQueueTime(System.currentTimeMillis());
                        planAssignInfoRepository.save(planAssignInfoEntity);
                    }

                });
    }

    public void pauseQueue(OrderPlanEntity planEntity) {
        log.info("pauseQueue: {} !", planEntity);
        planAssignInfoRepository.findByPlanId(planEntity.getId())
                .ifPresent(planAssignInfoEntity -> {
                    planAssignInfoEntity.reset();
                    planAssignInfoRepository.save(planAssignInfoEntity);
                });
    }

    public void resetPlanAssign(OrderPlanEntity planEntity) {
        log.info("resetPlanAssign: {} !", planEntity);
        planAssignInfoRepository.findByPlanId(planEntity.getId())
                .ifPresent(planAssignInfoEntity -> {
                    planAssignInfoEntity.reset();
                    planAssignInfoRepository.save(planAssignInfoEntity);
                });
    }

    @Transactional
    @Retryable(
            value = { OptimisticLockingFailureException.class },
            backoff = @Backoff(delay = 1000)
    )
    public void updateActualNum(long planId) {
        planAssignInfoRepository.findByPlanId(planId)
                .ifPresent(planAssignInfoEntity -> {

                    List<DeviceSampleEntity> deviceSampleList = deviceSampleRepository.findAllByPlanId(planId) ;
                    long deviceCount = deviceSampleList.stream().map(DeviceSampleEntity::getDeviceIp).distinct().count() ;
                    long distinctSampleCount = deviceSampleList.stream().map(DeviceSampleEntity::getNo).distinct().count();

                    planAssignInfoEntity.setUpdatedAt(System.currentTimeMillis());
                    planAssignInfoEntity.setActualDeviceNum((int) deviceCount);
                    planAssignInfoEntity.setActualSampleNum((int) distinctSampleCount);
                    planAssignInfoRepository.save(planAssignInfoEntity);
                });
    }

    /**
     * 当所有重试尝试都失败后，此方法将被调用以进行恢复处理。
     *
     * @param e      最终导致重试失败的 OptimisticLockingFailureException 异常。
     * @param planId 原始方法的 planId 参数。
     * @throws RuntimeException 重新抛出一个业务异常，通知调用方操作失败。
     */
    @Recover
    public void recoverUpdateActualNum(OptimisticLockingFailureException e, long planId) {
        log.error("Retry failed for updateActualNum: " + planId);

        // **此处添加你的恢复逻辑：**
        notificationService.sendErrorNotification("更新计划实际数量失败，已多次重试但未能成功, 请关注!!!! 错误详情: " + e.getMessage());
        throw new IllegalStateException("更新计划实际数量失败，已多次重试但未能成功。请稍后重试或联系支持人员。错误详情: " + e.getMessage(), e);
    }

    public void updateExceptedNum(OrderPlanEntity plan, List<PlanDeviceEntity> newDeviceEntityList, int deviceAvgSampleCapacity) {
        planAssignInfoRepository.findByPlanId(plan.getId())
                .ifPresent(planAssignInfoEntity -> {
                    int needSampleNum = planAssignInfoEntity.getNeedSampleNum() ;
                    if(planAssignInfoEntity.getNeedDeviceNum() == -1){
                        // 计算需要的设备数量 ,不能除尽的话,则需要多加一个设备
                        int needDeviceNum = needSampleNum / deviceAvgSampleCapacity ;
                        if(needSampleNum % deviceAvgSampleCapacity != 0){
                            needDeviceNum ++ ;
                        }
                        planAssignInfoEntity.setNeedDeviceNum(needDeviceNum);
                    }
                    long exceptedSampleNum = newDeviceEntityList.stream().map(PlanDeviceEntity::getTestNum).reduce(Integer::sum).orElse(0);

                    planAssignInfoEntity.setActualDeviceNum(planAssignInfoEntity.getActualDeviceNum() + newDeviceEntityList.size());
                    planAssignInfoEntity.setExceptedSampleNum((int) exceptedSampleNum);

                    // 如果exceptedSampleNum >= needSampleNum, 则认为已经完成分配了.
                    if(exceptedSampleNum >= needSampleNum){
                        planAssignInfoEntity.setStatus(PlanAssignInfoEntity.Status.COMPLETE);
                        planAssignInfoEntity.setEndQueueTime(System.currentTimeMillis());
                    }
                    planAssignInfoEntity.setUpdatedAt(System.currentTimeMillis());
                    log.info("plan:{} assign info update to ActualDeviceNum: {}, ExceptedSampleNum: {}",
                            plan.getName(),
                            planAssignInfoEntity.getActualDeviceNum(),
                            planAssignInfoEntity.getExceptedSampleNum()
                    );
                    planAssignInfoRepository.save(planAssignInfoEntity);
                });
    }

    /**
     * 查询某个Flash批次下对应测试负责人的Plan常用设备的机柜情况
     * @param orderId 工单id
     * @param flash flash批次
     * @param belongTo plan测试负责人
     * @return 机柜信息（已经行排序）
     */
    public List<Map.Entry<String, Long>> findTestRacksByOrderIdAndFlashAndBelong(long orderId, String flash, String belongTo){
        List<String> positionList = planDeviceRepository.findAllByOrderIdAndFlashAndBelongTo(orderId, flash, belongTo);
        Map<String, Long> rackList = positionList.stream()
                .filter(str -> str.contains("_"))
                .map(str -> str.substring(0, str.lastIndexOf("_")))
                .collect(Collectors.groupingBy(s -> s, Collectors.counting()));

        List<Map.Entry<String, Long>> sortedEntries = new ArrayList<>(rackList.entrySet());
        sortedEntries.sort(Map.Entry.comparingByValue(Comparator.reverseOrder()));
        log.info("sortedEntries: {}", sortedEntries);

        for (Map.Entry<String, Long> entry : sortedEntries) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
            if(entry.getValue() > 8){
                break;
            }
        }
        return sortedEntries;
    }

    /**
     * 是否进入队列
     *
     * @param planId plan id
     * @return WAITING 则返回true
     */
    public boolean isAssignQueue(long planId) {
        PlanAssignInfoEntity assignInfoEntity = findPlanAssignInfo(planId);
        if (PlanAssignInfoEntity.Status.WAITING == assignInfoEntity.getStatus()) {
            return false;
        }
        return true;
    }
}
