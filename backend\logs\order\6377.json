{"@timestamp":"2025-07-23T10:33:02.833+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:33:02.839+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.861+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.88+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.88+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.881+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.64+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:39:02.641+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.659+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.674+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.674+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.674+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:45:02.626+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.641+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.656+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.656+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.656+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.645+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:48:02.646+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.677+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.677+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.638+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:51:02.64+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.661+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.678+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.678+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.68+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:02.683+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.728+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.747+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.748+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.748+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.632+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:03:02.633+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.65+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.671+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.671+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:09:02.631+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.646+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.661+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.661+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.661+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:12:02.632+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.665+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:15:02.625+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.643+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.657+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.657+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.657+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.641+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:39:02.645+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.677+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.677+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.678+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:02.679+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.705+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.724+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.724+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.724+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:45:02.63+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.647+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.661+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.661+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.661+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.654+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:51:02.656+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.676+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.69+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.69+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.69+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.637+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:00:02.638+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.655+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.674+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.674+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.674+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:03:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.639+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.653+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.653+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.653+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.642+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:15:02.643+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.658+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.672+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.672+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.672+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.643+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:27:02.644+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.659+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.673+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.673+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.674+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.646+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:02.647+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.676+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.69+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.69+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.69+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:36:02.632+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.648+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.664+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.664+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:39:02.622+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.652+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.652+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:02.638+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.656+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.684+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.684+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.684+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.623+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:45:02.624+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.639+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.653+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.653+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.654+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.62+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:51:02.621+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.636+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.65+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.65+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.65+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.619+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:00:02.62+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.652+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.653+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.653+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.638+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:12:02.638+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.668+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:15:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.652+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.652+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:02.622+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.662+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.662+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.628+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:21:02.629+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.659+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.659+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.659+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:27:02.622+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.659+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.659+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.659+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.623+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:02.624+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.668+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:33:02.628+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.659+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.66+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:36:02.63+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.646+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.66+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.66+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.638+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:39:02.639+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.668+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:40:18.062+08:00","@version":"1","message":"Flash:XCCB-6285DA-9T25-A_64GB下的Plan22","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7036485f899d144c","spanId":"7036485f899d144c","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:40:18.062+08:00","@version":"1","message":"Flash:XCCB-6285DA-9T25-A_64GB下的Plan22使用设备:[************2, *************, *************, *************, ************, ************, ************, ************, *************, *************, ************, ************, ************, ************, ************, *************, ************, *************, *************, *************, ************, *************, ************, ************, *************, *************, ************, *************] -[8C-32-23-39-B8-90, 8C-32-23-39-B8-96, 8C-32-23-39-B8-CE, 8C-32-23-39-B9-28, 8C-32-23-39-B9-B9, 8C-32-23-39-BA-E5, 8C-32-23-39-BA-EF, 8C-32-23-39-BA-F0, 8C-32-23-39-BA-F6, 8C-32-23-39-BB-27, 8C-32-23-39-BB-6B, 8C-32-23-39-BB-CF, 8C-32-23-39-BB-F9, 8C-32-23-39-BC-10, 8C-32-23-39-BC-41, 8C-32-23-39-BC-55, 8C-32-23-39-BC-D4, 8C-32-23-39-BC-FF, 8C-32-23-39-BD-51, 8C-32-23-39-BD-72, 8C-32-23-39-BD-7B, A4-0C-66-13-D6-0F, A4-0C-66-14-27-2D, A4-0C-66-14-27-FC, A4-0C-66-14-28-3A, A4-0C-66-14-28-4C, A4-0C-66-14-2A-5B, A4-0C-66-14-2A-BC] 开始测试!","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7036485f899d144c","spanId":"7036485f899d144c","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:40:18.063+08:00","@version":"1","message":"startTestPlan with PlanTestParams(planName=Plan22, no=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, group=2, bAutoLoc=true, ipList=[************2, *************, *************, *************, ************, ************, ************, ************, *************, *************, ************, ************, ************, ************, ************, *************, ************, *************, *************, *************, ************, *************, ************, ************, *************, *************, ************, *************], macList=[8C-32-23-39-B8-90, 8C-32-23-39-B8-96, 8C-32-23-39-B8-CE, 8C-32-23-39-B9-28, 8C-32-23-39-B9-B9, 8C-32-23-39-BA-E5, 8C-32-23-39-BA-EF, 8C-32-23-39-BA-F0, 8C-32-23-39-BA-F6, 8C-32-23-39-BB-27, 8C-32-23-39-BB-6B, 8C-32-23-39-BB-CF, 8C-32-23-39-BB-F9, 8C-32-23-39-BC-10, 8C-32-23-39-BC-41, 8C-32-23-39-BC-55, 8C-32-23-39-BC-D4, 8C-32-23-39-BC-FF, 8C-32-23-39-BD-51, 8C-32-23-39-BD-72, 8C-32-23-39-BD-7B, A4-0C-66-13-D6-0F, A4-0C-66-14-27-2D, A4-0C-66-14-27-FC, A4-0C-66-14-28-3A, A4-0C-66-14-28-4C, A4-0C-66-14-2A-5B, A4-0C-66-14-2A-BC], product=SD, planPath=\\\\************\\软件工具\\03内部工具版本\\eSee\\GE\\SD\\Mars plan\\6285_V31.02.020.006.plan, marsPath=\\\\************\\软件工具\\03内部工具版本\\eSee\\GE\\SD\\Mars版本\\Mars_40.00.4.37.7z, mpPath=, username=tommie.zheng)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7036485f899d144c","spanId":"7036485f899d144c","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:40:18.063+08:00","@version":"1","message":"Product:SD testPerson:tommie.zheng order no:YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB plan name:Plan22  macList:[8C-32-23-39-B8-90, 8C-32-23-39-B8-96, 8C-32-23-39-B8-CE, 8C-32-23-39-B9-28, 8C-32-23-39-B9-B9, 8C-32-23-39-BA-E5, 8C-32-23-39-BA-EF, 8C-32-23-39-BA-F0, 8C-32-23-39-BA-F6, 8C-32-23-39-BB-27, 8C-32-23-39-BB-6B, 8C-32-23-39-BB-CF, 8C-32-23-39-BB-F9, 8C-32-23-39-BC-10, 8C-32-23-39-BC-41, 8C-32-23-39-BC-55, 8C-32-23-39-BC-D4, 8C-32-23-39-BC-FF, 8C-32-23-39-BD-51, 8C-32-23-39-BD-72, 8C-32-23-39-BD-7B, A4-0C-66-13-D6-0F, A4-0C-66-14-27-2D, A4-0C-66-14-27-FC, A4-0C-66-14-28-3A, A4-0C-66-14-28-4C, A4-0C-66-14-2A-5B, A4-0C-66-14-2A-BC]","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7036485f899d144c","spanId":"7036485f899d144c","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:40:18.063+08:00","@version":"1","message":"[6a2a978c] HTTP POST http://ereport.yeestor.com/wo/plan/test","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7036485f899d144c","spanId":"7036485f899d144c","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:40:18.22+08:00","@version":"1","message":"startTestPlan with YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB's Plan22 got data:HandleResp(code=0, data=PlanTestRespModel(successLst=[PlanTestRespModel.DeviceTestResult(ip=************2, no=GE-PC-SDhigh-64, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-65, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-67, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-59, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-57, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-63, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-66, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-69, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-70, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDHIGH-51, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-62, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDHIGH-55, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-52, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-71, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-49, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-54, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-56, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-60, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-61, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-50, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-68, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-48, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-42, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDHIGH-41, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDHIGH-46, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-44, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-47, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-43, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7036485f899d144c","spanId":"7036485f899d144c","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:40:18.225+08:00","@version":"1","message":"Plan22 启动测试成功, resp: PlanTestRespModel(successLst=[PlanTestRespModel.DeviceTestResult(ip=************2, no=GE-PC-SDhigh-64, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-65, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-67, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-59, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-57, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-63, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-66, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-69, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-70, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDHIGH-51, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-62, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDHIGH-55, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-52, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-71, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-49, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-54, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-56, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-60, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-61, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-50, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-68, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-48, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-42, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDHIGH-41, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDHIGH-46, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-44, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-47, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-43, reason=)], failLst=[])","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7036485f899d144c","spanId":"7036485f899d144c","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:42:02.639+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:02.64+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.666+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.68+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.681+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.681+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.595+08:00","@version":"1","message":"execute:AutoAssignDevice_GE_U3  map key:[subProduct, product] value:org.quartz.utils.DirtyFlagMap$DirtyFlagCollection@26e405d1 ","logger_name":"com.yeestor.work_order.service.job.AutoAssignDeviceJob","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47e66f9f33870835","spanId":"47e66f9f33870835","context":"QueueService","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:45:02.599+08:00","@version":"1","message":"AutoAssignDeviceJob execute product:GE subProduct:U3 orderCount:5","logger_name":"com.yeestor.work_order.service.job.AutoAssignDeviceJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47e66f9f33870835","spanId":"47e66f9f33870835","context":"QueueService","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:45:02.603+08:00","@version":"1","message":"[U3] start check order's plan . find 1 orders","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47e66f9f33870835","spanId":"47e66f9f33870835","context":"QueueService","no":"6377","traceType":"启动测试","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T13:45:02.647+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:45:02.648+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.678+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.678+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:48:02.632+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.648+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.662+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.662+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.642+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:51:02.643+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.659+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.672+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.672+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.672+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:00:02.622+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.637+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.651+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.651+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.651+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:03:02.637+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.669+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:02.631+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.66+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.66+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:09:02.629+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.644+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.658+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.658+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.659+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.632+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:15:02.633+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.663+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.663+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113561, orderId=6377, name=Plan46, status=QUEUE, priority=50), OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.663+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6377","traceType":"分配设备"}
