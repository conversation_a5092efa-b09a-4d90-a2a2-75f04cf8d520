{"@timestamp":"2025-07-23T15:09:03.086+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:09:03.095+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.102+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.103+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.654+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:12:02.656+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.659+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.659+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.655+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:15:02.656+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.658+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.658+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.68+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.685+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.691+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.691+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.663+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:24:02.665+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.671+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.786+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:51:02.824+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.833+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.834+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.649+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:57:02.65+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.654+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.07+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:00:05.072+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.077+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.078+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:03:02.631+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.635+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.632+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:09:02.635+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.638+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:12:02.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.624+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.646+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6367","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:24:02.654+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6367","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6367","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.661+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6367","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:30:02.605+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.609+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.609+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.645+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:33:02.648+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.652+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:36:02.611+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.614+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.614+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:39:02.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.614+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.615+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:48:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.626+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.626+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.608+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6367","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:51:02.609+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6603, orderId=6367, flash=YS-6285EN-AHJBX2-D_128GB, orderFlashNo=YS6285##MP#########16690#AHGJ0B-84CD_250412_YS-6285EN-AHJBX2-D_128GB, num=120, leftNum=76)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.612+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6367","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.612+08:00","@version":"1","message":"[6367] - [YS-6285EN-AHJBX2-D_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6367","traceType":"分配设备"}
