///<reference types="cypress" />
// 需要操作的工单id
import { orderId, flashList, addPlanNameList } from './case';

context('with flash confirm', () => {
  describe('confirm', () => {
    beforeEach(() => {
      cy.viewport(1920, 1080);
      cy.visit('http://172.18.11.245:8512/work_order/GE/list');
    });

    it('待测试', () => {
      window.sessionStorage.clear();
      // 点击进入对应工单页面中
      cy.get('.ant-table-row').wait(1000).get(`#confirm-${orderId}`).contains('flash确认').click();
      // 输入数据
      flashList.forEach((item: any) => {
        initFlash(item.name, item.num, item.volume, item.userId, item.testUserId);
      });

      // flash修改与删除
      editFlash();

      // 选择RMS与Mars信息
      setRMSInfo();

      setPlan(flashList[0].name, 0, flashList[0].userId, addPlanNameList);
      setPlanList(flashList[1].name, 1, flashList[1].testUserId);
      cy.get('#finish-confirm').contains('完成确认').click().wait(2000);
      flashList.forEach((item: any) => {
        cy.get(`#flash-tab-${item.name}_${item.volume}`).click().wait(1500);
      });
    });
  });
});

const initFlash = function (
  name: string,
  num: string,
  volume: string,
  userId: string,
  testUserId: string,
) {
  // 点击确认flash按钮
  cy.get('.add_flash_btn').contains('添加 Flash').click();

  cy.get('#add_flash_name')
    .type(name)
    .get('#add_flash_num')
    .type(num)
    .get('#add_flash_volume')
    .click()
    .get(`#add_flash_volume_${volume}`)
    .click()
    .get('#add_flash_test_user')
    .click()
    .get(`#testUser${userId}`)
    .click()
    .get('#add_flash_test_persons')
    .click()
    .get(`#testPersons${userId}`)
    .click()
    .get(`#testPersons${testUserId}`)
    .click()
    .get('#add_flash_test_persons')
    .blur()
    .wait(200) // 注
    .get('#add_flash_autoLoc_true')
    .check();

  // 提交数据
  cy.get('#add_flash_submit')
    // .wait(1000)
    .contains('确 定')
    .click();
};

const editFlash = function () {
  initFlash('test01', '12', '16GB', '16322748657306344', '0653290119972081');
  // 修改数据
  cy.get(`.ant-table-row:last-child .change16322748657306344`)
    .click()
    .get('#add_flash_name')
    .clear()
    .type('changeName01')
    .wait(500)
    .get('#add_flash_submit')
    .contains('确 定')
    .click();

  cy.wait(1000);
  //删除数据
  cy.get('.ant-table-row:last-child .delete16322748657306344').click();
};

// 选择单个plan
const setPlan = function (
  flashname: string,
  index: number,
  userId: string,
  addPlanNameList: string[],
) {
  cy.get(`#confirm-flash .ant-tabs-nav-list > .ant-tabs-tab:nth-child(${index + 1})`).click();
  // 点击增加plan按钮
  cy.get(`button#kanban_add_plan_${flashname}_${userId}`).click();
  // 选择plan类型
  cy.get('#type-accept').click();

  // 增加plan
  addPlanNameList.forEach((planName: string) => {
    cy.get(`#add-plan-name-${planName}`).click();
  });

  cy.get('#flash-confirm-add-plan-submit').click();
};

// 选择Plan合集
const setPlanList = function (flashname: string, index: number, userId: string) {
  cy.get(`#confirm-flash .ant-tabs-nav-list > .ant-tabs-tab:nth-child(${index + 1})`).click();
  // 点击增加plan按钮
  cy.get(`button#kanban_add_plan_${flashname}_${userId}`).click();
  // 选择plan类型
  cy.get('#type-accept').click();
  // 点击合集选项
  cy.get(`div#flash_add_plan_collect`)
    .click()
    .get('div#接收测试合集')
    .click()
    .wait(1000)
    .get('#flash-confirm-add-plan-submit')
    .click();
};

// 修改rms信息
const setRMSInfo = function () {
  cy.get('#mars_path')
    .click()
    .get('.ant-select-item:nth-child(1).mar-path-select')
    .click()

    .get('#mars_plan_path')
    .click()
    .get('.ant-select-item:nth-child(1).mar-plan-path')
    .click()

    .get('#rms_group')
    .click()
    .get('.ant-select-item:nth-child(1).rms-group-name')
    .click();
};
