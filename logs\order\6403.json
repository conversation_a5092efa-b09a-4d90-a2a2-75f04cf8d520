{"@timestamp":"2025-07-24T16:30:02.887+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.025+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6640, orderId=6403, flash=Mars_54_Plan_18o1_64GB, orderFlashNo=YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB, num=50, leftNum=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.128+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113869, orderId=6403, name=Plan26, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.204+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113871, orderId=6403, name=Plan59, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.243+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113872, orderId=6403, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.287+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113867, orderId=6403, name=Plan21, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.337+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113870, orderId=6403, name=Plan36, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.362+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113868, orderId=6403, name=Plan25, status=QUEUE, priority=60) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.362+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113869, orderId=6403, name=Plan26, status=QUEUE, priority=90), OrderPlanEntity(id=113871, orderId=6403, name=Plan59, status=QUEUE, priority=85), OrderPlanEntity(id=113872, orderId=6403, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113867, orderId=6403, name=Plan21, status=QUEUE, priority=80), OrderPlanEntity(id=113870, orderId=6403, name=Plan36, status=QUEUE, priority=75), OrderPlanEntity(id=113868, orderId=6403, name=Plan25, status=QUEUE, priority=60)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.363+08:00","@version":"1","message":" getValidPlanList sumList: [10, 18, 26, 34, 44] leftFlashNum:50 index:5 plans:[OrderPlanEntity(id=113869, orderId=6403, name=Plan26, status=QUEUE, priority=90), OrderPlanEntity(id=113871, orderId=6403, name=Plan59, status=QUEUE, priority=85), OrderPlanEntity(id=113872, orderId=6403, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113867, orderId=6403, name=Plan21, status=QUEUE, priority=80), OrderPlanEntity(id=113870, orderId=6403, name=Plan36, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.363+08:00","@version":"1","message":"[6403] - [Mars_54_Plan_18o1_64GB] find 5 plans: [OrderPlanEntity(id=113869, orderId=6403, name=Plan26, status=QUEUE, priority=90), OrderPlanEntity(id=113871, orderId=6403, name=Plan59, status=QUEUE, priority=85), OrderPlanEntity(id=113872, orderId=6403, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113867, orderId=6403, name=Plan21, status=QUEUE, priority=80), OrderPlanEntity(id=113870, orderId=6403, name=Plan36, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.364+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113869, orderId=6403, name=Plan26, status=QUEUE, priority=90), OrderPlanEntity(id=113871, orderId=6403, name=Plan59, status=QUEUE, priority=85), OrderPlanEntity(id=113872, orderId=6403, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113867, orderId=6403, name=Plan21, status=QUEUE, priority=80), OrderPlanEntity(id=113870, orderId=6403, name=Plan36, status=QUEUE, priority=75)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.839+08:00","@version":"1","message":"[41f86a60] HTTP GET http://ereport.yeestor.com/wo/device/list?p=eMMC","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:04.585+08:00","@version":"1","message":"getAllDeviceList with eMMC got data DeviceListResp(code=0, data=[{ EM4_3_21,************,F0-2F-74-34-05-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_10,***********,F0-2F-74-33-FD-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_18,************,F0-2F-74-34-05-23,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_09,***********3,E0-D5-5E-9F-65-AF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_13,************,F0-2F-74-34-01-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_14,*************,F0-2F-74-34-12-A3,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_2_04,*************,DC-4A-3E-40-CB-8F,[{SQA310=8}] }, { EM4_1_16,*************,F0-2F-74-33-F1-88,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_07,************,F0-2F-74-33-FA-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_27,************0,B4-2E-99-6B-88-C1,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_2_14,************2,F0-2F-74-33-FB-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_28,************,B4-2E-99-6A-84-91,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_1_04,************,F0-2F-74-33-FB-60,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_11,************2,F0-2F-74-33-FD-46,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_24,************,F0-2F-74-34-05-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_28,************2,F0-2F-74-33-FB-5C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_14,************5,B4-2E-99-59-FA-75,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_27,*************,F0-2F-74-33-FB-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_30,************,F0-2F-74-33-FC-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_22,***********6,F0-2F-74-33-FD-8A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_25,*************,B4-2E-99-5A-E1-15,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_2_03,***********01,DC-4A-3E-40-CB-E5,[{SQA310=8}] }, { EM4_2_13,*************,F0-2F-74-33-FC-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_21,*************,F0-2F-74-33-FB-FC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_22,************,B4-2E-99-5A-E1-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_20,*************,F0-2F-74-34-05-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_24,***********15,B4-2E-99-59-F8-00,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_02,*************,B4-2E-99-59-F9-D0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_13,***********20,B4-2E-99-59-F8-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_25,*************,F0-2F-74-33-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_19,*************,F0-2F-74-33-FF-02,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_29,*************,F0-2F-74-33-FE-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_13,*************,F0-2F-74-33-FB-E4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_08,*************,F0-2F-74-34-12-98,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_23,************,F0-2F-74-33-FC-1B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_05,************,F0-2F-74-33-FE-A6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_26,************,B4-2E-99-5A-E1-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_30,************,F0-2F-74-33-FC-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_12,************3,F0-2F-74-33-FE-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_03,************,F0-2F-74-33-FB-87,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_29,***********,F0-2F-74-32-5A-36,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_12,***********9,F0-2F-74-33-FB-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_09,*************,B4-2E-99-59-F8-09,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_11,************,F0-2F-74-33-FB-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_23,************,F0-2F-74-33-FB-BA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_24,************,F0-2F-74-33-FB-64,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_04,************,F0-2F-74-33-FC-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_17,*************,F0-2F-74-33-FB-E2,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_11,************,B4-2E-99-59-FA-24,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_26,************4,F0-2F-74-33-FC-81,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_13,************,E0-D5-5E-9F-64-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_12,************,E0-D5-5E-9D-11-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_05,***********43,DC-4A-3E-40-CB-FB,[{XU4=12}] }, { EM4_2_30,************,F0-2F-74-32-6D-F3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_07,************,F0-2F-74-33-FD-EC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_07,************,F0-2F-74-33-FB-30,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_29,************,B4-2E-99-5A-DF-40,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_29,************,F0-2F-74-34-0A-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_2_01,************,DC-4A-3E-40-CB-DF,[{SQA310=8}] }, { EM4_5_30,************,B4-2E-99-5A-DF-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_11,************,F0-2F-74-33-FC-5E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_13,************,F0-2F-74-33-FB-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_2_02,************1,DC-4A-3E-40-CB-E4,[{SQA310=8}] }, { EM4_5_15,***********44,B4-2E-99-5A-E1-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_12,*************,DC-4A-3E-40-D7-0B,[{XU4=12}] }, { EM4_1_09,*************,F0-2F-74-33-FB-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_29,*************,F0-2F-74-33-FB-32,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_1_12,*************,F0-2F-74-30-63-25,[{高温=4}, {Mars=4}] }, { EM4_2_16,*************,F0-2F-74-33-FB-91,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_24,*************,F0-2F-74-34-03-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_27,*************,B4-2E-99-59-F8-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_08,*************,F0-2F-74-33-FC-1C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_27,*************,F0-2F-74-33-FE-13,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_12,*************,B4-2E-99-5A-E1-12,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_21,*************,F0-2F-74-34-12-DE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_08,*************,F0-2F-74-33-FD-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_01,*************,F0-2F-74-33-FC-1E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_20,*************,F0-2F-74-34-12-A0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_21,***********,B4-2E-99-59-FA-18,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_05,***********,F0-2F-74-32-5F-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_01,*************,F0-2F-74-34-02-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_28,*************,F0-2F-74-33-FE-0B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_06,*************,F0-2F-74-33-FD-3E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_05,*************,F0-2F-74-33-FD-FA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_16,***********82,B4-2E-99-5A-E7-D7,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_04,*************,F0-2F-74-33-FA-C3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_17,*************,F0-2F-74-33-FB-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_02,*************,F0-2F-74-33-FE-01,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_18,*************,F0-2F-74-33-FB-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_10,*************,70-85-C2-82-BE-80,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_25,*************,F0-2F-74-33-FE-E8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_02,*************,F0-2F-74-33-FB-62,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_10,*************,F0-2F-74-4E-A5-9D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_30,*************,F0-2F-74-34-12-CC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_25,*************,F0-2F-74-34-02-8E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_28,*************,F0-2F-74-33-FC-79,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_14,*************,F0-2F-74-34-05-CE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_16,*************,F0-2F-74-33-FB-5D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_08,***********05,D0-50-99-69-EE-F6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_28,***********08,B4-2E-99-5A-E1-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_09,***********11,F0-2F-74-33-FB-ED,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_05,*************,B4-2E-99-59-FA-37,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_11,***********23,E0-D5-5E-9D-84-D5,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_08,***********10,DC-4A-3E-40-CB-C8,[{XU4=12}] }, { EM4_1_10,*************,F0-2F-74-33-FE-2C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_15,*************,F0-2F-74-34-04-5F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_28,***********30,F0-2F-74-33-FC-2F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_09,***********31,F0-2F-74-33-FB-EB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_15,*************,F0-2F-74-34-00-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_22,*************,F0-2F-74-33-FB-39,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_17,***********38,F0-2F-74-33-FB-6E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_26,*************,F0-2F-74-33-FB-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_12,************0,F0-2F-74-33-FC-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_06,*************,F0-2F-74-33-FB-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_16,************,F0-2F-74-33-FC-10,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_03,************,F0-2F-74-30-5D-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_23,*************,B4-2E-99-5A-EC-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM6_2_08,*************,B4-2E-99-EA-27-00,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_12,************,E0-D5-5E-E7-21-4E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_13,************,A4-0C-66-04-CC-32,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_07,************,B4-2E-99-5A-E2-D2,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_08,172.18.66.77,F0-2F-74-33-FB-85,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_04,172.18.66.78,F0-2F-74-33-FC-17,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_04,172.18.66.218,B4-2E-99-EA-B4-2E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_16,172.18.67.72,E0-D5-5E-9D-12-FB,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_10,172.18.67.225,F0-2F-74-33-FB-EE,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_08,172.18.66.86,F0-2F-74-F4-86-81,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_14,172.18.66.88,B4-2E-99-5A-DF-48,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_15,172.18.66.89,F0-2F-74-F4-86-65,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_12,172.18.66.90,70-85-C2-82-01-11,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_06,172.18.66.91,F0-2F-74-33-FD-0E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_06,172.18.67.128,B4-2E-99-59-FB-8F,[{高温=4}, {Mars=4}] }, { AE_HL_03,172.18.66.27,7C-10-C9-A0-1A-5A,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_01,172.18.66.222,50-EB-F6-26-FC-EC,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_02,172.18.66.33,50-EB-F6-26-FB-B0,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_06,172.18.66.35,B4-2E-99-EA-B5-1B,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_11,172.18.66.38,B4-2E-99-EA-B4-84,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_12,172.18.66.39,B4-2E-99-EA-1E-2E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_07,172.18.67.170,B4-2E-99-EA-B5-10,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_03,172.18.66.42,B4-2E-99-EA-1E-39,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_05,172.18.66.233,B4-2E-99-EA-AF-E1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_04,172.18.66.45,B4-2E-99-EA-BF-0F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_08,172.18.66.47,B4-2E-99-EA-BF-1A,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_13,172.18.66.49,B4-2E-99-EA-1E-1D,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_2_01,172.18.66.54,E0-D5-5E-5C-83-AF,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_11,***********8,50-EB-F6-27-01-1A,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_15,172.18.67.120,F0-2F-74-4E-9B-A5,[{高温=4}, {Mars=4}] }, { AE_HL_16,172.18.66.97,50-EB-F6-26-F9-4C,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_13,172.18.66.51,F0-2F-74-4E-9C-55,[{高温=4}, {Mars=4}] }, { AE_HL_04,172.18.66.81,50-EB-F6-26-FB-BF,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_05,172.18.67.239,04-42-1A-24-D7-65,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_06,***********5,7C-10-C9-20-50-5F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_07,***********6,50-EB-F6-27-01-BC,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_08,************,7C-10-C9-20-59-00,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_13,************,50-EB-F6-27-03-F5,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_14,************,04-42-1A-24-D8-4D,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_09,***********7,04-42-1A-24-D7-34,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_5_03,************,B4-2E-99-59-F9-CF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_06,*************,B4-2E-99-59-F8-05,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_08,************,E0-D5-5E-C4-92-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_04,************,B4-2E-99-59-F9-D3,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_2_02,***********0,E0-D5-5E-9D-C0-53,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_6_15,************8,B4-2E-99-6B-88-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_16,************,B4-2E-99-6A-84-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_17,************3,B4-2E-99-6A-87-90,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_18,***********68,B4-2E-99-6A-87-DB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_19,************,B4-2E-99-6A-88-69,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_20,************,B4-2E-99-6A-84-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_21,************5,B4-2E-99-6B-8C-1D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_22,***********42,B4-2E-99-6B-88-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_23,************,B4-2E-99-6A-84-45,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_24,************,B4-2E-99-6B-88-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_05,***********,F0-2F-74-4E-A5-F5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_06,*************,F0-2F-74-4E-9C-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_25.1,************,00-E0-70-E2-15-E6,[{性能=1}] }, { EM4_6_26.1,***********03,00-E0-70-E2-18-25,[{性能=1}] }, { EM7_1_08,*************,B4-2E-99-29-3F-DB,[{高温=4}, {Mars=4}] }, { EM6_2_03,************,B4-2E-99-EA-B5-C9,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_05,************,B4-2E-99-EA-1E-44,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_07,************,B4-2E-99-59-FD-4E,[{高温=4}, {Mars=4}] }, { AE_HL_10,************,7C-10-C9-A0-29-EF,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_12,************,04-42-1A-A8-3C-D1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_5_01,************,B4-2E-99-59-F9-D1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_2_05,***********,E0-D5-5E-9F-6A-BF,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_3_12,***********19,F0-2F-74-4E-A5-C6,[{TCPIP掉电=4}, {Mars=4}] }, { EM6_4_09,************,F0-2F-74-4E-A5-35,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_15,*************,50-EB-F6-26-FD-78,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_05,*************,40-8D-5C-17-4A-AB,[{高温=4}, {Mars=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_7_29,************7,D8-5E-D3-5E-75-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM6_4_11,*************,D8-5E-D3-5F-80-4F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_01,*************,B4-2E-99-5A-E1-25,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_02,*************,B4-2E-99-59-F8-2A,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_06,************,F0-2F-74-4E-9C-16,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_10,*************,70-85-C2-82-C0-01,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_12,*************,D8-5E-D3-5E-79-EC,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_13,*************,D8-5E-D3-5F-80-90,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_07,*************,E0-D5-5E-9D-86-CB,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_04,*************,B4-2E-99-5A-E1-24,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_03,*************,E0-D5-5E-9D-11-B7,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_05,*************,E0-D5-5E-9D-C0-60,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM8_1_04,*************,D8-5E-D3-5F-7F-38,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_01,*************,D8-5E-D3-5E-77-2B,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_02,************,D8-5E-D3-5F-84-32,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_06,*************,D8-5E-D3-5F-7C-7A,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_07,*************,D8-5E-D3-5E-7A-12,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_08,*************,D8-5E-D3-56-AF-7F,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_10,************,D8-5E-D3-5E-79-62,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_12,************,D8-5E-D3-5E-6F-30,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_15,************,D8-5E-D3-5F-83-C1,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_16,************,D8-5E-D3-5F-7F-78,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_28,*************,D8-5E-D3-56-AF-7D,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_27,*************,D8-5E-D3-5E-74-F1,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_26,*************,D8-5E-D3-5F-80-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_25,************,D8-5E-D3-5F-7F-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_17,************,D8-5E-D3-5F-7C-EE,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_22,*************,D8-5E-D3-5F-83-17,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_23,*************,D8-5E-D3-5E-76-53,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_24,*************,D8-5E-D3-5F-83-3E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_01,*************,00-E0-4C-3D-AC-E9,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_02,***********06,40-8D-5C-19-54-28,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_03,***********98,FC-AA-14-E2-14-4D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_04,************,F0-2F-74-F4-84-54,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_07,*************,D8-5E-D3-5F-7B-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_08,***********78,D8-5E-D3-5E-75-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_09,***********21,D8-5E-D3-5F-7C-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_10,*************,D8-5E-D3-5F-84-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_11,*************,D8-5E-D3-5F-7E-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_12,************,D8-5E-D3-5E-74-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_13,************5,D8-5E-D3-5B-D0-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_14,*************,D8-5E-D3-5E-74-FB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_15,************7,D8-5E-D3-56-AF-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_16,************,D8-5E-D3-5E-76-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_17,***********00,D8-5E-D3-5E-79-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_18,************,D8-5E-D3-5F-7C-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_19,************2,D8-5E-D3-5F-7C-4D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_20,*************,D8-5E-D3-5F-7B-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_21,************,D8-5E-D3-5F-7D-B8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_22,************5,D8-5E-D3-5F-7F-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_23,*************,D8-5E-D3-5E-79-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_24,************,D8-5E-D3-5E-74-E3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_25,************,D8-5E-D3-5F-83-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_26,*************,D8-5E-D3-5F-7B-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_27,***********91,D8-5E-D3-5B-D0-DF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_28,***********88,D8-5E-D3-5B-CF-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_30,*************,D8-5E-D3-5E-75-D1,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_05,***********40,40-8D-5C-19-54-1D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_5_01,*************,A4-0C-66-17-72-64,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_02,*************,A4-0C-66-15-A1-49,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_03,*************,A4-0C-66-15-9C-E1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_04,*************,A4-0C-66-15-A0-A0,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_5_05,*************,A4-0C-66-15-9C-D0,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_06,*************,A4-0C-66-15-9C-F3,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_12,*************,A4-0C-66-15-9C-E8,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_07,172.18.66.112,A4-0C-66-15-9C-E5,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_11,*************,A4-0C-66-17-72-B3,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_10,*************,A4-0C-66-15-9C-F1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_09,*************,A4-0C-66-15-9E-B1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_08,*************,A4-0C-66-15-9C-F0,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }], msg=测试机信息获取成功！, workPcLst=[{ EM4_2_20,************0,F0-2F-74-33-FC-11,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_1_16,*************,F0-2F-74-34-01-28,[{高温=4}, {Mars=4}] }, { EM4_2_22,***********08,F0-2F-74-34-00-98,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_10,***********51,DC-4A-3E-40-D7-18,[{XU4=12}] }, { EM4_5_07,************,B4-2E-99-59-FA-43,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_11,***********53,DC-4A-3E-40-CB-EB,[{XU4=12}] }, { EM5_1_03,************3,DC-4A-3E-44-99-9B,[{XU4=12}] }, { EM5_1_07,************,DC-4A-3E-40-CB-EA,[{XU4=12}] }, { EM4_2_19,*************,F0-2F-74-33-FC-EB,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_01,*************,DC-4A-3E-40-C8-19,[{XU4=12}] }, { EM5_1_09,***********50,DC-4A-3E-40-CB-D8,[{XU4=12}] }, { EM4_5_10,***********14,B4-2E-99-59-F7-FF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_20,***********,B4-2E-99-59-FA-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_04,************,DC-4A-3E-40-D7-1F,[{XU4=12}] }, { EM4_5_18,*************,B4-2E-99-59-FB-BF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_21,*************,F0-2F-74-33-FB-D7,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_17,***********12,B4-2E-99-59-F8-03,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_02,***********74,DC-4A-3E-40-CB-C2,[{XU4=12}] }, { EM4_5_19,***********6,B4-2E-99-59-FA-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM6_1_16,************,B4-2E-99-EA-B3-D0,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_15,************,B4-2E-99-EA-22-87,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_14,*************,B4-2E-99-EA-BB-B3,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM5_1_06,***********45,DC-4A-3E-41-48-30,[{XU4=12}] }, { EM4_2_18,************,F0-2F-74-4E-A6-B6,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_30,************,D8-5E-D3-5B-D0-D0,[{Mars=4}] }, { EM8_1_29,************,D8-5E-D3-5E-79-3E,[{Mars=4}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.109+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6403 flash: Mars_54_Plan_18o1_64GB available device: [{ EM4_3_21,************,F0-2F-74-34-05-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_10,***********,F0-2F-74-33-FD-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_18,************,F0-2F-74-34-05-23,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_09,***********3,E0-D5-5E-9F-65-AF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_13,************,F0-2F-74-34-01-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_14,*************,F0-2F-74-34-12-A3,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_2_04,*************,DC-4A-3E-40-CB-8F,[{SQA310=8}] }, { EM4_1_16,*************,F0-2F-74-33-F1-88,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_07,************,F0-2F-74-33-FA-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_27,************0,B4-2E-99-6B-88-C1,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_2_14,************2,F0-2F-74-33-FB-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_28,************,B4-2E-99-6A-84-91,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_1_04,************,F0-2F-74-33-FB-60,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_11,************2,F0-2F-74-33-FD-46,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_24,************,F0-2F-74-34-05-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_28,************2,F0-2F-74-33-FB-5C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_14,************5,B4-2E-99-59-FA-75,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_27,*************,F0-2F-74-33-FB-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_30,************,F0-2F-74-33-FC-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_22,***********6,F0-2F-74-33-FD-8A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_25,*************,B4-2E-99-5A-E1-15,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_2_03,***********01,DC-4A-3E-40-CB-E5,[{SQA310=8}] }, { EM4_2_13,*************,F0-2F-74-33-FC-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_21,*************,F0-2F-74-33-FB-FC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_22,************,B4-2E-99-5A-E1-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_20,*************,F0-2F-74-34-05-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_24,***********15,B4-2E-99-59-F8-00,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_02,*************,B4-2E-99-59-F9-D0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_13,***********20,B4-2E-99-59-F8-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_25,*************,F0-2F-74-33-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_19,*************,F0-2F-74-33-FF-02,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_29,*************,F0-2F-74-33-FE-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_13,*************,F0-2F-74-33-FB-E4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_08,*************,F0-2F-74-34-12-98,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_23,************,F0-2F-74-33-FC-1B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_05,************,F0-2F-74-33-FE-A6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_26,************,B4-2E-99-5A-E1-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_30,************,F0-2F-74-33-FC-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_12,************3,F0-2F-74-33-FE-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_03,************,F0-2F-74-33-FB-87,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_29,***********,F0-2F-74-32-5A-36,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_12,***********9,F0-2F-74-33-FB-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_09,*************,B4-2E-99-59-F8-09,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_11,************,F0-2F-74-33-FB-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_23,************,F0-2F-74-33-FB-BA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_24,************,F0-2F-74-33-FB-64,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_04,************,F0-2F-74-33-FC-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_17,*************,F0-2F-74-33-FB-E2,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_11,************,B4-2E-99-59-FA-24,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_26,************4,F0-2F-74-33-FC-81,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_13,************,E0-D5-5E-9F-64-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_12,************,E0-D5-5E-9D-11-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_05,***********43,DC-4A-3E-40-CB-FB,[{XU4=12}] }, { EM4_2_30,************,F0-2F-74-32-6D-F3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_07,************,F0-2F-74-33-FD-EC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_07,************,F0-2F-74-33-FB-30,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_29,************,B4-2E-99-5A-DF-40,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_29,************,F0-2F-74-34-0A-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_2_01,************,DC-4A-3E-40-CB-DF,[{SQA310=8}] }, { EM4_5_30,************,B4-2E-99-5A-DF-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_11,************,F0-2F-74-33-FC-5E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_13,************,F0-2F-74-33-FB-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_2_02,************1,DC-4A-3E-40-CB-E4,[{SQA310=8}] }, { EM4_5_15,***********44,B4-2E-99-5A-E1-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_12,*************,DC-4A-3E-40-D7-0B,[{XU4=12}] }, { EM4_1_09,*************,F0-2F-74-33-FB-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_29,*************,F0-2F-74-33-FB-32,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_1_12,*************,F0-2F-74-30-63-25,[{高温=4}, {Mars=4}] }, { EM4_2_16,*************,F0-2F-74-33-FB-91,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_24,*************,F0-2F-74-34-03-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_27,*************,B4-2E-99-59-F8-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_08,*************,F0-2F-74-33-FC-1C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_27,*************,F0-2F-74-33-FE-13,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_12,*************,B4-2E-99-5A-E1-12,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_21,*************,F0-2F-74-34-12-DE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_08,*************,F0-2F-74-33-FD-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_01,*************,F0-2F-74-33-FC-1E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_20,*************,F0-2F-74-34-12-A0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_21,***********,B4-2E-99-59-FA-18,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_05,***********,F0-2F-74-32-5F-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_01,*************,F0-2F-74-34-02-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_28,*************,F0-2F-74-33-FE-0B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_06,*************,F0-2F-74-33-FD-3E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_05,*************,F0-2F-74-33-FD-FA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_16,***********82,B4-2E-99-5A-E7-D7,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_04,*************,F0-2F-74-33-FA-C3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_17,*************,F0-2F-74-33-FB-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_02,*************,F0-2F-74-33-FE-01,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_18,*************,F0-2F-74-33-FB-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_10,*************,70-85-C2-82-BE-80,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_25,*************,F0-2F-74-33-FE-E8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_02,*************,F0-2F-74-33-FB-62,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_10,*************,F0-2F-74-4E-A5-9D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_30,*************,F0-2F-74-34-12-CC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_25,*************,F0-2F-74-34-02-8E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_28,*************,F0-2F-74-33-FC-79,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_14,*************,F0-2F-74-34-05-CE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_16,*************,F0-2F-74-33-FB-5D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_08,***********05,D0-50-99-69-EE-F6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_28,***********08,B4-2E-99-5A-E1-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_09,***********11,F0-2F-74-33-FB-ED,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_05,*************,B4-2E-99-59-FA-37,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_11,***********23,E0-D5-5E-9D-84-D5,[{TCPIP掉电=4}, {Mars=4}] }, { EM5_1_08,***********10,DC-4A-3E-40-CB-C8,[{XU4=12}] }, { EM4_1_10,*************,F0-2F-74-33-FE-2C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_15,*************,F0-2F-74-34-04-5F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_28,***********30,F0-2F-74-33-FC-2F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_09,***********31,F0-2F-74-33-FB-EB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_15,*************,F0-2F-74-34-00-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_22,*************,F0-2F-74-33-FB-39,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_17,***********38,F0-2F-74-33-FB-6E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_26,*************,F0-2F-74-33-FB-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_12,************0,F0-2F-74-33-FC-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_06,*************,F0-2F-74-33-FB-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_16,************,F0-2F-74-33-FC-10,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_03,************,F0-2F-74-30-5D-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_23,*************,B4-2E-99-5A-EC-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM6_2_08,*************,B4-2E-99-EA-27-00,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_12,************,E0-D5-5E-E7-21-4E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_13,************,A4-0C-66-04-CC-32,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_07,************,B4-2E-99-5A-E2-D2,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_08,172.18.66.77,F0-2F-74-33-FB-85,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_04,172.18.66.78,F0-2F-74-33-FC-17,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_04,172.18.66.218,B4-2E-99-EA-B4-2E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_16,172.18.67.72,E0-D5-5E-9D-12-FB,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_10,172.18.67.225,F0-2F-74-33-FB-EE,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_08,172.18.66.86,F0-2F-74-F4-86-81,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_14,172.18.66.88,B4-2E-99-5A-DF-48,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_15,172.18.66.89,F0-2F-74-F4-86-65,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_12,172.18.66.90,70-85-C2-82-01-11,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_3_06,172.18.66.91,F0-2F-74-33-FD-0E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_06,172.18.67.128,B4-2E-99-59-FB-8F,[{高温=4}, {Mars=4}] }, { AE_HL_03,172.18.66.27,7C-10-C9-A0-1A-5A,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_01,172.18.66.222,50-EB-F6-26-FC-EC,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_02,172.18.66.33,50-EB-F6-26-FB-B0,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_06,172.18.66.35,B4-2E-99-EA-B5-1B,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_11,172.18.66.38,B4-2E-99-EA-B4-84,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_12,172.18.66.39,B4-2E-99-EA-1E-2E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_07,172.18.67.170,B4-2E-99-EA-B5-10,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_03,172.18.66.42,B4-2E-99-EA-1E-39,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_05,172.18.66.233,B4-2E-99-EA-AF-E1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_04,172.18.66.45,B4-2E-99-EA-BF-0F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_08,172.18.66.47,B4-2E-99-EA-BF-1A,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_13,172.18.66.49,B4-2E-99-EA-1E-1D,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_2_01,172.18.66.54,E0-D5-5E-5C-83-AF,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_11,***********8,50-EB-F6-27-01-1A,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_15,172.18.67.120,F0-2F-74-4E-9B-A5,[{高温=4}, {Mars=4}] }, { AE_HL_16,172.18.66.97,50-EB-F6-26-F9-4C,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_13,172.18.66.51,F0-2F-74-4E-9C-55,[{高温=4}, {Mars=4}] }, { AE_HL_04,172.18.66.81,50-EB-F6-26-FB-BF,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_05,172.18.67.239,04-42-1A-24-D7-65,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_06,***********5,7C-10-C9-20-50-5F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_07,***********6,50-EB-F6-27-01-BC,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_08,************,7C-10-C9-20-59-00,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_13,************,50-EB-F6-27-03-F5,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_14,************,04-42-1A-24-D8-4D,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_09,***********7,04-42-1A-24-D7-34,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_5_03,************,B4-2E-99-59-F9-CF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_06,*************,B4-2E-99-59-F8-05,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_08,************,E0-D5-5E-C4-92-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_04,************,B4-2E-99-59-F9-D3,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_2_02,***********0,E0-D5-5E-9D-C0-53,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_6_15,************8,B4-2E-99-6B-88-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_16,************,B4-2E-99-6A-84-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_17,************3,B4-2E-99-6A-87-90,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_18,***********68,B4-2E-99-6A-87-DB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_19,************,B4-2E-99-6A-88-69,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_20,************,B4-2E-99-6A-84-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_21,************5,B4-2E-99-6B-8C-1D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_22,***********42,B4-2E-99-6B-88-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_23,************,B4-2E-99-6A-84-45,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_24,************,B4-2E-99-6B-88-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_05,***********,F0-2F-74-4E-A5-F5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_06,*************,F0-2F-74-4E-9C-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_25.1,************,00-E0-70-E2-15-E6,[{性能=1}] }, { EM4_6_26.1,***********03,00-E0-70-E2-18-25,[{性能=1}] }, { EM7_1_08,*************,B4-2E-99-29-3F-DB,[{高温=4}, {Mars=4}] }, { EM6_2_03,************,B4-2E-99-EA-B5-C9,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_2_05,************,B4-2E-99-EA-1E-44,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_07,************,B4-2E-99-59-FD-4E,[{高温=4}, {Mars=4}] }, { AE_HL_10,************,7C-10-C9-A0-29-EF,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_12,************,04-42-1A-A8-3C-D1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_5_01,************,B4-2E-99-59-F9-D1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_2_05,***********,E0-D5-5E-9F-6A-BF,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_3_12,***********19,F0-2F-74-4E-A5-C6,[{TCPIP掉电=4}, {Mars=4}] }, { EM6_4_09,************,F0-2F-74-4E-A5-35,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { AE_HL_15,*************,50-EB-F6-26-FD-78,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM7_1_05,*************,40-8D-5C-17-4A-AB,[{高温=4}, {Mars=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM4_7_29,************7,D8-5E-D3-5E-75-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM6_4_11,*************,D8-5E-D3-5F-80-4F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_01,*************,B4-2E-99-5A-E1-25,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_02,*************,B4-2E-99-59-F8-2A,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_06,************,F0-2F-74-4E-9C-16,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_10,*************,70-85-C2-82-C0-01,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_12,*************,D8-5E-D3-5E-79-EC,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_13,*************,D8-5E-D3-5F-80-90,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_07,*************,E0-D5-5E-9D-86-CB,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_04,*************,B4-2E-99-5A-E1-24,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_03,*************,E0-D5-5E-9D-11-B7,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_4_05,*************,E0-D5-5E-9D-C0-60,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM8_1_04,*************,D8-5E-D3-5F-7F-38,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_01,*************,D8-5E-D3-5E-77-2B,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_02,************,D8-5E-D3-5F-84-32,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_06,*************,D8-5E-D3-5F-7C-7A,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_07,*************,D8-5E-D3-5E-7A-12,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_08,*************,D8-5E-D3-56-AF-7F,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_10,************,D8-5E-D3-5E-79-62,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_12,************,D8-5E-D3-5E-6F-30,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_15,************,D8-5E-D3-5F-83-C1,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_16,************,D8-5E-D3-5F-7F-78,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_28,*************,D8-5E-D3-56-AF-7D,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_27,*************,D8-5E-D3-5E-74-F1,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_26,*************,D8-5E-D3-5F-80-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_25,************,D8-5E-D3-5F-7F-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_17,************,D8-5E-D3-5F-7C-EE,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_22,*************,D8-5E-D3-5F-83-17,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_23,*************,D8-5E-D3-5E-76-53,[{TCPIP掉电=4}, {Mars=4}] }, { EM8_1_24,*************,D8-5E-D3-5F-83-3E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_01,*************,00-E0-4C-3D-AC-E9,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_02,***********06,40-8D-5C-19-54-28,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_03,***********98,FC-AA-14-E2-14-4D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_04,************,F0-2F-74-F4-84-54,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_07,*************,D8-5E-D3-5F-7B-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_08,***********78,D8-5E-D3-5E-75-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_09,***********21,D8-5E-D3-5F-7C-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_10,*************,D8-5E-D3-5F-84-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_11,*************,D8-5E-D3-5F-7E-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_12,************,D8-5E-D3-5E-74-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_13,************5,D8-5E-D3-5B-D0-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_14,*************,D8-5E-D3-5E-74-FB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_15,************7,D8-5E-D3-56-AF-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_16,************,D8-5E-D3-5E-76-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_17,***********00,D8-5E-D3-5E-79-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_18,************,D8-5E-D3-5F-7C-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_19,************2,D8-5E-D3-5F-7C-4D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_20,*************,D8-5E-D3-5F-7B-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_21,************,D8-5E-D3-5F-7D-B8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_22,************5,D8-5E-D3-5F-7F-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_23,*************,D8-5E-D3-5E-79-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_24,************,D8-5E-D3-5E-74-E3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_25,************,D8-5E-D3-5F-83-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_26,*************,D8-5E-D3-5F-7B-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_27,***********91,D8-5E-D3-5B-D0-DF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_28,***********88,D8-5E-D3-5B-CF-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_30,*************,D8-5E-D3-5E-75-D1,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_05,***********40,40-8D-5C-19-54-1D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_5_01,*************,A4-0C-66-17-72-64,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_02,*************,A4-0C-66-15-A1-49,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_03,*************,A4-0C-66-15-9C-E1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_04,*************,A4-0C-66-15-A0-A0,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_5_05,*************,A4-0C-66-15-9C-D0,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_06,*************,A4-0C-66-15-9C-F3,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_12,*************,A4-0C-66-15-9C-E8,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_07,172.18.66.112,A4-0C-66-15-9C-E5,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_11,*************,A4-0C-66-17-72-B3,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_10,*************,A4-0C-66-15-9C-F1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_09,*************,A4-0C-66-15-9E-B1,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }, { EM6_5_08,*************,A4-0C-66-15-9C-F0,[{高温=4}, {低温=4}, {温循=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.119+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.123+08:00","@version":"1","message":"[6403] Plan26 need Num: 10 to test. Mars_54_Plan_18o1_64GB left num:50 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.124+08:00","@version":"1","message":"subProduct eMMC flashName Mars_54_Plan_18o1_64GB plan [Plan26] attrs Mars belongTo perry.chen is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.166+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM4_1_04, EM4_1_05, EM4_1_06, EM4_1_07, EM4_1_08, EM4_1_09, EM4_1_10, EM4_1_11, EM4_1_12, EM4_1_13, EM4_1_14, EM4_1_15, EM4_1_16, EM4_1_17, EM4_1_18, EM4_1_19, EM4_1_20, EM4_1_21, EM4_1_22, EM4_1_23, EM4_1_24, EM4_1_25, EM4_1_26, EM4_1_27, EM4_1_28, EM4_1_29, EM4_1_30, EM4_2_01, EM4_2_02, EM4_2_03, EM4_2_04, EM4_2_05, EM4_2_06, EM4_2_07, EM4_2_08, EM4_2_09, EM4_2_10, EM4_2_11, EM4_2_12, EM4_2_13, EM4_2_14, EM4_2_15, EM4_2_16, EM4_2_17, EM4_2_23, EM4_2_24, EM4_2_25, EM4_2_26, EM4_2_27, EM4_2_28, EM4_2_29, EM4_2_30, EM4_3_01, EM4_3_07, EM4_3_08, EM4_3_09, EM4_3_10, EM4_3_11, EM4_3_12, EM4_3_13, EM4_3_14, EM4_3_16, EM4_3_17, EM4_3_18, EM4_3_21, EM4_3_24, EM4_3_28, EM4_3_29, EM4_3_30, EM4_4_02, EM4_4_03, EM4_4_04, EM4_4_05, EM4_4_10, EM4_4_11, EM4_4_12, EM4_4_13, EM4_4_16, EM4_4_20, EM4_4_21, EM4_4_22, EM4_4_25, EM4_4_28, EM4_4_29, EM4_4_30, EM4_5_01, EM4_5_02, EM4_5_03, EM4_5_04, EM4_5_05, EM4_5_06, EM4_5_08, EM4_5_09, EM4_5_11, EM4_5_12, EM4_5_13, EM4_5_14, EM4_5_15, EM4_5_16, EM4_5_21, EM4_5_22, EM4_5_23, EM4_5_24, EM4_5_25, EM4_5_26, EM4_5_27, EM4_5_28, EM4_5_29, EM4_5_30, EM4_6_01, EM4_6_02, EM4_6_03, EM4_6_04, EM4_6_05, EM4_6_06, EM4_6_08, EM4_6_09, EM4_6_10, EM4_6_11, EM4_6_12, EM4_6_13, EM4_6_15, EM4_6_16, EM4_6_17, EM4_6_18, EM4_6_19, EM4_6_20, EM4_6_21, EM4_6_22, EM4_6_23, EM4_6_24, EM4_6_27, EM4_6_28, EM4_7_01, EM4_7_02, EM4_7_03, EM4_7_04, EM4_7_05, EM4_7_07, EM4_7_08, EM4_7_09, EM4_7_10, EM4_7_11, EM4_7_12, EM4_7_13, EM4_7_14, EM4_7_15, EM4_7_16, EM4_7_17, EM4_7_18, EM4_7_19, EM4_7_20, EM4_7_21, EM4_7_22, EM4_7_23, EM4_7_24, EM4_7_25, EM4_7_26, EM4_7_27, EM4_7_28, EM4_7_29, EM4_7_30, EM8_1_01, EM8_1_02, EM8_1_04, EM8_1_06, EM8_1_07, EM8_1_08, EM8_1_10, EM8_1_12, EM8_1_15, EM8_1_16, EM8_1_17, EM8_1_22, EM8_1_23, EM8_1_24, EM8_1_25, EM8_1_26, EM8_1_27, EM8_1_28]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.23+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<com.yeestor.admin.model.UserDTO>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.299+08:00","@version":"1","message":"nameList: [深圳办公室, 实验室TE180]","logger_name":"com.yeestor.work_order.utils.DingTalkUtils","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.3+08:00","@version":"1","message":"location: SZ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.321+08:00","@version":"1","message":"sortedEntries: []","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.339+08:00","@version":"1","message":"Plan26是该测试负责人第一个在该批次下分配plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.345+08:00","@version":"1","message":"Plan26在历史设备中找到满足条件的设备：[F0-2F-74-33-FE-A6, F0-2F-74-33-FA-C3, F0-2F-74-33-FB-87]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.348+08:00","@version":"1","message":"[6403] plan:Plan26 use 3 pc: [{ EM4_4_03,************,F0-2F-74-33-FB-87,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_04,*************,F0-2F-74-33-FA-C3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_05,************,F0-2F-74-33-FE-A6,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.348+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ EM4_4_03,************,F0-2F-74-33-FB-87,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_04,*************,F0-2F-74-33-FA-C3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_05,************,F0-2F-74-33-FE-A6,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.349+08:00","@version":"1","message":"[6403] Plan59 need Num: 8 to test. Mars_54_Plan_18o1_64GB left num:40 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.349+08:00","@version":"1","message":"subProduct eMMC flashName Mars_54_Plan_18o1_64GB plan [Plan59] attrs Mars;TCPIP掉电 belongTo perry.chen is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.355+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM4_1_04, EM4_1_05, EM4_1_06, EM4_1_07, EM4_1_08, EM4_1_09, EM4_1_10, EM4_1_11, EM4_1_12, EM4_1_13, EM4_1_14, EM4_1_15, EM4_1_16, EM4_1_17, EM4_1_18, EM4_1_19, EM4_1_20, EM4_1_21, EM4_1_22, EM4_1_23, EM4_1_24, EM4_1_25, EM4_1_26, EM4_1_27, EM4_1_28, EM4_1_29, EM4_1_30, EM4_2_01, EM4_2_02, EM4_2_03, EM4_2_04, EM4_2_05, EM4_2_06, EM4_2_07, EM4_2_08, EM4_2_09, EM4_2_10, EM4_2_11, EM4_2_12, EM4_2_13, EM4_2_14, EM4_2_15, EM4_2_16, EM4_2_17, EM4_2_23, EM4_2_24, EM4_2_25, EM4_2_26, EM4_2_27, EM4_2_28, EM4_2_29, EM4_2_30, EM4_3_01, EM4_3_07, EM4_3_08, EM4_3_09, EM4_3_10, EM4_3_11, EM4_3_12, EM4_3_13, EM4_3_14, EM4_3_16, EM4_3_17, EM4_3_18, EM4_3_21, EM4_3_24, EM4_3_28, EM4_3_29, EM4_3_30, EM4_4_02, EM4_4_10, EM4_4_11, EM4_4_12, EM4_4_13, EM4_4_16, EM4_4_20, EM4_4_21, EM4_4_22, EM4_4_25, EM4_4_28, EM4_4_29, EM4_4_30, EM4_5_01, EM4_5_02, EM4_5_03, EM4_5_04, EM4_5_05, EM4_5_06, EM4_5_08, EM4_5_09, EM4_5_11, EM4_5_12, EM4_5_13, EM4_5_14, EM4_5_15, EM4_5_16, EM4_5_21, EM4_5_22, EM4_5_23, EM4_5_24, EM4_5_25, EM4_5_26, EM4_5_27, EM4_5_28, EM4_5_29, EM4_5_30, EM4_6_01, EM4_6_02, EM4_6_03, EM4_6_04, EM4_6_05, EM4_6_06, EM4_6_08, EM4_6_09, EM4_6_10, EM4_6_11, EM4_6_12, EM4_6_13, EM4_6_15, EM4_6_16, EM4_6_17, EM4_6_18, EM4_6_19, EM4_6_20, EM4_6_21, EM4_6_22, EM4_6_23, EM4_6_24, EM4_6_27, EM4_6_28, EM4_7_01, EM4_7_02, EM4_7_03, EM4_7_04, EM4_7_05, EM4_7_07, EM4_7_08, EM4_7_09, EM4_7_10, EM4_7_11, EM4_7_12, EM4_7_13, EM4_7_14, EM4_7_15, EM4_7_16, EM4_7_17, EM4_7_18, EM4_7_19, EM4_7_20, EM4_7_21, EM4_7_22, EM4_7_23, EM4_7_24, EM4_7_25, EM4_7_26, EM4_7_27, EM4_7_28, EM4_7_29, EM4_7_30, EM8_1_01, EM8_1_02, EM8_1_04, EM8_1_06, EM8_1_07, EM8_1_08, EM8_1_10, EM8_1_12, EM8_1_15, EM8_1_16, EM8_1_17, EM8_1_22, EM8_1_23, EM8_1_24, EM8_1_25, EM8_1_26, EM8_1_27, EM8_1_28]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.361+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<com.yeestor.admin.model.UserDTO>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.363+08:00","@version":"1","message":"nameList: [深圳办公室, 实验室TE180]","logger_name":"com.yeestor.work_order.utils.DingTalkUtils","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.364+08:00","@version":"1","message":"location: SZ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.366+08:00","@version":"1","message":"sortedEntries: []","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.374+08:00","@version":"1","message":"Plan59是该测试负责人第一个在该批次下分配plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.374+08:00","@version":"1","message":"正在考虑使用非连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.375+08:00","@version":"1","message":"[6403] getPlanRunnableDevices [Plan59] testNum: 8 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.375+08:00","@version":"1","message":"[6403] runnableDeviceList: [{ EM4_1_04,************,F0-2F-74-33-FB-60,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_05,*************,F0-2F-74-33-FD-FA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_06,*************,F0-2F-74-33-FD-3E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_07,************,F0-2F-74-33-FB-30,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_08,*************,F0-2F-74-34-12-98,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_09,*************,F0-2F-74-33-FB-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_10,*************,F0-2F-74-33-FE-2C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_11,************,F0-2F-74-33-FB-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_12,***********9,F0-2F-74-33-FB-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_13,************,F0-2F-74-34-01-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_14,*************,F0-2F-74-34-05-CE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_15,*************,F0-2F-74-34-00-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_16,*************,F0-2F-74-33-F1-88,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_17,*************,F0-2F-74-33-FB-E2,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_18,************,F0-2F-74-34-05-23,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_19,*************,F0-2F-74-33-FF-02,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_20,*************,F0-2F-74-34-12-A0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_21,*************,F0-2F-74-34-12-DE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_22,*************,F0-2F-74-33-FB-39,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_23,************,F0-2F-74-33-FC-1B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_24,*************,F0-2F-74-34-03-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_25,*************,F0-2F-74-34-02-8E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_26,************4,F0-2F-74-33-FC-81,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_27,*************,F0-2F-74-33-FB-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_28,*************,F0-2F-74-33-FC-79,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_29,*************,F0-2F-74-33-FB-32,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_30,************,F0-2F-74-33-FC-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_01,*************,F0-2F-74-33-FC-1E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_02,*************,F0-2F-74-33-FE-01,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_03,************,F0-2F-74-30-5D-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_04,************,F0-2F-74-33-FC-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_05,***********,F0-2F-74-32-5F-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_06,*************,F0-2F-74-33-FB-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_07,************,F0-2F-74-33-FA-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_08,*************,F0-2F-74-33-FD-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_09,***********11,F0-2F-74-33-FB-ED,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_10,*************,F0-2F-74-4E-A5-9D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_11,************,F0-2F-74-33-FC-5E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_12,************0,F0-2F-74-33-FC-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_13,*************,F0-2F-74-33-FC-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_14,************2,F0-2F-74-33-FB-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_15,*************,F0-2F-74-34-04-5F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_16,*************,F0-2F-74-33-FB-91,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_17,***********38,F0-2F-74-33-FB-6E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_23,************,F0-2F-74-33-FB-BA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_24,************,F0-2F-74-33-FB-64,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_25,*************,F0-2F-74-33-FE-E8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_26,*************,F0-2F-74-33-FB-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_27,*************,F0-2F-74-33-FE-13,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_28,*************,F0-2F-74-33-FE-0B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_29,*************,F0-2F-74-33-FE-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_30,************,F0-2F-74-32-6D-F3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_01,*************,F0-2F-74-34-02-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_07,************,F0-2F-74-33-FD-EC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_08,*************,F0-2F-74-33-FC-1C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_09,***********31,F0-2F-74-33-FB-EB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_12,***********19,F0-2F-74-4E-A5-C6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_13,*************,F0-2F-74-33-FB-E4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_14,*************,F0-2F-74-34-12-A3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_16,*************,F0-2F-74-33-FB-5D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_17,*************,F0-2F-74-33-FB-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_18,*************,F0-2F-74-33-FB-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_21,************,F0-2F-74-34-05-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_24,************,F0-2F-74-34-05-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_28,************2,F0-2F-74-33-FB-5C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_29,************,F0-2F-74-34-0A-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_30,*************,F0-2F-74-34-12-CC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_02,*************,F0-2F-74-33-FB-62,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_10,***********,F0-2F-74-33-FD-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_11,************2,F0-2F-74-33-FD-46,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_12,************3,F0-2F-74-33-FE-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_13,************,F0-2F-74-33-FB-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_16,************,F0-2F-74-33-FC-10,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_20,*************,F0-2F-74-34-05-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_21,*************,F0-2F-74-33-FB-FC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_22,***********6,F0-2F-74-33-FD-8A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_25,*************,F0-2F-74-33-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_28,***********30,F0-2F-74-33-FC-2F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_29,***********,F0-2F-74-32-5A-36,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_30,************,F0-2F-74-33-FC-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_01,************,B4-2E-99-59-F9-D1,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_02,*************,B4-2E-99-59-F9-D0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_03,************,B4-2E-99-59-F9-CF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_04,************,B4-2E-99-59-F9-D3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_05,*************,B4-2E-99-59-FA-37,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_06,*************,B4-2E-99-59-F8-05,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_08,************,E0-D5-5E-C4-92-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_09,*************,B4-2E-99-59-F8-09,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_11,************,B4-2E-99-59-FA-24,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_12,*************,B4-2E-99-5A-E1-12,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_13,***********20,B4-2E-99-59-F8-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_14,************5,B4-2E-99-59-FA-75,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_15,***********44,B4-2E-99-5A-E1-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_16,***********82,B4-2E-99-5A-E7-D7,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_21,***********,B4-2E-99-59-FA-18,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_22,************,B4-2E-99-5A-E1-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_23,*************,B4-2E-99-5A-EC-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_24,***********15,B4-2E-99-59-F8-00,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_25,*************,B4-2E-99-5A-E1-15,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_26,************,B4-2E-99-5A-E1-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_27,*************,B4-2E-99-59-F8-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_28,***********08,B4-2E-99-5A-E1-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_29,************,B4-2E-99-5A-DF-40,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_30,************,B4-2E-99-5A-DF-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_05,***********,F0-2F-74-4E-A5-F5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_06,*************,F0-2F-74-4E-9C-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_08,***********05,D0-50-99-69-EE-F6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_09,***********3,E0-D5-5E-9F-65-AF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_10,*************,70-85-C2-82-BE-80,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_11,***********23,E0-D5-5E-9D-84-D5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_12,************,E0-D5-5E-9D-11-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_13,************,E0-D5-5E-9F-64-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_15,************8,B4-2E-99-6B-88-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_16,************,B4-2E-99-6A-84-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_17,************3,B4-2E-99-6A-87-90,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_18,***********68,B4-2E-99-6A-87-DB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_19,************,B4-2E-99-6A-88-69,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_20,************,B4-2E-99-6A-84-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_21,************5,B4-2E-99-6B-8C-1D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_22,***********42,B4-2E-99-6B-88-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_23,************,B4-2E-99-6A-84-45,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_24,************,B4-2E-99-6B-88-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_27,************0,B4-2E-99-6B-88-C1,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_6_28,************,B4-2E-99-6A-84-91,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_7_01,*************,00-E0-4C-3D-AC-E9,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_02,***********06,40-8D-5C-19-54-28,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_03,***********98,FC-AA-14-E2-14-4D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_04,************,F0-2F-74-F4-84-54,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_05,***********40,40-8D-5C-19-54-1D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_07,*************,D8-5E-D3-5F-7B-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_08,***********78,D8-5E-D3-5E-75-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_09,***********21,D8-5E-D3-5F-7C-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_10,*************,D8-5E-D3-5F-84-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_11,*************,D8-5E-D3-5F-7E-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_12,************,D8-5E-D3-5E-74-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_13,************5,D8-5E-D3-5B-D0-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_14,*************,D8-5E-D3-5E-74-FB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_15,************7,D8-5E-D3-56-AF-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_16,************,D8-5E-D3-5E-76-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_17,***********00,D8-5E-D3-5E-79-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_18,************,D8-5E-D3-5F-7C-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_19,************2,D8-5E-D3-5F-7C-4D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_20,*************,D8-5E-D3-5F-7B-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_21,************,D8-5E-D3-5F-7D-B8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_22,************5,D8-5E-D3-5F-7F-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_23,*************,D8-5E-D3-5E-79-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_24,************,D8-5E-D3-5E-74-E3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_25,************,D8-5E-D3-5F-83-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_26,*************,D8-5E-D3-5F-7B-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_27,***********91,D8-5E-D3-5B-D0-DF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_28,***********88,D8-5E-D3-5B-CF-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_29,************7,D8-5E-D3-5E-75-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_30,*************,D8-5E-D3-5E-75-D1,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.384+08:00","@version":"1","message":"group EM4_3 has 17 devices , score is 80 , testNum is 68 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.385+08:00","@version":"1","message":"group EM4_4 has 13 devices , score is 80 , testNum is 52 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.385+08:00","@version":"1","message":"group EM4_5 has 24 devices , score is 80 , testNum is 96 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.386+08:00","@version":"1","message":"group EM4_6 has 24 devices , score is 88 , testNum is 90 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.387+08:00","@version":"1","message":"group EM4_7 has 29 devices , score is 80 , testNum is 116 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.387+08:00","@version":"1","message":"group EM4_1 has 27 devices , score is 80 , testNum is 108 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.388+08:00","@version":"1","message":"group EM4_2 has 25 devices , score is 80 , testNum is 100 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.388+08:00","@version":"1","message":"group device: [[EM4_6,88,[{ EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_05,***********,F0-2F-74-4E-A5-F5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_06,*************,F0-2F-74-4E-9C-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_08,***********05,D0-50-99-69-EE-F6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_09,***********3,E0-D5-5E-9F-65-AF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_10,*************,70-85-C2-82-BE-80,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_11,***********23,E0-D5-5E-9D-84-D5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_12,************,E0-D5-5E-9D-11-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_13,************,E0-D5-5E-9F-64-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_15,************8,B4-2E-99-6B-88-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_16,************,B4-2E-99-6A-84-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_17,************3,B4-2E-99-6A-87-90,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_18,***********68,B4-2E-99-6A-87-DB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_19,************,B4-2E-99-6A-88-69,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_20,************,B4-2E-99-6A-84-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_21,************5,B4-2E-99-6B-8C-1D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_22,***********42,B4-2E-99-6B-88-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_23,************,B4-2E-99-6A-84-45,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_24,************,B4-2E-99-6B-88-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_27,************0,B4-2E-99-6B-88-C1,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_6_28,************,B4-2E-99-6A-84-91,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }]], [EM4_3,80,[{ EM4_3_01,*************,F0-2F-74-34-02-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_07,************,F0-2F-74-33-FD-EC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_08,*************,F0-2F-74-33-FC-1C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_09,***********31,F0-2F-74-33-FB-EB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_12,***********19,F0-2F-74-4E-A5-C6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_13,*************,F0-2F-74-33-FB-E4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_14,*************,F0-2F-74-34-12-A3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_16,*************,F0-2F-74-33-FB-5D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_17,*************,F0-2F-74-33-FB-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_18,*************,F0-2F-74-33-FB-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_21,************,F0-2F-74-34-05-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_24,************,F0-2F-74-34-05-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_28,************2,F0-2F-74-33-FB-5C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_29,************,F0-2F-74-34-0A-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_30,*************,F0-2F-74-34-12-CC,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_4,80,[{ EM4_4_02,*************,F0-2F-74-33-FB-62,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_10,***********,F0-2F-74-33-FD-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_11,************2,F0-2F-74-33-FD-46,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_12,************3,F0-2F-74-33-FE-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_13,************,F0-2F-74-33-FB-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_16,************,F0-2F-74-33-FC-10,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_20,*************,F0-2F-74-34-05-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_21,*************,F0-2F-74-33-FB-FC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_22,***********6,F0-2F-74-33-FD-8A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_25,*************,F0-2F-74-33-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_28,***********30,F0-2F-74-33-FC-2F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_29,***********,F0-2F-74-32-5A-36,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_30,************,F0-2F-74-33-FC-86,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_5,80,[{ EM4_5_01,************,B4-2E-99-59-F9-D1,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_02,*************,B4-2E-99-59-F9-D0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_03,************,B4-2E-99-59-F9-CF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_04,************,B4-2E-99-59-F9-D3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_05,*************,B4-2E-99-59-FA-37,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_06,*************,B4-2E-99-59-F8-05,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_08,************,E0-D5-5E-C4-92-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_09,*************,B4-2E-99-59-F8-09,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_11,************,B4-2E-99-59-FA-24,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_12,*************,B4-2E-99-5A-E1-12,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_13,***********20,B4-2E-99-59-F8-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_14,************5,B4-2E-99-59-FA-75,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_15,***********44,B4-2E-99-5A-E1-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_16,***********82,B4-2E-99-5A-E7-D7,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_21,***********,B4-2E-99-59-FA-18,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_22,************,B4-2E-99-5A-E1-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_23,*************,B4-2E-99-5A-EC-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_24,***********15,B4-2E-99-59-F8-00,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_25,*************,B4-2E-99-5A-E1-15,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_26,************,B4-2E-99-5A-E1-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_27,*************,B4-2E-99-59-F8-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_28,***********08,B4-2E-99-5A-E1-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_29,************,B4-2E-99-5A-DF-40,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_30,************,B4-2E-99-5A-DF-D9,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_7,80,[{ EM4_7_01,*************,00-E0-4C-3D-AC-E9,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_02,***********06,40-8D-5C-19-54-28,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_03,***********98,FC-AA-14-E2-14-4D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_04,************,F0-2F-74-F4-84-54,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_05,***********40,40-8D-5C-19-54-1D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_07,*************,D8-5E-D3-5F-7B-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_08,***********78,D8-5E-D3-5E-75-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_09,***********21,D8-5E-D3-5F-7C-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_10,*************,D8-5E-D3-5F-84-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_11,*************,D8-5E-D3-5F-7E-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_12,************,D8-5E-D3-5E-74-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_13,************5,D8-5E-D3-5B-D0-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_14,*************,D8-5E-D3-5E-74-FB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_15,************7,D8-5E-D3-56-AF-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_16,************,D8-5E-D3-5E-76-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_17,***********00,D8-5E-D3-5E-79-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_18,************,D8-5E-D3-5F-7C-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_19,************2,D8-5E-D3-5F-7C-4D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_20,*************,D8-5E-D3-5F-7B-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_21,************,D8-5E-D3-5F-7D-B8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_22,************5,D8-5E-D3-5F-7F-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_23,*************,D8-5E-D3-5E-79-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_24,************,D8-5E-D3-5E-74-E3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_25,************,D8-5E-D3-5F-83-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_26,*************,D8-5E-D3-5F-7B-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_27,***********91,D8-5E-D3-5B-D0-DF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_28,***********88,D8-5E-D3-5B-CF-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_29,************7,D8-5E-D3-5E-75-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_30,*************,D8-5E-D3-5E-75-D1,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_1,80,[{ EM4_1_04,************,F0-2F-74-33-FB-60,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_05,*************,F0-2F-74-33-FD-FA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_06,*************,F0-2F-74-33-FD-3E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_07,************,F0-2F-74-33-FB-30,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_08,*************,F0-2F-74-34-12-98,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_09,*************,F0-2F-74-33-FB-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_10,*************,F0-2F-74-33-FE-2C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_11,************,F0-2F-74-33-FB-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_12,***********9,F0-2F-74-33-FB-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_13,************,F0-2F-74-34-01-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_14,*************,F0-2F-74-34-05-CE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_15,*************,F0-2F-74-34-00-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_16,*************,F0-2F-74-33-F1-88,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_17,*************,F0-2F-74-33-FB-E2,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_18,************,F0-2F-74-34-05-23,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_19,*************,F0-2F-74-33-FF-02,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_20,*************,F0-2F-74-34-12-A0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_21,*************,F0-2F-74-34-12-DE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_22,*************,F0-2F-74-33-FB-39,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_23,************,F0-2F-74-33-FC-1B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_24,*************,F0-2F-74-34-03-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_25,*************,F0-2F-74-34-02-8E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_26,************4,F0-2F-74-33-FC-81,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_27,*************,F0-2F-74-33-FB-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_28,*************,F0-2F-74-33-FC-79,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_29,*************,F0-2F-74-33-FB-32,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_30,************,F0-2F-74-33-FC-74,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_2,80,[{ EM4_2_01,*************,F0-2F-74-33-FC-1E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_02,*************,F0-2F-74-33-FE-01,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_03,************,F0-2F-74-30-5D-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_04,************,F0-2F-74-33-FC-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_05,***********,F0-2F-74-32-5F-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_06,*************,F0-2F-74-33-FB-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_07,************,F0-2F-74-33-FA-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_08,*************,F0-2F-74-33-FD-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_09,***********11,F0-2F-74-33-FB-ED,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_10,*************,F0-2F-74-4E-A5-9D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_11,************,F0-2F-74-33-FC-5E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_12,************0,F0-2F-74-33-FC-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_13,*************,F0-2F-74-33-FC-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_14,************2,F0-2F-74-33-FB-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_15,*************,F0-2F-74-34-04-5F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_16,*************,F0-2F-74-33-FB-91,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_17,***********38,F0-2F-74-33-FB-6E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_23,************,F0-2F-74-33-FB-BA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_24,************,F0-2F-74-33-FB-64,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_25,*************,F0-2F-74-33-FE-E8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_26,*************,F0-2F-74-33-FB-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_27,*************,F0-2F-74-33-FE-13,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_28,*************,F0-2F-74-33-FE-0B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_29,*************,F0-2F-74-33-FE-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_30,************,F0-2F-74-32-6D-F3,[{TCPIP掉电=4}, {Mars=4}] }]]]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.392+08:00","@version":"1","message":"[6403] find 2 planDevices! ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.392+08:00","@version":"1","message":"[6403] plan:Plan59 use 2 pc: [{ EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.392+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ EM4_4_03,************,F0-2F-74-33-FB-87,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_04,*************,F0-2F-74-33-FA-C3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_05,************,F0-2F-74-33-FE-A6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.392+08:00","@version":"1","message":"[6403] Plan61 need Num: 8 to test. Mars_54_Plan_18o1_64GB left num:32 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.394+08:00","@version":"1","message":"subProduct eMMC flashName Mars_54_Plan_18o1_64GB plan [Plan61] attrs Mars belongTo perry.chen is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.398+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM4_1_04, EM4_1_05, EM4_1_06, EM4_1_07, EM4_1_08, EM4_1_09, EM4_1_10, EM4_1_11, EM4_1_12, EM4_1_13, EM4_1_14, EM4_1_15, EM4_1_16, EM4_1_17, EM4_1_18, EM4_1_19, EM4_1_20, EM4_1_21, EM4_1_22, EM4_1_23, EM4_1_24, EM4_1_25, EM4_1_26, EM4_1_27, EM4_1_28, EM4_1_29, EM4_1_30, EM4_2_01, EM4_2_02, EM4_2_03, EM4_2_04, EM4_2_05, EM4_2_06, EM4_2_07, EM4_2_08, EM4_2_09, EM4_2_10, EM4_2_11, EM4_2_12, EM4_2_13, EM4_2_14, EM4_2_15, EM4_2_16, EM4_2_17, EM4_2_23, EM4_2_24, EM4_2_25, EM4_2_26, EM4_2_27, EM4_2_28, EM4_2_29, EM4_2_30, EM4_3_01, EM4_3_07, EM4_3_08, EM4_3_09, EM4_3_10, EM4_3_11, EM4_3_12, EM4_3_13, EM4_3_14, EM4_3_16, EM4_3_17, EM4_3_18, EM4_3_21, EM4_3_24, EM4_3_28, EM4_3_29, EM4_3_30, EM4_4_02, EM4_4_10, EM4_4_11, EM4_4_12, EM4_4_13, EM4_4_16, EM4_4_20, EM4_4_21, EM4_4_22, EM4_4_25, EM4_4_28, EM4_4_29, EM4_4_30, EM4_5_01, EM4_5_02, EM4_5_03, EM4_5_04, EM4_5_05, EM4_5_06, EM4_5_08, EM4_5_09, EM4_5_11, EM4_5_12, EM4_5_13, EM4_5_14, EM4_5_15, EM4_5_16, EM4_5_21, EM4_5_22, EM4_5_23, EM4_5_24, EM4_5_25, EM4_5_26, EM4_5_27, EM4_5_28, EM4_5_29, EM4_5_30, EM4_6_03, EM4_6_04, EM4_6_05, EM4_6_06, EM4_6_08, EM4_6_09, EM4_6_10, EM4_6_11, EM4_6_12, EM4_6_13, EM4_6_15, EM4_6_16, EM4_6_17, EM4_6_18, EM4_6_19, EM4_6_20, EM4_6_21, EM4_6_22, EM4_6_23, EM4_6_24, EM4_6_27, EM4_6_28, EM4_7_01, EM4_7_02, EM4_7_03, EM4_7_04, EM4_7_05, EM4_7_07, EM4_7_08, EM4_7_09, EM4_7_10, EM4_7_11, EM4_7_12, EM4_7_13, EM4_7_14, EM4_7_15, EM4_7_16, EM4_7_17, EM4_7_18, EM4_7_19, EM4_7_20, EM4_7_21, EM4_7_22, EM4_7_23, EM4_7_24, EM4_7_25, EM4_7_26, EM4_7_27, EM4_7_28, EM4_7_29, EM4_7_30, EM8_1_01, EM8_1_02, EM8_1_04, EM8_1_06, EM8_1_07, EM8_1_08, EM8_1_10, EM8_1_12, EM8_1_15, EM8_1_16, EM8_1_17, EM8_1_22, EM8_1_23, EM8_1_24, EM8_1_25, EM8_1_26, EM8_1_27, EM8_1_28]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.403+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<com.yeestor.admin.model.UserDTO>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.405+08:00","@version":"1","message":"nameList: [深圳办公室, 实验室TE180]","logger_name":"com.yeestor.work_order.utils.DingTalkUtils","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.405+08:00","@version":"1","message":"location: SZ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.408+08:00","@version":"1","message":"sortedEntries: []","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.416+08:00","@version":"1","message":"Plan61是该测试负责人第一个在该批次下分配plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.416+08:00","@version":"1","message":"正在考虑使用非连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.416+08:00","@version":"1","message":"[6403] getPlanRunnableDevices [Plan61] testNum: 8 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.417+08:00","@version":"1","message":"[6403] runnableDeviceList: [{ EM4_1_04,************,F0-2F-74-33-FB-60,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_05,*************,F0-2F-74-33-FD-FA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_06,*************,F0-2F-74-33-FD-3E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_07,************,F0-2F-74-33-FB-30,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_08,*************,F0-2F-74-34-12-98,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_09,*************,F0-2F-74-33-FB-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_10,*************,F0-2F-74-33-FE-2C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_11,************,F0-2F-74-33-FB-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_12,***********9,F0-2F-74-33-FB-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_13,************,F0-2F-74-34-01-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_14,*************,F0-2F-74-34-05-CE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_15,*************,F0-2F-74-34-00-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_16,*************,F0-2F-74-33-F1-88,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_17,*************,F0-2F-74-33-FB-E2,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_18,************,F0-2F-74-34-05-23,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_19,*************,F0-2F-74-33-FF-02,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_20,*************,F0-2F-74-34-12-A0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_21,*************,F0-2F-74-34-12-DE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_22,*************,F0-2F-74-33-FB-39,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_23,************,F0-2F-74-33-FC-1B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_24,*************,F0-2F-74-34-03-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_25,*************,F0-2F-74-34-02-8E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_26,************4,F0-2F-74-33-FC-81,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_27,*************,F0-2F-74-33-FB-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_28,*************,F0-2F-74-33-FC-79,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_29,*************,F0-2F-74-33-FB-32,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_30,************,F0-2F-74-33-FC-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_01,*************,F0-2F-74-33-FC-1E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_02,*************,F0-2F-74-33-FE-01,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_03,************,F0-2F-74-30-5D-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_04,************,F0-2F-74-33-FC-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_05,***********,F0-2F-74-32-5F-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_06,*************,F0-2F-74-33-FB-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_07,************,F0-2F-74-33-FA-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_08,*************,F0-2F-74-33-FD-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_09,***********11,F0-2F-74-33-FB-ED,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_10,*************,F0-2F-74-4E-A5-9D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_11,************,F0-2F-74-33-FC-5E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_12,************0,F0-2F-74-33-FC-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_13,*************,F0-2F-74-33-FC-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_14,************2,F0-2F-74-33-FB-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_15,*************,F0-2F-74-34-04-5F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_16,*************,F0-2F-74-33-FB-91,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_17,***********38,F0-2F-74-33-FB-6E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_23,************,F0-2F-74-33-FB-BA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_24,************,F0-2F-74-33-FB-64,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_25,*************,F0-2F-74-33-FE-E8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_26,*************,F0-2F-74-33-FB-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_27,*************,F0-2F-74-33-FE-13,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_28,*************,F0-2F-74-33-FE-0B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_29,*************,F0-2F-74-33-FE-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_30,************,F0-2F-74-32-6D-F3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_01,*************,F0-2F-74-34-02-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_07,************,F0-2F-74-33-FD-EC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_08,*************,F0-2F-74-33-FC-1C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_09,***********31,F0-2F-74-33-FB-EB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_12,***********19,F0-2F-74-4E-A5-C6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_13,*************,F0-2F-74-33-FB-E4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_14,*************,F0-2F-74-34-12-A3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_16,*************,F0-2F-74-33-FB-5D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_17,*************,F0-2F-74-33-FB-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_18,*************,F0-2F-74-33-FB-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_21,************,F0-2F-74-34-05-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_24,************,F0-2F-74-34-05-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_28,************2,F0-2F-74-33-FB-5C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_29,************,F0-2F-74-34-0A-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_30,*************,F0-2F-74-34-12-CC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_02,*************,F0-2F-74-33-FB-62,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_10,***********,F0-2F-74-33-FD-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_11,************2,F0-2F-74-33-FD-46,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_12,************3,F0-2F-74-33-FE-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_13,************,F0-2F-74-33-FB-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_16,************,F0-2F-74-33-FC-10,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_20,*************,F0-2F-74-34-05-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_21,*************,F0-2F-74-33-FB-FC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_22,***********6,F0-2F-74-33-FD-8A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_25,*************,F0-2F-74-33-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_28,***********30,F0-2F-74-33-FC-2F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_29,***********,F0-2F-74-32-5A-36,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_30,************,F0-2F-74-33-FC-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_01,************,B4-2E-99-59-F9-D1,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_02,*************,B4-2E-99-59-F9-D0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_03,************,B4-2E-99-59-F9-CF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_04,************,B4-2E-99-59-F9-D3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_05,*************,B4-2E-99-59-FA-37,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_06,*************,B4-2E-99-59-F8-05,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_08,************,E0-D5-5E-C4-92-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_09,*************,B4-2E-99-59-F8-09,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_11,************,B4-2E-99-59-FA-24,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_12,*************,B4-2E-99-5A-E1-12,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_13,***********20,B4-2E-99-59-F8-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_14,************5,B4-2E-99-59-FA-75,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_15,***********44,B4-2E-99-5A-E1-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_16,***********82,B4-2E-99-5A-E7-D7,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_21,***********,B4-2E-99-59-FA-18,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_22,************,B4-2E-99-5A-E1-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_23,*************,B4-2E-99-5A-EC-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_24,***********15,B4-2E-99-59-F8-00,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_25,*************,B4-2E-99-5A-E1-15,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_26,************,B4-2E-99-5A-E1-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_27,*************,B4-2E-99-59-F8-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_28,***********08,B4-2E-99-5A-E1-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_29,************,B4-2E-99-5A-DF-40,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_30,************,B4-2E-99-5A-DF-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_05,***********,F0-2F-74-4E-A5-F5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_06,*************,F0-2F-74-4E-9C-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_08,***********05,D0-50-99-69-EE-F6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_09,***********3,E0-D5-5E-9F-65-AF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_10,*************,70-85-C2-82-BE-80,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_11,***********23,E0-D5-5E-9D-84-D5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_12,************,E0-D5-5E-9D-11-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_13,************,E0-D5-5E-9F-64-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_15,************8,B4-2E-99-6B-88-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_16,************,B4-2E-99-6A-84-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_17,************3,B4-2E-99-6A-87-90,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_18,***********68,B4-2E-99-6A-87-DB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_19,************,B4-2E-99-6A-88-69,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_20,************,B4-2E-99-6A-84-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_21,************5,B4-2E-99-6B-8C-1D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_22,***********42,B4-2E-99-6B-88-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_23,************,B4-2E-99-6A-84-45,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_24,************,B4-2E-99-6B-88-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_27,************0,B4-2E-99-6B-88-C1,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_6_28,************,B4-2E-99-6A-84-91,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_7_01,*************,00-E0-4C-3D-AC-E9,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_02,***********06,40-8D-5C-19-54-28,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_03,***********98,FC-AA-14-E2-14-4D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_04,************,F0-2F-74-F4-84-54,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_05,***********40,40-8D-5C-19-54-1D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_07,*************,D8-5E-D3-5F-7B-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_08,***********78,D8-5E-D3-5E-75-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_09,***********21,D8-5E-D3-5F-7C-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_10,*************,D8-5E-D3-5F-84-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_11,*************,D8-5E-D3-5F-7E-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_12,************,D8-5E-D3-5E-74-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_13,************5,D8-5E-D3-5B-D0-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_14,*************,D8-5E-D3-5E-74-FB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_15,************7,D8-5E-D3-56-AF-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_16,************,D8-5E-D3-5E-76-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_17,***********00,D8-5E-D3-5E-79-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_18,************,D8-5E-D3-5F-7C-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_19,************2,D8-5E-D3-5F-7C-4D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_20,*************,D8-5E-D3-5F-7B-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_21,************,D8-5E-D3-5F-7D-B8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_22,************5,D8-5E-D3-5F-7F-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_23,*************,D8-5E-D3-5E-79-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_24,************,D8-5E-D3-5E-74-E3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_25,************,D8-5E-D3-5F-83-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_26,*************,D8-5E-D3-5F-7B-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_27,***********91,D8-5E-D3-5B-D0-DF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_28,***********88,D8-5E-D3-5B-CF-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_29,************7,D8-5E-D3-5E-75-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_30,*************,D8-5E-D3-5E-75-D1,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.422+08:00","@version":"1","message":"group EM4_3 has 17 devices , score is 80 , testNum is 68 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.423+08:00","@version":"1","message":"group EM4_4 has 13 devices , score is 80 , testNum is 52 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.423+08:00","@version":"1","message":"group EM4_5 has 24 devices , score is 80 , testNum is 96 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.424+08:00","@version":"1","message":"group EM4_6 has 22 devices , score is 89 , testNum is 88 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.425+08:00","@version":"1","message":"group EM4_7 has 29 devices , score is 80 , testNum is 116 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.426+08:00","@version":"1","message":"group EM4_1 has 27 devices , score is 80 , testNum is 108 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.426+08:00","@version":"1","message":"group EM4_2 has 25 devices , score is 80 , testNum is 100 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.426+08:00","@version":"1","message":"group device: [[EM4_6,89,[{ EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_05,***********,F0-2F-74-4E-A5-F5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_06,*************,F0-2F-74-4E-9C-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_08,***********05,D0-50-99-69-EE-F6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_09,***********3,E0-D5-5E-9F-65-AF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_10,*************,70-85-C2-82-BE-80,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_11,***********23,E0-D5-5E-9D-84-D5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_12,************,E0-D5-5E-9D-11-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_13,************,E0-D5-5E-9F-64-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_15,************8,B4-2E-99-6B-88-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_16,************,B4-2E-99-6A-84-97,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_17,************3,B4-2E-99-6A-87-90,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_18,***********68,B4-2E-99-6A-87-DB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_19,************,B4-2E-99-6A-88-69,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_20,************,B4-2E-99-6A-84-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_21,************5,B4-2E-99-6B-8C-1D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_22,***********42,B4-2E-99-6B-88-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_23,************,B4-2E-99-6A-84-45,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_24,************,B4-2E-99-6B-88-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_27,************0,B4-2E-99-6B-88-C1,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }, { EM4_6_28,************,B4-2E-99-6A-84-91,[{TCPIP掉电=1}, {性能=1}, {Mars=4}] }]], [EM4_3,80,[{ EM4_3_01,*************,F0-2F-74-34-02-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_07,************,F0-2F-74-33-FD-EC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_08,*************,F0-2F-74-33-FC-1C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_09,***********31,F0-2F-74-33-FB-EB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_12,***********19,F0-2F-74-4E-A5-C6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_13,*************,F0-2F-74-33-FB-E4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_14,*************,F0-2F-74-34-12-A3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_16,*************,F0-2F-74-33-FB-5D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_17,*************,F0-2F-74-33-FB-9B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_18,*************,F0-2F-74-33-FB-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_21,************,F0-2F-74-34-05-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_24,************,F0-2F-74-34-05-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_28,************2,F0-2F-74-33-FB-5C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_29,************,F0-2F-74-34-0A-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_30,*************,F0-2F-74-34-12-CC,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_4,80,[{ EM4_4_02,*************,F0-2F-74-33-FB-62,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_10,***********,F0-2F-74-33-FD-D9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_11,************2,F0-2F-74-33-FD-46,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_12,************3,F0-2F-74-33-FE-8F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_13,************,F0-2F-74-33-FB-C5,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_16,************,F0-2F-74-33-FC-10,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_20,*************,F0-2F-74-34-05-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_21,*************,F0-2F-74-33-FB-FC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_22,***********6,F0-2F-74-33-FD-8A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_25,*************,F0-2F-74-33-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_28,***********30,F0-2F-74-33-FC-2F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_29,***********,F0-2F-74-32-5A-36,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_30,************,F0-2F-74-33-FC-86,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_5,80,[{ EM4_5_01,************,B4-2E-99-59-F9-D1,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_02,*************,B4-2E-99-59-F9-D0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_03,************,B4-2E-99-59-F9-CF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_04,************,B4-2E-99-59-F9-D3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_05,*************,B4-2E-99-59-FA-37,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_06,*************,B4-2E-99-59-F8-05,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_08,************,E0-D5-5E-C4-92-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_09,*************,B4-2E-99-59-F8-09,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_11,************,B4-2E-99-59-FA-24,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_12,*************,B4-2E-99-5A-E1-12,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_13,***********20,B4-2E-99-59-F8-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_14,************5,B4-2E-99-59-FA-75,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_15,***********44,B4-2E-99-5A-E1-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_16,***********82,B4-2E-99-5A-E7-D7,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_21,***********,B4-2E-99-59-FA-18,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_22,************,B4-2E-99-5A-E1-21,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_23,*************,B4-2E-99-5A-EC-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_24,***********15,B4-2E-99-59-F8-00,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_25,*************,B4-2E-99-5A-E1-15,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_26,************,B4-2E-99-5A-E1-28,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_27,*************,B4-2E-99-59-F8-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_28,***********08,B4-2E-99-5A-E1-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_29,************,B4-2E-99-5A-DF-40,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_5_30,************,B4-2E-99-5A-DF-D9,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_7,80,[{ EM4_7_01,*************,00-E0-4C-3D-AC-E9,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_02,***********06,40-8D-5C-19-54-28,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_03,***********98,FC-AA-14-E2-14-4D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_04,************,F0-2F-74-F4-84-54,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_05,***********40,40-8D-5C-19-54-1D,[{Mars=4}, {TCPIP掉电=4}] }, { EM4_7_07,*************,D8-5E-D3-5F-7B-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_08,***********78,D8-5E-D3-5E-75-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_09,***********21,D8-5E-D3-5F-7C-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_10,*************,D8-5E-D3-5F-84-44,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_11,*************,D8-5E-D3-5F-7E-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_12,************,D8-5E-D3-5E-74-8C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_13,************5,D8-5E-D3-5B-D0-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_14,*************,D8-5E-D3-5E-74-FB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_15,************7,D8-5E-D3-56-AF-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_16,************,D8-5E-D3-5E-76-54,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_17,***********00,D8-5E-D3-5E-79-C9,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_18,************,D8-5E-D3-5F-7C-74,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_19,************2,D8-5E-D3-5F-7C-4D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_20,*************,D8-5E-D3-5F-7B-77,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_21,************,D8-5E-D3-5F-7D-B8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_22,************5,D8-5E-D3-5F-7F-AC,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_23,*************,D8-5E-D3-5E-79-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_24,************,D8-5E-D3-5E-74-E3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_25,************,D8-5E-D3-5F-83-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_26,*************,D8-5E-D3-5F-7B-B4,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_27,***********91,D8-5E-D3-5B-D0-DF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_28,***********88,D8-5E-D3-5B-CF-61,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_29,************7,D8-5E-D3-5E-75-AE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_7_30,*************,D8-5E-D3-5E-75-D1,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_1,80,[{ EM4_1_04,************,F0-2F-74-33-FB-60,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_05,*************,F0-2F-74-33-FD-FA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_06,*************,F0-2F-74-33-FD-3E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_07,************,F0-2F-74-33-FB-30,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_08,*************,F0-2F-74-34-12-98,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_09,*************,F0-2F-74-33-FB-73,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_10,*************,F0-2F-74-33-FE-2C,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_11,************,F0-2F-74-33-FB-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_12,***********9,F0-2F-74-33-FB-86,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_13,************,F0-2F-74-34-01-0E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_14,*************,F0-2F-74-34-05-CE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_15,*************,F0-2F-74-34-00-6F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_16,*************,F0-2F-74-33-F1-88,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_17,*************,F0-2F-74-33-FB-E2,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_18,************,F0-2F-74-34-05-23,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_19,*************,F0-2F-74-33-FF-02,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_20,*************,F0-2F-74-34-12-A0,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_21,*************,F0-2F-74-34-12-DE,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_22,*************,F0-2F-74-33-FB-39,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_23,************,F0-2F-74-33-FC-1B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_24,*************,F0-2F-74-34-03-5A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_25,*************,F0-2F-74-34-02-8E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_26,************4,F0-2F-74-33-FC-81,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_27,*************,F0-2F-74-33-FB-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_28,*************,F0-2F-74-33-FC-79,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_29,*************,F0-2F-74-33-FB-32,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_1_30,************,F0-2F-74-33-FC-74,[{TCPIP掉电=4}, {Mars=4}] }]], [EM4_2,80,[{ EM4_2_01,*************,F0-2F-74-33-FC-1E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_02,*************,F0-2F-74-33-FE-01,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_03,************,F0-2F-74-30-5D-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_04,************,F0-2F-74-33-FC-76,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_05,***********,F0-2F-74-32-5F-7E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_06,*************,F0-2F-74-33-FB-59,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_07,************,F0-2F-74-33-FA-70,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_08,*************,F0-2F-74-33-FD-3A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_09,***********11,F0-2F-74-33-FB-ED,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_10,*************,F0-2F-74-4E-A5-9D,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_11,************,F0-2F-74-33-FC-5E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_12,************0,F0-2F-74-33-FC-31,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_13,*************,F0-2F-74-33-FC-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_14,************2,F0-2F-74-33-FB-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_15,*************,F0-2F-74-34-04-5F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_16,*************,F0-2F-74-33-FB-91,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_17,***********38,F0-2F-74-33-FB-6E,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_23,************,F0-2F-74-33-FB-BA,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_24,************,F0-2F-74-33-FB-64,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_25,*************,F0-2F-74-33-FE-E8,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_26,*************,F0-2F-74-33-FB-4F,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_27,*************,F0-2F-74-33-FE-13,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_28,*************,F0-2F-74-33-FE-0B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_29,*************,F0-2F-74-33-FE-6B,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_2_30,************,F0-2F-74-32-6D-F3,[{TCPIP掉电=4}, {Mars=4}] }]]]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.428+08:00","@version":"1","message":"[6403] find 2 planDevices! ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.428+08:00","@version":"1","message":"[6403] plan:Plan61 use 2 pc: [{ EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.429+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ EM4_4_03,************,F0-2F-74-33-FB-87,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_04,*************,F0-2F-74-33-FA-C3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_05,************,F0-2F-74-33-FE-A6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.43+08:00","@version":"1","message":"[6403] Plan21 need Num: 8 to test. Mars_54_Plan_18o1_64GB left num:24 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.43+08:00","@version":"1","message":"subProduct eMMC flashName Mars_54_Plan_18o1_64GB plan [Plan21] attrs Mars;TCPIP掉电 belongTo perry.chen is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.433+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM4_1_04, EM4_1_05, EM4_1_06, EM4_1_07, EM4_1_08, EM4_1_09, EM4_1_10, EM4_1_11, EM4_1_12, EM4_1_13, EM4_1_14, EM4_1_15, EM4_1_16, EM4_1_17, EM4_1_18, EM4_1_19, EM4_1_20, EM4_1_21, EM4_1_22, EM4_1_23, EM4_1_24, EM4_1_25, EM4_1_26, EM4_1_27, EM4_1_28, EM4_1_29, EM4_1_30, EM4_2_01, EM4_2_02, EM4_2_03, EM4_2_04, EM4_2_05, EM4_2_06, EM4_2_07, EM4_2_08, EM4_2_09, EM4_2_10, EM4_2_11, EM4_2_12, EM4_2_13, EM4_2_14, EM4_2_15, EM4_2_16, EM4_2_17, EM4_2_23, EM4_2_24, EM4_2_25, EM4_2_26, EM4_2_27, EM4_2_28, EM4_2_29, EM4_2_30, EM4_3_01, EM4_3_07, EM4_3_08, EM4_3_09, EM4_3_10, EM4_3_11, EM4_3_12, EM4_3_13, EM4_3_14, EM4_3_16, EM4_3_17, EM4_3_18, EM4_3_21, EM4_3_24, EM4_3_28, EM4_3_29, EM4_3_30, EM4_4_02, EM4_4_10, EM4_4_11, EM4_4_12, EM4_4_13, EM4_4_16, EM4_4_20, EM4_4_21, EM4_4_22, EM4_4_25, EM4_4_28, EM4_4_29, EM4_4_30, EM4_5_01, EM4_5_02, EM4_5_03, EM4_5_04, EM4_5_05, EM4_5_06, EM4_5_08, EM4_5_09, EM4_5_11, EM4_5_12, EM4_5_13, EM4_5_14, EM4_5_15, EM4_5_16, EM4_5_21, EM4_5_22, EM4_5_23, EM4_5_24, EM4_5_25, EM4_5_26, EM4_5_27, EM4_5_28, EM4_5_29, EM4_5_30, EM4_6_05, EM4_6_06, EM4_6_08, EM4_6_09, EM4_6_10, EM4_6_11, EM4_6_12, EM4_6_13, EM4_6_15, EM4_6_16, EM4_6_17, EM4_6_18, EM4_6_19, EM4_6_20, EM4_6_21, EM4_6_22, EM4_6_23, EM4_6_24, EM4_6_27, EM4_6_28, EM4_7_01, EM4_7_02, EM4_7_03, EM4_7_04, EM4_7_05, EM4_7_07, EM4_7_08, EM4_7_09, EM4_7_10, EM4_7_11, EM4_7_12, EM4_7_13, EM4_7_14, EM4_7_15, EM4_7_16, EM4_7_17, EM4_7_18, EM4_7_19, EM4_7_20, EM4_7_21, EM4_7_22, EM4_7_23, EM4_7_24, EM4_7_25, EM4_7_26, EM4_7_27, EM4_7_28, EM4_7_29, EM4_7_30, EM8_1_01, EM8_1_02, EM8_1_04, EM8_1_06, EM8_1_07, EM8_1_08, EM8_1_10, EM8_1_12, EM8_1_15, EM8_1_16, EM8_1_17, EM8_1_22, EM8_1_23, EM8_1_24, EM8_1_25, EM8_1_26, EM8_1_27, EM8_1_28]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.438+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<com.yeestor.admin.model.UserDTO>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.439+08:00","@version":"1","message":"nameList: [深圳办公室, 实验室TE180]","logger_name":"com.yeestor.work_order.utils.DingTalkUtils","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.439+08:00","@version":"1","message":"location: SZ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.443+08:00","@version":"1","message":"sortedEntries: []","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.456+08:00","@version":"1","message":"Plan21是该测试负责人第一个在该批次下分配plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.457+08:00","@version":"1","message":"Plan21在历史设备中找到满足条件的设备：[F0-2F-74-33-FB-C4, F0-2F-74-33-FB-1A]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.457+08:00","@version":"1","message":"[6403] plan:Plan21 use 2 pc: [{ EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.458+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ EM4_4_03,************,F0-2F-74-33-FB-87,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_04,*************,F0-2F-74-33-FA-C3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_05,************,F0-2F-74-33-FE-A6,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.458+08:00","@version":"1","message":"[6403] Plan36 need Num: 10 to test. Mars_54_Plan_18o1_64GB left num:16 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.458+08:00","@version":"1","message":"subProduct eMMC flashName Mars_54_Plan_18o1_64GB plan [Plan36] attrs Mars belongTo perry.chen is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.462+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM4_1_04, EM4_1_05, EM4_1_06, EM4_1_07, EM4_1_08, EM4_1_09, EM4_1_10, EM4_1_11, EM4_1_12, EM4_1_13, EM4_1_14, EM4_1_15, EM4_1_16, EM4_1_17, EM4_1_18, EM4_1_19, EM4_1_20, EM4_1_21, EM4_1_22, EM4_1_23, EM4_1_24, EM4_1_25, EM4_1_26, EM4_1_27, EM4_1_28, EM4_1_29, EM4_1_30, EM4_2_01, EM4_2_02, EM4_2_03, EM4_2_04, EM4_2_05, EM4_2_06, EM4_2_07, EM4_2_08, EM4_2_09, EM4_2_10, EM4_2_11, EM4_2_12, EM4_2_13, EM4_2_14, EM4_2_15, EM4_2_16, EM4_2_17, EM4_2_23, EM4_2_24, EM4_2_25, EM4_2_26, EM4_2_27, EM4_2_28, EM4_2_29, EM4_2_30, EM4_3_01, EM4_3_07, EM4_3_08, EM4_3_09, EM4_3_12, EM4_3_13, EM4_3_14, EM4_3_16, EM4_3_17, EM4_3_18, EM4_3_21, EM4_3_24, EM4_3_28, EM4_3_29, EM4_3_30, EM4_4_02, EM4_4_10, EM4_4_11, EM4_4_12, EM4_4_13, EM4_4_16, EM4_4_20, EM4_4_21, EM4_4_22, EM4_4_25, EM4_4_28, EM4_4_29, EM4_4_30, EM4_5_01, EM4_5_02, EM4_5_03, EM4_5_04, EM4_5_05, EM4_5_06, EM4_5_08, EM4_5_09, EM4_5_11, EM4_5_12, EM4_5_13, EM4_5_14, EM4_5_15, EM4_5_16, EM4_5_21, EM4_5_22, EM4_5_23, EM4_5_24, EM4_5_25, EM4_5_26, EM4_5_27, EM4_5_28, EM4_5_29, EM4_5_30, EM4_6_05, EM4_6_06, EM4_6_08, EM4_6_09, EM4_6_10, EM4_6_11, EM4_6_12, EM4_6_13, EM4_6_15, EM4_6_16, EM4_6_17, EM4_6_18, EM4_6_19, EM4_6_20, EM4_6_21, EM4_6_22, EM4_6_23, EM4_6_24, EM4_6_27, EM4_6_28, EM4_7_01, EM4_7_02, EM4_7_03, EM4_7_04, EM4_7_05, EM4_7_07, EM4_7_08, EM4_7_09, EM4_7_10, EM4_7_11, EM4_7_12, EM4_7_13, EM4_7_14, EM4_7_15, EM4_7_16, EM4_7_17, EM4_7_18, EM4_7_19, EM4_7_20, EM4_7_21, EM4_7_22, EM4_7_23, EM4_7_24, EM4_7_25, EM4_7_26, EM4_7_27, EM4_7_28, EM4_7_29, EM4_7_30, EM8_1_01, EM8_1_02, EM8_1_04, EM8_1_06, EM8_1_07, EM8_1_08, EM8_1_10, EM8_1_12, EM8_1_15, EM8_1_16, EM8_1_17, EM8_1_22, EM8_1_23, EM8_1_24, EM8_1_25, EM8_1_26, EM8_1_27, EM8_1_28]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.466+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<com.yeestor.admin.model.UserDTO>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.467+08:00","@version":"1","message":"nameList: [深圳办公室, 实验室TE180]","logger_name":"com.yeestor.work_order.utils.DingTalkUtils","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.467+08:00","@version":"1","message":"location: SZ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.469+08:00","@version":"1","message":"sortedEntries: []","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.48+08:00","@version":"1","message":"Plan36是该测试负责人第一个在该批次下分配plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.481+08:00","@version":"1","message":"Plan36在历史设备中找到满足条件的设备：[F0-2F-74-33-FD-3A]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.481+08:00","@version":"1","message":"[6403] plan:Plan36 use 1 pc: [{ EM4_2_08,*************,F0-2F-74-33-FD-3A,[{TCPIP掉电=4}, {Mars=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.481+08:00","@version":"1","message":"assignDevices [6403] - find 5 run devices:  {Plan61=[{ EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }], Plan21=[{ EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }], Plan26=[{ EM4_4_03,************,F0-2F-74-33-FB-87,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_04,*************,F0-2F-74-33-FA-C3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_05,************,F0-2F-74-33-FE-A6,[{TCPIP掉电=4}, {Mars=4}] }], Plan59=[{ EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }], Plan36=[{ EM4_2_08,*************,F0-2F-74-33-FD-3A,[{TCPIP掉电=4}, {Mars=4}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.484+08:00","@version":"1","message":"工单[6403] flash Mars_54_Plan_18o1_64GB , 参与此次Plan预分配的plan共有 [Plan26, Plan59, Plan61, Plan21, Plan36] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.54+08:00","@version":"1","message":" add 2 devices :[{ EM4_6_03,*************,B4-2E-99-5A-DE-EF,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_04,*************,B4-2E-99-5A-E1-14,[{TCPIP掉电=4}, {Mars=4}] }]  to plan:Plan61","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.563+08:00","@version":"1","message":"plan:Plan61 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 8","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.649+08:00","@version":"1","message":"[YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB] - Plan61 holdDevices  :[PlanDeviceEntity(id=277427, orderId=6403, planId=113872, planName=Plan61, ip=*************, mac=B4-2E-99-5A-DE-EF, no=EM4_6_03, position=EM4_6_03, score=80, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277428, orderId=6403, planId=113872, planName=Plan61, ip=*************, mac=B4-2E-99-5A-E1-14, no=EM4_6_04, position=EM4_6_04, score=80, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.834+08:00","@version":"1","message":"lock device:B4-2E-99-5A-DE-EF to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan61 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.892+08:00","@version":"1","message":"lock device:B4-2E-99-5A-E1-14 to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan61 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:05.905+08:00","@version":"1","message":"[13299c0f] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.177+08:00","@version":"1","message":"lockDevice with ipList:[*************, *************] - macList:[B4-2E-99-5A-DE-EF, B4-2E-99-5A-E1-14],no:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.199+08:00","@version":"1","message":"自动分配EM4_6_03(*************),EM4_6_04(*************)给Mars_54_Plan_18o1_64GB下的Plan61","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.208+08:00","@version":"1","message":"add 2 devices to plan: Plan61 ,expect num: 8, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.208+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6403, flash:Mars_54_Plan_18o1_64GB, planEntity:OrderPlanEntity(id=113872, orderId=6403, name=Plan61, status=QUEUE, priority=85)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.262+08:00","@version":"1","message":"测试单: YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB plan: Plan61已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.984+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.998+08:00","@version":"1","message":" add 2 devices :[{ EM4_3_10,*************,F0-2F-74-33-FB-1A,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_3_11,*************,F0-2F-74-33-FB-C4,[{TCPIP掉电=4}, {Mars=4}] }]  to plan:Plan21","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.022+08:00","@version":"1","message":"plan:Plan21 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 8","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.03+08:00","@version":"1","message":"[YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB] - Plan21 holdDevices  :[PlanDeviceEntity(id=277429, orderId=6403, planId=113867, planName=Plan21, ip=*************, mac=F0-2F-74-33-FB-1A, no=EM4_3_10, position=EM4_3_10, score=80, owner=Judy.Deng, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277430, orderId=6403, planId=113867, planName=Plan21, ip=*************, mac=F0-2F-74-33-FB-C4, no=EM4_3_11, position=EM4_3_11, score=80, owner=Judy.Deng, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.034+08:00","@version":"1","message":"lock device:F0-2F-74-33-FB-1A to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan21 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.042+08:00","@version":"1","message":"lock device:F0-2F-74-33-FB-C4 to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan21 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.048+08:00","@version":"1","message":"[27526401] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.191+08:00","@version":"1","message":"lockDevice with ipList:[*************, *************] - macList:[F0-2F-74-33-FB-1A, F0-2F-74-33-FB-C4],no:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.192+08:00","@version":"1","message":"自动分配EM4_3_10(*************),EM4_3_11(*************)给Mars_54_Plan_18o1_64GB下的Plan21","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.192+08:00","@version":"1","message":"add 2 devices to plan: Plan21 ,expect num: 8, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.192+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6403, flash:Mars_54_Plan_18o1_64GB, planEntity:OrderPlanEntity(id=113867, orderId=6403, name=Plan21, status=QUEUE, priority=80)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.194+08:00","@version":"1","message":"测试单: YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB plan: Plan21已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.422+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.439+08:00","@version":"1","message":" add 3 devices :[{ EM4_4_03,************,F0-2F-74-33-FB-87,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_04,*************,F0-2F-74-33-FA-C3,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_4_05,************,F0-2F-74-33-FE-A6,[{TCPIP掉电=4}, {Mars=4}] }]  to plan:Plan26","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.462+08:00","@version":"1","message":"plan:Plan26 assign info update to ActualDeviceNum: 3, ExceptedSampleNum: 10","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.469+08:00","@version":"1","message":"[YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB] - Plan26 holdDevices  :[PlanDeviceEntity(id=277431, orderId=6403, planId=113869, planName=Plan26, ip=************, mac=F0-2F-74-33-FB-87, no=EM4_4_03, position=EM4_4_03, score=80, owner=Judy.Deng, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277432, orderId=6403, planId=113869, planName=Plan26, ip=*************, mac=F0-2F-74-33-FA-C3, no=EM4_4_04, position=EM4_4_04, score=80, owner=Judy.Deng, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277433, orderId=6403, planId=113869, planName=Plan26, ip=************, mac=F0-2F-74-33-FE-A6, no=EM4_4_05, position=EM4_4_05, score=80, owner=Judy.Deng, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.471+08:00","@version":"1","message":"lock device:F0-2F-74-33-FB-87 to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan26 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.477+08:00","@version":"1","message":"lock device:F0-2F-74-33-FA-C3 to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan26 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.484+08:00","@version":"1","message":"lock device:F0-2F-74-33-FE-A6 to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan26 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.488+08:00","@version":"1","message":"[627c467d] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.628+08:00","@version":"1","message":"lockDevice with ipList:[************, *************, ************] - macList:[F0-2F-74-33-FB-87, F0-2F-74-33-FA-C3, F0-2F-74-33-FE-A6],no:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.629+08:00","@version":"1","message":"自动分配EM4_4_03(************),EM4_4_04(*************),EM4_4_05(************)给Mars_54_Plan_18o1_64GB下的Plan26","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.629+08:00","@version":"1","message":"add 3 devices to plan: Plan26 ,expect num: 10, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.629+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6403, flash:Mars_54_Plan_18o1_64GB, planEntity:OrderPlanEntity(id=113869, orderId=6403, name=Plan26, status=QUEUE, priority=90)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.631+08:00","@version":"1","message":"测试单: YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB plan: Plan26已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.81+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.819+08:00","@version":"1","message":" add 2 devices :[{ EM4_6_01,************,F0-2F-74-4E-9C-07,[{TCPIP掉电=4}, {Mars=4}] }, { EM4_6_02,*************,F0-2F-74-4E-9C-BB,[{TCPIP掉电=4}, {Mars=4}] }]  to plan:Plan59","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.838+08:00","@version":"1","message":"plan:Plan59 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 8","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.844+08:00","@version":"1","message":"[YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB] - Plan59 holdDevices  :[PlanDeviceEntity(id=277434, orderId=6403, planId=113871, planName=Plan59, ip=************, mac=F0-2F-74-4E-9C-07, no=EM4_6_01, position=EM4_6_01, score=80, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277435, orderId=6403, planId=113871, planName=Plan59, ip=*************, mac=F0-2F-74-4E-9C-BB, no=EM4_6_02, position=EM4_6_02, score=80, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.846+08:00","@version":"1","message":"lock device:F0-2F-74-4E-9C-07 to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan59 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.852+08:00","@version":"1","message":"lock device:F0-2F-74-4E-9C-BB to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan59 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:07.856+08:00","@version":"1","message":"[3d2a6fe0] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.004+08:00","@version":"1","message":"lockDevice with ipList:[************, *************] - macList:[F0-2F-74-4E-9C-07, F0-2F-74-4E-9C-BB],no:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.005+08:00","@version":"1","message":"自动分配EM4_6_01(************),EM4_6_02(*************)给Mars_54_Plan_18o1_64GB下的Plan59","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.005+08:00","@version":"1","message":"add 2 devices to plan: Plan59 ,expect num: 8, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.005+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6403, flash:Mars_54_Plan_18o1_64GB, planEntity:OrderPlanEntity(id=113871, orderId=6403, name=Plan59, status=QUEUE, priority=85)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.007+08:00","@version":"1","message":"测试单: YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB plan: Plan59已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.193+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.203+08:00","@version":"1","message":" add 1 devices :[{ EM4_2_08,*************,F0-2F-74-33-FD-3A,[{TCPIP掉电=4}, {Mars=4}] }]  to plan:Plan36","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.222+08:00","@version":"1","message":"plan:Plan36 assign info update to ActualDeviceNum: 1, ExceptedSampleNum: 4","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.226+08:00","@version":"1","message":"[YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB] - Plan36 holdDevices  :[PlanDeviceEntity(id=277436, orderId=6403, planId=113870, planName=Plan36, ip=*************, mac=F0-2F-74-33-FD-3A, no=EM4_2_08, position=EM4_2_08, score=80, owner=Lily.zou, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.229+08:00","@version":"1","message":"lock device:F0-2F-74-33-FD-3A to orderNo:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB planName:Plan36 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.233+08:00","@version":"1","message":"[412709c8] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.363+08:00","@version":"1","message":"lockDevice with ipList:[*************] - macList:[F0-2F-74-33-FD-3A],no:YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.363+08:00","@version":"1","message":"自动分配EM4_2_08(*************)给Mars_54_Plan_18o1_64GB下的Plan36","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.363+08:00","@version":"1","message":"add 1 devices to plan: Plan36 ,expect num: 10, left num: 6","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.363+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6403, flash:Mars_54_Plan_18o1_64GB, planEntity:OrderPlanEntity(id=113870, orderId=6403, name=Plan36, status=QUEUE, priority=75)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.366+08:00","@version":"1","message":"测试单: YS8297##MP200810###031400E09T########250142_Alpha_Mars_54_Plan_18o1_64GB plan: Plan36已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.573+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.574+08:00","@version":"1","message":"此次分配flash批次 Mars_54_Plan_18o1_64GB 下的Plan共消耗 44 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:08.581+08:00","@version":"1","message":"更新Flash:Mars_54_Plan_18o1_64GB的样片数量从50变更为6","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88c343642373aa1e","spanId":"88c343642373aa1e","context":"QueueService","no":"6403","traceType":"分配设备"}
