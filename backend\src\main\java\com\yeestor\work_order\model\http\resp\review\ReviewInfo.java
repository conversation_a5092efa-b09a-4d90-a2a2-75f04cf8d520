package com.yeestor.work_order.model.http.resp.review;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.entity.review.ReviewInfoEntity;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "OrderDetailReviewInfo", description = "工单详情的发起Review的信息")
public class ReviewInfo {
    private String flash ;

    private long createdAt ;
    private String createdBy ;
    private String createdPerson ;

    private long startAt ;
    private long endAt ;
    private Map<String,String> persons;
    private String description ;

    public static ReviewInfo entityToVO(ReviewInfoEntity reviewEntity) {
        ReviewInfo reviewInfo = new ReviewInfo();
        reviewInfo.setFlash(reviewEntity.getFlash());
        reviewInfo.setCreatedAt(reviewEntity.getCreatedAt());
        reviewInfo.setCreatedBy(reviewEntity.getCreatedBy());
        reviewInfo.setCreatedPerson(reviewEntity.getCreatedPerson());
        reviewInfo.setStartAt(reviewEntity.getStartAt());
        reviewInfo.setEndAt(reviewEntity.getEndAt());
        ObjectMapper mapper = new ObjectMapper();
        try {
            Map<String,String> persons = mapper.readValue(reviewEntity.getPersonList(),
                    new TypeReference<Map<String, String>>() {
                    });

            reviewInfo.setPersons(persons);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        reviewInfo.setDescription(reviewEntity.getDescription());
        return reviewInfo;
    }
}
