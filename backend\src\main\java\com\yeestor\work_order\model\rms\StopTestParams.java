package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@ApiModel(value = "StopTestParams", description = "停止plan测试的参数")
public class StopTestParams {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "PlanDeviceIpInfo", description = "Plan及设备对应信息")
    public static class PlanDeviceIpInfo {

        @ApiModelProperty("Plan的名称")
        private String plan;

        @ApiModelProperty("mac地址列表, 与ipList二选一,如果都有,以mac为准")
        private List<String> macList ;
        @ApiModelProperty("ip 列表")
        private List<String> ipList ;

    }

    @ApiModelProperty("工单号")
    private String orderNo ;

    /**
     * 重试次数。
     */
    private int count ;

    @ApiModelProperty("plan和设备对应关系表")
    private List<PlanDeviceIpInfo> planDeviceList ;
}
