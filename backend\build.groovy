import groovy.transform.Field
import groovy.json.JsonSlurper

import java.time.LocalDate
import java.time.format.DateTimeFormatter

// 调整 此文件为 本地测试部署.


// 用当前日期来来计算版本号
// Calculate the current date components
def currentDate = LocalDate.now()
def majorVersion = currentDate.year - 2022
def minorVersion = currentDate.format(DateTimeFormatter.ofPattern("MMdd"))
def patchVersion = 1
search_url = "https://gateway.yeestor.com/nexus/service/rest/v1/search?repository=yeestor-docker&name=workorder&sort=version&version=v${majorVersion}.${minorVersion}*"
search_url = "http://gateway.yeestor.com:8081/service/rest/v1/search?repository=yeestor-docker&name=workorder&sort=version&version=v${majorVersion}.${minorVersion}*"

//request search_url
def jsonSlurper = new JsonSlurper()
def response = jsonSlurper.parseText(search_url.toURL().text)
println response

def tags = response.items.findAll { it["version"] =~ "^v$majorVersion\\.$minorVersion\\.(\\d{2})(-alpha|-beta|-rc)?\$" }
        .collect {
            (it =~ /v\d+\.\d+\.(\d+)/)[0][1] as int
        }.sort()
if (!tags.isEmpty()) {
    patchVersion = tags.last() + 1
    println("majorVersion: $majorVersion, minorVersion: $minorVersion, patchVersion: $patchVersion")
}

// 通过majorVersion, minorVersion, patchVersion 来生成 tag, 例如: v1.0525.01-alpha
def tag = "v${majorVersion}.${minorVersion}.${String.format('%02d', patchVersion)}-alpha"

def GITLAB_TAG = tag

// 检查 gitlab的 tag 是否符合规范
// tag 的格式为: v1.0525.01[-alpha|beta|rc]
// 其中alpha, beta, rc 分别代表: 内部测试版本, 公测版本, 发布候选版本 (release candidate) , 为可选项
static def checkTag(tag) {
    def pattern = /v\d+\.\d{4}\.\d{2}(-alpha|-beta|-rc)?/
    return tag ==~ pattern
}

if (!checkTag(GITLAB_TAG)) {
    println "Tag ${GITLAB_TAG} is not valid"
    System.exit(1)
}

// 输出当前目录地址
def currentWorkingDir = System.getProperty('user.dir')
println "Current working directory: $currentWorkingDir"

@Field def imageName = 'gateway.yeestor.com:5000/workorder'
@Field def namespace = "yeestor"
def workDir = "${currentWorkingDir}"


// 执行命令, 并且不返回输出,只返回 process
static def executeWithoutOutput(command) {
    def process = command.execute()
    process.consumeProcessOutput(System.out, System.err)
    process.waitFor()
    return process
}

// 执行命令, 并且返回输出
static def executeWithOutput(command) {
    def process = command.execute()
    def output = process.text
    return output
}
def dockerPrefix = "docker "
//def dockerPrefix = "docker "
executeWithoutOutput("${dockerPrefix} info ")

def newTag = GITLAB_TAG
println "New tag: ${newTag}"

// Build the new image
def buildCommand = "${dockerPrefix} build -t ${imageName}:${newTag} ${workDir}"
println "Running build command: ${buildCommand}"
def buildProcess = executeWithoutOutput(buildCommand)

// 检查build 是否成功,如果没有成功,则退出
if (buildProcess.exitValue() != 0) {
    println "Build failed"
    System.exit(1)
}


// Push the new image
def pushCommand = "${dockerPrefix} push ${imageName}:${newTag}"
println "Running push command: ${pushCommand}"
def pushProcess = executeWithoutOutput(pushCommand)

// 检查push 是否成功,如果没有成功,则退出
if (pushProcess.exitValue() != 0) {
    println "Push failed"
    System.exit(1)
}
def updateAndCheckInKubernetes(deploymentName, containerName, newTag) {

    println "imageName: ${imageName}, newTag: ${newTag}"

    def getDeploymentCmd = "kubectl get deployment ${deploymentName} -n ${namespace} -o jsonpath='{.spec.template.spec.containers[0].image}'"
    def deploymentResults = executeWithOutput(getDeploymentCmd).replaceAll("\'", "").split('\n').findAll { it.trim() != "" }
    println "debployment: ${deploymentName} imageVersion: ${deploymentResults}"
    // 如果image 没有变化, 则不需要更新
    if (deploymentResults[0] == "${imageName}:${newTag}" && !newTag.contains("-alpha")) {
        println "Image has not changed, no need to update"
        return
    }
    def updateCommand = "kubectl set image deployment/${deploymentName} ${containerName}=${imageName}:${newTag} -n ${namespace}"
    println "Running update command: ${updateCommand}"
    def updateProcess = executeWithoutOutput(updateCommand)

    // 检查update 是否成功,如果没有成功,则退出
    if (updateProcess.exitValue() != 0) {
        println "Update failed"
        System.exit(1)
    }

    def podRunningCommand = "kubectl rollout status deployment/${deploymentName} -n ${namespace}  --timeout=30s"
    def podRunningProcess = executeWithoutOutput(podRunningCommand)

// 检查pod 是否正常运行,如果没有成功,则退出
    if (podRunningProcess.exitValue() != 0) {
        System.exit(1)
    }

}

updateAndCheckInKubernetes( "wo-alpha-deployment",  "wo-alpha", newTag)

if (GITLAB_TAG.endsWith("-beta") || GITLAB_TAG.endsWith("-rc") || !GITLAB_TAG.endsWith("-alpha")) {
    updateAndCheckInKubernetes("wo-prod-deployment", "wo-prod", newTag)
}