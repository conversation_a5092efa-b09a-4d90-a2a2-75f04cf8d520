package com.yeestor.work_order.model.http.req;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.entity.analysis.OrderFailAnalysisEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.SneakyThrows;

import java.util.HashMap;

@Data
@ApiModel(value = "SaveFailAnalysisParams", description = "保存失败分析参数")
public class SaveFailAnalysisParams {

    @ApiModelProperty(value = "完成fail 类型，0表示完成，1表示跳过", required = true)
    private int type ;

    @ApiModelProperty(value = "原因 , 和data 二选一并填")
    private String msg ;

    @ApiModelProperty(value = "Flash批次中的 测试项目和原因的键值对, key 为Flash批次， value 为测试项目和原因的键值对。")
    private HashMap<String, HashMap<String, String> > data ;

    @SneakyThrows
    public OrderFailAnalysisEntity toEntity(){
        OrderFailAnalysisEntity entity = new OrderFailAnalysisEntity();
        entity.setCreatedAt(System.currentTimeMillis());
        entity.setUpdatedAt(System.currentTimeMillis());
        entity.setType(type);
        if(type == 0){
            entity.setResult(new ObjectMapper().writeValueAsString(data));
        }
        else {
            entity.setResult(msg);
        }
        return entity;
    }

}
