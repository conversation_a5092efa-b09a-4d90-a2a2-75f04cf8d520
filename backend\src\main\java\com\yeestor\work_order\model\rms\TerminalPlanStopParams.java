package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("停止终端平台测试")
public class TerminalPlanStopParams {

    @ApiModelProperty("工单号")
    private String no;

    @ApiModelProperty("plan名称")
    private String plan;

    @ApiModelProperty("发起测试的用户")
    private String username;

    @ApiModelProperty("终端列表")
    private List<TerminalModel> terminalList;
}
