{"@timestamp":"2025-07-23T15:10:03.955+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:10:03.964+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:03.968+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:03.968+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.584+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:14:03.585+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.589+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.589+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.474+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:03.476+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.479+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.479+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.829+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:22:03.831+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.834+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.834+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.422+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:26:03.423+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.426+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.426+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.285+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:50:05.304+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.311+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.311+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.077+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:04.079+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.083+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.083+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.359+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:58:04.361+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.365+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.365+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.501+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:02:03.503+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.506+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.507+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.51+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:03.51+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.512+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.513+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.517+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:10:03.519+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.521+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.521+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.504+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:09.515+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.52+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.52+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.516+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:22:03.517+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.52+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.52+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.544+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:26:03.546+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.549+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.549+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.437+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:03.438+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.441+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.441+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.446+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:34:03.447+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.449+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.449+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:38:03.631+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.634+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.531+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:03.532+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.535+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.536+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.425+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:46:03.427+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.429+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.429+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.357+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:50:03.358+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.36+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.36+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:08.23+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:08.448+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:08.457+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:08.457+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.884+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:03.886+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.893+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.893+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.637+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:10:03.64+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.645+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.522+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:03.524+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.527+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.528+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.589+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:03.59+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.594+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.594+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:22:03.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.617+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.617+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.429+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:03.431+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.433+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.433+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.312+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:04.315+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.32+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.32+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.209+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:34:04.211+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.214+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.214+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.166+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:05.191+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.199+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.2+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.235+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:04.237+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.243+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.243+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.831+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:04.879+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.89+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.89+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.808+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:03.81+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.813+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.813+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.905+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:10:03.907+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.911+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.911+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:03.608+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.61+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.61+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.943+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:03.944+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.949+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.949+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.582+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:03.584+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.588+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.588+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.645+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:03.647+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.65+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.65+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.264+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5701","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:06.286+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5903, orderId=5701, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220001###0010##3D_TLC_E09T#250014_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.296+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5701","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.297+08:00","@version":"1","message":"[5701] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5701","traceType":"分配设备"}
