package com.yeestor.work_order.service.review;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.admin.api.UserFeignClient;
import com.yeestor.admin.model.UserDTO;
import com.yeestor.utils.TimeUtils;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.entity.analysis.OrderFailAnalysisEntity;
import com.yeestor.work_order.entity.review.ReviewItemEntity;
import com.yeestor.work_order.entity.review.ReviewResultEntity;
import com.yeestor.work_order.service.zentao.ZenTaoService;
import com.yeestor.work_order.utils.PathUtils;
import com.yeestor.zentao.model.ZtResolvedBug;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class ZenTaoCommentService {

    private final ZenTaoService zenTaoService;

    private final PathUtils pathUtils;

    private final UserFeignClient userFeignClient;

    /**
     * 获取基础信息
     * @param no 工单号
     * @param versionType 工单版本
     * @param flashName flash名称
     * @param url 构建访问 eSee 工单 url
     * @param fullVersion 版本号信息
     * @return 评论内容
     */
    public String getBaseInfo(String no, String versionType, String flashName, String url, String fullVersion){
        StringBuilder html = new StringBuilder();
        html.append("<div style='line-height: 24px; font-weight: 600;'>工单号：" + "<a target='_blank' href='" + url + "'>" + no + "</a>" + "</div>");

        html.append("<div style='line-height: 24px; font-weight: 600;'>版本号：" + fullVersion + "</div>");
//        html.append("<div style='line-height: 24px; font-weight: 600;'>版本类型：" + versionType + "</div>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>Flash批次：" + flashName + "</div>");

        return String.valueOf(html);
    }

    /**
     * 获取fail评论禅道内容
     * @param result fail分析内容
     * @param flashName flash名称
     * @param person Fail分析人员名称
     * @param baseHtml 基础信息
     * @return 评论内容
     */
    public String getFailComment(String result, String flashName, String person, String baseHtml){
        ObjectMapper mapper = new ObjectMapper();
        try {
            HashMap<String, HashMap<String, String>> info = mapper.readValue(result, HashMap.class);
            HashMap<String, String> resultInfo = info.get(flashName);
            StringBuilder html = new StringBuilder();

            html.append("<div style='line-height: 36px; font-weight: 600; font-size: 20px; color: #831843;'>Fail分析内容</div>");
            html.append("<div style='font-size: 14px; padding: 0 12px;'>");

            html.append("<div style='line-height: 24px; font-weight: 600;'>Fail分析人员：" + person + "</div>");
            html.append("<div style='line-height: 24px; font-weight: 600;'>类型：发起fail分析</div>");
            html.append("<div style='line-height: 24px; font-weight: 600;'>时间：" + TimeUtils.formatTimestamp(System.currentTimeMillis()) + "</div>");

            html.append(baseHtml);

            html.append("</div>");
            // 创建表格标记
            html.append("<table style='border-collapse: collapse; border: 1px solid #999; margin: 0 12px;'>");

            // 创建表头
            html.append("<thead><tr>");
            html.append("<th style='border: 1px solid #999; padding: 4px 12px; text-align: left; font-size: 15px; line-height: 28px; background-color: #D0D0D1;'>测试项目</th>");
            html.append("<th style='border: 1px solid #999; padding: 4px 12px; text-align: left; font-size: 15px; line-height: 28px; background-color: #D0D0D1;'>分析结论</th>");
            html.append("</tr></thead>");

            // 创建表格内容
            html.append("<tbody>");

            // keySet()方法获取所有键的集合
            for (String key : resultInfo.keySet()) {
                html.append("<tr>");
                html.append("<td style='border: 1px solid #999; width: 200px; padding: 4px 12px;'>" + key + "</a></td>");
                html.append("<td style='border: 1px solid #999; width: 320px; padding: 4px 12px;'>" + resultInfo.get(key) + "</td>");
                html.append("</tr>");
            }
            html.append("</tbody>");

            // 关闭表格标记
            html.append("</table>");
            return String.valueOf(html);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取跳过fail时的禅道评论内容
     * @param person Fail分析人员名称
     * @param baseHtml 基础信息
     * @param result fail分析内容
     * @return 评论内容
     */
    public String getSkipFailComment(String person, String baseHtml, String result) {
        StringBuilder html = new StringBuilder();

        html.append("<div style='line-height: 36px; font-weight: 600; font-size: 20px; color: #831843;'>Fail分析内容</div>");
        html.append("<div style='font-size: 14px; padding: 0 12px;'>");

        html.append("<div style='line-height: 24px; font-weight: 600;'>Fail分析人员：" + person + "</div>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>类型：跳过fail分析</div>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>时间：" + TimeUtils.formatTimestamp(System.currentTimeMillis()) + "</div>");
        html.append(baseHtml);
        html.append("<div style='line-height: 24px; font-weight: 600; width: 620px;'>跳过原因：" + result + "</div>");

        html.append("</div>");
        return String.valueOf(html);
    }

    /**
     * 填写禅道fail分析信息
     * @param orderDetailEntity 工单构建信息
     * @param orderEntity 工单信息
     * @param flashEntity flash信息
     * @param failInfo fail信息
     * @param bindList 绑定bug信息列表
     */
    public void saveZenTaoFailComment(
            OrderDetailEntity orderDetailEntity,
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderFailAnalysisEntity failInfo,
            List<OrderBugEntity> bindList
    ){
        String eSeeUrl = pathUtils.getFlashPageUrl(orderEntity.getProduct(), orderEntity.getSubProduct(), orderEntity.getId(), flashEntity.getFlash());
        log.info("eSeeUrl: {}", eSeeUrl);
        String versionType = flashEntity.getMarkVersion();      // 此处用flash标记的版本类型是因为可能fail分析过程中，flash的版本类型和order的版本类型不一致
        String baseHtml = getBaseInfo(orderEntity.getNo(), versionType, flashEntity.getFlash(), eSeeUrl, orderEntity.getFullVersion());
        String html;
        if (failInfo.getType() == 0) {
            UserDTO user = userFeignClient.findUserByDingTalkId(flashEntity.getFailAssignTo()).getData();
            html = getFailComment(failInfo.getResult(), flashEntity.getFlash(), user.getUsername(), baseHtml);
        } else {
            html = getSkipFailComment(failInfo.getCreatedPerson(), baseHtml, failInfo.getResult());
        }
        log.info("getFailComment html: {}!", html);

        String testTaskId = orderDetailEntity.getZentaoTestTask();

        // 如果禅道测试单不为空，则将数据填写值测试单中
        if(testTaskId != null && !testTaskId.equals("")){
            log.info("Fail Comment html to testTask： {}", testTaskId);
            // 请求参数
            HashMap<String, String> params = new HashMap<>();
            params.put("comment", html);
            zenTaoService.updateTestTask(testTaskId, params);
        }
        // 如果测试单为空，但是禅道 bug 信息不为空， 这分别把这部分数据都绑定到这些bug中
        if((testTaskId == null|| testTaskId.equals("")) && bindList.size() > 0){
            log.info("Fail Comment html to Bugs：{}", bindList);
            bindList.forEach(bug -> {
                log.info("bug info: {}", bug.getBugId());
                ZtResolvedBug ztBugInfo = new ZtResolvedBug();
                ztBugInfo.setComment(html);
                zenTaoService.updateBugInfo(bug.getBugId(), ztBugInfo);
            });
        }
    }

    /**
     * 获取review check表信息
     * @param reviewItemList check list信息
     * @return 表格信息
     */
    public String getReviewItemHtml(List<ReviewItemEntity> reviewItemList) {
        StringBuilder html = new StringBuilder();

        html.append("</div>");
        // 创建表格标记
        html.append("<table style='border-collapse: collapse; border: 1px solid #999; margin: 0 12px; font-size: 13px;'>");

        // 创建表头
        html.append("<thead><tr>");
        html.append("<th style='border: 1px solid #999; padding: 4px 12px; text-align: left; font-size: 15px; line-height: 28px; background-color: #D0D0D1;'>测试项</th>");
        html.append("<th style='border: 1px solid #999; padding: 4px 12px; text-align: left; font-size: 15px; line-height: 28px; background-color: #D0D0D1;'>评审结果</th>");
        html.append("<th style='border: 1px solid #999; padding: 4px 12px; text-align: left; font-size: 15px; line-height: 28px; background-color: #D0D0D1;'>备注</th>");
        html.append("</tr></thead>");

        // 创建表格内容
        html.append("<tbody>");
        reviewItemList.forEach(p -> {
            html.append("<tr>");
            html.append("<td style='border: 1px solid #999; width: 140px; padding: 4px 12px;'>" + p.getItem() + "</a></td>");
            html.append("<td style='border: 1px solid #999; width: 88px; padding: 4px 12px;'>" + p.getResult() + "</a></td>");
            html.append("<td style='border: 1px solid #999; width: 360px; padding: 4px 12px;'>" + p.getRemark() + "</a></td>");
            html.append("</tr>");
        });
        html.append("</tbody>");

        // 关闭表格标记
        html.append("</table>");



        return String.valueOf(html);
    }

    /**
     * 获取跳过review时的HTML信息
     * @param person 执行人员
     * @param baseHtml 基础信息Html
     * @param versionPath 版本发布路径
     * @param result review结论
     * @param remark 评审记录
     * @return HTML字符串
     */
    public String skipReviewComment(String person, String baseHtml, String result, String versionPath, String remark){
        StringBuilder html = new StringBuilder();

        html.append("<div style='line-height: 36px; font-weight: 600; font-size: 20px; color: #831843;'>Review内容</div>");
        html.append("<div style='font-size: 14px; padding: 0 12px;'>");

        html.append(baseHtml);

        html.append("<div style='line-height: 24px; font-weight: 600;'>执行人员：" + person + "</div>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>时间：" + TimeUtils.formatTimestamp(System.currentTimeMillis()) + "</div>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>方式：跳过Review</div>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>review结论：" + result + "</div>");
        if(result.equals("通过")){
            html.append("<div style='line-height: 24px; font-weight: 600;'>版本发布路径：" + versionPath + "</div>");
        }
        html.append("<div style='line-height: 24px; font-weight: 600; margin-bottom: 8px;'>评审结论：" + remark + "</div>");

        html.append("</div>");
        return String.valueOf(html);
    }

    /**
     * 获取发起review时的HTML信息
     * @param person 完成review人员
     * @param result review结论
     * @param baseHtml 基础信息Html
     * @param checkHtml check表信息
     * @param versionPath 版本发布路径
     * @param remark 评审记录
     * @param isReviewing 是否正在review
     * @return HTML字符串
     */
    public String reviewComment(String person, String result, String baseHtml, String checkHtml, String versionPath, String remark, boolean isReviewing){
        StringBuilder html = new StringBuilder();

        html.append("<div style='line-height: 36px; font-weight: 600; font-size: 20px; color: #831843;'>Review内容</div>");
        html.append("<div style='font-size: 14px; padding: 0 12px;'>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>方式：" + (isReviewing ? "发起Review" : "中途评审") + "</div>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>执行人员：" + person + "</div>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>时间：" + TimeUtils.formatTimestamp(System.currentTimeMillis()) + "</div>");
        html.append(baseHtml);

        html.append("<div style='line-height: 24px; font-weight: 600;'>review结论：" + result + "</div>");
        html.append("<div style='line-height: 24px; font-weight: 600;'>版本发布路径：" + versionPath + "</div>");
        html.append("<div style='line-height: 24px; font-weight: 600; margin-bottom: 8px;'>评审结论：" + remark + "</div>");

        html.append("</div>");

        html.append(checkHtml);

        return String.valueOf(html);
    }

    /**
     * 不发起review， 填写禅道review信息
     * @param orderDetailEntity 工单构建信息
     * @param orderEntity 工单信息
     * @param flashName flash名称
     * @param reviewResultEntity 批次review结果
     * @param bindList 绑定bug信息
     */
    public void saveSkipReviewComment(
            OrderDetailEntity orderDetailEntity,
            WorkOrderEntity orderEntity,
            String flashName,
            ReviewResultEntity reviewResultEntity,
            List<OrderBugEntity> bindList
    ){
        long orderId = orderDetailEntity.getOrderId();
        String eSeeUrl = pathUtils.getFlashPageUrl(orderEntity.getProduct(), orderEntity.getSubProduct(), orderId, flashName);
        String versionType = reviewResultEntity.getVersionType();
        String baseHtml = getBaseInfo(orderEntity.getNo(), versionType, flashName, eSeeUrl, orderEntity.getFullVersion());

        String commentHtml = "";
        String result = reviewResultEntity.getResult() ? "通过" : "不通过";
        String path = reviewResultEntity.getPath();
        String remark = reviewResultEntity.getRemark();

        String person = reviewResultEntity.getCreatedPerson();
        log.info("工单：{}，flash：{}，跳过review", orderId, flashName);
        commentHtml = skipReviewComment(person, baseHtml, result, path, remark);

        log.info("commentHtml: {}", commentHtml);

        String testTaskId = orderDetailEntity.getZentaoTestTask();

        save(testTaskId, commentHtml, bindList);
    }

    /**
     * 发起review时，填写禅道review信息
     * @param orderDetailEntity 工单构建信息
     * @param orderEntity 工单信息
     * @param flashEntity flash信息
     * @param reviewResultEntity 批次review结果
     * @param reviewItemList review check表信息
     * @param bindList 绑定bug信息
     */
    public void saveReviewComment(
            OrderDetailEntity orderDetailEntity,
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            ReviewResultEntity reviewResultEntity,
            List<ReviewItemEntity> reviewItemList,
            List<OrderBugEntity> bindList
    ){
        long orderId = orderDetailEntity.getOrderId();
        String eSeeUrl = pathUtils.getFlashPageUrl(orderEntity.getProduct(), orderEntity.getSubProduct(), orderId, flashEntity.getFlash());
        String versionType = reviewResultEntity.getVersionType();
        String baseHtml = getBaseInfo(orderEntity.getNo(), versionType, flashEntity.getFlash(), eSeeUrl, orderEntity.getFullVersion());

        String commentHtml = "";

        String result = reviewResultEntity.getResult() ? "通过" : "不通过";
        String path = reviewResultEntity.getPath();
        String remark = reviewResultEntity.getConclusion();
        String person = reviewResultEntity.getCreatedPerson();
        log.info("工单: {}, flash: {}, 状态: {} 正常进行review", orderId, flashEntity.getFlash(), flashEntity.getStatus());
        // review check 信息表
        String checkHtml = getReviewItemHtml(reviewItemList);
        boolean isReviewing = flashEntity.getStatus() == OrderFlashEntity.Status.REVIEW_STARTED || flashEntity.getStatus() == OrderFlashEntity.Status.COMPLETED;
        commentHtml = reviewComment(person, result, baseHtml, checkHtml, path, remark, isReviewing);
        log.info("commentHtml: {}", commentHtml);
        String testTaskId = orderDetailEntity.getZentaoTestTask();
        save(testTaskId, commentHtml, bindList);

    }

    /**
     * 根据测试单或者绑定bug信息填写
     * @param testTaskId 测试单id
     * @param html 填写的Html
     * @param bindList 绑定bug信息
     */
    public void save(String testTaskId, String html, List<OrderBugEntity> bindList){

        // 如果禅道测试单不为空，则将数据填写值测试单中
        if(testTaskId != null && !"".equals(testTaskId)){
            log.info("review Comment html to testTask： {}", testTaskId);
            // 请求参数
            HashMap<String, String> params = new HashMap<>();
            params.put("comment", html);
            zenTaoService.updateTestTask(testTaskId, params);
        }
        // 如果禅道 bug 信息不为空， 这分别把这部分数据都绑定到这些bug中
        if (bindList.size() > 0) {
            log.info("review Comment html to Bugs：{}", bindList);
            for (OrderBugEntity bug : bindList) {
                log.info("bug info: {}", bug.getBugId());
                ZtResolvedBug ztBugInfo = new ZtResolvedBug();
                ztBugInfo.setComment(html);
                zenTaoService.updateBugInfo(bug.getBugId(), ztBugInfo);
            }
        }
    }

}
