package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@JsonAutoDetect
@ApiModel(value = "CaseModel", description = "Case 的信息")
@NoArgsConstructor
@AllArgsConstructor
public class CaseModel {

    @ApiModelProperty("case名称")
    private String caseName;

    @ApiModelProperty(value = "case 的标题")
    private String title;

    @ApiModelProperty("case 测试的功能")
    private String feature;

    @ApiModelProperty("case所具备的属性")
    private List<String> attrs;

    @ApiModelProperty("所属产品线")
    private String product;

    @ApiModelProperty("所属产品")
    private String subProduct;
}
