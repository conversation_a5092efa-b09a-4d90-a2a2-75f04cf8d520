///<reference types="cypress" />

// context('with plan operate', () => {
//   describe('plan-operate', () => {
//     // 需要操作的工单id
//     const orderId = 363;
//     const flashName = 'flashname01_32GB';
//     const planName = 'Plan39';
//     beforeEach(() => {
//       cy.viewport(1920, 960);
//       cy.visit(`http://172.18.11.245:8512/work_order/GE/SD/${orderId}`).wait(5000);
//     });

//     it('Plan暂缓分配测试', () => {
//       cy.get('#flash-tab-' + flashName)
//         .click()
//         .wait(1000);
//       suspendPlan(planName);
//       apartmentPlan(planName);
//     });
//   });
// });

const suspendPlan = function (name: string) {
  cy.get(`a#plan-operate-menu-${name}`)
    .click()
    .get(`button#plan-suspend-${name}`)
    .click()
    .wait(30000);

  // 验证是否暂缓分配成功
  cy.get(`a#plan-operate-menu-${name}`)
    .click()
    .get(`button#plan-apartment-${name}`)
    .contains('开始分配');
};

const apartmentPlan = function (name: string) {
  cy.get(`a#plan-operate-menu-${name}`)
    .click()
    .get(`button#plan-apartment-${name}`)
    .click()
    .wait(30000);

  // 验证是否暂缓分配成功
  cy.get(`a#plan-operate-menu-${name}`)
    .click()
    .get(`button#plan-suspend-${name}`)
    .contains('暂缓分配');
};

export { suspendPlan, apartmentPlan };
