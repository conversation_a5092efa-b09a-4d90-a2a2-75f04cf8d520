package com.yeestor.work_order.entity.device;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity(name = "DeviceControl")
@Table(name = "wo_device_control")
public class DeviceControlEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "created_at")
    private Long createdAt;

    @Column(name = "order_id")
    private long orderId;

    @Column(name = "flash")
    private String flash;

    @Column(name = "plan_id")
    private long planId;

    @Column(name = "device_id")
    private long deviceId;

    @Column(name = "mac")
    private String mac;

    @Column(name = "ip")
    private String ip;

    @Column(name = "no")
    private String no;

    /**
     * 标题，用来简单描述这个历史主要记录了什么内容.
     * 1、Plan确认环境后电脑开机
     * 2、Plan添加电脑后电脑开机
     * 3、Plan停止测试后电脑关机
     * 4、Flash取消测试后电脑关机
     * 5、Plan暂缓分配后电脑关机
     * 6、手动释放设备后电脑关机
     * 7、重新选择电脑后电脑关机
     * 8、重新选择电脑后电脑开机
     * 9、电脑自动释放后电脑关机
     */
    @Column(name = "title")
    private String title;

    @Column(name = "operator")
    private String operator;

    /**
     * 操作失败的原因
     */
    @Column(name = "reason")
    private String reason;
}
