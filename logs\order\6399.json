{"@timestamp":"2025-07-24T16:12:03.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6399","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:12:03.697+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6639, orderId=6399, flash=HB-6285ENAB-0T26-A_128GB, orderFlashNo=YS6285##MP#########16698#0T26-DDR_TLC250425_Alpha_HB-6285ENAB-0T26-A_128GB, num=60, leftNum=60)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.716+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.716+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:03.716+08:00","@version":"1","message":"[6399] - [HB-6285ENAB-0T26-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78b370358d1debca","spanId":"78b370358d1debca","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.779+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6399","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:15:02.781+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6639, orderId=6399, flash=HB-6285ENAB-0T26-A_128GB, orderFlashNo=YS6285##MP#########16698#0T26-DDR_TLC250425_Alpha_HB-6285ENAB-0T26-A_128GB, num=60, leftNum=60)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.8+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.8+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.8+08:00","@version":"1","message":"[6399] - [HB-6285ENAB-0T26-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e21f3f3fb47453f","spanId":"5e21f3f3fb47453f","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.899+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6399","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.902+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6639, orderId=6399, flash=HB-6285ENAB-0T26-A_128GB, orderFlashNo=YS6285##MP#########16698#0T26-DDR_TLC250425_Alpha_HB-6285ENAB-0T26-A_128GB, num=60, leftNum=60)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.921+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.921+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.921+08:00","@version":"1","message":"[6399] - [HB-6285ENAB-0T26-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ee636d8d5ac86e6","spanId":"0ee636d8d5ac86e6","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.77+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6399","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:21:02.77+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6639, orderId=6399, flash=HB-6285ENAB-0T26-A_128GB, orderFlashNo=YS6285##MP#########16698#0T26-DDR_TLC250425_Alpha_HB-6285ENAB-0T26-A_128GB, num=60, leftNum=60)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.79+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.791+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.792+08:00","@version":"1","message":"[6399] - [HB-6285ENAB-0T26-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4feebca05e2be8b","spanId":"c4feebca05e2be8b","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.779+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6399","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:27:02.781+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6639, orderId=6399, flash=HB-6285ENAB-0T26-A_128GB, orderFlashNo=YS6285##MP#########16698#0T26-DDR_TLC250425_Alpha_HB-6285ENAB-0T26-A_128GB, num=60, leftNum=60)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.799+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.799+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.799+08:00","@version":"1","message":"[6399] - [HB-6285ENAB-0T26-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9c96e0a86e90cdf","spanId":"c9c96e0a86e90cdf","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.455+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6399","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.477+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6639, orderId=6399, flash=HB-6285ENAB-0T26-A_128GB, orderFlashNo=YS6285##MP#########16698#0T26-DDR_TLC250425_Alpha_HB-6285ENAB-0T26-A_128GB, num=60, leftNum=60)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.506+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.507+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113862, orderId=6399, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6399","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.507+08:00","@version":"1","message":"[6399] - [HB-6285ENAB-0T26-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"538474f7564f502d","spanId":"538474f7564f502d","context":"QueueService","no":"6399","traceType":"分配设备"}
