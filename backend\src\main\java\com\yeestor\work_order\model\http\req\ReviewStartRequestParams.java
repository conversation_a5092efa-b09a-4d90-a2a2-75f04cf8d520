package com.yeestor.work_order.model.http.req;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.entity.review.ReviewInfoEntity;
import com.yeestor.work_order.entity.review.ReviewResultEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.SneakyThrows;

import java.util.Map;

@Data
@ApiModel(value = "ReviewStartRequestParams", description = "发起Review 需要的参数")
public class ReviewStartRequestParams {

    @ApiModelProperty("发起review类型，0表示发起review，1表示不发起review")
    private int type ;

    @ApiModelProperty("当且仅当type为1时生效。review 通过与否，true表示review通过，false表示review不通过")
    private Boolean result ;

    @ApiModelProperty("测试小结.")
    private String msg ;

    @ApiModelProperty("当且仅当type为0时生效。参加Review的人员列表")
    private Map<String,String> userIdList ;

    @ApiModelProperty("钉钉会议的ID")
    private String meetingId ;

    @ApiModelProperty("当且仅当type为0时生效。预计会议起始时间")
    private Long startAt ;

    @ApiModelProperty("当且仅当type为0时生效。预计会议起始时间")
    private Long endAt ;

    @ApiModelProperty("版本外发路径.")
    private String path ;



    @SneakyThrows
    public ReviewInfoEntity toInfoEntity(){
    	assert type == 0;

        ObjectMapper mapper = new ObjectMapper();
        ReviewInfoEntity entity = new ReviewInfoEntity();

        entity.setDescription(msg);
    	entity.setStartAt(startAt);
    	entity.setEndAt(endAt);
        entity.setMeetingId(meetingId);
        entity.setPersonList(mapper.writeValueAsString(userIdList));

        entity.setCreatedAt(System.currentTimeMillis());
    	return entity;
    }

    public ReviewResultEntity toResultEntity(){
    	assert type == 1;
        ReviewResultEntity entity = new ReviewResultEntity();
        entity.setType(ReviewResultEntity.TYPE_SKIP_REVIEW);
        entity.setResult(result);
        entity.setRemark(msg);
        entity.setPath(path);
        entity.setCreatedAt(System.currentTimeMillis());
        return entity;
    }


}
