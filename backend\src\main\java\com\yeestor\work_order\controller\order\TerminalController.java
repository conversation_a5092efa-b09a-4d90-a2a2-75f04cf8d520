package com.yeestor.work_order.controller.order;

import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.req.order.AddPlanParams;
import com.yeestor.work_order.model.http.req.order.OrderFlashConfirmV2Req;
import com.yeestor.work_order.model.http.req.terminal.*;
import com.yeestor.work_order.model.http.resp.order.PlanDetailResp;
import com.yeestor.work_order.model.http.resp.order.PlanDetailVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.model.rms.*;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.repository.PlanPlatformRepository;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.plan.TerminalService;
import com.yeestor.work_order.service.role.RoleService;
import com.yeestor.work_order.utils.LogUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import springfox.documentation.annotations.ApiIgnore;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/terminal/")
@Api(tags = {"Plan"}, value = "Plan 相关接口。主要包含测试plan ，释放plan平台等")
public class TerminalController {
    private final OrderService orderService;
    private final RoleService roleService;
    private final TerminalService terminalService;
    private final FlashService flashService;
    private final OrderPlanRepository planRepository;
    private final PlanService planService;
    private final PlanPlatformRepository planPlatformRepository;
    private final DataChangeListener dataChangeListener;

    @GetMapping("/case/list")
    @ApiOperation(value = "获取终端测试Plan的相关Case信息")
    public HandleResp<List<CaseModel>> fetchCaseList(
            @RequestParam(value = "p") String product,
            @RequestParam(value = "sp") String subProduct
    ) {
        log.info("fetchCaseList product: {}", product);
        return HandleResp.ok(terminalService.fetchTerminalCaseList(product, subProduct), "获取Case列表成功");
    }

    @GetMapping("/plan/model/list")
    @ApiOperation(value = "获取产品线下的Plan信息")
    public HandleResp<List<TerminalPlanModel>> fetchPlanModelList(
            @RequestParam(value = "p") String product,
            @RequestParam(value = "sp") String subProduct,
            @RequestParam(value = "tool") String tool
    ) {
        return HandleResp.ok(terminalService.fetchTerminalPlanList(product, subProduct, tool), "获取Plan模型列表成功");
    }

    @GetMapping("/plan/group/model/list")
    @ApiOperation(value = "获取产品线下的Plan集合信息")
    public HandleResp<List<TerminalPlanGroupModel>> fetchPlanModelGroups(
            @RequestParam(value = "p") String product,
            @RequestParam(value = "sp") String subProduct,
            @RequestParam(value = "tool") String tool
    ) {
        return HandleResp.ok(terminalService.fetchTerminalPlanGroups(product, subProduct, tool), "获取Plan模型组成功");
    }

    @SneakyThrows
    @ApiOperation(value = "使用EventSource监听Plan详细信息.", notes = "使用EventSource监听Plan详细信息.")
    @GetMapping(value = "/plan/{orderId}/{flash}/{planId}/detail",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter fetchPlanDetailInfo(
            @PathVariable("orderId") @ApiParam("order ID") Long orderId,
            @ApiParam(value= "Flash批次") @PathVariable("flash") String flash,
            @PathVariable("planId") @ApiParam("Plan ID") Long planId
    ){
        SseEmitter sseEmitter = new SseEmitter(-1L);
        dataChangeListener.addEmitter(
                DataChangeListener.EmitterType.Plan,
                planId,
                sseEmitter
        );
        Consumer<Throwable> consumer = e -> dataChangeListener.removeEmitter(
                DataChangeListener.EmitterType.Plan,
                planId,
                sseEmitter
        );
        sseEmitter.onTimeout(()-> consumer.accept(null));
        sseEmitter.onCompletion(()-> consumer.accept(null));
        sseEmitter.onError(consumer);

        new Thread(()->{
            WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
            OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
            OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
            PlanDetailVO planDetail = terminalService.fetchTerminalPlanInfo(planEntity);

            PlanDetailResp resp = PlanDetailResp.convertToSimple(orderEntity, planDetail);
            resp.setFlashStatus(flashEntity.getStatus());
            try {
                sseEmitter.send(SseEmitter.event()
                        .name("plan")
                        .id(String.valueOf(System.currentTimeMillis()))
                        .data(resp, MediaType.APPLICATION_JSON)
                );
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }).start();
        return sseEmitter ;

    }

    @Transactional
    @ApiOperation(value = "给指定工单下的指定Flash批次添加plan.", notes = "给指定工单下的指定Flash批次添加plan,此plan不能是flash批次中已有的plan.")
    @PostMapping("/plan/add")
    public HandleResp<String> addPlanToFlash(
            @RequestBody @Valid AddPlanParams body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "添加终端测试plan";
        long orderId = body.getOrderId();
        String flashName = body.getFlash();
        LogUtils.setOrderAndFlashTracePoint(orderId, body.getFlash(), operate);
        List<OrderFlashConfirmV2Req.PlanItem> planList = body.getPlanList();
        log.info("add terminal plan to flash, orderId: {}, flash: {}, plan: {}", orderId, flashName, planList);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flashName);

        if (flashEntity.getStatus().ordinal() > OrderFlashEntity.Status.IN_PROGRESS.ordinal()) {
            return HandleResp.failed("当前Flash批次测试已结束，不允许添加测试Plan！");
        }

        List<TerminalPlanModel> planModelList = terminalService.fetchTerminalPlanList(orderEntity.getProduct(), orderEntity.getSubProduct(), "");
        // 判断当前Plan是否已存在在Flash中和是否存在当前Plan名称
        for (OrderFlashConfirmV2Req.PlanItem planItem : planList) {
            String plan = planItem.getName();
            if (planRepository.existsByOrderIdAndFlashAndName(orderId, flashName, plan)) {
                throw new DataNotFoundException("plan:[" + plan + "]已存在");
            }

            if (planModelList.stream().noneMatch(planModel -> planModel.getName().equals(plan))) {
                throw new DataNotFoundException("RMS中找不到对应的plan:[" + plan + "]!");
            }
        }
        terminalService.addTerminalPlanToFlash(userDetail, orderId, flashEntity, planList, planModelList);
        return HandleResp.ok(null, "添加成功");
    }

    @Transactional
    @ApiOperation(value = "增加临时Plan", notes = "用于增加一些临时性的Plan")
    @PostMapping("/plan/temp/add")
    public HandleResp<PlanModel> addTempPlan(
            @Valid @RequestBody AddTerminalPlanParams body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "增加临时终端Plan";
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(body.getOrderId());
        LogUtils.setOrderTracePoint(body.getOrderId(), operate);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN, Permission.START_TEST);
        TempPlanEntity tempPlanEntity = terminalService.saveTerminalTemPlan(body, orderEntity, userDetail);
        log.info("临时终端Plan: {} 添加成功!", tempPlanEntity.getName());
        OrderPlanEntity planEntity = tempPlanEntity.convert2PlanEntity("terminal", body.getPhase(), body.getFlash(), body.getAttrList());
        OrderPlanEntity result = planRepository.save(planEntity);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_ADDED)
                .orderId(body.getOrderId())
                .flash(body.getFlash())
                .planId(result.getId())
                .build()
        );

        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "添加成功!");

    }

    @Transactional
    @ApiOperation(value = "给指定的Plan添加平台.", notes = "给指定的Plan添加平台.")
    @PostMapping("/plan/{planId}/platform/add")
    public HandleResp<String> addPlatform(
            @PathVariable("planId") @ApiParam("Plan id") @Min(value = 1, message = "无效的Plan ID") long planId,
            @RequestBody @Valid AddPlatformParams body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        log.info("addPlatform AddPlatformParams: {}", body);
        String operate = "Plan添加平台";
        long orderId = body.getOrderId();
        String flashName = body.getFlash();
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        LogUtils.setOrderAndFlashTracePoint(orderId, flashName, operate);
        log.info("{} add platforms {} to {}-{}:", userDetail.getUsername(), body, planEntity.getName(), planId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flashName);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN);
        if (
                planEntity.getStatus() == OrderPlanEntity.Status.STOPPED ||
                        planEntity.getStatus() == OrderPlanEntity.Status.NEW ||
                        planEntity.getStatus() == OrderPlanEntity.Status.QUEUE
        ) {
            return HandleResp.failed(planEntity.getStatus() + "状态下的Plan 不能添加设备!");
        }

        // 检查当前平台是否被其他Plan占用
        for (PlatformInfo numInfo : body.getPlatformList()) {
            terminalService.checkInsertPlatform(numInfo.getMac(), numInfo.getNumber());
        }

        terminalService.addPlatformToPlan(orderEntity, flashEntity, planEntity, body.getPlatformList(), userDetail);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.DEVICE_ADDED)
                .orderId(body.getOrderId())
                .flash(body.getFlash())
                .planId(body.getPlanId())
                .build()
        );
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "平台添加成功");
    }

    @Transactional
    @PostMapping("/plan/{planId}/release")
    @ApiOperation(value = "释放某个plan下的所有的平台", notes = "释放某个plan下的所有的平台，只有当所有的平台都处于测试完成的状态下，才能释放")
    public HandleResp<String> releasePlanAllDevice(
            @PathVariable("planId") @ApiParam("Plan id") long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "释放Plan下的所有手机平台";
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(), planEntity.getFlash(), operate);
        // 获取 工单实体
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN);
        // 获取 对应Flash 批次的实体
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());
        assert flashEntity != null;

        List<PlanPlatformEntity> platformEntityList = planPlatformRepository.findAllByPlanId(planId);
        for (PlanPlatformEntity entity : platformEntityList) {
            terminalService.checkPlatformHasUsed(entity.getMac(), entity.getNumber(), planId);
        }
        terminalService.releasePlanPlatform(flashEntity, platformEntityList, userDetail);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_RELEASE_ALL)
                .orderId(planEntity.getOrderId())
                .flash(planEntity.getFlash())
                .planId(planEntity.getId())
                .build());
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "释放成功");
    }

    @PostMapping("/plan/{orderId}/{planId}/start")
    @ApiOperation(value = "启动某个工单下的某个Plan的测试", notes = "测试人员在某个，进行此操作需要相应的权限")
    public HandleResp<String> startTestPlan(
            @PathVariable("orderId") @ApiParam("工单id") long orderId,
            @PathVariable("planId") @ApiParam("Plan id") long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "启动终端Plan测试";
        LogUtils.setOrderTracePoint(orderId, operate);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN, Permission.START_TEST);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);

        // 如果plan 已经启动，则不需要再次启动
        if (planEntity.getStatus() == OrderPlanEntity.Status.RUNNING) {
            return HandleResp.ok(null, "终端Plan已经启动，不需要再次启动");
        }

        // 获取 对应的Flash批次。
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, planEntity.getFlash());
        List<PlanPlatformEntity> platformEntityList = planPlatformRepository.findAllByPlanId(planId);

        terminalService.startTestTerminalPlan(orderEntity, flashEntity, planEntity, platformEntityList, userDetail);
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "Plan启动成功！");
    }

    @PostMapping("/plan/{planId}/stop")
    @ApiOperation(value = "取消某个plan 的测试", notes = "取消某个plan的测试")
    public HandleResp<String> stopPlan(
            @PathVariable("planId") @ApiParam("Plan id") long planId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "取消终端Plan测试";
        // 获取 plan 实体
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        if (planEntity.getStatus().greaterThan(OrderPlanEntity.Status.COMPLETED)) {
            throw new IllegalArgumentException("当前 " + planEntity.getName() + " 状态为: " + planEntity.getStatus() + " 不允许取消测试！");
        }

        List<PlanPlatformEntity> platformEntityList = planPlatformRepository.findAllByPlanId(planId);
        List<PlanPlatformEntity> waitHandleList = platformEntityList.stream().filter(platform -> {
            PlanPlatformEntity.Status status = platform.getStatus();
            return status == PlanPlatformEntity.Status.RUNNING || status == PlanPlatformEntity.Status.OCCUPIED || (status == PlanPlatformEntity.Status.FINISHED_FAILED && platform.getReleaseAt() == null);
        }).collect(Collectors.toList());
//        for (PlanPlatformEntity entity : waitHandleList) {
//            terminalService.checkPlatformHasUsed(entity.getMac(), entity.getNumber(), planId);
//        }

        // 获取 工单实体
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        LogUtils.setOrderAndFlashTracePoint(orderEntity.getId(), planEntity.getFlash(), operate);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.CANCEL_PLAN);

        // 获取 工单Flash 批次的实体
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());
        terminalService.stopTestTerminalPlan(orderEntity, flashEntity, planEntity, waitHandleList, userDetail);
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "取消成功");
    }

    @Transactional
    @ApiOperation(value = "已取消的Plan重新测试.", notes = "已取消的Plan重新测试.")
    @PostMapping("/plan/{planId}/retest")
    public HandleResp<String> retryCanceledPlan(
            @PathVariable("planId") @ApiParam("Plan id") long planId,
            @ApiIgnore @AuthenticationPrincipal @NotNull(message = "尚未登录") OAuthUserDetail userDetail
    ) {
        String operate = "重测已取消终端Plan";
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());

        if (flashEntity.getStatus() == OrderFlashEntity.Status.NEW
                || flashEntity.getStatus() == OrderFlashEntity.Status.WAITING_FOR_START
                || flashEntity.getStatus() == OrderFlashEntity.Status.IN_PROGRESS
        ) {
            LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(), planEntity.getFlash(), operate);
            terminalService.retryCanceledPlan(planEntity, userDetail);
            log.info("终端plan: {} 已加入测试列表中!", planEntity.getName());
            LogUtils.clearTracePoint();
            return HandleResp.ok(null, "终端Plan重测完成！");
        } else {
            return HandleResp.failed("Flash状态不支持重测Plan！");
        }
    }

    @GetMapping("/platform/list/paired")
    @ApiOperation(value = "获取匹配的测试平台列表", notes = "获取指定plan下的可以测试的平台列表")
    public HandleResp<List<TerminalDeviceModel>> fetchNeedPlanPlatform(
            @RequestParam(value = "planId") long planId
    ) {
        log.info("fetchNeedPlanPlatform, params: {}", planId);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);

        // 获取工单实体类
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        String attrs = planEntity.getAttrs().replaceAll(" ", "");
        List<String> attrList = (attrs != null && !attrs.isBlank())
                ? Arrays.asList(attrs.split(";"))
                : Collections.emptyList();
        List<TerminalDeviceModel> deviceModelList = terminalService.fetchPlatformFreeList(orderEntity.getSubProduct());
        return HandleResp.ok(terminalService.fetchPlanFreePlatformList(deviceModelList, attrList), "获取成功");
    }

    @PostMapping("/platform/release")
    @ApiOperation(value = "平台释放", notes = "释放指定工单下的指定plan的指定平台")
    public HandleResp<String> releasePlanPlatform(
            @RequestBody @Valid PlanPlatformParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "手动释放终端平台";
        LogUtils.setOrderTracePoint(params.getOrderId(), operate);
        log.info("release Platform, params: {}", params);
        if (params.getPlatformList().size() == 0) {
            return HandleResp.failed("Plan终端列表中有平台信息!");
        }
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(params.getOrderId());
        roleService.checkPermission(
                userDetail,
                orderEntity.getSubProduct(),
                operate,
                Permission.OPERATE_PLAN,
                Permission.START_TEST,
                Permission.FORCE_RELEASE_DEVICE);

        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(params.getPlanId());

        // 如果plan 已经结束，则释放流程异常
        OrderPlanEntity.Status status = planEntity.getStatus();
        if (status == OrderPlanEntity.Status.STOPPED || status == OrderPlanEntity.Status.COMPLETED) {
            return HandleResp.ok(null, "Plan已经结束，无法手动释放平台，请联系系统管理员");
        }
        // 获取 对应Flash 批次的实体
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());
        terminalService.releasePlatform(orderEntity, flashEntity, planEntity, params.getPlatformList(), userDetail);

        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "释放成功");
    }

    @PostMapping("/platform/test/start")
    @ApiOperation(value = "启动测试", notes = "测试指定plan 下的指定设备")
    public HandleResp<String> testPlatformStart(
            @RequestParam(value = "planId") long planId,
            @RequestParam(value = "number") String number,
            @RequestParam(value = "mac") String mac,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "启动测试平台";
        log.info("test start plan: {} platform, number: {} -- mac: {}", planId, number, mac);
        PlanPlatformEntity platformEntity = terminalService.fetchPlatformByIdAndMacAndNumber(planId, mac, number);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(), planEntity.getFlash(), operate);

        if (planEntity.getStatus().ordinal() > OrderPlanEntity.Status.RUNNING.ordinal()) {
            return HandleResp.failed("已完成或者取消的Plan 不能直接启动测试");
        }

        // 工单实体类
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.REPEAT_TEST);

        // 获取 对应Flash 批次的实体
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());

        //如果Plan没有启动的话， 启动Plan
        planService.updatePlanStatusToRunning(
                orderEntity,
                flashEntity,
                planEntity,
                userDetail
        );

        // 释放完成后，检查任务的状态
        log.info("用户:{} 测试平台:{}", userDetail.getUsername(), number);
        terminalService.startTestPlatform(
                orderEntity,
                flashEntity,
                planEntity,
                platformEntity,
                userDetail
        );
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "测试成功,请留意测试结果!");
    }

    @PostMapping("/platform/test/stop")
    @ApiOperation(value = "停止指定的电脑上的测试", notes = "停止指定的电脑上的测试")
    public HandleResp<String> stopPlatformTest(
            @RequestBody @Valid PlanPlatformParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        long planId = params.getPlanId();
        log.info("platform stop test, planId: {}, platformList: {}", planId, params.getPlatformList());

        if (params.getPlatformList().isEmpty()) {
            return HandleResp.failed("平台列表不能为空");
        }

        String operate = "停止测试平台";
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(params.getPlanId());

        LogUtils.setOrderTracePoint(planEntity.getOrderId(), operate);
        // 工单实体类
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.OPERATE_PLAN, Permission.START_TEST);

        // 获取 对应Flash 批次的实体
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());

        terminalService.stopTestPlatform(orderEntity, flashEntity, planEntity, params, userDetail);
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "停止成功");
    }

//    @SneakyThrows
//    @PostMapping("/device/test/reselect")
//    @ApiOperation(value = "替换测试平台信息", notes = "重新测试指定plan 下的指定设备， 可以变更为其他的设备，只有当设备已经测试完成，才能重新选择环境")
//    public HandleResp<String> restartPlatform(
//            @RequestBody @Valid PlatformReplaceParams params,
//            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
//    ) {
//        String operate = "重新选择环境";
//
//        return HandleResp.ok(null, "测试环境分配成功！");
//    }


}
