{"@timestamp":"2025-07-23T10:34:04.27+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:34:04.283+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:04.289+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:04.289+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:03.341+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:38:03.342+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:03.344+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:03.344+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.365+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:42:03.367+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.37+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.37+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:03.446+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:46:03.447+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:03.449+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:03.449+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:03.357+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:50:03.358+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:03.36+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:03.36+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:03.587+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:03.588+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:03.591+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:03.591+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:03.294+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:58:03.295+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:03.297+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:03.297+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:03.506+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:02:03.508+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:03.511+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:03.511+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:03.336+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:06:03.337+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:03.338+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:03.338+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:03.329+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:10:03.33+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:03.332+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:03.332+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:03.528+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:14:03.529+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:03.532+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:03.532+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:03.298+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:18:03.299+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:03.301+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:03.301+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:05.09+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:34:05.145+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:05.151+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:05.152+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:03.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:38:03.626+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:03.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:03.629+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:03.335+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:03.336+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:03.338+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:03.338+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:03.327+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:46:03.328+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:03.33+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:03.33+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:03.279+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:50:03.28+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:03.282+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:03.282+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:03.298+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:54:03.298+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:03.3+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:03.3+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:03.317+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:58:03.318+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:03.32+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:03.32+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:03.297+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:02:03.298+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:03.299+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:03.299+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.295+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:06:03.296+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.298+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.298+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:03.355+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:10:03.357+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:03.358+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:03.358+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:03.31+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:14:03.311+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:03.314+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:03.314+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:03.383+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:18:03.384+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:03.386+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:03.386+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:03.319+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:22:03.32+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:03.321+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:03.321+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:03.309+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:26:03.309+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:03.311+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:03.311+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:03.277+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:03.278+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:03.279+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:03.279+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:03.272+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:34:03.273+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:03.274+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:03.274+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:03.329+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:38:03.33+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:03.331+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:03.331+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:03.358+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:03.358+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:03.36+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:03.36+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:03.333+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:46:03.333+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:03.335+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:03.335+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:03.326+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:50:03.326+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:03.328+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:03.328+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:03.348+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:54:03.349+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:03.35+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:03.35+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:03.346+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:58:03.347+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:03.348+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:03.348+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:03.311+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:02:03.311+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:03.313+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:03.313+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:03.337+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:06:03.338+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:03.34+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:03.34+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:03.378+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:10:03.378+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:03.38+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:03.38+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:03.375+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:14:03.376+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:03.378+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:03.378+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:03.365+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:03.366+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:03.368+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:03.368+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:03.382+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:22:03.383+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:03.384+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:03.384+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:04.345+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:26:04.346+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:04.347+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:04.347+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:03.414+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:03.415+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:03.418+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:03.418+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:03.421+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:34:03.422+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:03.424+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:03.424+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:03.378+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:38:03.379+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:03.381+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:03.381+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:03.355+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:03.356+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:03.358+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:03.358+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:03.312+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:46:03.313+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:03.314+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:03.314+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:03.294+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:50:03.295+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:03.296+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:03.296+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:03.306+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:54:03.307+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:03.308+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:03.309+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:03.291+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:58:03.292+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:03.293+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:03.293+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:03.389+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:02:03.39+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:03.393+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:03.393+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:03.385+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:03.386+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:03.388+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:03.388+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:03.502+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:10:03.504+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:03.507+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:03.507+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:03.447+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:14:03.448+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:03.45+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:03.45+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:03.403+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:18:03.404+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:03.407+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:03.407+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:03.342+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:22:03.343+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:03.345+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:03.345+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"5648","traceType":"分配设备"}
