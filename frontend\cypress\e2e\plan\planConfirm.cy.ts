///<reference types="cypress" />

// context('with flash operate', () => {
//   describe('flash-operate', () => {
//     // 操作工单号 WO_YS6285##MPTOOL##2470##YMTC-TAS####220205
//     // 需要操作的工单id
//     const orderId = 363;
//     const flashName = 'flashname01_32GB';
//     const confirmPlanId = ['3682'];
//     beforeEach(() => {
//       cy.viewport(1920, 960);
//       cy.visit(`http://172.18.11.245:8512/work_order/GE/SD/${orderId}`).wait(5000);
//     });

//     it('点击 flash tab', () => {
//       cy.get('#flash-tab-' + flashName)
//         .click()
//         .wait(1000);
//       // confirmPlan(confirmPlanId);
//       confirmAllPlan();
//     });
//   });
// });

// 确认Plan设备
const confirmPlan = function (planList: string[]) {
  planList.forEach((id: string) => {
    cy.get(`#plan-check-plan-${id}`).click();
  });
  cy.get('#plan-assign-btn').wait(2000).click();
  cy.get('#confirm-submit-btn').click();
};

// 全选Plan并确认分配
const confirmAllPlan = function () {
  cy.get('#plan-check-plan-all').click().get('#plan-assign-btn').wait(2000).click();
  cy.get('#confirm-submit-btn').click();
};

export { confirmAllPlan, confirmPlan };
