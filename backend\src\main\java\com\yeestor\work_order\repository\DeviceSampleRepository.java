package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.DeviceSampleEntity;
import com.yeestor.work_order.model.http.resp.order.DeviceSamplesVO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface DeviceSampleRepository extends JpaRepository<DeviceSampleEntity, Long> {
    List<DeviceSampleEntity> findAllByPlanId(Long planId);

    @Query(value = "select distinct d.no from DeviceSample d  where d.orderFlashNo = :orderFlashNo and d.planName = :planName and d.deviceIp = :deviceIp " )
    List<String> findDistinctSamplesByOrderFlashAndPlan(String orderFlashNo, String planName, String deviceIp);

    List<DeviceSampleEntity> findByDeviceId(Long deviceId);

    @Query(value = "select new com.yeestor.work_order.model.http.resp.order.DeviceSamplesVO(d) from DeviceSample d where d.orderId = :orderId and d.planId = :planId and d.deviceId = :deviceId " )
    List<DeviceSamplesVO> findSamplesNoteListByOrderIdAndPlan(Long orderId, Long planId, Long deviceId);

}
