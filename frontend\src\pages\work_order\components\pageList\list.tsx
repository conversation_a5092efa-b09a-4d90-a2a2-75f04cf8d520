import { List, PageTable } from '@/pages/work_order/components';
import { useEffect, useState, useRef } from 'react';
import { useLocation, history } from 'umi';
import { getOrders } from '@/models/workorder/handle/orderList';
import HeaderTab from '@/pages/work_order/components/pageList/header';

type TableProps = {
  isReload: boolean;
  productLine: string;
  products: string[];
  product: string;
  setReload: (arg: boolean) => void;
  setProduct: (arg: string) => void;
  orderBtn: (record: any) => JSX.Element;
  importOrder?: (flashName: string, taskId: number) => void;
};

const PageList: React.FC<TableProps> = ({
  isReload,
  productLine,
  products,
  product,
  setReload,
  setProduct,
  orderBtn,
  importOrder,
}) => {
  const location: any = useLocation();
  const { query }: any = location;

  const [dataList, setDataList] = useState<any[]>([]);
  const [size, setSize] = useState<number>(10);
  const [current, setCurrent] = useState<number>(1);
  const [collation, setCollation] = useState<string>('descend');
  const [fieldName, setFieldName] = useState<string>('createdAt');

  const [typeActive, setTypeActive] = useState<string>(query?.type || '0');
  const [fuzzyVersion, setFuzzyVersion] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [totalSize, setTotalSize] = useState<number>(1);
  const [testerUid, setTesterUid] = useState<string>(query?.testerUid || '');
  const [flashName, setFlashName] = useState<string>(query?.flashName || '');
  const [builderBy, setBuilder] = useState<string>(query?.builderBy || '');
  const [chip, setChip] = useState<string>(query?.chip || '');
  const [feature, setFeature] = useState<string>(query?.feature || '');

  // 使用typeActive作为唯一的状态源，确保状态一致性
  const status: string = typeActive;

  function getList(sp: string, orderType: string) {
    setLoading(true);
    getOrders({
      p: productLine,
      sp,
      chip,
      status: orderType,
      page: current,
      size,
      direction: collation,
      fieldName,
      builderBy: builderBy || '',
      flashName: flashName || '',
      fuzzyVersion,
      testUid: testerUid || '',
      feature,
    }).then((res) => {
      setReload(false);
      setLoading(false);
      setDataList(res.data);
      setTotalSize(res.total);
    });
  }

  // 同步URL参数变化到本地状态
  useEffect(() => {
    const urlType = query?.type || '0';
    if (urlType !== typeActive) {
      setTypeActive(urlType);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query?.type]);

  useEffect(() => {
    if (product != '') {
      getList(product, status);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    size,
    current,
    collation,
    fieldName,
    fuzzyVersion,
    product,
    status,
    testerUid,
    flashName,
    builderBy,
    chip,
    feature,
  ]);

  // 智能URL更新：只在用户操作时更新，避免初始化时的循环
  const updateUrlIfNeeded = () => {
    const currentQuery = new URLSearchParams(location.search);
    const newQuery = {
      type: typeActive,
      product,
      testerUid: testerUid || '',
      flashName: flashName || '',
      builderBy: builderBy || '',
      chip: chip || '',
      feature: feature || '',
    };

    // 检查是否真的需要更新URL
    const needsUpdate = Object.entries(newQuery).some(([key, value]) => {
      const currentValue = currentQuery.get(key) || '';
      return currentValue !== value;
    });

    if (needsUpdate) {
      history.replace({
        pathname: `/work_order/${productLine}/list`,
        query: newQuery,
      });
    }
  };

  // 使用useRef来跟踪是否是初始化
  const isInitialMount = useRef(true);

  useEffect(() => {
    // 跳过初始挂载时的URL更新，避免循环
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // 只有在非初始化时才更新URL
    updateUrlIfNeeded();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product, productLine, typeActive, testerUid, flashName, builderBy, chip, feature]);

  useEffect(() => {
    if (isReload) {
      getList(product, status);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isReload]);

  const TableList = (listColumns: any[]) => (
    <List
      columns={listColumns}
      data={dataList}
      totalSize={totalSize}
      setSize={setSize}
      size={size}
      current={current}
      setCurrent={setCurrent}
      loading={loading}
      setCollation={setCollation}
      setFieldName={setFieldName}
      fuzzyVersion={fuzzyVersion}
      setFuzzyVersion={setFuzzyVersion}
    />
  );

  return (
    <>
      <HeaderTab
        productLine={productLine}
        products={products}
        typeActive={typeActive}
        product={product}
        chip={chip}
        flashName={flashName}
        builderBy={builderBy}
        testerUid={testerUid}
        feature={feature}
        setTesterUid={setTesterUid}
        setProduct={setProduct}
        setTypeActive={setTypeActive}
        importOrder={importOrder}
        setFlashName={setFlashName}
        setBuilder={setBuilder}
        setChip={setChip}
        setFeature={setFeature}
        reload={() => {
          getList(product, status);
        }}
      />

      <PageTable typeActive={typeActive} orderBtn={orderBtn} TableList={TableList} />
    </>
  );
};
export default PageList;
