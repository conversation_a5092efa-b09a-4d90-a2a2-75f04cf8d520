package com.yeestor.work_order.model.http.req;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonAutoDetect
@ApiModel(value = "RoleParams", description = "创建或者修改角色的时候需要传递给后端的参数。")
public class RoleParams {
    /**
     * 角色的名称
     */
    @ApiModelProperty("角色的名称")
    private String name ;
    /**
     * 角色的描述
     */
    @ApiModelProperty("角色的描述")
    private String desc ;
    /**
     * 用户的钉钉id 列表。
     */
    @ApiModelProperty("用户的钉钉id 列表。")
    private List<String> users ;

    @ApiModelProperty("产品线列表")
    private List<String> products ;

    @ApiModelProperty(value = "权限列表")
    private List<String> permissions ;


}
