{"@timestamp":"2025-07-23T15:10:03.968+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:10:03.975+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:03.98+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:03.98+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"071d174395a34ad6","spanId":"071d174395a34ad6","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.589+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:14:03.591+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.594+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:03.594+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a9fbe18b0cc5804","spanId":"9a9fbe18b0cc5804","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.479+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:03.481+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.484+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:03.484+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f6348bf92fc5147","spanId":"9f6348bf92fc5147","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.834+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:22:03.836+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.839+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:03.839+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af475d16703ad848","spanId":"af475d16703ad848","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.426+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:26:03.428+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.43+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:03.43+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b0c08be681a4b10","spanId":"8b0c08be681a4b10","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.311+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:50:05.323+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.329+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:05.329+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88b93af4f076f7e5","spanId":"88b93af4f076f7e5","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.083+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:04.085+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.089+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:04.089+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"240ba1eed664d150","spanId":"240ba1eed664d150","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.365+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:58:04.367+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.371+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:04.371+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0cca7467466c2d24","spanId":"0cca7467466c2d24","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.507+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:02:03.508+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.511+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:03.512+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"676e95225b589247","spanId":"676e95225b589247","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.513+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:03.514+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.516+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:03.516+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"30eab2b79c120d8f","spanId":"30eab2b79c120d8f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.521+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:10:03.522+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.525+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:03.525+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edfcd7c3c3b23da2","spanId":"edfcd7c3c3b23da2","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.521+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:09.531+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.536+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.536+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfe6e848f55722a8","spanId":"bfe6e848f55722a8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.52+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:22:03.522+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.524+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:03.525+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3dc00ae63763fe5d","spanId":"3dc00ae63763fe5d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.549+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:26:03.55+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.554+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:03.554+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b02ceda5874b2921","spanId":"b02ceda5874b2921","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.441+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:03.443+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.445+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:03.445+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d1a86a50394b018","spanId":"0d1a86a50394b018","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.449+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:34:03.45+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.452+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:03.452+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4efdc60ffa8c02b3","spanId":"4efdc60ffa8c02b3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.634+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:38:03.636+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:03.639+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7019a6f972f17cd3","spanId":"7019a6f972f17cd3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.536+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:03.537+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.541+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:03.541+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c5b90cff28b982f","spanId":"0c5b90cff28b982f","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.429+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:46:03.43+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.432+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:03.433+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a4374b69b50dc04","spanId":"2a4374b69b50dc04","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.361+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:50:03.361+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.364+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:03.364+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a0a7f56b191599ed","spanId":"a0a7f56b191599ed","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:08.457+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:08.736+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:08.742+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:08.743+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8727dd6611fb9c24","spanId":"8727dd6611fb9c24","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.893+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:03.896+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.899+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:03.9+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc6b296852e20602","spanId":"bc6b296852e20602","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.645+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:10:03.648+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.655+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:03.655+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f21793171bd128e4","spanId":"f21793171bd128e4","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.528+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:03.529+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.533+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:03.533+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4af5aa6f8d11cc25","spanId":"4af5aa6f8d11cc25","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.594+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:03.596+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.599+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:03.599+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"abc474207402c2f8","spanId":"abc474207402c2f8","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:22:03.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.622+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:03.622+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6141c7b170c92cb5","spanId":"6141c7b170c92cb5","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.433+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:03.434+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.436+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:03.437+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"857ab6f2965b95a4","spanId":"857ab6f2965b95a4","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.32+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:04.322+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.327+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:04.327+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64da75725049082d","spanId":"64da75725049082d","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.214+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:34:04.218+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.222+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:04.223+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f288e586112e060c","spanId":"f288e586112e060c","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.2+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:05.216+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.223+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:05.223+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66df2e85f05f710a","spanId":"66df2e85f05f710a","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.243+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:04.246+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.251+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:04.251+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9aa5779ba3f5fb43","spanId":"9aa5779ba3f5fb43","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.89+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:04.908+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.918+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:04.918+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eb38c13519b9c79e","spanId":"eb38c13519b9c79e","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.813+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:03.815+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.819+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.82+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c5888679416df785","spanId":"c5888679416df785","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.911+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:10:03.913+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.917+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:03.917+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c3a68f892528ad7","spanId":"6c3a68f892528ad7","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:03.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.614+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:03.614+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"add4874da6cea1ed","spanId":"add4874da6cea1ed","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.949+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:03.951+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.955+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:03.955+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bb300feb590385f3","spanId":"bb300feb590385f3","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.588+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:03.59+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.593+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:03.593+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"775416cff51ea2bd","spanId":"775416cff51ea2bd","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.651+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:03.652+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.655+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:03.655+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"875aa082bb622da0","spanId":"875aa082bb622da0","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.297+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5648","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:06.311+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5861, orderId=5648, flash=8803_WTX2_128G_128GB, orderFlashNo=YS8803##MP220000###0010##3D_TLC_E09T#250013_Alpha_8803_WTX2_128G_128GB, num=300, leftNum=300)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.319+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5648","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:06.319+08:00","@version":"1","message":"[5648] - [8803_WTX2_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5459a7557735e98","spanId":"b5459a7557735e98","context":"QueueService","no":"5648","traceType":"分配设备"}
