# project info
sonar.projectKey=WorkOrder
sonar.projectName=WorkOrder
sonar.projectVersion=1.0
sonar.host.url=http://************:9000
sonar.login=****************************************

# java file & class info
sonar.sources=./src
sonar.java.binaries=./target/classes
sonar.language=java


sonar.core.codeCoveragePlugin=jacoco
# jacoco.exec
sonar.jacoco.reportPaths=target/coverage-reports/jacoco.exec