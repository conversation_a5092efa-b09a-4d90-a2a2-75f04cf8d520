package com.yeestor.work_order.model.http.resp;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;

@Data
@Builder
public class PathInfoResp {
    private String product ;
    private String subProduct ;
    private HashMap<String, String> marsPath;
    private HashMap<String, String> planPath;
    private HashMap<String, String> mpToolPath;

    private HashMap<String, String> scrcpyPath;
    private HashMap<String, String> scrcpyPlanPath;
}
