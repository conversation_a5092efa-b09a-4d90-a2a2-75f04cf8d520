package com.yeestor.work_order.model.http.req.order;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.model.http.req.Person;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Flash 批次信息V2")
public class FlashInfo {

    @ApiModelProperty(value = "flash批次名称", required = true)
    private String flash;

    @Min(value = 1, message = "样片数量不能小于1")
    @ApiModelProperty(value = "flash批次数量，需要大于0", required = true)
    private int num;
    @ApiModelProperty(value = "flash批次先后顺序", required = true)
    private int index;

    @ApiModelProperty(value = "Flash批次的容量", required = true)
    private String size;

    @ApiModelProperty(value = "测试负责人ID", required = true)
    private String testUserID;

    @ApiModelProperty(value = "测试负责人姓名。使用英文名", required = true)
    private String testUserName;

    @ApiModelProperty(value = "测试人员列表")
    private List<Person> testPersons = new ArrayList<>();

    @ApiModelProperty(value = "flash 批次是否需要自动定位", required = true)
    private boolean autoLoc;

    @ApiModelProperty(value = "确认Flash时,传递过来的Plan信息", required = true)
    @NotEmpty(message = "Plan列表不能为空")
    private List<OrderFlashConfirmV2Req.PlanItem> planList;

    @ApiModelProperty(value = "Flash批次的CE数，当前仅eMMC需要填写")
    private String ceCount;

    @SneakyThrows
    public OrderFlashEntity buildFlashEntity(long orderId) {
        ObjectMapper mapper = new ObjectMapper();
        OrderFlashEntity orderFlashEntity = new OrderFlashEntity();
        orderFlashEntity.setOrderId(orderId);
        orderFlashEntity.setFlash(this.getFlash());
        orderFlashEntity.setSize(this.getSize());
        orderFlashEntity.setNum(this.getNum());
        orderFlashEntity.setIdx(this.getIndex());
        orderFlashEntity.setStatus(OrderFlashEntity.Status.NEW);
        orderFlashEntity.setTestBy(this.getTestUserID());
        orderFlashEntity.setTestPerson(this.getTestUserName());
        orderFlashEntity.setLeftNum(this.getNum());
        orderFlashEntity.setAutoLoc(this.isAutoLoc());
        orderFlashEntity.setTestRelatedPerson(mapper.writeValueAsString(testPersons));
        orderFlashEntity.setCeCount(this.ceCount);
        return orderFlashEntity;
    }

    public String getFlash() {
        if (size == null) {
            return flash;
        } else {
            return flash + "_" + size;
        }
    }


}