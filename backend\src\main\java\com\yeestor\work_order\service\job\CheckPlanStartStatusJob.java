package com.yeestor.work_order.service.job;

import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.repository.PlanDeviceRepository;
import com.yeestor.work_order.service.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class CheckPlanStartStatusJob implements Job {

    public final static String JOB_GROUP_NAME = "CheckPlanStartStatusJob" ;

    private final PlanDeviceRepository deviceRepository ;
    private final NotificationService notificationService ;

    public static Map<String ,Object> buildJobDataMap(
            long orderId,
            String orderFlashNo,
            long planId,
            List<String> deviceMacList
    ) {
        HashMap<String,Object> dataMap = new HashMap<>();
        dataMap.put("orderId", orderId);
        dataMap.put("orderFlashNo", orderFlashNo);
        dataMap.put("planId", planId);
        dataMap.put("deviceMacList", String.join(";", deviceMacList));
        return dataMap;
    }

    public static String buildJobName(String orderFlashNo ,String plan){
        return orderFlashNo + "_" + plan + "_checkStartStatus";
    }


    @Override
    public void execute(JobExecutionContext context) {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        log.debug("execute:{}  map key:{} value:{} ", context.getJobDetail().getKey().getName(), Arrays.toString(dataMap.getKeys()), dataMap.values());
        if (
                !dataMap.containsKey("orderId") ||
                        !dataMap.containsKey("orderFlashNo") ||
                        !dataMap.containsKey("planId") ||
                        !dataMap.containsKey("deviceMacList")
        ) {
            log.warn(" execute:{}  map key:{} value:{} ", context.getJobDetail().getKey().getName(), Arrays.toString(dataMap.getKeys()), dataMap.values());
            return;
        }
        long orderId = dataMap.getLong("orderId");
        String orderFlashNo = dataMap.getString("orderFlashNo");
        long planId = dataMap.getLong("planId");
        String deviceMacListStr = dataMap.getString("deviceMacList");
        List<String> deviceMacList = Arrays.asList(deviceMacListStr.split(";"));

        List<PlanDeviceEntity> deviceEntityList = deviceRepository.findAllByPlanIdAndMacIn(planId, deviceMacList);
        notificationService.sendPlanStartStatusNotification(orderId, orderFlashNo,planId,deviceEntityList);

    }
}
