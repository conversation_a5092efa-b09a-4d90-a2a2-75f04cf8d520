package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.OrderBugEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface OrderBugRepository extends JpaRepository<OrderBugEntity,Long> {
    List<OrderBugEntity> findAllByOrderId(long orderId);

    List<OrderBugEntity> findAllByOrderIdAndFlash(long orderId, String flash);
}
