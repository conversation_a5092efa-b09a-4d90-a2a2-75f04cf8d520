package com.yeestor.work_order.controller.order;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.admin.api.UserFeignClient;
import com.yeestor.admin.model.UserDTO;
import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.config.UserDetailsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;


@Slf4j
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class OrderV2ControllerTest {

    private final ObjectMapper mapper = new ObjectMapper();
    private MockMvc mockMvc;

    private static String accessToken;
    private static final String dUserId = "0653290119972081" ;

    @Autowired
    private WebApplicationContext webApplicationContext ;

    @Mock
    UserFeignClient userFeignClient;

    @InjectMocks
    UserDetailsServiceImpl userDetailsService;



    @BeforeAll
    @DisplayName("模拟登陆")
    static void testLogin() throws Exception {
        WebClient webClient = WebClient.builder()
                .baseUrl("http://172.18.2.41:8789/")
                .build();

        HashMap<String, String> map = webClient.post()
                .uri("/auth/oauth/token")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData(new LinkedMultiValueMap<String, String>() {{
                    add("grant_type", "password");
                    add("username", dUserId);
                    add("password", "yeestor_"+ dUserId);
                    add("client_id", "client");
                    add("client_secret", "123456");
                }}))
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<HashMap<String, String>>() {
                })
                .log()
                .blockFirst();
        assertNotNull(map);
        assertNotNull(map.getOrDefault("access_token", null));
        accessToken = map.getOrDefault("access_token", null);
        log.info("map:{}", map);


    }

    @BeforeEach
    @DisplayName("配置mock")
    void setMock() {

        MockitoAnnotations.openMocks(this) ;
        Mockito.when(userFeignClient.findUserByUsername("Bugs.Wan"))
                .thenReturn(
                        HandleResp.<UserDTO>builder()
                                .code(0)
                                .msg("success")
                                .data(UserDTO.builder()
                                        .avatar("https://static-legacy.dingtalk.com/media/lADPBE1XdcBXSb_NAz7NAzw_828_830.jpg")
                                        .dUserId(dUserId)
                                        .email("<EMAIL>")
                                        .jobNumber("Bugs.Wan")
                                        .uid(50L)
                                        .username("Bugs.Wan")
                                        .build()
                                )
                                .build()
                );
        this.mockMvc =  MockMvcBuilders.webAppContextSetup(this.webApplicationContext).build();
    }









    @AfterAll
    void afterAll(){
        log.info("--- after all!");
    }

}