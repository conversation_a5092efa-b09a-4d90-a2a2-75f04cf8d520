package com.yeestor.work_order.model.http.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "BugConnectParams", description = "关联禅道bug的参数")
public class BugConnectParams {
    @NotBlank
    @ApiModelProperty(value = "禅道ID", required = true)
    private String bugId;
    @Min(value = 1, message = "无效的工单ID")
    @ApiModelProperty(value = "工单ID", required = true)
    private long orderId;

    @NotBlank
    @ApiModelProperty(value = "flash批次", required = true)
    private String flash;
    @ApiModelProperty(value = "plan的名称")
    private String plan;
    @ApiModelProperty(value = "设备标识")
    private String device;
    @ApiModelProperty(value = "样片编号")
    private String sample;

}