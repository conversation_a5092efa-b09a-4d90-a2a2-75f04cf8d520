package com.yeestor.work_order.model.zt;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListResp {

    private int total ;
    private Integer limit ;
    private List<BuildInfo> builds = new ArrayList<>();
    private List<BugInfo> bugs = new ArrayList<>();

}
