package com.yeestor.work_order.service;

import com.yeestor.dingtalk.api.DingTalkFeignClient;
import com.yeestor.dingtalk.utils.ConversationMessageBuilder;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.config.TimeoutConfigEntity;
import com.yeestor.work_order.model.http.req.Person;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.repository.TimeoutConfigRepository;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.service.flow.FlowService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.plan.PlanService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Lazy;
import org.springframework.test.context.ActiveProfiles;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@SpringBootTest
@ActiveProfiles("prod")
class NotificationServiceTest {



    private final static String corpId = "ding31d78c632c4953fa35c2f4657eb6378f";
    private final static String appId = "1368250694" ;

    private final static  String userId = "16322748657306344";
    @Autowired
    DingTalkFeignClient dingTalkFeignClient ;

    @Autowired
    OrderFlashRepository orderFlashRepository;

    @Setter(onMethod_ = { @Autowired, @Lazy} )
    OrderService orderService;

    @Setter(onMethod_ = { @Autowired, @Lazy} )
    FlashService flashService;

    @Setter(onMethod_ = { @Autowired, @Lazy} )
    PlanService planService;

    @Setter(onMethod_ = { @Autowired, @Lazy} )
    NotificationService notificationService;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss") ;

    @Autowired
    WorkOrderRepository orderRepository;
    @Autowired
    FlowService flowService;
    @Autowired
    TimeoutConfigRepository timeoutCOnfigRepository;


    @Test
    void formatText(){
        String title = "你有新的工单待确认Flash！" + dateFormat.format(new Date());;
        String no = "431242421" ;
        String version  = "123";
        String url = "dingtalk://dingtalkclient/action/openapp?corpid="+corpId+"&container_type=work_platform&app_id=0_"+appId+"&redirect_type=jump&redirect_url=";
        String text = String.format(
                "# 你有新的工单待确认Flash！  \n" +
                        "## %s  \n" +
                        "测试版本: **%s**  \n "+
                        "构建人: **%s**  \n "+
                        "版本发布时间: **%s**  \n "+
                        "产品: **%s**  \n "+
                        "构建时间: **%s**  \n "+
                        "版本类型: **%s**  \n "+
                        "[查看详情](%s)  \n ",
                        no,
                        version,
                        version,
                version,
                version,
                version,
                version,
                url+ URLEncoder.encode("http://172.18.11.245:8512/work_order/general/flash/" + 1+ "/test" )


        );

        String userDingTalkID = "0653290119972081";

        dingTalkFeignClient.sendConversationMsg(
                false,
                ConversationMessageBuilder.buildMarkdown(
                        Collections.singletonList(userDingTalkID),
                        title,
                        text
                )
        );

        log.info("text:{}",text);
    }

    @Test
    void checkConfirmFlashNotification(){
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(606);
        log.info("orderEntity: {}", orderEntity);
        notificationService.sendOrderNotification("你有新的工单待确认Flash", orderEntity);
    }

    @Test
    void checkFlashInteractiveMsg(){
        long orderId = 32;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        log.info("orderEntity: {}", orderEntity);
        List<OrderFlashEntity> orderFlashEntityList=  orderFlashRepository.findAllByOrderId(orderId);
        orderFlashEntityList.forEach(flashEntity -> {
            notificationService.sendFlashInteractiveMsg(
                    "Flash 批次已确定，排队中",
                    orderEntity,
                    flashEntity
            );
        });
    }

    @Test
    void checkUpdateFlashInteractiveMsg(){
        long orderId = 121;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        log.info("orderEntity: {}", orderEntity);
        List<OrderFlashEntity> orderFlashEntityList= orderFlashRepository.findAllByOrderId(orderId);
        orderFlashEntityList.forEach(flashEntity -> {
            notificationService.updateFlashInteractiveMsg(
                    "你有新的Flash批次待测试",
                    orderEntity,
                    flashEntity
            );
        });
    }

    @Test
    void checkFailAnalysisNotification(){
        long orderId = 35;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        log.info("orderEntity: {}", orderEntity);
        List<OrderFlashEntity> orderFlashEntityList= orderFlashRepository.findAllByOrderId(orderId);
        orderFlashEntityList.forEach(flashEntity -> {
            notificationService.sendFailAnalysisNotification(
                    orderEntity,
                    flashEntity,
                    Collections.singletonList(userId)
            );
        });
    }

    @Test
    void checkFailAnalysisFinishNotification(){
        long orderId = 32;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        log.info("orderEntity: {}", orderEntity);
        List<OrderFlashEntity> orderFlashEntityList= orderFlashRepository.findAllByOrderId(orderId);
        OrderFlashEntity flashEntity = orderFlashEntityList.stream().findFirst().orElse(null);
        notificationService.sendFailAnalysisFinishNotification(
                orderEntity,
                flashEntity
        );
    }

    @Test
    void checkPlanReadyNotification(){
        long orderId = 606;
        String flashName = "HF-6285ENAB-B58R-A_8GB";
        long planId = 7344;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity= flashService.findFlashOrElseThrow(orderId, flashName);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        notificationService.sendPlanReadyNotification(orderEntity, flashEntity, planEntity);
    }

    @Test
    void checkPlanTestEndNotification(){
        long orderId = 606;
        String flashName = "HF-6285ENAB-B58R-A_8GB";
        long planId = 7345;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity= flashService.findFlashOrElseThrow(orderId, flashName);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        notificationService.sendPlanTestEndNotification(orderEntity, flashEntity, planEntity);
    }

    @Test
    void checkNeedUploadReportNotification(){
        long orderId = 606;
        String flashName = "HF-6285ENAB-B58R-A_8GB";
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flashName);
        notificationService.sendNeedUploadReportNotification(orderEntity, flashEntity);
    }

    @Test
    void checkTimes(){
        List<TimeoutConfigEntity> configList = timeoutCOnfigRepository.findAll();
        log.info("TimeoutCheckJob start: {}", configList);
        List<WorkOrderEntity> notFinishedOrders = orderRepository.findAllByStatusNotIn(List.of(
                WorkOrderEntity.Status.COMPLETED, WorkOrderEntity.Status.REVOKED
        ));
        notFinishedOrders = notFinishedOrders.stream().filter(order -> order.getProduct().equals("GE")).collect(Collectors.toList());
//        log.info("notFinishedOrders: {}", notFinishedOrders);
        List<String> users = Arrays.asList("16322748657306344");
        for (WorkOrderEntity order : notFinishedOrders) {
            WorkOrderEntity.Status orderStatus = order.getStatus();
            log.info("order no: {}", order.getNo());
            configList.stream()
                    .filter(e -> e.getType() == TimeoutConfigEntity.Type.ORDER
                            && orderStatus.name().equalsIgnoreCase(e.getPhase())
                            && e.getProduct().equalsIgnoreCase(order.getProduct())
                            && e.getSubProduct().equalsIgnoreCase(order.getSubProduct())
                            && e.getTimeout() > 0
                    )
                    .findFirst()
                    .ifPresent(config -> {
                        log.info("config: {}", config);
                        // 检测是否超时, 如果超时
                        long startTime = flowService.getOrderCurrentStartTime(order);
                        long now = System.currentTimeMillis();
                        long hours = (now - startTime) / 1000 / 60 / 60;
                        long timeDuration = hours - config.getTimeout();
                        log.info("order timeDuration: {}", timeDuration);
                        if (timeDuration < 0) {
                            return;
                        }
                        log.info("工单已超时！！！！！");
//                        List<String> users = config.getListeners().stream().map(Person::getId).collect(Collectors.toList());
                        notificationService.sendTimeoutNotification(
                                order,
                                users,
                                null,
                                orderStatus.getDisplayName(),
                                config.getTimeout(),
                                hours
                        );

                    });

            List<OrderFlashEntity> flashList = orderFlashRepository.findAllByOrderId(order.getId());
            log.info("flashList: {}", flashList);
            for (OrderFlashEntity flashEntity : flashList) {
                OrderFlashEntity.Status flashStatus = flashEntity.getStatus();
                configList.stream()
                        .filter(e -> e.getType() == TimeoutConfigEntity.Type.ORDER
                                && flashStatus.name().equalsIgnoreCase(e.getPhase())
                                && e.getProduct().equalsIgnoreCase(order.getProduct())
                                && e.getSubProduct().equalsIgnoreCase(order.getSubProduct())
                                && e.getTimeout() > 0
                        )
                        .findFirst()
                        .ifPresent(config -> {
                            long now = System.currentTimeMillis();
                            long startTime = flowService.getFlashCurrentStartTime(flashEntity);
                            long hours = (now - startTime) / 1000 / 60 / 60;
                            long timeDuration = hours - config.getTimeout();
                            if (timeDuration < 0) {
                                return;
                            }
                            log.info("Flash批次已超时！！！！！{}", flashEntity.getOrderFlashNo());
//                            List<String> users = config.getListeners().stream().map(Person::getId).collect(Collectors.toList());
                            notificationService.sendTimeoutNotification(
                                    order,
                                    users,
                                    flashEntity.getFlash(),
                                    orderStatus.getDisplayName(),
                                    config.getTimeout(),
                                    hours
                            );

                        });
            }

        }
    }


}