package com.yeestor.work_order.entity.plan;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity(name = "PlanNote")
@Table(name = "wo_plan_note")
public class PlanNoteMsgEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "plan_id")
    private long planId;

    @Column(name = "create_at")
    private Long createAt = System.currentTimeMillis();

    @Column(name = "msg", length=1024)
    private String msg;
}
