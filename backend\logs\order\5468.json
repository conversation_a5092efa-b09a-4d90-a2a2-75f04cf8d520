{"@timestamp":"2025-07-23T10:38:21.955+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9332d64c4eb9dbb","spanId":"f9332d64c4eb9dbb","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:38:21.961+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9332d64c4eb9dbb","spanId":"f9332d64c4eb9dbb","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:21.966+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9332d64c4eb9dbb","spanId":"f9332d64c4eb9dbb","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:21.966+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9332d64c4eb9dbb","spanId":"f9332d64c4eb9dbb","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:21.966+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9332d64c4eb9dbb","spanId":"f9332d64c4eb9dbb","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:47:21.955+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4203b05ac967bc51","spanId":"4203b05ac967bc51","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:47:21.956+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4203b05ac967bc51","spanId":"4203b05ac967bc51","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:47:21.959+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4203b05ac967bc51","spanId":"4203b05ac967bc51","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:47:21.959+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4203b05ac967bc51","spanId":"4203b05ac967bc51","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:47:21.959+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4203b05ac967bc51","spanId":"4203b05ac967bc51","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:22.019+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9091f2f405c6bbee","spanId":"9091f2f405c6bbee","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:50:22.02+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9091f2f405c6bbee","spanId":"9091f2f405c6bbee","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:22.022+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9091f2f405c6bbee","spanId":"9091f2f405c6bbee","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:22.022+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9091f2f405c6bbee","spanId":"9091f2f405c6bbee","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:22.022+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9091f2f405c6bbee","spanId":"9091f2f405c6bbee","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:56:21.976+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f22f04e480e2e066","spanId":"f22f04e480e2e066","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:56:21.978+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f22f04e480e2e066","spanId":"f22f04e480e2e066","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:56:21.98+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f22f04e480e2e066","spanId":"f22f04e480e2e066","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:56:21.981+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f22f04e480e2e066","spanId":"f22f04e480e2e066","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:56:21.981+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f22f04e480e2e066","spanId":"f22f04e480e2e066","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:22.003+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e82c2732e07c72a7","spanId":"e82c2732e07c72a7","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:02:22.005+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e82c2732e07c72a7","spanId":"e82c2732e07c72a7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:22.007+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e82c2732e07c72a7","spanId":"e82c2732e07c72a7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:22.008+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e82c2732e07c72a7","spanId":"e82c2732e07c72a7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:22.008+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e82c2732e07c72a7","spanId":"e82c2732e07c72a7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:05:22.061+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"720525de19f1e7ba","spanId":"720525de19f1e7ba","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:05:22.061+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"720525de19f1e7ba","spanId":"720525de19f1e7ba","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:05:22.063+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"720525de19f1e7ba","spanId":"720525de19f1e7ba","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:05:22.063+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"720525de19f1e7ba","spanId":"720525de19f1e7ba","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:05:22.063+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"720525de19f1e7ba","spanId":"720525de19f1e7ba","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:11:21.93+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d6d9c6841e91952","spanId":"1d6d9c6841e91952","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:11:21.931+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d6d9c6841e91952","spanId":"1d6d9c6841e91952","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:11:21.933+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d6d9c6841e91952","spanId":"1d6d9c6841e91952","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:11:21.933+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d6d9c6841e91952","spanId":"1d6d9c6841e91952","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:11:21.933+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1d6d9c6841e91952","spanId":"1d6d9c6841e91952","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:21.925+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cafc734284f6f94b","spanId":"cafc734284f6f94b","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:14:21.926+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cafc734284f6f94b","spanId":"cafc734284f6f94b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:21.928+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cafc734284f6f94b","spanId":"cafc734284f6f94b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:21.928+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cafc734284f6f94b","spanId":"cafc734284f6f94b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:21.928+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cafc734284f6f94b","spanId":"cafc734284f6f94b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:17:21.967+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"310eebe9ba7b2abd","spanId":"310eebe9ba7b2abd","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:17:21.968+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"310eebe9ba7b2abd","spanId":"310eebe9ba7b2abd","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:17:21.97+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"310eebe9ba7b2abd","spanId":"310eebe9ba7b2abd","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:17:21.97+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"310eebe9ba7b2abd","spanId":"310eebe9ba7b2abd","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:17:21.97+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"310eebe9ba7b2abd","spanId":"310eebe9ba7b2abd","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:32:22.65+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de1791135c022eee","spanId":"de1791135c022eee","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:32:22.656+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de1791135c022eee","spanId":"de1791135c022eee","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:32:22.659+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de1791135c022eee","spanId":"de1791135c022eee","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:32:22.66+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de1791135c022eee","spanId":"de1791135c022eee","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:32:22.66+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de1791135c022eee","spanId":"de1791135c022eee","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:22.018+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10dea8f043eed563","spanId":"10dea8f043eed563","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:38:22.022+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10dea8f043eed563","spanId":"10dea8f043eed563","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:22.024+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10dea8f043eed563","spanId":"10dea8f043eed563","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:22.025+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10dea8f043eed563","spanId":"10dea8f043eed563","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:22.025+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10dea8f043eed563","spanId":"10dea8f043eed563","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:41:21.972+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ba3045897d576b6","spanId":"4ba3045897d576b6","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:41:21.973+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ba3045897d576b6","spanId":"4ba3045897d576b6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:41:21.975+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ba3045897d576b6","spanId":"4ba3045897d576b6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:41:21.975+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ba3045897d576b6","spanId":"4ba3045897d576b6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:41:21.975+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ba3045897d576b6","spanId":"4ba3045897d576b6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:21.948+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a180ed7357a839b","spanId":"9a180ed7357a839b","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:50:21.949+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a180ed7357a839b","spanId":"9a180ed7357a839b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:21.951+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a180ed7357a839b","spanId":"9a180ed7357a839b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:21.951+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a180ed7357a839b","spanId":"9a180ed7357a839b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:21.951+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9a180ed7357a839b","spanId":"9a180ed7357a839b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:56:21.934+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f86baeb82ddd07af","spanId":"f86baeb82ddd07af","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:56:21.935+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f86baeb82ddd07af","spanId":"f86baeb82ddd07af","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:56:21.937+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f86baeb82ddd07af","spanId":"f86baeb82ddd07af","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:56:21.937+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f86baeb82ddd07af","spanId":"f86baeb82ddd07af","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:56:21.937+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f86baeb82ddd07af","spanId":"f86baeb82ddd07af","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:59:21.939+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2a68efa427990ab","spanId":"a2a68efa427990ab","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:59:21.94+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2a68efa427990ab","spanId":"a2a68efa427990ab","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:59:21.942+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2a68efa427990ab","spanId":"a2a68efa427990ab","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:59:21.943+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2a68efa427990ab","spanId":"a2a68efa427990ab","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:59:21.943+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a2a68efa427990ab","spanId":"a2a68efa427990ab","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:21.971+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87c6c1505e27ec56","spanId":"87c6c1505e27ec56","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:02:21.973+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87c6c1505e27ec56","spanId":"87c6c1505e27ec56","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:21.975+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87c6c1505e27ec56","spanId":"87c6c1505e27ec56","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:21.975+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87c6c1505e27ec56","spanId":"87c6c1505e27ec56","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:21.975+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"87c6c1505e27ec56","spanId":"87c6c1505e27ec56","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:05:21.952+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"672a050e37bdf82f","spanId":"672a050e37bdf82f","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:05:21.953+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"672a050e37bdf82f","spanId":"672a050e37bdf82f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:05:21.954+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"672a050e37bdf82f","spanId":"672a050e37bdf82f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:05:21.954+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"672a050e37bdf82f","spanId":"672a050e37bdf82f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:05:21.954+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"672a050e37bdf82f","spanId":"672a050e37bdf82f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:08:21.928+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"888a902c126356a6","spanId":"888a902c126356a6","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:08:21.929+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"888a902c126356a6","spanId":"888a902c126356a6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:08:21.931+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"888a902c126356a6","spanId":"888a902c126356a6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:08:21.931+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"888a902c126356a6","spanId":"888a902c126356a6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:08:21.931+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"888a902c126356a6","spanId":"888a902c126356a6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:21.971+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b6c233ef2b272568","spanId":"b6c233ef2b272568","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:14:21.972+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b6c233ef2b272568","spanId":"b6c233ef2b272568","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:21.973+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b6c233ef2b272568","spanId":"b6c233ef2b272568","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:21.973+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b6c233ef2b272568","spanId":"b6c233ef2b272568","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:21.974+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b6c233ef2b272568","spanId":"b6c233ef2b272568","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:17:21.959+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a99217ea57e2a94","spanId":"7a99217ea57e2a94","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:17:21.96+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a99217ea57e2a94","spanId":"7a99217ea57e2a94","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:17:21.962+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a99217ea57e2a94","spanId":"7a99217ea57e2a94","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:17:21.962+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a99217ea57e2a94","spanId":"7a99217ea57e2a94","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:17:21.962+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a99217ea57e2a94","spanId":"7a99217ea57e2a94","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:21.922+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5b6283a1fb1478f8","spanId":"5b6283a1fb1478f8","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:26:21.923+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5b6283a1fb1478f8","spanId":"5b6283a1fb1478f8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:21.924+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5b6283a1fb1478f8","spanId":"5b6283a1fb1478f8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:21.925+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5b6283a1fb1478f8","spanId":"5b6283a1fb1478f8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:21.925+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5b6283a1fb1478f8","spanId":"5b6283a1fb1478f8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:32:21.934+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fffcfb70a320634","spanId":"7fffcfb70a320634","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:32:21.934+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fffcfb70a320634","spanId":"7fffcfb70a320634","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:32:21.936+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fffcfb70a320634","spanId":"7fffcfb70a320634","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:32:21.936+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fffcfb70a320634","spanId":"7fffcfb70a320634","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:32:21.936+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7fffcfb70a320634","spanId":"7fffcfb70a320634","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:21.931+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a6c9ea0a5e5728e","spanId":"3a6c9ea0a5e5728e","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:38:21.933+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a6c9ea0a5e5728e","spanId":"3a6c9ea0a5e5728e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:21.934+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a6c9ea0a5e5728e","spanId":"3a6c9ea0a5e5728e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:21.934+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a6c9ea0a5e5728e","spanId":"3a6c9ea0a5e5728e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:21.934+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a6c9ea0a5e5728e","spanId":"3a6c9ea0a5e5728e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:21.943+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fa274bc32c44311","spanId":"0fa274bc32c44311","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:50:21.944+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fa274bc32c44311","spanId":"0fa274bc32c44311","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:21.946+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fa274bc32c44311","spanId":"0fa274bc32c44311","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:21.946+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fa274bc32c44311","spanId":"0fa274bc32c44311","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:21.946+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fa274bc32c44311","spanId":"0fa274bc32c44311","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:53:21.972+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ef4375bccddc7d9","spanId":"4ef4375bccddc7d9","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:53:21.973+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ef4375bccddc7d9","spanId":"4ef4375bccddc7d9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:53:21.975+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ef4375bccddc7d9","spanId":"4ef4375bccddc7d9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:53:21.975+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ef4375bccddc7d9","spanId":"4ef4375bccddc7d9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:53:21.975+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4ef4375bccddc7d9","spanId":"4ef4375bccddc7d9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:56:21.946+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f47e8259c1726e2a","spanId":"f47e8259c1726e2a","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:56:21.947+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f47e8259c1726e2a","spanId":"f47e8259c1726e2a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:56:21.949+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f47e8259c1726e2a","spanId":"f47e8259c1726e2a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:56:21.949+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f47e8259c1726e2a","spanId":"f47e8259c1726e2a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:56:21.949+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f47e8259c1726e2a","spanId":"f47e8259c1726e2a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:59:21.946+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea404cf193b1dea8","spanId":"ea404cf193b1dea8","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:59:21.947+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea404cf193b1dea8","spanId":"ea404cf193b1dea8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:59:21.949+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea404cf193b1dea8","spanId":"ea404cf193b1dea8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:59:21.949+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea404cf193b1dea8","spanId":"ea404cf193b1dea8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:59:21.949+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ea404cf193b1dea8","spanId":"ea404cf193b1dea8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:21.957+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aba7f9df140cdb26","spanId":"aba7f9df140cdb26","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:02:21.957+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aba7f9df140cdb26","spanId":"aba7f9df140cdb26","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:21.959+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aba7f9df140cdb26","spanId":"aba7f9df140cdb26","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:21.96+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aba7f9df140cdb26","spanId":"aba7f9df140cdb26","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:21.96+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aba7f9df140cdb26","spanId":"aba7f9df140cdb26","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:05:22.044+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8104183cb5ad38f8","spanId":"8104183cb5ad38f8","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:05:22.045+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8104183cb5ad38f8","spanId":"8104183cb5ad38f8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:05:22.047+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8104183cb5ad38f8","spanId":"8104183cb5ad38f8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:05:22.047+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8104183cb5ad38f8","spanId":"8104183cb5ad38f8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:05:22.047+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8104183cb5ad38f8","spanId":"8104183cb5ad38f8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:08:22.01+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f51953754a762d8f","spanId":"f51953754a762d8f","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:08:22.013+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f51953754a762d8f","spanId":"f51953754a762d8f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:08:22.017+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f51953754a762d8f","spanId":"f51953754a762d8f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:08:22.017+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f51953754a762d8f","spanId":"f51953754a762d8f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:08:22.017+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f51953754a762d8f","spanId":"f51953754a762d8f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:21.967+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c151582f221c698","spanId":"3c151582f221c698","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:14:21.968+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c151582f221c698","spanId":"3c151582f221c698","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:21.97+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c151582f221c698","spanId":"3c151582f221c698","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:21.97+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c151582f221c698","spanId":"3c151582f221c698","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:21.97+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c151582f221c698","spanId":"3c151582f221c698","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:22.288+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd29a5b60dbc195d","spanId":"dd29a5b60dbc195d","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:26:22.29+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd29a5b60dbc195d","spanId":"dd29a5b60dbc195d","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:22.292+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd29a5b60dbc195d","spanId":"dd29a5b60dbc195d","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:22.292+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd29a5b60dbc195d","spanId":"dd29a5b60dbc195d","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:22.292+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dd29a5b60dbc195d","spanId":"dd29a5b60dbc195d","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:35:22.012+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acb0bb9a0351fd26","spanId":"acb0bb9a0351fd26","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:35:22.014+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acb0bb9a0351fd26","spanId":"acb0bb9a0351fd26","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:35:22.016+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acb0bb9a0351fd26","spanId":"acb0bb9a0351fd26","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:35:22.016+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acb0bb9a0351fd26","spanId":"acb0bb9a0351fd26","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:35:22.016+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acb0bb9a0351fd26","spanId":"acb0bb9a0351fd26","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:21.949+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a4adb34b5941ae5","spanId":"7a4adb34b5941ae5","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:38:21.95+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a4adb34b5941ae5","spanId":"7a4adb34b5941ae5","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:21.952+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a4adb34b5941ae5","spanId":"7a4adb34b5941ae5","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:21.952+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a4adb34b5941ae5","spanId":"7a4adb34b5941ae5","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:21.952+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a4adb34b5941ae5","spanId":"7a4adb34b5941ae5","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:47:21.919+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"663ab457eda2a423","spanId":"663ab457eda2a423","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:47:21.92+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"663ab457eda2a423","spanId":"663ab457eda2a423","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:47:21.921+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"663ab457eda2a423","spanId":"663ab457eda2a423","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:47:21.921+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"663ab457eda2a423","spanId":"663ab457eda2a423","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:47:21.921+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"663ab457eda2a423","spanId":"663ab457eda2a423","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:21.921+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3751f08e7e924bca","spanId":"3751f08e7e924bca","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:50:21.922+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3751f08e7e924bca","spanId":"3751f08e7e924bca","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:21.924+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3751f08e7e924bca","spanId":"3751f08e7e924bca","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:21.924+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3751f08e7e924bca","spanId":"3751f08e7e924bca","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:21.924+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3751f08e7e924bca","spanId":"3751f08e7e924bca","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:53:21.936+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"792c9adb7eca3bb5","spanId":"792c9adb7eca3bb5","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:53:21.937+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"792c9adb7eca3bb5","spanId":"792c9adb7eca3bb5","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:53:21.939+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"792c9adb7eca3bb5","spanId":"792c9adb7eca3bb5","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:53:21.939+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"792c9adb7eca3bb5","spanId":"792c9adb7eca3bb5","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:53:21.939+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"792c9adb7eca3bb5","spanId":"792c9adb7eca3bb5","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:56:21.924+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29262c41625170d7","spanId":"29262c41625170d7","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:56:21.925+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29262c41625170d7","spanId":"29262c41625170d7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:56:21.927+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29262c41625170d7","spanId":"29262c41625170d7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:56:21.927+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29262c41625170d7","spanId":"29262c41625170d7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:56:21.927+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"29262c41625170d7","spanId":"29262c41625170d7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:59:21.956+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2203f22a33f9170a","spanId":"2203f22a33f9170a","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:59:21.957+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2203f22a33f9170a","spanId":"2203f22a33f9170a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:59:21.959+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2203f22a33f9170a","spanId":"2203f22a33f9170a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:59:21.959+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2203f22a33f9170a","spanId":"2203f22a33f9170a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:59:21.959+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2203f22a33f9170a","spanId":"2203f22a33f9170a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:22.039+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86bce53c71520d52","spanId":"86bce53c71520d52","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:02:22.041+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86bce53c71520d52","spanId":"86bce53c71520d52","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:22.043+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86bce53c71520d52","spanId":"86bce53c71520d52","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:22.043+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86bce53c71520d52","spanId":"86bce53c71520d52","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:22.044+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86bce53c71520d52","spanId":"86bce53c71520d52","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:11:21.972+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa92005f92fa7ff9","spanId":"aa92005f92fa7ff9","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:11:21.973+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa92005f92fa7ff9","spanId":"aa92005f92fa7ff9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:11:21.976+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa92005f92fa7ff9","spanId":"aa92005f92fa7ff9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:11:21.976+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa92005f92fa7ff9","spanId":"aa92005f92fa7ff9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:11:21.976+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa92005f92fa7ff9","spanId":"aa92005f92fa7ff9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:22.516+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d51766de79fe2c59","spanId":"d51766de79fe2c59","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:14:22.518+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d51766de79fe2c59","spanId":"d51766de79fe2c59","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:22.522+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d51766de79fe2c59","spanId":"d51766de79fe2c59","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:22.522+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d51766de79fe2c59","spanId":"d51766de79fe2c59","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:22.522+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d51766de79fe2c59","spanId":"d51766de79fe2c59","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:20:22.217+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fd48d169fa0ca5b4","spanId":"fd48d169fa0ca5b4","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:20:22.22+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fd48d169fa0ca5b4","spanId":"fd48d169fa0ca5b4","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:20:22.223+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fd48d169fa0ca5b4","spanId":"fd48d169fa0ca5b4","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:20:22.224+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fd48d169fa0ca5b4","spanId":"fd48d169fa0ca5b4","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:20:22.224+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fd48d169fa0ca5b4","spanId":"fd48d169fa0ca5b4","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:23:22.109+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c64c1c7b82dd036","spanId":"2c64c1c7b82dd036","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:23:22.111+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c64c1c7b82dd036","spanId":"2c64c1c7b82dd036","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:23:22.115+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c64c1c7b82dd036","spanId":"2c64c1c7b82dd036","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:23:22.115+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c64c1c7b82dd036","spanId":"2c64c1c7b82dd036","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:23:22.115+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c64c1c7b82dd036","spanId":"2c64c1c7b82dd036","context":"QueueService","no":"5468","traceType":"分配设备"}
