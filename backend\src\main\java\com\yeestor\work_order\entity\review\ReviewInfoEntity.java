package com.yeestor.work_order.entity.review;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity(name = "ReviewInfo")
@Table(name="wo_review_info")
public class ReviewInfoEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "order_id")
    private long orderId ;

    @Column(name = "flash")
    private String flash ;

    @Column(name = "created_at")
    private long createdAt ;

    @Column(name = "created_by")
    private String createdBy ;

    @Column(name = "created_p")
    private String createdPerson ;

    /**
     * 会议的起始时间和结束时间.
     */
    @Column(name = "start_at")
    private long startAt ;

    @Column(name = "end_at")
    private long endAt ;


    /**
     * 钉钉的会议id.
     */
    @Column(name = "meeting_id")
    private String meetingId ;

    @Lob
    @Column(name= "persons", length=512)
    private String personList ;

    @Lob
    @Column(name= "description", length=512)
    private String description ;

}
