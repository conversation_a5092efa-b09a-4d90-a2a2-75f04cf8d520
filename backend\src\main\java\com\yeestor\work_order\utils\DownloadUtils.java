package com.yeestor.work_order.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.net.URI;

@Slf4j
public class DownloadUtils {


    private DownloadUtils(){

    }

    public static int getSizeWithWebclient(String url) {

        return downloadFileWithWebclient(url)
                .map(DataBuffer::readableByteCount)
                .blockOptional()
                .orElse(0);
    }

    public static Mono<DataBuffer> downloadFileWithWebclient(String fileUrl) {
        final String url = fileUrl.replace(" ", "%20")
                .replace("#", "%23");

        return WebClient.builder()
                .codecs(config -> config.defaultCodecs().maxInMemorySize(20 * 1024 * 1024)) // 最大20M
                .build().get()
                .uri(URI.create(url))
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .exchangeToMono( clientResponse -> {
                    MediaType type = clientResponse.headers().contentType().orElse(MediaType.APPLICATION_OCTET_STREAM);
                    if(type.isCompatibleWith(MediaType.TEXT_HTML)) {
                        log.info("download file:{} failed! contentType is text/html",url);
                        return Mono.empty();
                    }
                    return clientResponse.bodyToMono(DataBuffer.class);
                });
    }

}
