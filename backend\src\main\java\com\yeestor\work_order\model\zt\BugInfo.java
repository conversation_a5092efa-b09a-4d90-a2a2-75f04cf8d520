package com.yeestor.work_order.model.zt;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BugInfo", description = "禅道中的Bug信息")
public class BugInfo {

    private int id;
    private int project;
    private int product;
    private int branch;
    private int module;
    private int execution;
    private int plan;
    private int story;
    private int storyVersion;
    private int task;
    private int toTask;
    private int toStory;
    private String title;
    private String keywords;
    private int severity;
    private int pri;
    private String type;
    private String os;
    private String browser;
    private String hardware;
    private String found;
    private String steps;
    private String status;
    private String subStatus;
    private String color;
    private int confirmed;
    private int activatedCount;
    private String activatedDate;
    private String feedbackBy;
    private String notifyEmail;
    private String mailto;
    private String openedBy;
    private String openedDate;
    private String openedBuild;
    private String assignedTo;
    private String assignedDate;
    private String deadline;
    private String resolvedBy;
    private String resolution;
    private String resolvedBuild;
    private String resolvedDate;
    private String closedBy;
    private String closedDate;
    private int duplicateBug;
    private String linkBug;
    private int caseId;
    private int caseVersion;
    private int result;
    private int repo;
    private int mr;
    private String entry;
    private String lines;
    private String v1;
    private String v2;
    private String repoType;
    private int testtask;
    private String lastEditedBy;
    private String lastEditedDate;
    private boolean deleted;
    private String statusName;



}