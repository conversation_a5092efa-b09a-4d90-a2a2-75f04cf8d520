# eSee 项目本地开发环境启动指南

## 前置条件检查

### 环境要求
- **Java**: JDK 11 或以上版本
- **Node.js**: 14.19.2 或以上版本
- **Maven**: 3.6+ 
- **数据库**: MariaDB/MySQL 访问权限
- **Redis**: Redis 服务访问权限

### 网络要求
- 能够访问外部数据库：`gateway.yeestor.com:3307`
- 能够访问 Redis 服务：`***********:6379`
- 能够访问 Eureka 服务：`gateway.yeestor.com:8788`

## 1. 后端启动步骤

### 1.1 检查 Java 环境
```bash
# 检查 Java 版本
java -version
javac -version

# 确保使用 JDK 11
export JAVA_HOME=/path/to/jdk11
```

### 1.2 检查 Maven 配置
```bash
# 检查 Maven 版本
mvn -version

# 检查 Maven 仓库配置
cat ~/.m2/settings.xml
```

### 1.3 编译后端项目
```bash
# 进入后端目录
cd backend

# 清理并编译项目（跳过测试）
mvn clean compile -Dmaven.test.skip=true

# 如果编译失败，尝试强制更新依赖
mvn clean compile -U -Dmaven.test.skip=true
```

### 1.4 配置数据库连接
检查 `backend/src/main/resources/application-dev.yml` 中的数据库配置：

```yaml
spring:
  datasource:
    url: ************************************************************************************
    username: root
    password: Reliable123!
```

### 1.5 启动后端服务
```bash
# 方式1：使用 Maven 启动
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 方式2：使用 IDE 启动
# 在 IntelliJ IDEA 中：
# 1. 打开 WorkOrderApplication.java
# 2. 设置 VM options: -Dspring.profiles.active=dev
# 3. 点击运行按钮

# 方式3：打包后启动
mvn clean package -Dmaven.test.skip=true
java -jar target/system.work_order-1.0.0-SNAPSHOT.jar --spring.profiles.active=dev
```

### 1.6 验证后端启动
```bash
# 检查端口是否被占用
netstat -an | grep 8793

# 访问健康检查接口
curl http://localhost:8793/actuator/health

# 访问 API 文档
# 浏览器打开：http://localhost:8793/doc.html
```

## 2. 前端启动步骤

### 2.1 检查 Node.js 环境
```bash
# 检查 Node.js 版本
node -v
npm -v

# 如果版本不符合要求，建议使用 nvm 管理版本
nvm install 14.19.2
nvm use 14.19.2
```

### 2.2 安装依赖
```bash
# 进入前端目录
cd frontend

# 检查是否有 pnpm
pnpm -v

# 如果没有 pnpm，先安装
npm install -g pnpm@7.5.0

# 安装项目依赖
pnpm install

# 如果 pnpm 安装失败，可以使用 npm
npm install
```

### 2.3 配置前端代理
检查 `frontend/config/defaultSettings.ts` 中的 API 配置：

```typescript
const Settings = {
  apiHost: `http://************:8789`,  // 这个可能需要修改为本地后端地址
  apiUrl: NODE_ENV === 'development' ? '/w0' : '/wo',
  // ...
};
```

### 2.4 修改前端配置指向本地后端
创建或修改 `frontend/config/proxy.ts`：

```typescript
export default {
  dev: {
    '/w0': {
      target: 'http://localhost:8793',  // 指向本地后端
      changeOrigin: true,
      pathRewrite: { '^/w0': '' },
    },
    '/rms/': {
      target: 'http://ereport.yeestor.com/',
      changeOrigin: true,
      pathRewrite: { '^/rms/': '' },
    },
  },
};
```

### 2.5 启动前端服务
```bash
# 启动开发服务器
npm run start

# 或者使用 pnpm
pnpm start

# 指定端口启动（如果 8512 被占用）
cross-env PORT=8513 npm run start
```

### 2.6 验证前端启动
```bash
# 检查端口是否被占用
netstat -an | grep 8512

# 浏览器访问
# http://localhost:8512
```

## 3. 联调验证步骤

### 3.1 检查前后端通信
```bash
# 1. 确认后端服务正常
curl http://localhost:8793/actuator/health

# 2. 确认前端代理配置正确
# 在浏览器开发者工具中查看网络请求
# 前端请求应该是：http://localhost:8512/w0/api/xxx
# 代理后应该转发到：http://localhost:8793/api/xxx
```

### 3.2 测试 API 接口
```bash
# 测试工单列表接口（需要认证）
curl -X GET "http://localhost:8793/api/order/list?p=GE&sp=U2&chip=all&status=0&page=0&size=20" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3.3 验证认证机制
1. 打开浏览器访问：`http://localhost:8512`
2. 应该会重定向到登录页面
3. 根据配置，可能需要钉钉登录或测试登录

## 4. 常见问题排查

### 4.1 后端启动问题

#### 问题1：数据库连接失败
```bash
# 错误信息：Could not connect to MariaDB
# 解决方案：
1. 检查网络连接到 gateway.yeestor.com:3307
2. 验证数据库用户名密码
3. 检查防火墙设置
```

#### 问题2：Redis 连接失败
```bash
# 错误信息：Unable to connect to Redis
# 解决方案：
1. 检查网络连接到 ***********:6379
2. 可以临时注释掉 Redis 相关配置进行测试
```

#### 问题3：Eureka 注册失败
```bash
# 错误信息：Cannot register with Eureka
# 解决方案：
1. 检查网络连接到 gateway.yeestor.com:8788
2. 可以设置 eureka.client.register-with-eureka=false 进行本地测试
```

#### 问题4：端口被占用
```bash
# 错误信息：Port 8793 is already in use
# 解决方案：
1. 查找占用进程：lsof -i :8793
2. 杀死进程：kill -9 PID
3. 或修改端口：server.port=8794
```

### 4.2 前端启动问题

#### 问题1：依赖安装失败
```bash
# 错误信息：npm ERR! peer dep missing
# 解决方案：
1. 清理缓存：npm cache clean --force
2. 删除 node_modules：rm -rf node_modules
3. 重新安装：npm install --legacy-peer-deps
```

#### 问题2：Node.js 版本不兼容
```bash
# 错误信息：Node Sass version X.X.X is incompatible
# 解决方案：
1. 使用正确的 Node.js 版本：nvm use 14.19.2
2. 添加 NODE_OPTIONS=--openssl-legacy-provider
```

#### 问题3：端口冲突
```bash
# 错误信息：Port 8512 is already in use
# 解决方案：
1. 修改启动命令：PORT=8513 npm run start
2. 或杀死占用进程
```

### 4.3 联调问题

#### 问题1：跨域错误
```bash
# 错误信息：CORS policy blocked
# 解决方案：
1. 检查后端 @CrossOrigin 注解
2. 确认前端代理配置正确
3. 检查 changeOrigin: true 设置
```

#### 问题2：认证失败
```bash
# 错误信息：401 Unauthorized
# 解决方案：
1. 检查 JWT Token 是否正确设置
2. 确认 Cookie 配置
3. 检查认证接口是否正常
```

#### 问题3：API 请求失败
```bash
# 错误信息：404 Not Found
# 解决方案：
1. 检查 API 路径是否正确
2. 确认代理配置的 pathRewrite 规则
3. 查看后端控制器映射路径
```

## 5. 开发调试技巧

### 5.1 后端调试
```bash
# 启用 SQL 日志
logging.level.org.hibernate.SQL=DEBUG

# 启用 Web 请求日志
logging.level.org.springframework.web=DEBUG

# 使用 IDE 断点调试
# 在 IntelliJ IDEA 中设置断点，以 Debug 模式启动
```

### 5.2 前端调试
```bash
# 启用详细日志
REACT_APP_ENV=dev npm run start

# 使用浏览器开发者工具
# 1. Network 标签查看 API 请求
# 2. Console 标签查看错误信息
# 3. Application 标签查看 Cookie/LocalStorage
```

### 5.3 网络调试
```bash
# 使用 curl 测试 API
curl -v http://localhost:8793/api/order/list

# 使用 Postman 测试接口
# 导入 Swagger 文档：http://localhost:8793/doc.html

# 使用 Charles/Fiddler 抓包分析
```

## 6. 快速启动脚本

### 6.1 后端启动脚本 (start-backend.sh)
```bash
#!/bin/bash
cd backend
echo "正在启动后端服务..."
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 6.2 前端启动脚本 (start-frontend.sh)
```bash
#!/bin/bash
cd frontend
echo "正在安装依赖..."
pnpm install
echo "正在启动前端服务..."
npm run start
```

### 6.3 完整启动脚本 (start-all.sh)
```bash
#!/bin/bash
echo "正在启动 eSee 开发环境..."

# 启动后端（后台运行）
cd backend
nohup mvn spring-boot:run -Dspring-boot.run.profiles=dev > backend.log 2>&1 &
echo "后端服务启动中，日志文件：backend.log"

# 等待后端启动
sleep 30

# 启动前端
cd ../frontend
echo "正在启动前端服务..."
npm run start
```

---

**注意事项：**
1. 首次启动可能需要较长时间下载依赖
2. 确保网络能够访问外部服务（数据库、Redis、Eureka）
3. 如果遇到问题，请查看对应的日志文件
4. 建议使用 IDE 进行开发调试，可以更方便地设置断点和查看日志
