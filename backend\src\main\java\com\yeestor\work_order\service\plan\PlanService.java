package com.yeestor.work_order.service.plan;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.entity.device.DeviceLockInfoEntity;
import com.yeestor.work_order.entity.plan.PlanAssignInfoEntity;
import com.yeestor.work_order.entity.plan.PlanNoteMsgEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.ci.OrderInfoModel;
import com.yeestor.work_order.model.http.req.AddTempPlanReq;
import com.yeestor.work_order.model.http.req.order.OrderEnvConfirmReq;
import com.yeestor.work_order.model.http.req.order.OrderFlashConfirmV2Req;
import com.yeestor.work_order.model.http.resp.order.*;
import com.yeestor.work_order.model.rms.*;
import com.yeestor.work_order.repository.*;

import com.yeestor.work_order.repository.device.DeviceLockInfoRepository;
import com.yeestor.work_order.repository.document.PlanDocumentRepository;
import com.yeestor.work_order.repository.plan.PlanNoteMsgRepository;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.device.QueueService;
import com.yeestor.work_order.service.device.RMSDeviceService;
import com.yeestor.work_order.service.job.CheckPlanStartStatusJob;
import com.yeestor.work_order.service.job.JobService;
import com.yeestor.work_order.service.job.AutoCompletePlanJob;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.product.ProductContext;
import com.yeestor.work_order.utils.DingTalkUtils;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.work_order.utils.RMSApis;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;
import org.joda.time.DateTime;
import org.slf4j.MarkerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yeestor.work_order.entity.PlanDeviceEntity.COMPLETED_STATUS_LIST;
import static com.yeestor.work_order.utils.Const.DEVICES_ASSIGNED_TO_PLAN;
import static com.yeestor.work_order.utils.Const.DEVICES_REPLACED;
import static com.yeestor.work_order.entity.PlanDeviceEntity.OCCUPY_STATUS_LIST;

@Slf4j
@Service
@RequiredArgsConstructor
public class PlanService {
    private final RMSApis rmsApis ;
    private final OrderPlanRepository planRepository;
    private final PlanDeviceRepository planDeviceRepository ;

    private final TempPlanRepository tempPlanRepository ;
    private final RMSDeviceService rmsDeviceService ;
    private final DeviceService deviceService ;

    private final TempPlanRepository tmpPlanRepository ;
    private final JobService jobService ;

    private final ProductContext productContext ;

    @Setter(onMethod_ = { @Autowired,@Lazy} )
    private FlashService flashService ;

    @Setter(onMethod_ = { @Autowired,@Lazy} )
    private PlanAssignService planAssignService ;

    @Setter(onMethod_ = { @Autowired,@Lazy} )
    private OrderService orderService ;

    @Setter(onMethod_ = { @Autowired, @Lazy} )
    private NotificationService notificationService ;

    private final WorkOrderRepository orderRepository;

    private final DataChangeListener dataChangeListener ;

    private final PlanHistoryRepository planHistoryRepository ;

    private final PlanDocumentRepository planDocumentRepository;
    private final DeviceLockInfoRepository deviceLockInfoRepository;
    private final OrderFlashRepository flashRepository;
    private final PlanNoteMsgRepository planNoteMsgRepository;

    @Setter(onMethod_ = { @Autowired,@Lazy} )
    private TerminalService terminalService;

    /**
     * 给工单分配Plan列表
     * @param orderId 工单ID
     * @param flashEntity flash 批次
     * @param planModelList plan 列表
     * @param status plan 初始化的状态。
     */
    @Transactional
    void assignConnectPlan(long orderId, OrderFlashEntity flashEntity, List<PlanModel> planModelList, OrderPlanEntity.Status status) {
        // 此时的plan 是不知道样片数量以及Flash的批次，但是这个时候，是能够知道CI这边给的Flash 到底包含了什么
        // 但是CI中的Flash 信息和测试
        List<OrderPlanEntity> entities = planModelList.stream().map(
                plan -> plan.toEntity(orderId,
                        flashEntity,
                        status
                )).collect(Collectors.toList());

        List<OrderPlanEntity> savedPlans = planRepository.saveAll(entities);

        // 增加对应的PlanAssignInfo
        if(flashEntity != null) {
            planAssignService.preparePlanAssignInfo(savedPlans);
        }
    }

    /**
     * 在RMS中获取指定产品的对应Plan信息
     *
     * @param product     产品线
     * @param subProduct  子产品
     * @param versionType 版本类型
     * @return plan列表
     */
    public List<PlanModel> fetchRMSPlanList(String product, String subProduct, String versionType) {
        if (subProduct.equals("TECH_SD")) {
            return rmsApis.getPlanList("GE", "SD", null);
        }
        return rmsApis.getPlanList(product, subProduct, versionType);
    }

    /**
     * 在RMS中获取指定产品的对应Plan合集的信息
     *
     * @param product     产品线
     * @param subProduct  子产品
     * @param versionType 版本类型
     * @return plan列表
     */
    public List<PlanGroupModel> fetchRMSPlanGroupList(String product, String subProduct, String versionType) {
        if (subProduct.equals("TECH_SD")) {
            return rmsApis.getPlanGroups("GE", "SD", null);
        }
        return rmsApis.getPlanGroups(product, subProduct, versionType);
    }

    public List<PlanModel> getReleaseDefaultPlanList(String product, String subProduct){
        List<PlanGroupModel> planGroupList = fetchRMSPlanGroupList(product, subProduct, OrderInfoModel.VERSION_TYPE_RELEASE);
        List<PlanModel> planList = fetchRMSPlanList(product, subProduct, OrderInfoModel.VERSION_TYPE_RELEASE);
        List<PlanModel> resultPlanList = new LinkedList<>() ;
        planGroupList
                .stream()
                .filter(g -> OrderPlanEntity.Phase.anyMatch(g.getName()))
                .forEach(g -> g.getPlans()
                        .stream()
                        .map(p -> planList.stream().filter(p1 -> p1.getName().equals(p.getName()))
                                    .findFirst()
                                    .orElse(null)
                        )
                        .filter(Objects::nonNull)
                        .forEach(p -> {
                                    p.setPhase(OrderPlanEntity.Phase.findMatch(g.getName()));
                                    if (resultPlanList.stream().noneMatch(p1 -> p1.getName().equals(p.getName()))) {
                                        resultPlanList.add(p);
                                    }
                                }
                        ));

        return resultPlanList ;

    }


    /**
     * 预分配 release plan
     */
    @Transactional
    public void preAssignPlan(WorkOrderEntity entity){
        // 调用 RMS 的接口来获取plan 的信息。
        List<PlanModel> planList = getReleaseDefaultPlanList(entity.getProduct(), entity.getSubProduct()) ;
        if(planList.isEmpty()){
            planList = fetchRMSPlanList(entity.getProduct(), entity.getSubProduct(), "release");
        }
        assignConnectPlan(entity.getId(), null, planList, OrderPlanEntity.Status.NEW);
    }

    /**
     * 给指定的工单分配plan。 只有当实体类的版本类型为 alpha 的时候，才会分配plan。
     * 这些plan都是在CI 那边自行分配的。
     * @param entity 工单实体
     * @param planNameList plan名称列表
     */
    public void assignAlphaPlan(WorkOrderEntity entity, List<String> planNameList) {
        // 给alpha 的工单分配plan。
        if(!OrderInfoModel.VERSION_TYPE_ALPHA.equalsIgnoreCase(entity.getVersionType())){
            return;
        }
        List<PlanModel> planList = fetchRMSPlanList(entity.getProduct(), entity.getSubProduct(), entity.getVersionType());
        assignConnectPlan(
                entity.getId(),
                null,
                planList.stream().filter(plan -> planNameList.contains(plan.getName())).collect(Collectors.toList()),
                OrderPlanEntity.Status.NEW
        );
    }


    /**
     * 更新 指定工单中指定Flash批次的 plan的状态至ready 状态。
     * @param orderId 工单ID
     * @param flash Flash批次
     * @param planEntity plan 列表
     */
    @Transactional
    public void updatePlanStatusToReady(long orderId, String flash, OrderPlanEntity planEntity) {
        log.info("updatePlanStatusToReady orderId:{}, flash:{}, planEntity:{}", orderId, flash, planEntity);

        planEntity.setStatus(OrderPlanEntity.Status.READY);
        planEntity.setReadyAt(System.currentTimeMillis());
        planEntity.setUpdatedAt(System.currentTimeMillis());
        planRepository.save(planEntity);

    }

    /**
     * 确认Flash时添加Plan信息
     * @param orderEntity order实体类
     * @param flashEntity flash实体类
     * @param planItemList 需要添加的Plan列表
     */
    public void buildFlashPlanList(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, List<OrderFlashConfirmV2Req.PlanItem> planItemList) {
        // 先清空这个工单下的所有的Plan，然后重新构建。
        long orderId = orderEntity.getId();
        String subProduct = orderEntity.getSubProduct();
        String product = orderEntity.getProduct();
        List<PlanModel> planList = fetchRMSPlanList(product, subProduct, null);

        if (planList.size() == 0) {
            notificationService.sendErrorNotification("确认Flash时，查询到的eReport中的Plan列表为空！");
            throw new DataNotFoundException("未找到任何相关的Plan信息！");
        }

        // 删除所有在确认Flash之前添加的Plan
        planRepository.deleteAllByOrderIdAndFlash(orderId, flashEntity.getFlash());

        // 需要添加的读卡器类型的Plan列表
        List<OrderFlashConfirmV2Req.PlanItem> generalPlanList = planItemList.stream().filter(plan -> plan.getType().equals("general")).collect(Collectors.toList());
        // 添加读卡器类型Plan列表
        buildConnectPlanList(orderId, subProduct, flashEntity, planList, generalPlanList);

        List<TerminalPlanModel> terminalPlanModelList = terminalService.fetchTerminalPlanList(product, subProduct, "");
        // 需要添加终端类型的Plan列表
        List<OrderFlashConfirmV2Req.PlanItem> terminalPlanList = planItemList.stream().filter(plan -> plan.getType().equals("terminal")).collect(Collectors.toList());
        buildTerminalPlanList(orderId, flashEntity, terminalPlanModelList, terminalPlanList);
    }

    /**
     * 添加接入模式的Plan
     * @param orderId 工单id
     * @param subProduct 所属子产品
     * @param flashEntity flash实体类
     * @param planModelList Plan Model列表
     * @param planItemList 需要添加的Plan列表
     */
    public void buildConnectPlanList(
            long orderId,
            String subProduct,
            OrderFlashEntity flashEntity,
            List<PlanModel> planModelList,
            List<OrderFlashConfirmV2Req.PlanItem> planItemList
    ) {
        // 需要添加的Plan中的非临时Plan
        Set<String> planNameList = planItemList.stream()
                .filter(p -> !p.isTemporal())
                .map(OrderFlashConfirmV2Req.PlanItem::getName)
                .collect(Collectors.toSet());

        // 需要添加的临时Plan
        List<PlanModel> temporalPlanList = planItemList.stream()
                .filter(OrderFlashConfirmV2Req.PlanItem::isTemporal)
                .map(item -> {
                    TempPlanEntity tempPlan = tmpPlanRepository.findByNameAndSubProduct(item.getName(), subProduct).orElse(null);
                    assert tempPlan != null;
                    return tempPlan.convert2PlanModel();
                })
                .collect(Collectors.toList());

        List<PlanModel> allPlanList = new ArrayList<>();
        //增加所有的非临时plan
        planModelList.stream().filter(plan -> planNameList.contains(plan.getName())).forEach(allPlanList::add);
        allPlanList.addAll(temporalPlanList);
        log.info("给工单-Flash： {} 添加读卡器类型的Plan列表：{}", flashEntity.getOrderFlashNo(), planNameList);
        assignConnectPlan(
                orderId,
                flashEntity,
                allPlanList.stream()
                        .map(m -> planItemList.stream()
                                .filter(p -> p.getName().equals(m.getName()))
                                .findFirst()
                                .map(i -> {
                                    m.setPhase(i.getPhase());
                                    m.setBelongTo(i.getTestUserID());
                                    m.setBelongToPerson(i.getTestUserName());
                                    return m;
                                })
                                .orElse(null)
                        )
                        .collect(Collectors.toList()),
                OrderPlanEntity.Status.QUEUE
        );
    }

    /**
     * 添加终端测试凭栏
     * @param orderId 工单id
     * @param flashEntity flash实体类
     * @param planModelList plan模板列表
     * @param planItemList 需要添加的Plan列表
     */
    public void buildTerminalPlanList(
            long orderId,
            OrderFlashEntity flashEntity,
            List<TerminalPlanModel> planModelList,
            List<OrderFlashConfirmV2Req.PlanItem> planItemList
    ) {
        // 需要添加的Plan
        Set<String> planNameList = planItemList.stream().map(OrderFlashConfirmV2Req.PlanItem::getName).collect(Collectors.toSet());

        //添加的plan模型信息
        List<TerminalPlanModel> allPlanList = planModelList.stream()
                .filter(plan -> planNameList.contains(plan.getName()))
                .collect(Collectors.toList());

        log.info("给工单-Flash： {} 添加终端测试类型的Plan列表：{}", flashEntity.getFlash(), planNameList);
        List<OrderPlanEntity> entities = allPlanList.stream()
                .map(m -> planItemList.stream()
                        .filter(p -> p.getName().equals(m.getName()))
                        .findFirst()
                        .map(i -> {
                            m.setPhase(i.getPhase());
                            m.setBelongTo(i.getTestUserID());
                            m.setBelongToPerson(i.getTestUserName());
                            m.setType(i.getType());     // 获取Plan的所有类型 读卡器测试或者终端测试Plan
                            return m;
                        })
                        .orElse(null)
                )
                .map(plan -> plan.toEntity(orderId, flashEntity))
                .collect(Collectors.toList());

        planRepository.saveAll(entities);
    }


    /**
     * 将 对应的设备列表 添加到数据库，并与plan 关联。
     * @param plan       需要添加设备的plan。
     * @param deviceList 设备列表
     */
    @Transactional
    public void addDevicesToPlan(OrderPlanEntity plan , List<DeviceModel> deviceList) {
        if( deviceList.isEmpty()) {
            log.warn(" can not add 0 devices to plan: {}",plan);
            notificationService.sendErrorNotification("添加设备到Plan失败,设备列表为空");
            return;
        }

        // TODO 此处暂时仅作通知处理
        // 当分配冲突时，发送钉钉工作通知，需要加快处理
        deviceList.forEach(device -> {
            List<DeviceLockInfoEntity> deviceLocks = deviceLockInfoRepository.findByMacInAndLockedTrue(List.of(device.getMac()));
            Optional<DeviceLockInfoEntity> deviceLock = deviceLocks.stream().findFirst();
            deviceLock.ifPresent(dev -> notificationService.sendAssignPlanErrNotice(dev.getNo() + "已被其他Plan锁定，请查看并定位问题"));
        });

        int planTestNum = plan.getTestNum() ;
        // 获取plan的旧设备列表
        List<PlanDeviceEntity> oldDeviceEntityList = planDeviceRepository.findAllByPlanId(plan.getId()) ;
        int leftNum = addDevicesToPlan(plan, deviceList, planTestNum,oldDeviceEntityList,false);
        log.info("add {} devices to plan: {} ,expect num: {}, left num: {}", deviceList.size(), plan.getName(), planTestNum, leftNum);
    }

    /**
     * 替换Plan中的指定的设备列表 替换成 deviceList 中的设备列表,并且将 planTestNum 个样片 平均分配到 deviceList 中的设备上。
     * 一方面也是为了后续的单台设备分配做准备。
     *
     * @param plan           需要添加设备的plan。
     * @param deviceList     新的设备列表
     * @param planTestNum    需要分配的样片数量
     * @param oldDeviceList  旧的设备列表
     * @return 返回分配后剩余的样片数量
     */
    @Transactional
    private int addDevicesToPlan(
            OrderPlanEntity plan ,
            List<DeviceModel> deviceList,
            int planTestNum,
            List<PlanDeviceEntity> oldDeviceList,
            boolean isReplace
    ) {
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(plan.getOrderId());
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(plan.getOrderId(), plan.getFlash());

        int avgNum = planTestNum / deviceList.size();
        log.info(" add {} devices :{}  to plan:{}",deviceList.size(),deviceList, plan.getName());

        // 设置新的设备列表
        List<PlanDeviceEntity> newDeviceEntityList = new ArrayList<>() ;
        int leftNum = planTestNum;
        int canTestSampleNum = 0 ;
        for (int i = 0; i < deviceList.size(); i++) {
            DeviceModel m = deviceList.get(i);
            PlanDeviceEntity p = DeviceModel.toEntity(m, plan.getId(), plan.getOrderId()) ;
            int minNum = deviceService.getTestNum(m, orderEntity, plan);
            canTestSampleNum += minNum ;

            // 尽可能的把样片数量平均分配到每个设备上 ，
            // 如果最后一个剩余过多的话，使用最小容量。

            p.updateInfo(
                    Math.min(i == deviceList.size() - 1 ? leftNum : avgNum, minNum),
                    plan.getName()
            );

            leftNum -= p.getTestNum();
            newDeviceEntityList.add(p);
        }
        if(!isReplace){
            // 如果是替换的话,就不需要再更新 Plan分配信息中的expectNum了
            planAssignService.updateExceptedNum(plan, newDeviceEntityList, canTestSampleNum / deviceList.size());
        }
        List<PlanDeviceEntity> holdDeviceList = deviceService.saveDeviceList(
                isReplace ? DEVICES_REPLACED : DEVICES_ASSIGNED_TO_PLAN,
                oldDeviceList,
                newDeviceEntityList);

        rmsDeviceService.holdDevices(
                orderEntity.getSubProduct(),
                flashEntity.getOrderFlashNo(),
                plan,
                holdDeviceList,
                PlanDeviceEntity.Status.OCCUPIED
        );

        log.info(
                "自动分配{}给{}下的{}",
                deviceList.stream().map(d -> d.getPcNo() + "(" + d.getIp() + ")").collect(Collectors.joining(",")),
                flashEntity.getFlash(),
                plan.getName()
        );


        return leftNum;
    }


    /**
     * Plan 自动分配，需要给Plan添加设备、更新Plan状态、发送钉钉通知
     * 通过Plan与设备的Map 以及 plan实体类 来添加 plan的设备依赖 到数据库中。
     * @param orderEntity order的实体类
     * @param flashEntity flash的实体类
     * @param planDeviceMap plan和对应需要设备的map
     * @param planEntities plan的实体类
     */
    @Transactional
    public void assignDevicesToPlan(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            Map<String, List<DeviceModel>> planDeviceMap,
            List<OrderPlanEntity> planEntities
    ) {
        log.info("工单[{}] flash {} , 参与此次Plan预分配的plan共有 {} ",
                orderEntity.getId(),
                flashEntity.getFlash(),
                planEntities.stream().map(OrderPlanEntity::getName).collect(Collectors.toList())
        );
        AtomicInteger totalNum = new AtomicInteger();
        planDeviceMap.forEach((planName, devices) -> planEntities.stream()
                .filter(p -> p.getName().equals(planName))
                .findFirst()
                .ifPresent(planEntity -> {
                    // 统计此次分配Plan消耗样片的数量
                    totalNum.addAndGet(planEntity.getTestNum());

                    // 给Plan分配设备
                    addDevicesToPlan(planEntity, devices);

                    // 将对应的Plan 设置为 ready 状态。
                    updatePlanStatusToReady(orderEntity.getId(), flashEntity.getFlash(), planEntity);

                    // 给plan的测试负责人发送通知。
                    notificationService.sendPlanReadyNotification(orderEntity, flashEntity, planEntity);
                })
        );
        log.info("此次分配flash批次 {} 下的Plan共消耗 {} 样片", flashEntity.getFlash(), totalNum.get());
        //  更新 Flash 批次样片 占用数量。
        flashService.updateLeftSampleNum(orderEntity.getId(), flashEntity.getFlash(), -totalNum.get());
    }

    /**
     * 强行将指定的设备列表添加到指定的plan中。
     * @param flashEntity flash 实体类
     * @param planEntity  plan实体类
     * @param macList 设备的mac列表
     * @param userDetail 用户信息
     */
    @Transactional
    public void forceAddDevicesToPlan(
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            List<String> macList,
            OAuthUserDetail userDetail
    ) {
        
        List<DeviceLockInfoEntity> needHandleDeviceList = new ArrayList<>();
        rmsDeviceService.findAllLockDevicesByMacList(macList).forEach(data -> {
            // 如果当前的Plan 与找到的data 中的设备的Plan信息不一致，则表示这个设备是被其他的Plan占用的。
            boolean isSamePlan = data.getPlanId() == planEntity.getId();
            if (!isSamePlan) {
                log.info(data.getPlanName() + "的设备" + data.getNo() + "被" + planEntity.getName() + "抢占, 需要更新lock_info数据！");
                // 如果不是同一个Plan的话, 则需要检测这个Plan 是否在planList中.
                // 将需要移除的设备记录下来.
                needHandleDeviceList.add(data);
            }
            // 如果是同一个plan的话,也不需要处理这个data.
        });
        long orderId = planEntity.getOrderId();
        long planId = planEntity.getId();
        String subProduct = productContext.getOrderSubProduct(orderId);

        List<PlanDeviceEntity> planDeviceList = planDeviceRepository.findAllByPlanIdAndMacIn(planId, macList);

        List<PlanDeviceEntity> oldDeviceList = planDeviceList.stream()
                .filter(d -> macList.contains(d.getMac()))
                .collect(Collectors.toList());

        List<DeviceModel> deviceModelList = rmsDeviceService.fetchDevicesByMacList(subProduct, macList);

        List<PlanDeviceEntity> deviceList = deviceModelList.stream()
                .map(m -> {
                    PlanDeviceEntity deviceEntity = oldDeviceList
                            .stream()
                            .filter(d-> d.isSameDevice(m))
                            .findFirst()
                            .map(PlanDeviceEntity::duplicateEmpty)
                            .orElse(DeviceModel.toEntity(m, planId, orderId));

                    deviceEntity.setStartAt(null);
                    deviceEntity.setEndAt(null);
                    deviceEntity.setFailReason(null);
                    deviceEntity.setActualNum(null);
                    deviceEntity.setStatus(PlanDeviceEntity.Status.CONFIRMED);
                    deviceEntity.setConfirmAt(System.currentTimeMillis());

                    deviceEntity.setTestNum(0) ;
                    deviceEntity.setPlanName(planEntity.getName());
                    deviceEntity.setAddedBy(userDetail.getUid());
                    deviceEntity.setAddedPerson(userDetail.getUsername());
                    deviceEntity.setCreatedAt(System.currentTimeMillis());
                    deviceEntity.setUpdatedAt(System.currentTimeMillis());
                    return deviceEntity ;
                })
                .collect(Collectors.toList());

        // 需要对设备进行枷锁。
        // 如果是之前也是添加的这些设备,那么就需要移入历史状态.
        List<PlanDeviceEntity> holdDeviceList = deviceService.saveDeviceList("添加设备", oldDeviceList, deviceList);
        // 抢占的电脑/手动的电脑,一开始不在对应的Plan 中,所以无法通过Plan来查找设备,并锁定,而是要在这里进行锁定. #191
        rmsDeviceService.holdDevices(subProduct, flashEntity.getOrderFlashNo(), planEntity, holdDeviceList,PlanDeviceEntity.Status.CONFIRMED);

        deviceService.saveDeviceControlList(
                productContext.getOrderSubProduct(planEntity.getOrderId()),
                flashEntity,
                DingTalkUtils.getCurrentUserName(),
                "Plan添加电脑后电脑开机",
                true,
                deviceList
        );

        if(planEntity.getStatus() == OrderPlanEntity.Status.COMPLETED) {
            PlanHistoryEntity historyEntity =  PlanHistoryEntity.of(planEntity) ;
            planHistoryRepository.save(historyEntity);

            planEntity.clearEndStatus();
            planEntity.setStatus(OrderPlanEntity.Status.RUNNING);

            planEntity.setStartAt(System.currentTimeMillis());
            planEntity.setStartBy(userDetail.getUid());
            planEntity.setStartPerson(userDetail.getUsername());

            planRepository.save(planEntity);
        }
        handleDeviceLockData(needHandleDeviceList);


    }

    /**
     * 确认plan 的环境。
     * @param orderEntity plan 对应的工单
     * @param flashEntity plan 对应的 Flash 批次
     * @param deviceInfoList 前端选择的plan
     */
    @Transactional
    public List<OrderPlanEntity> confirmEnv(
            WorkOrderEntity orderEntity ,
            OrderFlashEntity flashEntity ,
            List<OrderPlanEntity> planList,
            List<OrderEnvConfirmReq.PlanDeviceInfo> deviceInfoList
    ){
        String orderNo = flashEntity.getOrderFlashNo();
        // 将plan 对应的设备展开成 一个所有设备的列表。
        // 过滤空的ip
        List<String> newDeviceMacList = deviceInfoList.stream()
                .map(OrderEnvConfirmReq.PlanDeviceInfo::getMacList)
                .flatMap(Collection::stream)
                .filter(s -> !TextUtils.isBlank(s))
                .collect(Collectors.toList());


        String userDingTalkID = DingTalkUtils.getCurrentUserDingTalkID();
        String confirmPerson = DingTalkUtils.getCurrentUserName();

        // 获取Redis中的设备信息
        List<DeviceModel> deviceModelList = rmsDeviceService.fetchDevicesByMacList(orderEntity.getSubProduct(), newDeviceMacList);
        if (deviceModelList.size() != newDeviceMacList.size()) {
            throw new IllegalArgumentException("参数中存在无效的设备");
        }

        // 检查此次分配中，有多少设备属于新赠的设备，未使用的旧设备手动释放掉
        planList.forEach(planEntity -> {
            // 获取之前系统自动分配的旧设备列表。
            List<PlanDeviceEntity> oldDeviceList = planDeviceRepository.findAllByPlanId(planEntity.getId());

            // 预分配中的设备如果不存在确认列表中，则表示这个设备需要解锁
            List<PlanDeviceEntity> needUnLockDevices = oldDeviceList.stream().filter(s -> !newDeviceMacList.contains(s.getMac())).collect(Collectors.toList());

            log.info("{} 预分配设备中需要解锁的设备列表:{}",
                    planEntity.getName(),
                    needUnLockDevices.stream().map(d -> String.format("%s(%s - %s)", d.getNo(), d.getIp(), d.getMac()))
                            .collect(Collectors.joining(","))
            );

            // 解锁需要解锁设备
            rmsDeviceService.releaseDevices(orderEntity.getSubProduct(), orderNo, planEntity, needUnLockDevices);
        });

        // 此次确认分配的电脑中，占用了之前分配给本次分配的其他Plan的电脑，即抢占了本次其他Plan的设备有哪些
        List<DeviceLockInfoEntity> needHandleDeviceList = new ArrayList<>();
        deviceInfoList.forEach(info -> {
            OrderPlanEntity planEntity = planList.stream().filter(p -> p.getId() == info.getPlanId()).findFirst().orElse(null);
            assert planEntity != null;

            // 获取lock_info表中的信息
            List<DeviceLockInfoEntity> deviceLockDataList = rmsDeviceService.findAllLockDevicesByMacList(info.getMacList());
            log.info("deviceLockDataList:{}", deviceLockDataList);
            deviceLockDataList.forEach(data -> {
                String mac = data.getMac();
                if (!info.getMacList().contains(mac)) {
                    // 最后没有找到这个信息的话, 则表示异常.
                    return;
                }
                // 如果当前的Plan 与找到的data 中的设备的Plan信息不一致，则表示这个设备是被其他的Plan占用的。
                boolean isSamePlan = data.getPlanId() == planEntity.getId();

                boolean existInPlanList = planList.stream().noneMatch(p -> Objects.equals(p.getName(), data.getPlanName()));
                if (!isSamePlan && existInPlanList) {
                    // 然后在数据库中查看这个设备的信息
                    PlanDeviceEntity device = planDeviceRepository.findById(data.getDeviceId()).orElse(null);
                    if (device == null) {
                        throw new IllegalArgumentException("设备" + mac + "不存在");
                    }

                    if (device.getStatus() == PlanDeviceEntity.Status.RUNNING || device.getStatus() == PlanDeviceEntity.Status.CONFIRMED) {
                        throw new IllegalArgumentException("设备" + mac + "已经被其他Plan占用");
                    }

                    // 如果不是同一个Plan的话, 则需要检测这个Plan 是否在planList中.
                    // 将需要移除的设备记录下来.
                    needHandleDeviceList.add(data);
                }
                // 如果是同一个plan的话,也不需要处理这个data.
            });
        });
        log.info("需要处理的设备列表:{}", needHandleDeviceList);

        deviceInfoList.forEach(info -> {
            List<DeviceModel> devices = rmsDeviceService.fetchDevicesByMacList(orderEntity.getSubProduct(), info.getMacList());

            OrderPlanEntity planEntity = planList.stream().filter(p -> p.getId() == info.getPlanId()).findFirst().orElse(null);
            assert planEntity != null;

            log.info("更新Plan：{} 的状态至： 资源分配完成", planEntity.getName());
            // 更新Plan 的状态为Confirmed
            planEntity.setStatus(OrderPlanEntity.Status.CONFIRMED);
            planEntity.setUpdatedAt(System.currentTimeMillis());
            planEntity.setConfirmedAt(System.currentTimeMillis());
            planEntity.setConfirmedBy(userDingTalkID);
            planEntity.setConfirmedPerson(confirmPerson);
            planRepository.save(planEntity);
            log.info("给 {} 分配设备：{}", planEntity.getName(), devices.stream().map(DeviceModel::getPcNo).collect(Collectors.toList()));

            // 保存此次分配的信息
            int leftNumExcepted = confirmPlanDevices(orderEntity, flashEntity, planEntity, devices);
            // 由于确认设备时,减少了部分设备,导致使用的预期样片数量变少了, 所以需要将这多出来的样片数量放回Flash批次中.
            if (leftNumExcepted > 0) {
                log.info("{} 预期样片数:{} ,实际{}分配{}个样片, 将 {} 个样片放回Flash批次中",
                        planEntity.getName(),
                        planEntity.getTestNum(),
                        confirmPerson,
                        planEntity.getTestNum() - leftNumExcepted,
                        leftNumExcepted
                );
                flashService.updateLeftSampleNum(flashEntity, leftNumExcepted);
            }

        });
        // 处理这轮确认Plan环境抢占Plan的设备
        return handleDeviceLockData(needHandleDeviceList);
    }

    /**
     * 处理被抢占的Plan的分配,如果不能分配,则回到排队状态.
     * @param deviceLockDataList 被抢占的电脑
     * @return 处理过的Plan列表
     */
    private List<OrderPlanEntity> handleDeviceLockData(List<DeviceLockInfoEntity> deviceLockDataList) {

        // 将needHandleDeviceList 中的这些设备按照  orderId + Flash + Plan 来进行分组
        Map<String, List<DeviceLockInfoEntity>> groupByOrderIdAndFlashAndPlan = deviceLockDataList.stream()
                .collect(Collectors.groupingBy(data -> data.getOrderId() +";" + data.getFlash()  +";" + data.getPlanName()));
        List<OrderPlanEntity> planEntityList = new ArrayList<>();

        //然后将这些设备从这些Plan中移除 ,或者替换成其他的空闲设备.
        groupByOrderIdAndFlashAndPlan.forEach((key, lockDataList) -> {

            DeviceLockInfoEntity firstLockData = lockDataList.get(0) ;
            long orderId = firstLockData.getOrderId() ;
            String flash = firstLockData.getFlash() ;
            String planName = firstLockData.getPlanName() ;
            String product = firstLockData.getProduct();

            List<DeviceModel> availableDevices = rmsDeviceService.getAvailableDevices(product);

            // 查询出对应的Plan.
            WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
            OrderPlanEntity planEntity = planRepository.findByOrderIdAndFlashAndName(orderId, flash, planName);
            OrderFlashEntity lockFlashEntity = flashService.findFlashOrElseThrow(firstLockData.getLockedOrderFlashNo());

            // 阶段2 ,将这些设备从这个Plan中移除.
            // 将deviceList 中的设备替换成其他的相近的相仿的设备.
            // 如果没有办法替换的话,则让这个Plan重新回到排队中.
            List<String> macList = lockDataList.stream().map(DeviceLockInfoEntity::getMac).collect(Collectors.toList());
            List<PlanDeviceEntity> usedDevices = planDeviceRepository.findAllByPlanIdAndMacIn(planEntity.getId(), macList);

            boolean result = replacePlanDevicesAutomatic(
                    orderEntity,
                    planEntity,
                    usedDevices,
                    availableDevices
            );
            if(!result) {
                // 如果没有办法替换的话,则先将这些设备先移除掉,然后再让这个Plan重新回到排队中.
                deviceService.saveDeviceList("设备被抢占",usedDevices, new ArrayList<>());
                rollbackPlanToQueueStatus(lockFlashEntity, planEntity,"设备被抢占");
            }
            planEntityList.add(planEntity) ;

        });
        return planEntityList ;

    }

    /**
     * 给指定的Plan确认 环境
     * @param orderEntity 工单 实体
     * @param flashEntity 批次实体
     * @param planEntity Plan 实体
     * @param deviceList 设备列表
     * @return 返回还剩余的没有处理的样片数量
     */
    private int confirmPlanDevices(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity ,
            List<DeviceModel> deviceList
    ) {
        int num = planEntity.getTestNum();
        if (deviceList.isEmpty()) {
            log.warn(" can not add 0 devices to plan: {}", planEntity);
            return num;
        }

        // 尽可能的把样片数量平均分配到每个设备上 , 如果不能整除,就平均分配到前面的设备上。
        int avgNum = num / deviceList.size() + (num % deviceList.size() > 0 ? 1 : 0);

        List<String> attrs = Arrays.asList(planEntity.getAttrs().split(";"));

        log.info("[confirmed] add {} devices :{}  to planEntity:{}", deviceList.size(), deviceList, planEntity.getName());
        // 获取plan的旧设备列表
        List<PlanDeviceEntity> oldDeviceEntityList = planDeviceRepository.findAllByPlanId(planEntity.getId());

        // 设置新的设备列表
        List<PlanDeviceEntity> newDeviceEntityList = new ArrayList<>();
        for (int i = 0; i < deviceList.size(); i++) {
            DeviceModel m = deviceList.get(i);
            int minNum = attrs.stream()
                    .map(m::getTestNumByAttr)
                    .min(Comparator.comparingInt(Integer::intValue))
                    .orElse(0);

            PlanDeviceEntity p = DeviceModel.toEntity(m, planEntity.getId(), orderEntity.getId());
            p.updateInfo(
                    Math.min(i == deviceList.size() - 1 ? num : avgNum, minNum),
                    planEntity.getName()
            );
            p.setStatus(PlanDeviceEntity.Status.CONFIRMED);
            p.setConfirmAt(System.currentTimeMillis());
            num -= p.getTestNum();
            newDeviceEntityList.add(p);
        }
        List<PlanDeviceEntity> holdDeviceList = deviceService.saveDeviceList(
                "确认Plan的设备",
                oldDeviceEntityList,
                newDeviceEntityList);

        // 锁定新的设备,并且更新设备的状态为占用(Redis)
        rmsDeviceService.holdDevices(
                orderEntity.getSubProduct(),
                flashEntity.getOrderFlashNo(),
                planEntity,
                holdDeviceList,
                PlanDeviceEntity.Status.CONFIRMED
        );
        // Plan确认环境后电脑开机
        deviceService.saveDeviceControlList(
                orderEntity.getSubProduct(),
                flashEntity,
                DingTalkUtils.getCurrentUserName(),
                "Plan确认环境后电脑开机",
                true,
                newDeviceEntityList
        );
        return num;
    }

    /**
     * 将planEntity中的已经使用了的设备 usedDevices 替换成availableDevices 中的 usedDevices.size() 个设备.
     * @param orderEntity    工单
     * @param planEntity    Plan 实体
     * @param usedDevices   已经使用了的设备,旧设备,这些设备会被替换掉, 并且不会被释放.
     * @param availableDevices 可用的设备,新设备,这些设备中的一些设备会被替换成旧设备.
     * @return 如果替换成功,则返回true,否则返回false.
     */
    private boolean replacePlanDevicesAutomatic(
            WorkOrderEntity orderEntity,
            OrderPlanEntity planEntity,
            List<PlanDeviceEntity> usedDevices,
            List<DeviceModel> availableDevices
    ) {
        if(usedDevices.isEmpty() || availableDevices.isEmpty()) {
            return false;
        }
        // 从空闲设备中找出 ipList.size() 个设备,并且将这些设备分配给这个Plan
        List<DeviceModel> matchedDevices = deviceService.findSupportDevicesByPlan(orderEntity, planEntity, false, availableDevices);

        // 如果matchedDevices.size() < ipList.size() 的话,则表示没有办法替换.
        if(matchedDevices.size() < usedDevices.size()) {
            return false;
        }
        else {
            // 如果matchedDevices.size() >= ipList.size() 的话,则表示可以替换.
            List<DeviceModel> newDevices = matchedDevices.subList(0, usedDevices.size());
            if (newDevices.isEmpty()) {
                return false;
            }
            addDevicesToPlan(planEntity,
                    newDevices,
                    usedDevices.stream().map(PlanDeviceEntity::getTestNum).reduce(Integer::sum).orElse(0),
                    usedDevices,
                    true);
            return true;
        }
    }


    /**
     * 将Plan 回滚到排队状态.
     * @param flashEntity flash批次
     * @param planEntity plan
     * @param title 更新标题
     */
    private void rollbackPlanToQueueStatus(OrderFlashEntity flashEntity ,OrderPlanEntity planEntity,String title) {

        List<PlanDeviceEntity> deviceList = planDeviceRepository.findAllByPlanId(planEntity.getId());

        if(!deviceList.isEmpty()){
            log.info("释放还未开始测试的设备，直接释放样片！");
            flashService.updateLeftSampleNum(flashEntity, planEntity.getTestNum());
            // 更新设备历史
            deviceService.saveDeviceList(title,deviceList, new ArrayList<>());

            // 如果已经分配了设备,则解锁对应的设备
            rmsDeviceService.releaseDevices(
                    productContext.getOrderSubProduct(planEntity.getOrderId()),
                    flashEntity.getOrderFlashNo(),
                    planEntity,
                    deviceList
            );

            // 如果是暂缓分配则需要将设备关机
            if ("暂缓分配".equals(title) && planEntity.getStatus() == OrderPlanEntity.Status.CONFIRMED) {
                deviceService.saveDeviceControlList(
                        productContext.getOrderSubProduct(planEntity.getOrderId()),
                        flashEntity,
                        DingTalkUtils.getCurrentUserName(),
                        "Plan暂缓分配后电脑关机",
                        false,
                        deviceList
                );
            }
            log.debug("Flash {} Plan {} already set {} devices. delete and release !  ",flashEntity.getFlash(), planEntity.getName(), deviceList.size());
        }

        planEntity.setStatus(OrderPlanEntity.Status.QUEUE);
        planEntity.setUpdatedAt(System.currentTimeMillis());
        planEntity.setConfirmedBy(null);
        planEntity.setConfirmedAt(null);
        planEntity.setConfirmedPerson(null);
        planEntity.setReadyAt(null);
        planRepository.save(planEntity);

        // 更新PlanAssignInfoEntity的状态至WAITING
        planAssignService.pauseQueue(planEntity);

        log.info("[{}] 清空 {} 下 {} 状态，并更新至排队状态！", title, flashEntity.getFlash() ,planEntity.getName());
    }


    /**
     * 通过plan 来释放对应的设备资源。
     * @param title 操作名称 分为撤销工单、取消Flash测试
     * @param flashEntity flash实体
     * @param planEntity plan实体
     * @param orderFlashNo flash 批次所属的RMS工单
     */
    public void revokeDevicesByPlan(String title, OrderFlashEntity flashEntity, OrderPlanEntity planEntity, String orderFlashNo) {
        OrderPlanEntity.Status status = planEntity.getStatus();
        List<PlanDeviceEntity> allDeviceEntityList = planDeviceRepository.findAllByPlanId(planEntity.getId());
        // 对于还没释放，且 没有正在测试中的设备，则可以释放。
        List<PlanDeviceEntity> releaseDeviceEntityList = allDeviceEntityList.stream()
                .filter(p -> p.getReleaseAt() == null && p.getStatus() != PlanDeviceEntity.Status.RUNNING)
                .collect(Collectors.toList());

        // 解锁设备
        rmsDeviceService.releaseDevices(productContext.getOrderSubProduct(planEntity.getOrderId()), orderFlashNo, planEntity, releaseDeviceEntityList);
        // 删除plan 对应的设备信息
        // 不应该删除plan 对应的设备信息，而是应该将 对应的设备信息 状态改为 已释放。
        List<PlanDeviceEntity> newDeviceEntityList = releaseDeviceEntityList.stream().map(PlanDeviceEntity::duplicate).collect(Collectors.toList());

        newDeviceEntityList.forEach(planDeviceEntity -> {
            planDeviceEntity.setStatus(PlanDeviceEntity.Status.CANCELED);
            planDeviceEntity.setUpdatedAt(System.currentTimeMillis());
        });

        // 对plan下设备进行的操作备注，撤销Plan 或者 取消Plan
        deviceService.saveDeviceList(title, releaseDeviceEntityList, newDeviceEntityList);

        // 将Plan的Assign 状态改为停止.
        planAssignService.terminatePlan(planEntity);

        // 对于还在运行中的设备，则需要停止测试，并且释放设备。
        List<PlanDeviceEntity> runningDeviceEntityList = allDeviceEntityList.stream()
                .filter(p -> p.getStatus() == PlanDeviceEntity.Status.RUNNING)
                .collect(Collectors.toList());

        // 转换成ip 列表
        List<String> runningIpList = runningDeviceEntityList.stream().map(PlanDeviceEntity::getIp).collect(Collectors.toList());
        List<String> macList = runningDeviceEntityList.stream().map(PlanDeviceEntity::getMac).collect(Collectors.toList());

        if (!runningIpList.isEmpty()) {
            // 停止测试.
            deviceService.stopTestPlanAndCheck(
                    planEntity,
                    StopTestParams.builder()
                            .orderNo(orderFlashNo)
                            .planDeviceList(Collections.singletonList(new StopTestParams.PlanDeviceIpInfo(planEntity.getName(), macList, runningIpList)))
                            .build());
        }

        // 如果Plan已经
        if (status == OrderPlanEntity.Status.COMPLETED || status == OrderPlanEntity.Status.RUNNING) {
            // 获取运行中或者占用状态的电脑
            List<PlanDeviceEntity> unfinishedDeviceList = allDeviceEntityList.stream()
                    .filter(d -> !COMPLETED_STATUS_LIST.contains(d.getStatus()))
                    .collect(Collectors.toList());
            // 获取当前测试失败并且未释放的电脑
            List<PlanDeviceEntity> failDeviceEntityList = allDeviceEntityList.stream().filter(d ->
                    d.getStatus() == PlanDeviceEntity.Status.FINISHED_FAILED && d.getReleaseAt() == null)
                    .collect(Collectors.toList());
            unfinishedDeviceList.addAll(failDeviceEntityList);

            deviceService.saveDeviceControlList(
                    productContext.getOrderSubProduct(planEntity.getOrderId()),
                    flashEntity,
                    DingTalkUtils.getCurrentUserName(),
                    "Flash取消测试后电脑关机",
                    false,
                    unfinishedDeviceList
            );

        }

    }


    /**
     * 获取理想情况下的 工单下plan 所需要设备的数量
     * @param orderId 工单ID
     * @return 所需要设备的数量。
     */
    public int fetchPlanDeviceCount(long orderId){

        List<OrderPlanEntity> orderPlanEntityList = planRepository.findAllByOrderIdAndType(orderId, 0);

        // 获取plan的测试数量。
        // 对于每个设备最少测试的plan数量。需要有个基准才能判断、
        return orderPlanEntityList.stream().map(OrderPlanEntity::getTestNum).map(i -> (int)Math.ceil( i / 4.0)) .reduce(0,Integer::sum);
    }

    /**
     * 获取工单下的所有已经准备就绪的plan
     * @param orderId 工单ID
     * @return 已经准备就绪的plan 数
     */
    public int getReadyPlanCount(long orderId, String flash) {
        return planRepository.countByOrderIdAndFlashAndStatus(orderId, flash, OrderPlanEntity.Status.READY) ;
    }

    /**
     * 获取工单下的所有等待分配环境的plan
     * @param orderId 工单ID
     * @return 已经等待分配环境的plan 数
     */
    public int getQueuePlanCount(long orderId, String flash){
        return planRepository.countByOrderIdAndFlashAndStatus(orderId, flash, OrderPlanEntity.Status.QUEUE);
    }

    /**
     * 获取工单下的所有测试中的Plan
     * @param orderId 工单ID
     * @return 测试中的plan 数
     */
    public int getTestingPlanCount(long orderId, String flash) {
        return planRepository.countByOrderIdAndFlashAndStatus(orderId, flash, OrderPlanEntity.Status.RUNNING) ;
    }

    /**
     * 获取工单下的所有已经测试完成的plan
     * @param orderId 工单ID
     * @return 已经测试完成的plan 数
     */
    public int getCompletedPlanCount(long orderId, String flash) {
        return planRepository.countByOrderIdAndFlashAndStatus(orderId, flash, OrderPlanEntity.Status.COMPLETED) ;
    }

    public int leftPlanCount(String product,List<OrderPlanEntity> planEntityList) {

        if("SATA".equalsIgnoreCase(product) || "PCIe".equalsIgnoreCase(product)){
            return planEntityList.size() ;
        }

        return (int) (5 - planEntityList.stream().filter(planEntity -> planEntity.getStatus() == OrderPlanEntity.Status.READY).count());
    }

    /**
     * 获取当前Flash批次测试人员还可分配的Plan数量
     * 通过可分配额度 - 当前批次下某个测试人员已分配的Plan数量 获得
     * @param planEntityList 工单的所有Plan列表
     * @param userId 测试人员
     * @return 某一个测试人员可分配的Plan额度
     */
    public int leftPlanCountByUserId(List<OrderPlanEntity> planEntityList, String userId) {
        long num = planEntityList.stream()
                .filter(planEntity -> planEntity.getStatus() == OrderPlanEntity.Status.READY)
                .filter(p -> userId.equals(p.getBelongTo()))
                .count();
        if (num > 5) {
            return 0;
        } else {
            return (int) (5 - num);
        }
    }

    /**
     * 检查 Plan的依赖关系，是否符合要求。
     * @param p 需要检查的Plan
     * @param allPlanList Flash 批次下所有的Plan
     *
     * @return 检查结果，如果依赖的plan 已经完成或者没有依赖的Plan，则返回true，否则返回false。
     */
    public boolean checkParent(OrderPlanEntity p, List<OrderPlanEntity> allPlanList) {
        // 判断 p.getParent的Plan 是否属于完成状态。
        String parentPlan = p.getParentPlan();
        if (parentPlan == null) {
            return true;
        }
        List<String> plans = Arrays.asList(parentPlan.split(","));

        // 当前批次的所有Plan列表中不存在 未测试完成的依赖的Plan ，则可以测试。
        return  allPlanList
                .stream()
                .noneMatch(plan -> plans.contains(plan.getName()) && !plan.isFinish());
    }


    /**
     * 获取需要测试的Plan的列表。
     * 一个批次最多同时只能有5个plan 待确认环境。
     *
     * @param subProduct 子产品
     * @param orderId 工单id
     * @param flash flash 批次
     * @return 需要测试的plan 列表
     */
    public List<OrderPlanEntity> fetchNeedTestPlanList(String subProduct, long orderId, String flash) {
        OrderFlashEntity orderFlashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        int leftFlashNum = orderFlashEntity.getLeftNum() ;
        if(leftFlashNum == 0) {
            return new ArrayList<>();
        }
        // 查出这个批次下所有的plan列表。
        List<OrderPlanEntity> allPlanEntityList = planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash) ;
        // 如果planEntityList 中 status为OrderPlanEntity.Status.READY的数量超过了5个的话，则直接返回空的列表。
        int leftPlanCount = leftPlanCount(subProduct, allPlanEntityList);
        if(leftPlanCount <= 0) {
            return new ArrayList<>();
        }
        // 将Plan 进行排序。
        List<OrderPlanEntity> sortedPlanList = sortPlanList(allPlanEntityList);

        // 然后 从 sortedPlanList 中获取可以测试的Plan。
        List<OrderPlanEntity> planEntityList =  sortedPlanList.stream()
                .filter(OrderPlanEntity::isQueue)
                .filter(this::isPlanInQueue)
                .collect(Collectors.toList());

        // 找到最后一个测试全部样片的Plan，在这个Plan之前的Plan都可以测试。
        int testAllIndex = planEntityList.stream()
                .filter(p -> p.isTestAll() && p.getStatus() != OrderPlanEntity.Status.RUNNING )
                .mapToInt(planEntityList::indexOf)
                .findFirst()
                .orElse(-1);
        if(testAllIndex >= 0) {
            planEntityList = planEntityList.subList(0, testAllIndex + 1);
        }
        log.info("fetchNeedTestPlanList planEntityList: {}", planEntityList);


        return getValidPlanList(allPlanEntityList, leftFlashNum, leftPlanCount, planEntityList.subList(0, Math.min(planEntityList.size(), leftPlanCount)));

    }

    /**
     * 检查plan是否已经在队列中。
     * @param planEntity 需要检查的plan
     * @return 如果plan已经在队列中，则返回true，否则返回false。
     */
    public boolean isPlanInQueue(OrderPlanEntity planEntity) {

        // 如果Plan是手动Plan,并且不是测试全部样片的Plan，则不需要检查。
        if(planEntity.isManualPlan() && !planEntity.isTestAll()) {
            return false;
        }
        if (planEntity.getStatus() == OrderPlanEntity.Status.RUNNING) {
            return false;
        }

        PlanAssignInfoEntity assignInfo = planAssignService.findPlanAssignInfo(planEntity.getId());
        if(assignInfo == null) {
            boolean isQueue = planEntity.getStatus() == OrderPlanEntity.Status.QUEUE ;

            // 需要考虑到 测试全部样片的Plan
            boolean isManualConfirmed = planEntity.getStatus() == OrderPlanEntity.Status.CONFIRMED && planEntity.isManualPlan() && planEntity.isTestAll();
            return isQueue || isManualConfirmed;
        }
        log.debug("Plan {} is in queue, status is {}", planEntity, assignInfo.getStatus());
        // 如果已经完成分配,或者尚未分配,则不需要再分配
        return assignInfo.getStatus() == PlanAssignInfoEntity.Status.QUEUE || assignInfo.getStatus() == PlanAssignInfoEntity.Status.WAITING;
    }

    /**
     * 从筛选出来的Plan中，获取可以测试的Plan列表。
     * @param allPlanEntityList 所有的Plan列表
     * @param leftFlashNum 剩余的Flash 样片的数量
     * @param leftPlanCount 剩余的Plan 的配额
     * @param needTestPlanList 预筛选的Plan 列表
     * @return 返回从 {needTestPlanList} 中筛选出来适合的Plan。
     */
    public List<OrderPlanEntity> getValidPlanList(
            List<OrderPlanEntity> allPlanEntityList,
            Integer leftFlashNum,
            int leftPlanCount,
            List<OrderPlanEntity> needTestPlanList
    ) {
        // 如果剩余的数量小于需要测试的数量，那么就只取小于 剩余数量的plan。
        List<OrderPlanEntity> planEntityList = needTestPlanList.stream()
                .filter( p -> p.getTestNum() <= leftFlashNum)
                // 判断父plan是否完成，如果完成，则可以测试。
                .filter(p -> checkParent(p, allPlanEntityList))
                // 过滤手动Plan，因为手动Plan 不需要分配设备
                .filter(p -> !p.isManualPlan())
                .sorted(Comparator.comparing(OrderPlanEntity::getPriority).reversed())
                .collect(Collectors.toList());
        if(planEntityList.isEmpty()) {
            return new ArrayList<>();
        }

        List<Integer> sumList = new ArrayList<>();
        AtomicInteger lastSum = new AtomicInteger();
        planEntityList.forEach( p -> {
            lastSum.addAndGet(p.getTestNum());
            sumList.add(lastSum.get()) ;
        });
        // 使用 binarySearch 来查找最后一个小于等于 剩余数量的plan 总数。
        int index = Collections.binarySearch(sumList, leftFlashNum);
        if(index < 0) {
            index = -index - 1;
        }
        else {
            index =  index == 0 ? 1 : index ;
        }

        log.info(MarkerFactory.getMarker(QueueService.class.getSimpleName()),
                " getValidPlanList sumList: {} leftFlashNum:{} index:{} plans:{}",
                sumList, leftFlashNum, index,planEntityList);


        return planEntityList.subList(0, Math.min(Math.min(index, planEntityList.size()), leftPlanCount));
    }

    /**
     * 获取需要测试的Plan的列表。
     * 一个批次最多同时只能有5个plan 待确认环境。
     * @param product 产品
     * @param orderId 工单id
     * @param flash flash 批次
     * @param filterFunction 测试部分样片的Plan的筛选函数。
     * @return 需要测试的plan 列表
     */
    public List<OrderPlanEntity> fetchNeedTestPlanList(
            String product,
            long orderId,
            String flash,
            BiFunction<List<OrderPlanEntity>, Integer, List<OrderPlanEntity>> filterFunction
    ) {

        log.debug(MarkerFactory.getMarker(QueueService.class.getSimpleName()),"fetchNeedTestPlanList : orderId: {}, flash: {}", orderId, flash);
        OrderFlashEntity orderFlashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        // 如果Flash 样片剩余的数量不足的话，就不要开启测试 了、
        int leftFlashNum = orderFlashEntity.getLeftNum() ;
        log.debug(MarkerFactory.getMarker(QueueService.class.getSimpleName()),"left Flash Num:{} ",leftFlashNum) ;
        if(leftFlashNum == 0) {
            return new ArrayList<>();
        }
        // 查出这个批次下所有的plan列表。
        List<OrderPlanEntity> allPlanEntityList = planRepository.findAllByOrderIdAndFlashAndNotDisabled(orderId, flash) ;

        // 如果planEntityList 中 status为OrderPlanEntity.Status.READY的数量超过了5个的话，则直接返回空的列表。
        int leftPlanCount = leftPlanCount(product, allPlanEntityList);
        if(leftPlanCount <= 0) {
            return new ArrayList<>();
        }

        List<OrderPlanEntity> planEntityList = allPlanEntityList.stream()
                .filter(this::isPlanInQueue)
                .collect(Collectors.toList());


        // 如果最高的优先级的全部样片是手动Plan的话，那么就不需要再测试其他的Plan了。
        if (
                planEntityList.stream()
                        .filter(OrderPlanEntity::isTestAll)
                        .max(Comparator.comparing(OrderPlanEntity::getPriority))
                        .map(OrderPlanEntity::isManualPlan)
                        .orElse(false)
        ) {
            return new ArrayList<>();
        }


        if( planEntityList.stream().anyMatch(OrderPlanEntity::isTestAll)) {

            if(leftFlashNum != orderFlashEntity.getNum()) {
                log.debug(MarkerFactory.getMarker(QueueService.class.getSimpleName()),"left Flash Num:{} and left ",leftFlashNum) ;
                return new ArrayList<>();
            }


            return planEntityList.stream()
                    // 只考虑测试全部样片的、
                    .filter(OrderPlanEntity::isTestAll)
                    // 判断父plan是否完成，如果完成，则可以测试。
                    .filter(p -> checkParent(p, allPlanEntityList))
                    // 过滤手动Plan，因为手动Plan 不需要分配设备
                    .filter(p -> !p.isManualPlan())
                    .max(Comparator.comparing(OrderPlanEntity::getPriority))
                    .map(Collections::singletonList)
                    .orElse(new ArrayList<>());
        }
        else {
            return filterFunction.apply(allPlanEntityList,leftFlashNum);
        }
    }

    /**
     * 更新对应设备中的 plan状态。
     * @param orderFlashNo 工单 + Flash 批次组合成的字符串
     * @param plan plan 名称
     * @param body 详细的变更内容.
     */
    @Async
    public void updatePlanDeviceStatus(String orderFlashNo, String plan, PlanStatusChangeParams body) {

        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderFlashNo);
        WorkOrderEntity orderEntity = orderRepository.findById(flashEntity.getOrderId()).orElse(null);
        assert orderEntity != null;
        LogUtils.setOrderAndFlashTracePoint(orderEntity.getId(), flashEntity.getFlash(),"设备状态回调");

        OrderPlanEntity planEntity = planRepository.findByOrderIdAndFlashAndName(orderEntity.getId(), flashEntity.getFlash(), plan);
        if (planEntity == null) {
            log.warn("updatePlanDeviceStatus: planEntity is null. orderId: {}, flash: {}, plan: {}", orderEntity.getId(), flashEntity.getFlash(), plan);
            return;
        }
        try {
            productContext.onPlanStatusChange(orderEntity, flashEntity, planEntity, body);
        } catch (Exception e) {
            log.error("updatePlanDeviceStatus error: {}", e.getMessage(), e);
            notificationService.sendErrorNotification("OrderId: "+ orderEntity.getId() + " Flash: " + flashEntity.getFlash() + " Plan: " + body.getPlan() + " 下的状态回调失败, 请及时关注.");
        }
        LogUtils.clearTracePoint();
    }


    /**
     * 释放 flash 批次工单下 指定 plan 的设备 ，并更新 plan 的状态， 然后按需更新批次 的状态。
     * @param orderFlashNo flash批次的工单号
     * @param planEntity plan 实体
     */
    @Transactional
    public void releasePlanDevices(String orderFlashNo, OrderPlanEntity planEntity) {
        if(planEntity == null) {
            log.error("releasePlanDevices: planEntity is null.");
            return;
        }
        LogUtils.setOrderTracePoint(planEntity.getOrderId(), "自动释放设备");
        log.info("{} 自动释放设备!",planEntity.getName());
        // flash 批次实体类
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());
        assert flashEntity != null;

        WorkOrderEntity orderEntity = orderRepository.findById(flashEntity.getOrderId()).orElse(null);
        assert orderEntity != null ;


        if ( planEntity.getStatus() == OrderPlanEntity.Status.COMPLETED) {
            log.info("RMS {} -- {} [{}] 已经完成，无需再释放设备!", orderFlashNo, planEntity.getName(),planEntity.getId());
            // 检查是否需要更新Flash的状态。
            flashService.checkWaitMerge(orderEntity, flashEntity);
            return;
        }

        // 只释放还没有释放的设备。
        // 根据planId获取 设备信息
        List<PlanDeviceEntity> oldDeviceList = planDeviceRepository.findAllByPlanId(planEntity.getId())
                .stream()
                .filter(d -> d.getReleaseAt() == null)
                .collect(Collectors.toList());
        deviceService.releaseDevices(flashEntity, planEntity,oldDeviceList,null,null);

        checkDeviceReleaseStatus(orderEntity,flashEntity, planEntity);
    }


    /**
     * 检查plan下的设备释放状态。
     * @param orderEntity 工单实体类
     * @param planEntity plan 实体类
     * @param flashEntity flash 实体类
     */
    @Transactional
    public void checkDeviceReleaseStatus( WorkOrderEntity orderEntity,OrderFlashEntity flashEntity, OrderPlanEntity planEntity){

        // 找出plan 下的所有设备
        List<PlanDeviceEntity> allPlanDeviceEntityList = planDeviceRepository.findAllByPlanId(planEntity.getId()) ;

        // 判断工单下的设备是不是 所有的都被释放了。
        int releasedDeviceCount = planDeviceRepository.countByPlanIdAndReleaseAtNotNull(planEntity.getId());

        if(allPlanDeviceEntityList.size() != releasedDeviceCount){
            log.info("{}下的设备未释放完成,目前释放进度: {}/{}",planEntity.getName(), releasedDeviceCount, allPlanDeviceEntityList.size());
            return;
        }
        log.info("{}下的{}台设备都已释放,将状态更新至已完成",planEntity.getName(),allPlanDeviceEntityList.size());
        // 如果所有的设备都已经释放了,则移除Plan对应的任务.
        cancelPlanJob(flashEntity, planEntity);
        if(planEntity.getStatus() != OrderPlanEntity.Status.STOPPED) {
            long failedDeviceCount = allPlanDeviceEntityList.stream().filter(PlanDeviceEntity::isFailed).count();

            // 如果所有的设备都被释放了， 就更新 Plan 的状态
            planEntity.setUpdatedAt(System.currentTimeMillis());
            planEntity.setStatus(OrderPlanEntity.Status.COMPLETED);
            planEntity.setEndStatus(failedDeviceCount > 0 ? OrderPlanEntity.END_STATUS_FAIL : OrderPlanEntity.END_STATUS_SUCCESS );
            planEntity.setEndAt(System.currentTimeMillis());
            planRepository.save(planEntity);
            log.info("更新：{}的状态至完成",planEntity.getName());
        }
        // 判断，并更新对应批次，工单的状态。
        flashService.checkWaitMerge(orderEntity, flashEntity);
    }

    /**
     * 检查 plan 下的任务状态， 如果Plan 的任务状态还存在，则取消，因为plan 已经完成。
     * @param flashEntity flash 实体类
     * @param planEntity plan 实体类
     */
    public void cancelPlanJob(OrderFlashEntity flashEntity, OrderPlanEntity planEntity){
        log.info("取消{}下的{}的所有任务!",flashEntity.getFlash(),planEntity.getName());
        String jobName = flashEntity.getOrderFlashNo() + "_" + planEntity.getName() ;

        updateExpectedEndTime(planEntity.getId(), null);
        // 取消任务
        jobService.cancelJobIfExist(jobName, AutoCompletePlanJob.JOB_GROUP_NAME);

        String checkStartStatusJobName = flashEntity.getOrderFlashNo() + "_" + planEntity.getName() + "_checkStartStatus" ;
        jobService.cancelJobIfExist(checkStartStatusJobName, CheckPlanStartStatusJob.JOB_GROUP_NAME);

    }

    /**
     * 获取工单下的失败的plan 的数量。
     * @param orderId 工单id
     * @return 失败的plan 的数量
     */
    public int getErrorPlanCount(long orderId) {
        List<Long> planIdList = planDeviceRepository.findAllPlanIDByOrderIdAndStatus(orderId, PlanDeviceEntity.Status.FINISHED_FAILED);
        return planIdList.size();
    }

    @Transactional
    public void updatePlanPriority(@NotNull OrderPlanEntity planEntity , int priority) {
        planEntity.setUpdatedAt(System.currentTimeMillis());
        planEntity.setPriority(priority);
        planRepository.save(planEntity);
    }

    @Transactional
    public void pausePlan(OrderPlanEntity planEntity,OrderFlashEntity flashEntity){
        rollbackPlanToQueueStatus(flashEntity,planEntity,"暂缓分配");
    }

    /**
     * 更新 plan 的状态至运行中.
     * @param planEntity plan 实体类
     * @param userDetail 用户信息
     */
    @Transactional
    public void updatePlanStatusToRunning(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            OAuthUserDetail userDetail
    ){

        if( planEntity.getStatus().ordinal() < OrderPlanEntity.Status.RUNNING.ordinal() ) {
            log.info("更新Flash:{}下{}的状态至运行中.", flashEntity.getFlash(), planEntity.getName());
            // 更新 对应的Plan 。Flash . Order 的信息。
            planEntity.setUpdatedAt(System.currentTimeMillis());
            planEntity.setStatus(OrderPlanEntity.Status.RUNNING);
            planEntity.setStartAt(System.currentTimeMillis());
            planEntity.setStartBy(userDetail.getUid());
            planEntity.setStartPerson(userDetail.getUsername());
            planRepository.save(planEntity);
            notificationService.updateFlashInteractiveMsg("Flash测试中", orderEntity, flashEntity);
        }

        flashService.startFlash(flashEntity);
        orderService.startOrder(orderEntity);
    }

    /**
     * 通过plan id 来查找Plan，如果不存在的话，则抛出异常。
     * @param planId Plan的ID
     * @return Plan 实体类
     * @throws DataNotFoundException 如果不存在的话，则抛出异常。
     */
    public OrderPlanEntity findPlanOrElseThrow(long planId) {
        return planRepository.findById(planId).orElseThrow(()-> new DataNotFoundException("id为"+planId+"的Plan不存在！"));
    }

    public OrderPlanEntity findPlanOrElseThrow(long orderId , String flash, String planName) {
        OrderPlanEntity planEntity = planRepository.findByOrderIdAndFlashAndName(orderId, flash, planName);
        if(planEntity == null){
            throw new DataNotFoundException("工单"+orderId+"的Flash为"+flash+"下不存在名为"+planName+"的Plan");
        }
        return planEntity;
    }


    /**
     * 修改指定Plan的预期结束时间。
     * @param planId Plan的ID
     * @param time 预期结束时间
     */
    public void updateExpectedEndTime(long planId, Long time) {
        planRepository.updateExpectedEndTime(planId, time);
    }

    public void updateAutoEndTime(long planId, Long time) {
        planRepository.updateAutoEndTime(planId, time);
    }

    /**
     * 启动指定的Plan,并且更新Plan的状态,但是并不会对Plan的状态进行校验,所以请在调用该方法之前,先进行校验。
     * @param orderEntity 工单实体
     * @param flashEntity Flash 实体
     * @param planEntity Plan 实体
     * @param userDetail 用户信息
     */
    @Transactional
    public void startPlan(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            OAuthUserDetail userDetail
    ) {
        //IMPORT: 只要用户点击了plan的开始，则表示plan已经开始了，如果出现了部分设备没有启动成功，也认为plan已经开始了。
        updatePlanStatusToRunning(
                orderEntity,
                flashEntity,
                planEntity,
                userDetail
        );

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_STARTED)
                .orderId(planEntity.getOrderId())
                .flash(planEntity.getFlash())
                .planId(planEntity.getId())
                .build()
        );

        if(planEntity.isManualPlan()) {
            // 如果是手动plan 。到这就结束了，不需要启动设备。
            log.info("{}为手动Plan,无需调用RMS启动",planEntity.getName());
            return ;
        }

        // 查询Plan 下对应的CONFIRMED的设备。
        List<PlanDeviceEntity> planDeviceEntityList = planDeviceRepository.findAllByPlanIdAndStatusInAndMacNotNull(
                planEntity.getId(),
                List.of(PlanDeviceEntity.Status.CONFIRMED)
        );

        // 目前启动Plan 的时候，只需要启动已经确认的设备。
        List<String> macList = planDeviceEntityList.stream()
                .map(PlanDeviceEntity::getMac)
                .collect(Collectors.toList());
        if (macList.isEmpty()) {
            log.warn("{}下没有需要启动的设备",planEntity.getName());
            throw new IllegalArgumentException(planEntity.getName()+"下没有需要启动的设备");
        }

        // 检测当前需要启动的设备是否被重复占用，如果重复占用无法启动
        for (String mac : macList) {
            rmsDeviceService.findLockInfoInTest(planEntity.getId(), mac);
        }

        log.info("Flash:{}下的{}使用设备:{} 开始测试!", flashEntity.getFlash(), planEntity.getName(),
                planDeviceEntityList.stream().map(
                        d -> String.format("%s(%s-%s)", d.getNo(), d.getIp(),d.getMac())
                ).collect(Collectors.joining(",")));
        deviceService.startTest(
                planEntity,
                macList
        );

    }

    /**
     * 终止Plan, 终止Plan的运行,并释放对应的设备.
     * @param orderEntity 工单实体
     * @param flashEntity flash 批次
     * @param planEntity 需要终止的Plan.
     */
    public void terminatePlan(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity
    ) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        OAuthUserDetail userDetail = (OAuthUserDetail) authentication.getPrincipal();

        // 更新Plan的状态。
        log.info("更新{}状态至停止!", planEntity.getName());
        // 不管plan 之前是处于什么状态，
        OrderPlanEntity.Status lastStatus = planEntity.getStatus();

        planEntity.setUpdatedAt(System.currentTimeMillis());
        planEntity.setStatus(OrderPlanEntity.Status.STOPPED);
        planEntity.setEndStatus(OrderPlanEntity.END_STATUS_TERMINATED);
        planEntity.setTerminateAt(System.currentTimeMillis());
        planEntity.setTerminateBy(userDetail.getUid());
        planEntity.setTerminatePerson(userDetail.getUsername());
        planRepository.save(planEntity);

        log.info("取消{}关联的所有任务", planEntity.getName());
        // Plan 的状态已经完成, 取消Plan下的所有的任务
        cancelPlanJob(flashEntity, planEntity);

        // 获取 plan 下的所有设备
        List<PlanDeviceEntity> allPlanList = planDeviceRepository.findAllByPlanId(planEntity.getId());
        // 过滤出不处于完成状态的设备
        List<PlanDeviceEntity> planDeviceEntityList = allPlanList.stream().filter(d -> !COMPLETED_STATUS_LIST.contains(d.getStatus())).collect(Collectors.toList());
        // 处于占用状态的设备列表
        List<PlanDeviceEntity> occupyDeviceEntityList = planDeviceEntityList.stream().filter(d -> OCCUPY_STATUS_LIST.contains(d.getStatus())).collect(Collectors.toList());
        // 处于运行中的设备列表
        List<PlanDeviceEntity> runningDeviceEntityList = planDeviceEntityList.stream().filter(d -> PlanDeviceEntity.Status.RUNNING == d.getStatus()).collect(Collectors.toList());

        int total = allPlanList.size();
        int completed = total - planDeviceEntityList.size();
        // 如果 完成的设备数量等于总设备数量,则表示所有设备都已经完成了,则不需要再进行处理了.
        log.info("Plan:{}下的设备总数:{},已完成数量:{},占用中数量:{},运行中数量:{}", planEntity.getName(), total, completed, occupyDeviceEntityList.size(), runningDeviceEntityList.size());

        // FIXME: 理论上来说, Plan 不处于(RUNNING,CONFIRMED,READY)状态的时候,是不需要处理设备的.
        if (
                lastStatus == OrderPlanEntity.Status.READY
                        || lastStatus == OrderPlanEntity.Status.CONFIRMED
                        || lastStatus == OrderPlanEntity.Status.RUNNING
        ) {
            // 需要更新的Plan设备信息
            List<PlanDeviceEntity> newDeviceList = planDeviceEntityList.stream().map(d -> {
                PlanDeviceEntity d1 = d.duplicate();
                d1.setOperator(userDetail.getUid());
                d1.setTerminateAt(System.currentTimeMillis());
                d1.setTerminateBy(userDetail.getUid());
                d1.setTerminatePerson(userDetail.getUsername());

                if (lastStatus == OrderPlanEntity.Status.CONFIRMED || lastStatus == OrderPlanEntity.Status.READY) {
                    // 撤销所有的设备信息
                    d1.setReleaseAt(System.currentTimeMillis());
                    d1.setReleaseBy(userDetail.getUid());
                    d1.setReleasePerson(userDetail.getUsername());
                    d1.setStatus(PlanDeviceEntity.Status.CANCELED);
                }
                return d1;
            }).collect(Collectors.toList());

            if (lastStatus == OrderPlanEntity.Status.RUNNING) {
                log.info("调用RMS停止{}下的设备：{}", planEntity.getName(), runningDeviceEntityList.stream().map(d -> d.getNo() + "(" + d.getIp() + ")").collect(Collectors.joining(",")));
                // 转成ip 列表
                List<String> ipList = runningDeviceEntityList.stream().map(PlanDeviceEntity::getIp).collect(Collectors.toList());
                List<String> macList = runningDeviceEntityList.stream().map(PlanDeviceEntity::getMac).collect(Collectors.toList());

                // 停止 plan 下的所有设备，停止后直接释放当前设备
                deviceService.stopTestPlanAndCheck(
                        planEntity,
                        StopTestParams.builder()
                                .orderNo(flashEntity.getOrderFlashNo())
                                .planDeviceList(Collections.singletonList(new StopTestParams.PlanDeviceIpInfo(planEntity.getName(), macList, ipList)))
                                .build()
                );

                // 获取当前测试失败并且未释放的电脑
                List<PlanDeviceEntity> failDeviceEntityList = allPlanList.stream().filter(d ->
                        d.getStatus() == PlanDeviceEntity.Status.FINISHED_FAILED && d.getReleaseAt() == null).collect(Collectors.toList());

                //所有需要释放的电脑，包括锁定但是未开始测试的电脑、测试失败并且未释放的电脑
                List<PlanDeviceEntity> combinedList = Stream.concat(occupyDeviceEntityList.stream(), failDeviceEntityList.stream()).collect(Collectors.toList());
                rmsDeviceService.releaseDevices(orderEntity.getSubProduct(), flashEntity.getOrderFlashNo(), planEntity, combinedList);
                log.info("occupyDeviceEntityList: {} failDeviceEntityList: {} combinedList: {}", occupyDeviceEntityList,failDeviceEntityList, combinedList);
                deviceService.saveDeviceControlList(
                        orderEntity.getSubProduct(),
                        flashEntity,
                        DingTalkUtils.getCurrentUserName(),
                        "Plan停止测试后电脑关机",
                        false,
                        combinedList
                );
            } else {
                // CONFIRMED,READY 状态下的plan ,直接释放设备
                log.info("{} 已经分配设备，但是尚未开始运行,可以直接释放样片即可.", planEntity.getName());
                // 撤销所有的设备信息
                rmsDeviceService.releaseDevices(orderEntity.getSubProduct(), flashEntity.getOrderFlashNo(), planEntity, occupyDeviceEntityList);
                flashService.updateLeftSampleNum(flashEntity, planEntity.getTestNum());
                deviceService.saveDeviceControlList(
                        orderEntity.getSubProduct(),
                        flashEntity,
                        DingTalkUtils.getCurrentUserName(),
                        "Plan停止测试后电脑关机",
                        false,
                        occupyDeviceEntityList
                );
            }

            deviceService.saveDeviceList(
                    "停止Plan的测试",
                    planDeviceEntityList,
                    newDeviceList);
        }
        // 如果是直接释放设备的话,则同时也需要检查状态.
        flashService.checkWaitMerge(orderEntity, flashEntity);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_STOPPED)
                .orderId(planEntity.getOrderId())
                .flash(planEntity.getFlash())
                .planId(planEntity.getId())
                .build()
        );
    }


    public void checkBatchPlanValid(long orderId , String flash , List<OrderPlanEntity> planEntities){

        for(OrderPlanEntity planEntity : planEntities){
            if(planEntity.getOrderId() != orderId){
                throw new DataNotFoundException("Plan:" + planEntity.getName() + "不属于工单:" + orderId);
            }
            if(!Objects.equals(planEntity.getFlash(), flash)) {
                throw new DataNotFoundException("Plan:" + planEntity.getName() + "不属于Flash:" + flash);
            }
        }
    }

    public OrderPlanEntity findFirstCompletedAndTestAllPlan(long orderId ,String flash){
        List<OrderPlanEntity> planList = planRepository.findAllByOrderIdAndFlashAndType(orderId, flash, OrderPlanEntity.TYPE_AUTO);
        return planList.stream()
                .filter(OrderPlanEntity::isTestAll)
                .filter(p -> p.getStatus() == OrderPlanEntity.Status.COMPLETED)
                .findFirst()
                .orElse(null);
    }

    /**
     * 重测已经取消的Plan
     * @param planEntity 需要重测的Plan
     * @param userDetail 操作人
     */
    public void retryCanceledPlan(OrderPlanEntity planEntity, OAuthUserDetail userDetail){
        PlanHistoryEntity historyEntity =  PlanHistoryEntity.of(planEntity) ;
        planHistoryRepository.save(historyEntity);
        planEntity.clearEndStatus();
        if(planEntity.getType() == 0){
            planEntity.setStatus(OrderPlanEntity.Status.QUEUE);
            log.info("Plan重测完成：更新{} 状态至排队中!", planEntity.getName());
        }else{
            planEntity.setStatus(OrderPlanEntity.Status.CONFIRMED);
            // 手动plan上传的报告增加重测标记
            List<PlanDocumentItemVO> planDocumentEntityList = planDocumentRepository.findAllByPlanId(planEntity.getId());
            for (PlanDocumentItemVO doc : planDocumentEntityList) {
                planDocumentRepository.findById(doc.getDocId()).ifPresent(planDocumentEntity -> {
                    planDocumentEntity.setIsRetest(true);
                    planDocumentRepository.save(planDocumentEntity);
                });

            }
            log.info("Plan重测完成：更新{} 状态至等待开始!", planEntity.getName());
        }
        List<PlanDeviceEntity> deviceList = planDeviceRepository.findAllByPlanId(planEntity.getId());

        if(!deviceList.isEmpty()){
            // 更新设备历史
            deviceService.saveDeviceList("已取消的Plan重新测试", deviceList, new ArrayList<>());
            log.debug("Plan {} 的历史测试设备 {} ， 在wo_plan_device表已被清除 !  ",planEntity.getName(), deviceList.size());
        }

        planEntity.setReTestAt(System.currentTimeMillis());
        planEntity.setReTestBy(userDetail.getUid());
        planEntity.setReTestPerson(userDetail.getUsername());
        planRepository.save(planEntity);

        // 将 Plan 对应的Assign 也重置
        planAssignService.resetPlanAssign(planEntity);

        // 发送变更到前端.
        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .orderId(planEntity.getOrderId())
                        .flash(planEntity.getFlash())
                        .planId(planEntity.getId())
                        .type(DataChangeEvent.Type.PLAN_RETEST)
                        .build()
        );

    }


    /**
     * 检查planEntityList中的Plan,如果这些Plan有 WAITING 状态的话,则将其分配状态更新至 QUEUE 中.
     * @param planEntityList 需要检查的Plan列表
     */
    public void updatePlanStatusToQueue(List<OrderPlanEntity> planEntityList) {
        planAssignService.updatePlanStatusToQueue(planEntityList);
    }

    /**
     * 添加plan 到指定的flash批次中
     * @param flashEntity  指定的flash批次
     * @param plan  需要添加的plan
     * @param userDetail 操作人
     */
    public void addPlanToFlash(OrderFlashEntity flashEntity, PlanModel plan, OAuthUserDetail userDetail) {

        OrderPlanEntity planEntity = plan.toEntity(flashEntity.getOrderId(),
                flashEntity ,
                OrderPlanEntity.Status.QUEUE
        );
        planEntity.setAddedBy(userDetail.getUid());
        planEntity.setAddedPerson(userDetail.getUsername());
        OrderPlanEntity result = planRepository.save(planEntity);


        // 给自动Plan增加对应的 PlanAssignInfoEntity.
        if(planEntity.isAutoPlan()) {
            planAssignService.preparePlanAssignInfo(result);
        }

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_ADDED)
                .orderId(flashEntity.getOrderId())
                .flash(flashEntity.getFlash())
                .planId(result.getId())
                .build()
        ) ;
    }

    /**
     * 获取 plan 的详细信息数据.
     * @param subProduct 设备归属产品线
     * @param planEntity plan的信息
     * @param flashEntity flash信息
     */
    @Transactional
    public PlanDetailVO fetchPlanInfo(String subProduct, OrderPlanEntity planEntity, OrderFlashEntity flashEntity){
        PlanDetailVO planDetail = PlanDetailVO.of(planEntity);

        String planType = Optional.ofNullable(planEntity.getPlanType()).orElse("");
        // 查询Plan 的设备列表。
        List<DeviceDetailVO> deviceDetailList = deviceService.getPlanDevicesVO(subProduct, planEntity);
        planDetail.setDevices(deviceDetailList);

        List<PlatformDetailVO> platformDetailVOS = terminalService.getPlanTerminalVO(planEntity);
        planDetail.setPlatforms(platformDetailVOS);

        // 查询临时plan
        if(planEntity.getTempPlanId() != null) {
            TempPlanEntity tempPlanEntity = tempPlanRepository.findById(planEntity.getTempPlanId()).orElse(null) ;
            assert tempPlanEntity != null ;
            planDetail.setTitle(tempPlanEntity.getTitle());
            planDetail.setOwnerName(tempPlanEntity.getOwnerName());
        }

        // 查询plan的排队信息
        PlanAssignInfoEntity planAssignInfo = planAssignService.findPlanAssignInfo(planEntity.getId());
        if(planAssignInfo != null){
            planDetail.setAssignInfo(PlanDetailVO.AssignPlan.toAssign(planAssignInfo));
        }


        if(planEntity.getBelongToPerson() == null && flashEntity != null) {
            planEntity.setBelongToPerson(flashEntity.getTestPerson());
        }
        return planDetail;
    }

    /**
     * 获取 plan 的详细信息数据.
     * @param temPlan 设备归属产品线
     * @param orderEntity 工单信息
     * @param userDetail 用户登录信息
     */
    @Transactional
    public TempPlanEntity saveTemPlan(AddTempPlanReq temPlan, WorkOrderEntity orderEntity, OAuthUserDetail userDetail){
        // 统计今日指定产品已经创建了多少个plan.
        int count = tempPlanRepository.countBySubProductAndCreatedAtBetween(
                orderEntity.getSubProduct(),
                DateTime.now().withTimeAtStartOfDay().toDate().getTime(),
                DateTime.now().toDate().getTime()
        );
        String name = String.format("%s%02d",DateTime.now().toString("yyyMMdd"), count + 1) ;
        log.info("{}下今日已有{}个临时Plan, 生成Plan的名称为:{}", temPlan.getSubProduct(), count, name);

        TempPlanEntity tempPlanEntity = new TempPlanEntity();
        tempPlanEntity.setTitle(temPlan.getTitle());
        tempPlanEntity.setName(name);
        tempPlanEntity.setFeature(temPlan.getDesc());
        tempPlanEntity.setOrderId(temPlan.getOrderId());
        tempPlanEntity.setPriority(temPlan.getPriority());
        tempPlanEntity.setType(1); // 临时Plan 都是手动plan
        tempPlanEntity.setProduct(orderEntity.getProduct());
        tempPlanEntity.setSubProduct(orderEntity.getSubProduct());
        tempPlanEntity.setTestNum(temPlan.getTestNum());
        tempPlanEntity.setVersionType(orderEntity.getVersionType());
        if(temPlan.getOwner() != null ){
            tempPlanEntity.setOwnerId(temPlan.getOwner().getId());
            tempPlanEntity.setOwnerName(temPlan.getOwner().getName());
        }

        tempPlanEntity.setAddedBy(userDetail.getUid());
        tempPlanEntity.setAddedPerson(userDetail.getUsername());
        tempPlanEntity.setCreatedAt(System.currentTimeMillis());
        tempPlanEntity.setUpdatedAt(System.currentTimeMillis());

        return tempPlanRepository.save(tempPlanEntity);
    }


    /**
     * 将Plan列表按照优先级以及依赖关系进行先后排序.
     * @param allPlanEntityList 需要排序的的Plan 列表
     * @return 排序后的Plan 列表
     */
    public List<OrderPlanEntity> sortPlanList(List<OrderPlanEntity> allPlanEntityList){
        List<OrderPlanEntity> sortedPlanList = new LinkedList<>() ;
        allPlanEntityList.stream()
                .sorted(
                        Comparator.comparing(OrderPlanEntity::getPriority)
                                .reversed()
                                .thenComparing(Comparator.comparing(OrderPlanEntity::isTestAll).reversed())
                                .thenComparing(OrderPlanEntity::getName)
                ).forEachOrdered(p -> {

                    if(sortedPlanList.contains(p)) {
                        return;
                    }
                    if(p.getParentPlan() != null && !p.getParentPlan().isEmpty()) {
                        // 从 allPlanEntityList 中找到父Plan, 并且把父Plan放到前面。
                        Arrays.stream(p.getParentPlan().split(","))
                                .map(parentPlanName -> allPlanEntityList.stream()
                                        .filter(parentPlan -> parentPlan.getName().equals(parentPlanName))
                                        .findFirst()
                                        .orElse(null))
                                .filter(Objects::nonNull)
                                .filter(parentPlan -> !sortedPlanList.contains(parentPlan))
                                .forEach(sortedPlanList::add);
                    }
                    sortedPlanList.add(p);
                });
        return sortedPlanList;
    }

    /**
     * 查询flash下所有plan列表
     * @param orderId 工单id
     * @param flashName flash名称
     * @return 查询Plan列表
     */
    public List<OrderPlanEntity> fetchAllPlanByFlash(long orderId, String flashName){
        return planRepository.findAllByOrderIdAndFlash(orderId, flashName);
    }

    /**
     * 解决设备冲突问题，释放设备、更改解锁信息、保存设备历史、删除Plan设备信息
     * @param planId plan id
     */
    @Transactional
    public void reassignPlan(long planId){
        List<PlanDeviceEntity> deviceEntityList = planDeviceRepository.findAllByPlanId(planId);
        List<String> macList = deviceEntityList.stream().map(PlanDeviceEntity::getMac).collect(Collectors.toList());
        List<DeviceLockInfoEntity> lockInfoEntityList = deviceLockInfoRepository.findByMacInAndLockedTrue(macList);
        List<Long> planIds = lockInfoEntityList.stream().map(DeviceLockInfoEntity::getPlanId)
                .distinct().collect(Collectors.toList());
//        if(planIds.size() < 2){
//            throw new DataNotFoundException("Plan下的设备不存在冲突!");
//        }
        List<OrderPlanEntity> planEntityList = planRepository.findAllByIdIn(planIds);

        planEntityList.forEach(plan -> {
            if (plan.getStatus() != OrderPlanEntity.Status.READY) {
                throw new DataNotFoundException("[" + plan.getId() + "]" + plan.getName() + "的状态为" + plan.getStatus() + "不支持重新分配操作!");
            }
        });

        planEntityList.forEach(plan -> {
            WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(plan.getOrderId());
            OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(plan.getOrderId(), plan.getFlash());
            log.info("flash {} "+ flashEntity.getFlash() + "下的Plan " + plan.getName() + "移除分配信息!");
            List<PlanDeviceEntity> planDeviceList = planDeviceRepository.findAllByPlanId(plan.getId());
            int addCount = planDeviceList.stream().mapToInt(PlanDeviceEntity::getTestNum).sum();
            // 解锁设备
            // FIXME 此处存在多次解锁设备问题
            rmsDeviceService.unlockPlanDevice(orderEntity.getSubProduct(), flashEntity.getOrderFlashNo(), plan, planDeviceList);
            // 保存设备历史
            deviceService.recordDeviceHistory("设备冲突重新分配", planDeviceList);
            // 删除Plan下的所有设备信息
            planDeviceRepository.deleteAllByPlanId(plan.getId());
            // 更新Flash的样片数量
            flashEntity.setLeftNum(flashEntity.getLeftNum() + addCount);
            flashRepository.save(flashEntity);
            // 更新PlanAssignInfoEntity的状态至WAITING
            planAssignService.pauseQueue(plan);
        });

        // 修改wo_device_lock_info 表中数据
        lockInfoEntityList.forEach(lockEntity -> {
            log.info("设备" + lockEntity.getNo() + "的lockInfo更新!");
            lockEntity.setLocked(false);
            lockEntity.setUnlockTime(System.currentTimeMillis());
            lockEntity.setUpdatedAt(System.currentTimeMillis());
            deviceLockInfoRepository.save(lockEntity);
        });

        // Plan 状态调整成 QUEUE
        planEntityList.forEach(p -> {
            log.info("Plan " + p.getName() + "的状态调整为QUEUE!");
            p.setStatus(OrderPlanEntity.Status.QUEUE);
            p.setUpdatedAt(System.currentTimeMillis());
            p.setReadyAt(null);
            planRepository.save(p);

            dataChangeListener.onDataChange(DataChangeEvent.builder()
                    .type(DataChangeEvent.Type.PLAN_PAUSE)
                    .orderId(p.getOrderId())
                    .flash(p.getFlash())
                    .planId(planId)
                    .build()
            );
        });
    }

    /**
     * 获取测试人员填写的Plan相关测试描述
     * @param planId plan id
     * @return 描述信息
     */
    public String fetchPlanNote(long planId){
        PlanNoteMsgEntity entity = planNoteMsgRepository.findByPlanId(planId).orElse(new PlanNoteMsgEntity());
        return entity.getMsg();
    }

    /**
     * 保存测试描述信息
     * @param planId plan id
     * @param msg 描述信息
     */
    public void handlePlanNote(long planId, String msg){
        PlanNoteMsgEntity entity = new PlanNoteMsgEntity();
        if(planNoteMsgRepository.existsByPlanId(planId)){
            entity = planNoteMsgRepository.findByPlanId(planId).orElse(new PlanNoteMsgEntity());
        }
        entity.setMsg(msg);
        entity.setPlanId(planId);
        planNoteMsgRepository.save(entity);
    }

}
