package com.yeestor.work_order.entity;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.Optional;

/**
 * 工单历史
 */
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity(name = "PlanHistory")
@Table(name="wo_plan_history")
public class PlanHistoryEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "order_id")
    private long orderId ;

    @Column(name = "plan_id")
    private long planId ;

    @Column(name = "flash")
    private String flash ;

    /**
     * plan 的名称
     */
    @Column(name = "name")
    private String name ;

    /**
     * 结束的时候的状态
     */
    @Column(name = "end_status")
    private int endStatus ;


    /**
     * Plan 的开始测试时间。
     */
    @Column(name = "start_at")
    private Long startAt ;

    /**
     * 开始测试plan的人员, 钉钉ID
     */
    @Column(name = "start_by")
    private String startBy ;

    /**
     * 开始测试plan的人员。名称
     */
    @Column(name = "start_p")
    private String startPerson  ;
    /**
     * Plan 的结束测试时间. 真正结束的时间，通常是被释放，或者放弃重测了。
     */
    @Column(name = "end_at")
    private Long endAt ;

    /**
     * 结束测试的人员， 只有手动plan 才会有此信息，钉钉ID
     */
    @Column(name = "end_by")
    private String endBy ;

    /**
     * 结束测试的人员 名称， 只有手动plan 才会有此信息
     */
    @Column(name = "end_p")
    private String endPerson ;

    /**
     * Plan 自动结束的时间。如果一直在重测的话，这个时间会一直更新。
     */
    @Column(name = "auto_end_at")
    private Long autoEndAt ;

    @ColumnDefault("0")
    @Column(name = "version")
    private long version ;

    @Column(name = "created_at")
    private long createdAt ;

    public static PlanHistoryEntity of(OrderPlanEntity planEntity){
        PlanHistoryEntity historyEntity = new PlanHistoryEntity();
        historyEntity.orderId = planEntity.getOrderId() ;
        historyEntity.flash = planEntity.getFlash() ;
        historyEntity.planId = planEntity.getId();
        historyEntity.name = planEntity.getName();
        historyEntity.endStatus = planEntity.getEndStatus() ;
        historyEntity.startAt = planEntity.getStartAt();
        historyEntity.startBy = planEntity.getStartBy() ;
        historyEntity.startPerson = planEntity.getStartPerson() ;
        historyEntity.endAt = planEntity.getEndAt() ;
        historyEntity.endBy = planEntity.getEndBy() ;
        historyEntity.endPerson = planEntity.getEndPerson() ;
        historyEntity.autoEndAt = planEntity.getAutoEndAt() ;
        historyEntity.version = Optional.ofNullable(planEntity.getVersion()).orElse(0L) ;
        historyEntity.createdAt = System.currentTimeMillis() ;

        return historyEntity ;
    }



}
