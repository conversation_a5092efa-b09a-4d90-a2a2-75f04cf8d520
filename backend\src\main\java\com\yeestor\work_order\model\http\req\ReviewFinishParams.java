package com.yeestor.work_order.model.http.req;


import com.yeestor.work_order.entity.review.ReviewItemEntity;
import com.yeestor.work_order.entity.review.ReviewResultEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel(value = "ReviewFinishParams", description = "完成Review 需要的参数")
public class ReviewFinishParams {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReviewCheckItem {
        private String checkItem;
        private String checkResult;
        private String comment ;

        public ReviewItemEntity toEntity(){
            ReviewItemEntity entity = new ReviewItemEntity();
            entity.setItem(checkItem);
            entity.setResult(checkResult);
            entity.setRemark(comment);
            return entity;
        }

    }

    @ApiModelProperty("版本评审Check表")
    private List<ReviewCheckItem> checkList ;

    @ApiModelProperty("版本发布的路径！")
    private String path ;

    @ApiModelProperty("review 通过与否，true表示review通过，false表示review不通过")
    private boolean pass ;

    @ApiModelProperty("review产生的评审结论")
    private String conclusion ;

    public ReviewResultEntity toEntity(){
        ReviewResultEntity reviewResultEntity = new ReviewResultEntity();
        reviewResultEntity.setResult(pass);
        reviewResultEntity.setPath(path) ;
        reviewResultEntity.setConclusion(conclusion);
        reviewResultEntity.setType(ReviewResultEntity.TYPE_REVIEW_FINISH);
        reviewResultEntity.setCreatedAt(System.currentTimeMillis());
        return reviewResultEntity;
    }

}
