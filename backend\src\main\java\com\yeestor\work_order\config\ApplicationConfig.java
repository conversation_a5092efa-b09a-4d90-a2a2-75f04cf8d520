package com.yeestor.work_order.config;

import com.yeestor.admin.api.UserFeignClient;
import com.yeestor.dingtalk.api.DingTalkFeignClient;
import com.yeestor.file.api.FileFeignClient;
import com.yeestor.work_order.service.job.JobService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.zentao.api.ZentaoFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.redis.repository.configuration.EnableRedisRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import java.util.Optional;

@Slf4j
@Configuration

@EnableRetry
@EnableWebMvc
@EnableScheduling
@EnableResourceServer
@EnableDiscoveryClient
@EnableRedisRepositories(basePackages = "com.yeestor.work_order.model.redis" )
@EnableJpaRepositories(basePackages = "com.yeestor.work_order.repository")
@EnableFeignClients(basePackageClasses = {
        DingTalkFeignClient.class,
        UserFeignClient.class,
        ZentaoFeignClient.class,
        FileFeignClient.class
})
public class ApplicationConfig {



    @Value("${app.version}")
    private String version ;

    @Value("${app.build.time}")
    private String buildTime ;
    @Value("${spring.quartz.enable}")
    private Boolean quartzEnable ;

    @Bean
    CommandLineRunner initDataListener(DataChangeListener listener, OrderService orderService){
        return args -> listener.startHandleData();
    }


    /**
     * 初始化分配设备的任务
     * @param jobService 任务服务
     * @param scheduler Quartz 计划服务
     * @return CommandLineRunner 应用启动后，自动运行。
     */
    @Bean
    CommandLineRunner initAssignJob(JobService jobService, Scheduler scheduler){
        return args -> {
            log.info("app version: {} -- buildTime:{} -- quartzEnable: {}", version, buildTime,quartzEnable);
            if(!Optional.ofNullable(quartzEnable).orElse(false)) {
                return;//不启用定时任务
            }
            jobService.scheduleAssignDeviceJob("GE","SD");
            jobService.scheduleAssignDeviceJob("GE","U2");
            jobService.scheduleAssignDeviceJob("GE","U3");
            jobService.scheduleAssignDeviceJob("SSD","SATA");
            jobService.scheduleAssignDeviceJob("SSD","PCIe");
            jobService.scheduleAssignDeviceJob("EM","eMMC");
            jobService.scheduleAssignDeviceJob("EM","UFS");

            jobService.scheduleAssignDeviceJob("IND","IND_SD");
            jobService.scheduleAssignDeviceJob("IND","IND_EMMC");
//            jobService.scheduleAssignDeviceJob("IND","IND_UFS");
//            jobService.scheduleAssignDeviceJob("IND","IND_PCIE");
            jobService.scheduleAssignDeviceJob("IND","IND_SATA");
            jobService.scheduleTimeoutCheckJob();
            jobService.scheduleDeviceTestCheckJob();
            jobService.scheduleStatOrderJob();
            scheduler.start();
        };
    }
}
