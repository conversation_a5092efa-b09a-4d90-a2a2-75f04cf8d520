package com.yeestor.work_order.model.http.resp.review;

import com.yeestor.work_order.entity.review.ReviewItemEntity;
import com.yeestor.work_order.entity.review.ReviewResultEntity;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ReviewResult", description = "工单详情的Review的结果")
public class ReviewResult {
    private String flash ;
    private String path ;
    private String remark ;
    private int type ;
    private Boolean skipResult ;
    private long createdAt ;
    private String createdBy ;
    private String createdPerson ;
    private List<ReviewItem> items ;
    private String conclusion;
    private String versionType;

    public static ReviewResult entityToVo(
            ReviewResultEntity reviewResultEntity,
            List<ReviewItemEntity> reviewItemList
    ) {
        ReviewResult reviewResult = new ReviewResult();
        reviewResult.setFlash(reviewResultEntity.getFlash());
        reviewResult.setPath(reviewResultEntity.getPath());
        reviewResult.setRemark(reviewResultEntity.getRemark());
        reviewResult.setType(reviewResultEntity.getType());
        reviewResult.setSkipResult(reviewResultEntity.getResult());
        reviewResult.setCreatedAt(reviewResultEntity.getCreatedAt());
        reviewResult.setCreatedBy(reviewResultEntity.getCreatedBy());
        reviewResult.setCreatedPerson(reviewResultEntity.getCreatedPerson());
        reviewResult.setConclusion(reviewResultEntity.getConclusion());
        reviewResult.setVersionType(reviewResultEntity.getVersionType());
        if(reviewItemList != null && reviewItemList.size() > 0) {
            reviewResult.setItems(reviewItemList.stream()
                    .map(ReviewItem::entityToVO)
                    .collect(Collectors.toList())
            );
        }
        return reviewResult;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "flash ReviewItem", description = "工单详情的Review的结果")
    public static class ReviewItem {
        private String item ;
        private String result ;
        private String remark ;

        public static ReviewItem entityToVO(ReviewItemEntity reviewItemEntity) {
            ReviewItem reviewItemVO = new ReviewItem();
            reviewItemVO.setItem(reviewItemEntity.getItem());
            reviewItemVO.setResult(reviewItemEntity.getResult());
            reviewItemVO.setRemark(reviewItemEntity.getRemark());
            return reviewItemVO;
        }
    }
}
