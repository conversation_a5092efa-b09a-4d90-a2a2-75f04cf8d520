{"@timestamp":"2025-07-23T10:34:04.082+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:34:04.089+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:04.095+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:04.095+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db959f75ce363eaf","spanId":"db959f75ce363eaf","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:03.31+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:38:03.311+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:03.314+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:03.314+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8db620f028d4b28","spanId":"b8db620f028d4b28","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.342+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:42:03.342+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.344+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.345+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5de1c895bf477bda","spanId":"5de1c895bf477bda","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:03.422+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:46:03.423+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:03.425+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:03.426+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6595ba583b486186","spanId":"6595ba583b486186","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:03.339+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:50:03.339+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:03.341+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:03.341+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d2f236bb97bbfc44","spanId":"d2f236bb97bbfc44","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:03.554+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:03.556+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:03.56+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:03.56+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d5aa2386d3cc108b","spanId":"d5aa2386d3cc108b","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:03.273+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:58:03.274+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:03.276+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:03.276+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"54ef2530c99d09dd","spanId":"54ef2530c99d09dd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:03.476+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:02:03.477+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:03.48+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:03.48+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d6d2a705a4d93d3d","spanId":"d6d2a705a4d93d3d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:03.317+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:06:03.318+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:03.321+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:03.322+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ceea1d5650b8f00","spanId":"7ceea1d5650b8f00","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:03.31+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:10:03.311+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:03.312+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:03.312+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fdb844cf308672e3","spanId":"fdb844cf308672e3","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:03.498+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:14:03.499+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:03.502+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:03.502+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ec62e0c7e666d509","spanId":"ec62e0c7e666d509","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:03.281+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:18:03.281+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:03.283+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:03.283+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dfce18e8deddd668","spanId":"dfce18e8deddd668","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:04.797+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:34:04.84+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:04.845+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:04.846+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4a6a296301c67f9","spanId":"d4a6a296301c67f9","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:03.568+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:38:03.572+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:03.575+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:03.575+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7c3e82a9390216d8","spanId":"7c3e82a9390216d8","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:03.312+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:03.313+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:03.315+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:03.315+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8dc33e2e3edc3f66","spanId":"8dc33e2e3edc3f66","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:03.305+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:46:03.306+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:03.308+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:03.308+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d425ccda49f6d60","spanId":"5d425ccda49f6d60","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:03.255+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:50:03.257+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:03.259+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:03.259+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"778c1a0950c6636f","spanId":"778c1a0950c6636f","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:03.28+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:54:03.281+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:03.283+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:03.283+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6b0e8a3044f66f74","spanId":"6b0e8a3044f66f74","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:03.298+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:58:03.299+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:03.3+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:03.3+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fcb694f9e90b929","spanId":"0fcb694f9e90b929","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:03.28+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:02:03.28+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:03.282+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:03.282+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c068008e473597bb","spanId":"c068008e473597bb","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.277+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:06:03.278+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.28+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.28+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fe2213814e675d","spanId":"b1fe2213814e675d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:03.327+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:10:03.33+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:03.334+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:03.334+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"937c0f883d26be3d","spanId":"937c0f883d26be3d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:03.289+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:14:03.29+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:03.292+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:03.292+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581ca4527c4a7f1c","spanId":"581ca4527c4a7f1c","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:03.368+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:18:03.369+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:03.37+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:03.37+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75ef7f0bfc437cf1","spanId":"75ef7f0bfc437cf1","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:03.304+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:22:03.304+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:03.306+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:03.306+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f38991f3e04484a","spanId":"9f38991f3e04484a","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:03.293+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:26:03.294+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:03.296+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:03.296+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"528a765c3b8ff94d","spanId":"528a765c3b8ff94d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:03.261+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:03.262+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:03.263+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:03.263+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed1a055b3fb1e48f","spanId":"ed1a055b3fb1e48f","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:03.255+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:34:03.256+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:03.257+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:03.258+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4eee28a53ac444d1","spanId":"4eee28a53ac444d1","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:03.298+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:38:03.298+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:03.3+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:03.3+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f7b65441f55cf3bd","spanId":"f7b65441f55cf3bd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:03.341+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:03.341+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:03.343+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:03.343+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1fc42898b74be4e","spanId":"b1fc42898b74be4e","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:03.317+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:46:03.318+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:03.319+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:03.32+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4c4e7333a7602b8","spanId":"e4c4e7333a7602b8","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:03.308+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:50:03.309+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:03.311+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:03.311+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"10e99f6ab256f678","spanId":"10e99f6ab256f678","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:03.331+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:54:03.332+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:03.334+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:03.334+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75cf2b766ea895fd","spanId":"75cf2b766ea895fd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:03.33+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:58:03.33+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:03.332+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:03.332+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6f5a11dffd071cd","spanId":"e6f5a11dffd071cd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:03.293+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:02:03.294+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:03.296+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:03.296+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b109b06fde259589","spanId":"b109b06fde259589","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:03.321+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:06:03.322+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:03.323+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:03.323+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f9bfbe1a3b4a9662","spanId":"f9bfbe1a3b4a9662","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:03.362+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:10:03.363+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:03.364+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:03.365+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d340392429faf74","spanId":"2d340392429faf74","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:03.359+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:14:03.359+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:03.361+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:03.361+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e4e909f118de8568","spanId":"e4e909f118de8568","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:03.348+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:03.349+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:03.35+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:03.35+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f1d51d02c35863cd","spanId":"f1d51d02c35863cd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:03.366+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:22:03.367+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:03.368+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:03.368+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"903f0d6478515525","spanId":"903f0d6478515525","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:04.33+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:26:04.331+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:04.332+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:04.332+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"46764db07081936a","spanId":"46764db07081936a","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:03.388+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:03.389+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:03.391+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:03.391+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4fb45315e403be19","spanId":"4fb45315e403be19","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:03.393+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:34:03.395+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:03.398+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:03.398+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"50f231405a281bf6","spanId":"50f231405a281bf6","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:03.357+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:38:03.358+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:03.36+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:03.36+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cb22963afc776d02","spanId":"cb22963afc776d02","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:03.335+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:03.336+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:03.338+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:03.338+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d1d755dafdbccf5","spanId":"6d1d755dafdbccf5","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:03.296+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:46:03.297+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:03.298+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:03.298+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5809d519fb0ec7cf","spanId":"5809d519fb0ec7cf","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:03.275+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:50:03.276+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:03.278+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:03.278+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"334af2917d38cff3","spanId":"334af2917d38cff3","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:03.29+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:54:03.291+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:03.292+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:03.292+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"036cedfbe4e5d8fa","spanId":"036cedfbe4e5d8fa","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:03.274+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:58:03.275+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:03.277+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:03.277+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3d5cf0944bfe1c43","spanId":"3d5cf0944bfe1c43","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:03.364+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:02:03.365+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:03.368+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:03.368+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df81d70586575947","spanId":"df81d70586575947","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:03.367+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:03.368+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:03.37+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:03.37+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12c91828253f62e6","spanId":"12c91828253f62e6","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:03.47+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:10:03.471+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:03.474+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:03.474+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e792115943019bd","spanId":"5e792115943019bd","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:03.419+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:14:03.42+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:03.423+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:03.423+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b8e08fe00044e03d","spanId":"b8e08fe00044e03d","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:03.341+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:18:03.342+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:03.344+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:03.344+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c9451759811c702","spanId":"8c9451759811c702","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:03.319+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"6052","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:22:03.32+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6274, orderId=6052, flash=WT_8803_128G_128GB, orderFlashNo=YS8803##MP020101###0010##3D_TLC_E09T#250023_WT_8803_128G_128GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:03.323+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"6052","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:03.323+08:00","@version":"1","message":"[6052] - [WT_8803_128G_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9731d4252a0dc4e6","spanId":"9731d4252a0dc4e6","context":"QueueService","no":"6052","traceType":"分配设备"}
