package com.yeestor.work_order.model.http.resp;

import lombok.Builder;
import lombok.Data;
import org.quartz.Trigger;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class JobInfoVo {

    private String jobClass ;
    private String groupName ;
    private String jobName ;
    private List<TriggerInfoVO> triggerTimeList ;

    private Map<String,Object> dataMap ;

    @Data
    @Builder
    public static class TriggerInfoVO {

        private long startTime ;
        private long endTime ;
        private long nextFireTime ;
        private long previousFireTime ;
        private String description ;


        public static TriggerInfoVO buildFrom(Trigger trigger) {
            return TriggerInfoVO.builder()
                    .startTime(trigger.getStartTime().getTime())
                    .endTime(trigger.getEndTime().getTime())
                    .nextFireTime(trigger.getNextFireTime().getTime())
                    .previousFireTime(trigger.getPreviousFireTime().getTime())
                    .description(trigger.getDescription())
                    .build();
        }

    }


}
