{"@timestamp":"2025-07-23T15:10:02.65+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:10:02.66+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.661+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.666+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.666+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.667+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.667+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.667+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.667+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.667+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:10:02.667+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b89989234d2564aa","spanId":"b89989234d2564aa","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:14:02.62+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.62+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.625+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.625+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.625+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.625+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.626+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.626+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.626+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:02.626+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3c9d5f0ee617e8d7","spanId":"3c9d5f0ee617e8d7","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.627+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.627+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.631+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.631+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.632+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.632+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.632+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.632+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.632+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.632+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9d38df852d24fd6","spanId":"c9d38df852d24fd6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:22:02.614+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.614+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.617+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.618+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.618+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.618+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:22:02.618+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e227d47a96321bc1","spanId":"e227d47a96321bc1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.051+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:50:03.072+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.073+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.088+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.088+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.089+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.09+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.09+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.091+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.091+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:03.092+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dedaed3ee1ee3450","spanId":"dedaed3ee1ee3450","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.634+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:02.636+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.637+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.644+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.644+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.644+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.644+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.644+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.645+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.645+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.645+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3b8094649154565","spanId":"b3b8094649154565","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.623+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:02.624+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.624+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.628+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.628+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.628+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.629+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.629+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.629+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.629+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.629+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c3f0a7f75bb267dd","spanId":"c3f0a7f75bb267dd","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.427+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:04.618+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.619+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.633+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.633+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.634+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.634+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.634+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.635+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.635+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.635+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aeb8d4bc3a0ee0e0","spanId":"aeb8d4bc3a0ee0e0","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.626+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.626+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.63+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.63+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.63+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.63+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.63+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.63+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.631+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.631+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0e72f2351aed9be1","spanId":"0e72f2351aed9be1","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:02.63+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.63+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.633+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.634+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.634+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.634+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.634+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.634+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.634+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.634+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bad5ae2e6853fa8a","spanId":"bad5ae2e6853fa8a","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:46:02.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.619+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.622+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.622+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.622+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.623+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.623+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.623+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.623+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:46:02.623+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"329c783a4a80f7cc","spanId":"329c783a4a80f7cc","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.18+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:04.282+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.283+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.298+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.299+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.299+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.3+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.3+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.301+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.301+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:04.301+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.806+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.808+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.808+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.821+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.822+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.822+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.822+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.823+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.823+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.823+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.823+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.744+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:02.745+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.746+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.75+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.75+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.751+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.751+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.751+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.751+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.752+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.752+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.799+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.8+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.8+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.804+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.804+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.805+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.805+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.805+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.805+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.805+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.806+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.715+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:02.717+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.717+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.722+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.722+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.722+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.722+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.722+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.722+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.722+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.722+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.815+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.819+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.819+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.826+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.826+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.826+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.826+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.827+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.827+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.827+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.827+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.756+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:34:02.766+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.767+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.778+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.78+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.78+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.781+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.782+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.782+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.782+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.782+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.18+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:03.209+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.21+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.217+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.217+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.218+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.219+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.219+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.22+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.22+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:03.221+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.06+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:03.141+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.142+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.148+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.149+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.149+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.15+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.15+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.15+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.151+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:03.151+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.802+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:02.803+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.803+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.806+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.824+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.827+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.828+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.829+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.83+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.831+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.832+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.891+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.892+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.893+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.897+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.897+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.897+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.897+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.897+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.897+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.897+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.898+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.799+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:02.803+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.803+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.807+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.807+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.808+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.808+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.808+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.808+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.808+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.808+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.696+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.709+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6448, orderId=6219, flash=N38A_2x4_1024GB, orderFlashNo=YS9085##MP#########15008#N38A_QLC_1LU250169_N38A_2x4_1024GB, num=100, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.709+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.72+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.72+08:00","@version":"1","message":"执行预分配前共有68颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.72+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.721+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.721+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.722+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.722+08:00","@version":"1","message":"[6219] - [N38A_2x4_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.722+08:00","@version":"1","message":"[SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6219","traceType":"分配设备"}
