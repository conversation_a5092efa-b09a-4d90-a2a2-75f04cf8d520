package com.yeestor.work_order.service.order;

import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.entity.analysis.FlashAnalysisHistoryEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.rms.OrderInfo;
import com.yeestor.work_order.repository.*;
import com.yeestor.work_order.model.http.req.UpdateFlashInfoReq;
import com.yeestor.work_order.repository.analysis.FailAnalysisRepository;
import com.yeestor.work_order.repository.analysis.FlashAnalysisHistoryRepository;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.zentao.ZenTaoService;
import com.yeestor.work_order.utils.DingTalkUtils;
import com.yeestor.work_order.utils.RMSApis;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FlashService {
    private final WorkOrderRepository workOrderRepository;
    private final OrderFlashRepository flashRepository;
    private final OrderPlanRepository planRepository;
    private final RMSApis rmsApis;
    private final DingTalkUtils dingTalkUtils;

    @Setter(onMethod_ = {@Autowired, @Lazy})
    private OrderService orderService;

    @Setter(onMethod_ = {@Autowired, @Lazy})
    private PlanService planService;

    private final ZenTaoService zenTaoService;

    private final OrderBugRepository orderBugRepository;
    @Setter(onMethod_ = {@Autowired, @Lazy})
    private NotificationService notificationService;

    private final PlanDeviceRepository planDeviceRepository;
    private final FailAnalysisRepository failAnalysisRepository;
    private final FlashAnalysisHistoryRepository analysisHistoryRepository;

    public List<OrderFlashEntity.Status> runningStatus = Arrays.asList(OrderFlashEntity.Status.WAITING_FOR_START, OrderFlashEntity.Status.IN_PROGRESS, OrderFlashEntity.Status.NEW);

    /**
     * <h3>一定要是在plan的状态刚刚变更后才可以调用，不应该重复调用、</h3>
     * 检查 Flash批次的状态是否可以切换为 等待合并报告的状态 ，如果可以，则切换状态。
     *
     * @param orderEntity 工单
     * @param flashEntity 需要检查的 Flash批次
     */
    @Transactional
    public void checkWaitMerge(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {

        // 如果flash 批次的状态已经越过了等待合并报告，则不需要检查
        if (flashEntity.getStatus().greaterThan(OrderFlashEntity.Status.WAITING_FOR_MERGE)) {
            return;
        }
        if (
                planRepository.countByOrderIdAndFlashAndStatusIn(
                        flashEntity.getOrderId(),
                        flashEntity.getFlash(),
                        Arrays.asList(OrderPlanEntity.Status.COMPLETED, OrderPlanEntity.Status.STOPPED))
                        == planRepository.countByOrderIdAndFlash(flashEntity.getOrderId(), flashEntity.getFlash())
        ) {
            // 如果 指定批次的所有plan都完成，则认为该批次的状态为测试完成 ，等待上传报告
            flashEntity.setUpdatedAt(System.currentTimeMillis());
            flashEntity.setStatus(OrderFlashEntity.Status.WAITING_FOR_MERGE);
            flashEntity.setTestEndAt(System.currentTimeMillis());
            flashRepository.save(flashEntity);
            log.info("Flash:{} 下所有的Plan都已完成,调用合并报告并等待.", flashEntity.getFlash());

            // 调用RMS中的文档合并接口。
            rmsApis.mergeOrderReport(flashEntity.getOrderFlashNo(), orderEntity.getSubProduct());
            updateFlashNotice(flashEntity, "等待上传报告");

        }
    }

    /**
     * 更新Flash批次的状态至 WAITING_FOR_FAIL_ANALYSIS .
     *
     * @param orderEntity 工单
     * @param flashEntity 需要更新的 Flash批次
     */
    @Transactional
    public void updateToWaitingForFailAnalysis(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity
    ) {
        if (flashEntity.getStatus().ordinal() >= OrderFlashEntity.Status.WAITING_FOR_FAIL_ANALYSIS.ordinal()) {
            return;
        }
        // #182 由于前端放开了对于报告上传的限制, 这个时候可能会出现上传报告完成, 但是工单的合并完成时间还是空的情况, 这种情况就需要更新合并完成时间.
        if (flashEntity.getMergeCompleteAt() == null) {
            // 向前推进1s
            flashEntity.setMergeCompleteAt(System.currentTimeMillis() - 1000);
        }
        flashEntity.setUpdatedAt(System.currentTimeMillis());
        flashEntity.setStatus(OrderFlashEntity.Status.WAITING_FOR_FAIL_ANALYSIS);
        flashRepository.save(flashEntity);
        log.info("Flash:{} 已进入等待发起Fail分析的状态.", flashEntity.getFlash());

        // 检查并更新工单的状态
        log.debug("orderId: {} flash:{} enter to WAITING_FOR_FAIL_ANALYSIS status!", orderEntity.getId(), flashEntity.getFlash());
        List<OrderFlashEntity> flashEntityList = flashRepository.findAllByOrderId(flashEntity.getOrderId());
        flashEntityList.forEach(e -> log.debug("orderId:{} flash: {} status:{}", e.getOrderId(), e.getFlash(), e.getStatus()));

        long filterCount = flashEntityList.stream()
                .filter(e -> e.getStatus().ordinal() >= OrderFlashEntity.Status.WAITING_FOR_FAIL_ANALYSIS.ordinal())
                .count();
        long allCount = flashEntityList.size();

        if (filterCount == allCount) {
            orderService.startEvaluateOrder(orderEntity);
        }
        log.info("关闭RMS中的Flash批次对应的工单:{}", flashEntity.getOrderFlashNo());
        // 关闭RMS中的工单.
        rmsApis.closeOrder(flashEntity.getOrderFlashNo(), "由工单系统关闭，合并报告已上传。");

    }

    /**
     * 更新Flash批次的状态至 FAIL_ANALYSIS_STARTED .
     *
     * @param flashEntity  需要更新的 Flash批次
     * @param developerUid 开发者Uid
     * @param initiator    发起者
     */
    @Transactional
    public void startFailAnalysis(
            OrderFlashEntity flashEntity,
            String developerUid,
            OAuthUserDetail initiator
    ) {
        updateFlashNotice(flashEntity, "Fail分析中");
        // 判断用户是否有权限发起fail 分析请求
        flashEntity.setUpdatedAt(System.currentTimeMillis());
        flashEntity.setStatus(OrderFlashEntity.Status.FAIL_ANALYSIS_STARTED);
        flashEntity.setStartFailAt(System.currentTimeMillis());
        flashEntity.setStartFailBy(initiator.getUid());
        flashEntity.setStartFailPerson(initiator.getUsername());
        flashEntity.setFailAssignTo(developerUid);
        flashRepository.save(flashEntity);
        log.info("Flash:{} 已进入开始Fail分析状态,由{}来负责完成!", flashEntity.getFlash(), dingTalkUtils.getPersonName(developerUid));

    }

    /**
     * 更新Flash批次的状态至 WAITING_FOR_REVIEW .
     *
     * @param flashEntity 需要更新的 Flash批次
     */
    @Transactional
    public void completeFailAnalysis(
            OrderFlashEntity flashEntity
    ) {

        flashEntity.setUpdatedAt(System.currentTimeMillis());
        flashEntity.setStatus(OrderFlashEntity.Status.WAITING_FOR_REVIEW);
        flashRepository.save(flashEntity);
        updateFlashNotice(flashEntity, "等待Review");
        log.info("Flash:{} 已进入等待Review状态.", flashEntity.getFlash());
    }


    /**
     * 更新Flash批次的状态至 REVIEW_STARTED .
     *
     * @param orderEntity 工单
     * @param flashEntity 需要更新的 Flash批次
     */
    @Transactional
    public void startReview(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity
    ) {

        flashEntity.setUpdatedAt(System.currentTimeMillis());
        flashEntity.setStatus(OrderFlashEntity.Status.REVIEW_STARTED);
        flashRepository.save(flashEntity);
        log.info("Flash:{} 已进入开始Review状态.", flashEntity.getFlash());
        updateFlashNotice(flashEntity, "Review进行中");

        // 检查并更新工单的状态
        log.debug("orderId: {} flash:{} enter to REVIEW_STARTED status!", orderEntity.getId(), flashEntity.getFlash());

    }

    /**
     * 更新Flash批次的状态至 COMPLETED .
     *
     * @param orderEntity  工单
     * @param flashEntity  需要更新的 Flash批次
     * @param reviewResult 审核结果
     */
    @Transactional
    public void completeReview(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            Boolean reviewResult
    ) {

        orderEntity.setReviewResult(Optional.ofNullable(reviewResult).orElse(false) || Optional.ofNullable(orderEntity.getReviewResult()).orElse(false));

        flashEntity.setCompleteAt(System.currentTimeMillis());
        flashEntity.setUpdatedAt(System.currentTimeMillis());
        flashEntity.setStatus(OrderFlashEntity.Status.COMPLETED);
        flashRepository.save(flashEntity);
        log.info("Flash:{} 已进入完成状态.", flashEntity.getFlash());
        // 发送结束通知
        updateFlashNotice(flashEntity, "Flash测试已完成");

        // 检查并更新工单的状态
        log.info("orderId: {} flash:{} enter to COMPLETED status!", orderEntity.getId(), flashEntity.getFlash());
        List<OrderFlashEntity> flashEntityList = flashRepository.findAllByOrderId(flashEntity.getOrderId());
        flashEntityList.forEach(e -> log.info("orderId:{} flash: {} status:{}", e.getOrderId(), e.getFlash(), e.getStatus()));

        long filterCount = flashEntityList.stream()
                .filter(e -> e.getStatus().ordinal() >= OrderFlashEntity.Status.COMPLETED.ordinal())
                .count();
        long allCount = flashEntityList.size();

        if (filterCount == allCount) {
            orderService.completeReview(orderEntity);
        } else {
            orderService.save(orderEntity);
        }

    }


    /**
     * 按照工单 以及 flash 批次来释放对应的设备资源、
     *
     * @param orderEntity      工单
     * @param orderFlashEntity flash 批次
     */
    public void releaseDevicesByFlash(String title, WorkOrderEntity orderEntity, OrderFlashEntity orderFlashEntity) {
        // 获取对应Flash 批次对应的plan列表
        List<OrderPlanEntity> orderPlanEntities = planRepository.findAllByOrderIdAndFlashAndType(orderEntity.getId(), orderFlashEntity.getFlash(), 0);

        orderPlanEntities.forEach(planEntity -> planService.revokeDevicesByPlan(title, orderFlashEntity, planEntity, orderFlashEntity.getOrderFlashNo()));
    }

    @Transactional
    public void disableFlash(OrderFlashEntity flashEntity) {
        flashEntity.setDisabled(true);
        flashEntity.setUpdatedAt(System.currentTimeMillis());
        flashRepository.save(flashEntity);
        log.info("工单{}的批次{}已暂缓分配！", flashEntity.getOrderId(), flashEntity.getFlash());
    }

    /**
     * 修改Flash批次的信息。
     *
     * @param flashEntity Flash批次实体
     * @param body        需要修改的信息
     */
    @Transactional
    public void updateFlashInfo(OrderFlashEntity flashEntity, UpdateFlashInfoReq body) {
        int addCount = body.getAddCount();
        log.info("增加{}个样片，目前样片数量为 {}, 剩余样片数量为:{}",
                addCount, flashEntity.getNum() + addCount, flashEntity.getLeftNum() + addCount
        );
        flashEntity.setNum(flashEntity.getNum() + addCount);
        flashEntity.setLeftNum(flashEntity.getLeftNum() + addCount);
        flashEntity.setUpdatedAt(System.currentTimeMillis());
        flashEntity.setTestRelatedPerson(body.getAddTestPerson());

        flashRepository.save(flashEntity);
    }

    /**
     * 将Flash 批次设置为开始运行的状态,如果已经是运行中的状态，则不做任何操作
     *
     * @param flashEntity Flash 批次
     */
    @Transactional
    public void startFlash(OrderFlashEntity flashEntity) {

        // 只要有 Flash批次 有一个plan进入到测试，则Flash批次的状态变为 测试状态。
        if (flashEntity.getStatus().ordinal() < OrderFlashEntity.Status.IN_PROGRESS.ordinal()) {
            log.info("更新Flash：{}的状态至运行中.", flashEntity.getFlash());
            flashEntity.setUpdatedAt(System.currentTimeMillis());
            flashEntity.setStatus(OrderFlashEntity.Status.IN_PROGRESS);
            flashEntity.setTestStartAt(System.currentTimeMillis());
            flashRepository.save(flashEntity);
        }
    }

    /**
     * 根据flash批次的工单号获取Flash批次实体, 如果不存在则抛出DataNotFoundException 。
     *
     * @param orderFlashNo flash批次的工单号
     * @return Flash批次实体
     * @throws DataNotFoundException 如果不存在则抛出该异常
     */
    public OrderFlashEntity findFlashOrElseThrow(String orderFlashNo) {
        return flashRepository.findByOrderFlashNo(orderFlashNo).orElseThrow(() -> new DataNotFoundException("工单号为" + orderFlashNo + "的工单不存在！"));
    }

    /**
     * 根据工单ID以及Flash获取Flash批次实体, 如果不存在则抛出DataNotFoundException 。
     *
     * @param orderId 工单ID
     * @param flash   Flash批次
     * @return Flash批次实体
     * @throws DataNotFoundException 如果不存在则抛出该异常
     */
    public OrderFlashEntity findFlashOrElseThrow(long orderId, String flash) {
        return flashRepository.findByOrderIdAndFlash(orderId, flash).orElseThrow(() -> new DataNotFoundException("id为" + orderId + "的工单不存在 flash:" + flash));
    }

    public boolean isFlashExist(long orderId, String flash) {
        return flashRepository.existsByOrderIdAndFlash(orderId, flash);
    }

    /**
     * 修改Flash批次的剩余样片数量 的私有方法, 该方法不会做任何的校验。
     *
     * @param currentData 当前的Flash批次实体,一定要是最新的数据。
     * @param increaseNum 增加的样片数量
     */
    private void updateFlashLeftNum(OrderFlashEntity currentData, int increaseNum) {
        int leftNum = currentData.getLeftNum() + increaseNum;
        log.info("更新Flash:{}的样片数量从{}变更为{}", currentData.getFlash(), currentData.getLeftNum(), leftNum);
        currentData.setLeftNum(currentData.getLeftNum() + increaseNum);
        currentData.setUpdatedAt(System.currentTimeMillis());
        flashRepository.save(currentData);
    }

    /**
     * 将Flash 批次的剩余样片数量 加上 @increaseNum
     *
     * @param flashEntity 需要修改的Flash批次
     * @param increaseNum 需要增加的样片数量
     */
    public void updateLeftSampleNum(OrderFlashEntity flashEntity, int increaseNum) {
        //  更新 Flash 批次样片 占用数量。
        OrderFlashEntity currentData = flashRepository.findById(flashEntity.getId()).orElse(null);
        if (currentData == null) {
            throw new DataNotFoundException("Flash批次不存在！");
        }
        updateFlashLeftNum(currentData, increaseNum);
    }

    /**
     * 通过工单号以及Flash批次 来 将Flash 批次的剩余样片数量 加上 @increaseNum
     *
     * @param orderId     工单ID
     * @param flash       Flash批次
     * @param increaseNum 需要增加的样片数量
     */
    public void updateLeftSampleNum(long orderId, String flash, int increaseNum) {
        OrderFlashEntity currentData = findFlashOrElseThrow(orderId, flash);
        updateFlashLeftNum(currentData, increaseNum);
    }

    public List<OrderFlashEntity> fetchFlashListByOrderId(long id) {
        return flashRepository.findAllByOrderIdOrderByIdx(id);
    }

    public OrderFlashEntity fetchFirstFlashOrElseThrow(long id) {
        return flashRepository.findAllByOrderIdOrderByIdx(id).stream().findFirst().orElseThrow(() -> new DataNotFoundException("id为" + id + "的工单不存在Flash批次！"));
    }

    public OrderFlashEntity fetchFlashByFlashNoOrElseThrow(String orderFlashNo) {
        return flashRepository.findByOrderFlashNo(orderFlashNo).stream().findFirst().orElseThrow(() -> new DataNotFoundException("orderFlashNo为" + orderFlashNo + "的工单不存在Flash批次！"));
    }

    /**
     * 取消Flash 批次测试，flash状态更新为
     *
     * @param orderEntity 工单信息
     * @param flashEntity Flash 批次
     * @param reason      取消测试原因
     */
    @Transactional
    public void cancelFlash(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, String reason) {
        // 获取当前登录用户的钉钉ID
        String userDingTalkID = DingTalkUtils.getCurrentUserDingTalkID();
        String person = DingTalkUtils.getCurrentUserName();

        // 将其中plan 用到的设备释放掉
        releaseDevicesByFlash("取消Plan", orderEntity, flashEntity);
        // 如果 指定批次的所有plan都完成，则认为该批次的状态为测试完成 ，等待上传报告
        flashEntity.setUpdatedAt(System.currentTimeMillis());
        flashEntity.setStatus(OrderFlashEntity.Status.WAITING_FOR_MERGE);
        flashEntity.setTestEndAt(System.currentTimeMillis());

        // 取消测试
        flashEntity.setCancelAt(System.currentTimeMillis());
        flashEntity.setCancelBy(userDingTalkID);
        flashEntity.setCancelPerson(person);
        flashEntity.setCancelReason(reason);
        flashRepository.save(flashEntity);

        // 如果只有一个Flash批次，则更新工单状态
        if (flashRepository.countByOrderIdAndStatusIn(orderEntity.getId(), runningStatus) == 0) {
            orderService.startEvaluateOrder(orderEntity);
        }

        log.info("orderNo: {}，Flash:{} 下所有的Plan都取消测试，调用合并报告并等待.", orderEntity.getNo(), flashEntity.getFlash());

        // 调用RMS中的文档合并接口。
        rmsApis.mergeOrderReport(flashEntity.getOrderFlashNo(), orderEntity.getSubProduct());
    }


    /**
     * 获取产品线下flash未绑定的禅道bug信息
     *
     * @param orderId       工单id
     * @param flash         flash 名称
     * @param zenTaoProduct 禅道产品ID
     * @return 未绑定bug
     */
    public HashMap<String, String> fetchFlashUnboundBugs(long orderId, String flash, String zenTaoProduct) {
        HashMap<String, String> bugInfo = zenTaoService.fetchBugList(zenTaoProduct);
        List<OrderBugEntity> bindList = orderBugRepository.findAllByOrderIdAndFlash(orderId, flash);
        bindList.forEach(bug -> {
            bugInfo.remove(bug.getBugId());
        });
        return bugInfo;
    }

    /**
     * 更新钉钉机器人Flash通知
     *
     * @param flashEntity flash信息
     * @param title       标题
     */
    public void updateFlashNotice(OrderFlashEntity flashEntity, String title) {
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(flashEntity.getOrderId());
        notificationService.updateFlashInteractiveMsg(title, orderEntity, flashEntity);
    }

    /**
     * eReport 获取工单批次的容量信息
     *
     * @param orderFlashNo 测试单号
     * @return 详细信息
     */
    public OrderInfo fetchTestFlashInfo(String orderFlashNo) {
        OrderFlashEntity flashEntity = findFlashOrElseThrow(orderFlashNo);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(flashEntity.getOrderId());
        List<OrderFlashEntity> flashEntityList = flashRepository.findAllByOrderId(flashEntity.getOrderId());
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setNo(orderEntity.getNo());
        orderInfo.setBuilder(orderEntity.getBuildPerson());
        orderInfo.setVerType(orderEntity.getVersionType());
        orderInfo.setNoLst(flashEntityList.stream().map(entity -> {
            OrderInfo.FlashModelInfo flashModel = new OrderInfo.FlashModelInfo();
            flashModel.setCap(entity.getSize());
            flashModel.setCeCnt(entity.getCeCount());
            flashModel.setTestNo(entity.getOrderFlashNo());
            return flashModel;
        }).collect(Collectors.toList()));
        return orderInfo;
    }

    /**
     * 结束flash批次的测试流程，此时并未产生任何相关的fail分析与review信息
     *
     * @param orderEntity orderEntity
     * @param flashEntity flashEntity
     */
    public void endingFlashFlow(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {
        // 获取当前登录用户的钉钉ID
        String userDingTalkID = DingTalkUtils.getCurrentUserDingTalkID();

        flashEntity.setEndingBy(userDingTalkID);
        flashEntity.setStatus(OrderFlashEntity.Status.COMPLETED);

        flashEntity.setCompleteAt(System.currentTimeMillis());
        flashRepository.save(flashEntity);
        // 如果只有一个Flash批次，则更新工单状态
        if (flashRepository.countByOrderIdAndStatusIn(orderEntity.getId(), runningStatus) == 0) {
            orderService.startEvaluateOrder(orderEntity);
            orderService.completeReview(orderEntity);
        }
    }

    /**
     * flash状态回退到测试中
     * @param flashEntity flash实体类
     */
    public void fallbackFlashToInProgress(OrderFlashEntity flashEntity) {
        // 结束时间调整为null
        flashEntity.setTestEndAt(null);
        flashEntity.setStatus(OrderFlashEntity.Status.IN_PROGRESS);

        flashRepository.save(flashEntity);

    }

    /**
     * 校验Flash样片数量
     * @param orderId 工单id
     * @param flashEntity flash实体类
     */
    public void checkFlashSampleNum(long orderId, OrderFlashEntity flashEntity) {
        if (flashEntity.getStatus().ordinal() > OrderFlashEntity.Status.IN_PROGRESS.ordinal()) {
            log.info("Flash批次已结束，全部样片回收！");
            flashEntity.setLeftNum(flashEntity.getNum());
        } else {
            List<OrderPlanEntity.Status> statusList = Arrays.asList(OrderPlanEntity.Status.READY, OrderPlanEntity.Status.CONFIRMED);
            List<OrderPlanEntity> planEntityList = planRepository.findAllByOrderIdAndFlashAndTypeAndStatusIn(orderId, flashEntity.getFlash(), 0, statusList);
            int notRunNum = planEntityList.stream().mapToInt(plan -> plan.isTestAll() ? flashEntity.getNum() : plan.getTestNum()).sum();

            List<OrderPlanEntity> runningList = planRepository.findAllByOrderIdAndFlashAndTypeAndStatusIn(orderId, flashEntity.getFlash(), 0, Arrays.asList(OrderPlanEntity.Status.RUNNING));
            List<Long> planIds = runningList.stream().map(p -> p.getId()).collect(Collectors.toList());
            List<PlanDeviceEntity> deviceEntityList = planDeviceRepository.findAllByPlanIdInAndStatusIn(
                    planIds,
                    List.of(
                            PlanDeviceEntity.Status.OCCUPIED,
                            PlanDeviceEntity.Status.CONFIRMED,
                            PlanDeviceEntity.Status.RUNNING
                    )
            );
            int runningNum = deviceEntityList.stream().mapToInt(PlanDeviceEntity::getTestNum).sum();

            List<PlanDeviceEntity> failDeviceList = planDeviceRepository.findByPlanIdInAndStatusAndReleaseAtIsNull(planIds, PlanDeviceEntity.Status.FINISHED_FAILED);
            int failNum = failDeviceList.stream().mapToInt(PlanDeviceEntity::getTestNum).sum();

            log.info("未运行Plan占用样片: {} , 运行中的设备占用样片: {}, 运行失败设备占用样片： {}", notRunNum, runningNum, failNum);
            flashEntity.setLeftNum(flashEntity.getNum() - (notRunNum + runningNum + failNum));
        }
        flashRepository.save(flashEntity);
    }

    /**
     * 如果工单测试已结束，转release版本后，可进行复测release版本
     * @param orderEntity 工单实体类
     * @param flashEntity flash实体类
     * @param userDetail 用户信息
     */
    public void retestReleaseFlash(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, OAuthUserDetail userDetail) {
//        orderEntity.setStatus(WorkOrderEntity.Status.TESTING);
//        orderEntity.clearOrderStatus();
//        workOrderRepository.save(orderEntity);
//
//        flashEntity.setRetestAt(System.currentTimeMillis());
//        flashEntity.setRetestPerson(userDetail.getUsername());
//        flashEntity.setRetestBy(userDetail.getUid());
//        flashEntity.setHasTestConvert(true);
//        flashEntity.setMarkVersion("AlphaToRelease");
//        flashEntity.setStatus(OrderFlashEntity.Status.IN_PROGRESS);
//        // 移除结束测试后记录的信息
//        flashEntity.clearFlashStatus();
//        flashRepository.save(flashEntity);
//
//        // 查询fail分析信息【跳过fail分析也会有记录信息】
//        failAnalysisRepository.findFirstByOrderIdAndFlash(flashEntity.getOrderId(), flashEntity.getFlash())
//                .ifPresent(entity -> {
//                    FlashAnalysisHistoryEntity historyEntity = new FlashAnalysisHistoryEntity().analysisToHistory(entity);
//                    // 保存历史的fail分析记录
//                    analysisHistoryRepository.save(historyEntity);
//                    // 清空当前的fail分析信息
//                    failAnalysisRepository.delete(entity);
//                });
//        notificationService.updateFlashInteractiveMsg("版本转换，Flash复测中", orderEntity, flashEntity);
    }

}
