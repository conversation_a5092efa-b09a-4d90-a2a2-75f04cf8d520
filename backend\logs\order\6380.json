{"@timestamp":"2025-07-23T10:33:02.806+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:33:02.813+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.833+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.833+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.833+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.617+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:39:02.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.639+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.64+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:45:02.608+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.624+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.625+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:48:02.63+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.645+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:51:02.62+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.638+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:02.631+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.679+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.68+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.68+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:03:02.609+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.632+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.632+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:09:02.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.63+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.63+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.606+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:12:02.607+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.631+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.631+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.605+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:15:02.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.622+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.622+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.622+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:39:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.64+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.641+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.641+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:02.63+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.678+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.608+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:45:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.628+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.628+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:51:02.636+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.653+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.654+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.617+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:00:02.618+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.637+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.637+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.605+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:03:02.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.622+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.622+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.622+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:15:02.626+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.642+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.642+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.642+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.619+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:27:02.62+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.642+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.642+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.642+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.604+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:02.605+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.646+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.646+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.646+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:36:02.611+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.631+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.631+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.605+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:39:02.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.621+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.621+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.621+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.63+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.63+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.604+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:45:02.605+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.623+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.623+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.623+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.603+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:51:02.604+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.619+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.619+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.62+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.602+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:00:02.603+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.619+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.619+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.619+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:12:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.637+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.637+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.603+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:15:02.604+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.622+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.622+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.622+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.605+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:02.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.621+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.621+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.621+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.603+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:21:02.605+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.627+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.627+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.603+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:27:02.604+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.621+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.621+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.621+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.606+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:02.607+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.623+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.623+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.623+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.604+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:33:02.605+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.627+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.627+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:36:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.629+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.629+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.615+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:39:02.616+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.638+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.605+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:02.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.638+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:45:02.632+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.647+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.647+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.603+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:48:02.604+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.631+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.631+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:51:02.626+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.642+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.642+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.642+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.605+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:00:02.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.621+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.621+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.621+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:03:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.636+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.636+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:02.614+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.63+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.63+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.608+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:09:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.627+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.627+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:15:02.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.632+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.632+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6380","traceType":"分配设备"}
