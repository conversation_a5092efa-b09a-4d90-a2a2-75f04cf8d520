{"@timestamp":"2025-07-23T15:09:03.019+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:09:03.032+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.057+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.057+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.058+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.614+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:12:02.616+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.635+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.635+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:15:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.636+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.636+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.619+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.621+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.645+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:24:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.643+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.643+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.641+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:51:02.655+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.682+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.683+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.683+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.602+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:57:02.604+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.624+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.624+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.598+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:00:02.6+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=119)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.624+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.625+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.625+08:00","@version":"1","message":" getValidPlanList sumList: [119] leftFlashNum:119 index:1 plans:[OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:00:02.625+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 1 plans: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.626+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.751+08:00","@version":"1","message":"[5e299d02] HTTP GET http://ereport.yeestor.com/wo/device/list?p=SD","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:02.993+08:00","@version":"1","message":"getAllDeviceList with SD got data DeviceListResp(code=0, data=[{ GE2_3_15,************,E0-D5-5E-9D-10-AB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_23,************1,18-C0-4D-BB-C2-BB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_22,*************,18-C0-4D-BB-C5-BB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_11,*************,2C-F0-5D-40-F5-23,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_18,*************,E0-D5-5E-C2-AD-7C,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_29,*************,B4-2E-99-29-3B-F5,[{DUT=8}, {年限2=-1}] }, { GE2_3_17,*************,E0-D5-5E-9F-67-0E,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_16,************,B4-2E-99-29-3F-76,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_03,************5,B4-2E-99-E7-5F-C2,[{性能=4}, {年限2=-1}] }, { GE2_2_32,************,2C-F0-5D-40-F5-7E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_19,************,B4-2E-99-29-3F-8A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_25,************,18-C0-4D-BB-BF-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_29,************,18-C0-4D-BB-B7-30,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE-PC-SDhigh-02,*************,E0-D5-5E-9F-6C-01,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-13,************,B4-2E-99-5A-E9-C2,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_3_03,************,B4-2E-99-5A-E4-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_17,************1,18-C0-4D-BA-04-2A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_1_03,************8,B4-2E-99-29-3B-C7,[{开卡架=40}, {年限2=-1}] }, { GE2_1_26,*************,18-C0-4D-BB-C6-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_02,*************,18-C0-4D-BA-23-0F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE4_2_02,*************,B4-2E-99-5A-E4-D7,[{程控掉电=16}, {USB3.0HUB=8}] }, { GE2_1_19,*************,18-C0-4D-BA-04-85,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_02,************,B4-2E-99-29-3F-96,[{TCPIP掉电=4}, {Mars=4}, {DUT=6}, {年限2=-1}] }, { GE2_1_27,*************,18-C0-4D-BA-21-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_20,***********,18-C0-4D-BA-04-82,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_32,***********04,18-C0-4D-BA-03-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_19,***********17,2C-F0-5D-40-F5-28,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_10,***********2,B4-2E-99-29-3F-CD,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE4_2_01,***********53,B4-2E-99-59-F7-26,[{程控掉电=16}, {USB3.0HUB=8}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_16,************,2C-F0-5D-40-F5-1D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_26,***********,E0-D5-5E-9A-F9-43,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_2_12,*************,2C-F0-5D-40-F5-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_28,************,18-C0-4D-BA-25-1B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_12,***********,E0-D5-5E-9D-86-DB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_21,***********2,18-C0-4D-BB-C5-E4,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_25,***********00,2C-F0-5D-40-F7-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_24,************,18-C0-4D-BB-C5-BF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_15,************3,2C-F0-5D-40-F5-0E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_07,***********,E0-D5-5E-9F-6B-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=6}, {年限2=-1}] }, { GE2_3_21,***********1,E0-D5-5E-9D-C0-77,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE-PC-SDhigh-27,************,B4-2E-99-59-FA-3A,[{Mars=4}, {DUT=4}, {高温=4}] }, { GE-PC-SDhigh-23,*************,B4-2E-99-5A-EA-F1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-15,*************,B4-2E-99-5A-E6-A8,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-04,*************,B4-2E-99-29-3F-BA,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-05,*************,B4-2E-99-29-3F-E5,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-03,*************,B4-2E-99-29-3F-E8,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-11,*************,B4-2E-99-5A-E5-2B,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-18,*************,B4-2E-99-5A-E1-DC,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-25,*************,B4-2E-99-5A-E6-A3,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-16,************,B4-2E-99-5A-DF-B6,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-07,172.18.41.133,B4-2E-99-29-3F-AC,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-19,172.18.41.42,B4-2E-99-5A-E1-C2,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-12,************,B4-2E-99-5A-EB-33,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-09,************,B4-2E-99-29-3C-B6,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-10,************,B4-2E-99-29-40-6B,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-08,*************,B4-2E-99-29-3F-AF,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_3_05,***********,E0-D5-5E-C1-2B-4C,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_31,***********1,18-C0-4D-BB-C6-4E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE-PC-SDhigh-01,************,E0-D5-5E-9F-6B-9C,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-26,***********9,B4-2E-99-5A-E2-F1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-06,*************,B4-2E-99-29-3F-59,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_11,************,E0-D5-5E-C1-30-6A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE-PC-SDhigh-20,************,B4-2E-99-5A-EC-20,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-22,***********5,B4-2E-99-5A-EA-81,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-14,*************,B4-2E-99-5A-EA-F0,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-21,*************,B4-2E-99-5A-EB-2F,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE2_1_14,*************,18-C0-4D-BA-04-33,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_18,*************,18-C0-4D-BA-04-89,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_27,***********7,2C-F0-5D-40-F8-F2,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_24,************6,2C-F0-5D-40-F5-3D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{温循=4}, {高温=4}, {Mars=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{高温=4}, {低温=4}, {Mars=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{温循=4}, {高温=4}, {Mars=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { GE2_1_30,************,18-C0-4D-BB-C6-AC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_2_04,***********0,18-C0-4D-BA-23-11,[{性能=4}, {年限1=-1}] }, { GE3_2_17,************3,0C-9D-92-75-B0-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE2_3_09,*************,E0-D5-5E-99-46-D5,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_05,************,18-C0-4D-B5-45-91,[{性能=4}, {年限1=-1}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE2_3_22,***********40,E0-D5-5E-9D-12-FA,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_23,************,E0-D5-5E-9D-86-FB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_24,***********49,B4-2E-99-29-3B-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_25,************,E0-D5-5E-9D-BB-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=4}] }, { GE2_3_27,************,B4-2E-99-29-40-6A,[{DUT=8}, {年限2=-1}] }, { GE2_3_28,*************,B4-2E-99-29-40-53,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_31,*************,E0-D5-5E-9F-63-7F,[{DUT=8}, {年限2=-1}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { GE-PC-SDhigh-32,************,D8-5E-D3-59-EB-B1,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-29,************8,D8-5E-D3-51-8F-12,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-28,************,D8-5E-D3-51-87-5A,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-36,************,18-C0-4D-AB-1E-33,[{Mars=4}, {高温=4}] }, { GE_PC_010,*************,D8-5E-D3-51-87-5C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_008,*************,D8-5E-D3-59-E9-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_011,************,D8-5E-D3-59-EB-2E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {温循=4}, {低温=4}, {高温=4}] }, { GE_PC_012,*************,D8-5E-D3-59-EB-68,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_013,*************,D8-5E-D3-59-EC-60,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_014,*************,D8-5E-D3-51-81-E2,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {温循=4}, {性能=4}, {低温=4}, {高温=4}] }, { GE_PC_015,*************,D8-5E-D3-51-8E-94,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_016,*************,D8-5E-D3-59-E8-AB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_020,*************,D8-5E-D3-59-E7-55,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_017,*************,D8-5E-D3-51-81-E0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {低温=4}, {高温=4}, {温循=4}, {性能=4}] }, { GE_PC_018,************,D8-5E-D3-59-E8-5F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_022,*************,D8-5E-D3-51-8E-C2,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_028,*************,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_009,*************,D8-5E-D3-59-EB-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_021,*************,D8-5E-D3-51-8F-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {低温=4}, {高温=4}, {温循=4}, {性能=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_019,***********,D8-5E-D3-51-81-E3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE3_2_23,***********3,08-BF-B8-6F-CA-49,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_24,************2,08-BF-B8-6F-C8-B8,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_29,*************,08-BF-B8-6F-CA-DA,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_28,***********07,08-BF-B8-39-8D-08,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_30,************,08-BF-B8-6F-CA-D2,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_32,*************,08-BF-B8-6F-CB-1E,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_15,***********31,08-BF-B8-39-68-1D,[{年限3=-1}] }, { GE_PC_055,***********08,A4-0C-66-14-25-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_047,***********12,A4-0C-66-14-28-45,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_049,************8,A4-0C-66-14-28-4D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_050,*************,A4-0C-66-14-2B-FE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_046,*************,A4-0C-66-14-2C-08,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_053,***********1,A4-0C-66-14-27-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_054,************,A4-0C-66-14-26-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_058,************,A4-0C-66-14-25-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_036,*************,A4-0C-66-14-28-EE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_039,************4,A4-0C-66-14-25-B3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_040,***********05,A4-0C-66-14-26-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_041,***********48,A4-0C-66-14-26-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_042,************9,A4-0C-66-14-29-49,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_043,*************,A4-0C-66-14-25-B0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_044,************0,A4-0C-66-14-28-64,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_045,************,A4-0C-66-14-25-B1,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_048,***********48,A4-0C-66-14-28-39,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_051,***********,A4-0C-66-14-28-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_052,************,A4-0C-66-14-2C-42,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_056,************,A4-0C-66-14-26-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_057,***********6,A4-0C-66-14-2C-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_059,************,A4-0C-66-14-28-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_060,***********53,A4-0C-66-14-2A-95,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,***********8,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-67,************5,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }], msg=测试机信息获取成功！, workPcLst=[{ GE3_1_12,************7,2C-4D-54-54-78-1D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限2=-1}, {开卡架=40}] }, { GE2_2_13,************,2C-F0-5D-40-F5-51,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_1_11,************8,2C-4D-54-54-77-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限2=1}] }, { GE3_2_18.1,***********39,08-BF-B8-6F-CB-16,[{性能=4}, {Mars=4}, {TCPIP掉电=4}] }, { GE3_2_26,************,08-BF-B8-6F-CA-D5,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_27,***********4,08-BF-B8-6F-C9-9D,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_31,************,08-BF-B8-39-8C-19,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.079+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6380 flash: YXW-6285ENAB-8T2M-A_32GB available device: [{ GE2_3_15,************,E0-D5-5E-9D-10-AB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_23,************1,18-C0-4D-BB-C2-BB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_22,*************,18-C0-4D-BB-C5-BB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_11,*************,2C-F0-5D-40-F5-23,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_18,*************,E0-D5-5E-C2-AD-7C,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_29,*************,B4-2E-99-29-3B-F5,[{DUT=8}, {年限2=-1}] }, { GE2_3_17,*************,E0-D5-5E-9F-67-0E,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_16,************,B4-2E-99-29-3F-76,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_03,************5,B4-2E-99-E7-5F-C2,[{性能=4}, {年限2=-1}] }, { GE2_2_32,************,2C-F0-5D-40-F5-7E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_19,************,B4-2E-99-29-3F-8A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_25,************,18-C0-4D-BB-BF-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_29,************,18-C0-4D-BB-B7-30,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE-PC-SDhigh-02,*************,E0-D5-5E-9F-6C-01,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-13,************,B4-2E-99-5A-E9-C2,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_3_03,************,B4-2E-99-5A-E4-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_17,************1,18-C0-4D-BA-04-2A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_1_03,************8,B4-2E-99-29-3B-C7,[{开卡架=40}, {年限2=-1}] }, { GE2_1_26,*************,18-C0-4D-BB-C6-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_02,*************,18-C0-4D-BA-23-0F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE4_2_02,*************,B4-2E-99-5A-E4-D7,[{程控掉电=16}, {USB3.0HUB=8}] }, { GE2_1_19,*************,18-C0-4D-BA-04-85,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_02,************,B4-2E-99-29-3F-96,[{TCPIP掉电=4}, {Mars=4}, {DUT=6}, {年限2=-1}] }, { GE2_1_27,*************,18-C0-4D-BA-21-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_20,***********,18-C0-4D-BA-04-82,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_32,***********04,18-C0-4D-BA-03-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_19,***********17,2C-F0-5D-40-F5-28,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_10,***********2,B4-2E-99-29-3F-CD,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE4_2_01,***********53,B4-2E-99-59-F7-26,[{程控掉电=16}, {USB3.0HUB=8}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_16,************,2C-F0-5D-40-F5-1D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_26,***********,E0-D5-5E-9A-F9-43,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_2_12,*************,2C-F0-5D-40-F5-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_28,************,18-C0-4D-BA-25-1B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_12,***********,E0-D5-5E-9D-86-DB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_1_21,***********2,18-C0-4D-BB-C5-E4,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_25,***********00,2C-F0-5D-40-F7-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_24,************,18-C0-4D-BB-C5-BF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_15,************3,2C-F0-5D-40-F5-0E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_07,***********,E0-D5-5E-9F-6B-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=6}, {年限2=-1}] }, { GE2_3_21,***********1,E0-D5-5E-9D-C0-77,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE-PC-SDhigh-27,************,B4-2E-99-59-FA-3A,[{Mars=4}, {DUT=4}, {高温=4}] }, { GE-PC-SDhigh-23,*************,B4-2E-99-5A-EA-F1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-15,*************,B4-2E-99-5A-E6-A8,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-04,*************,B4-2E-99-29-3F-BA,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-05,*************,B4-2E-99-29-3F-E5,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-03,*************,B4-2E-99-29-3F-E8,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-11,*************,B4-2E-99-5A-E5-2B,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-18,*************,B4-2E-99-5A-E1-DC,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-25,*************,B4-2E-99-5A-E6-A3,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-16,************,B4-2E-99-5A-DF-B6,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-07,172.18.41.133,B4-2E-99-29-3F-AC,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-19,172.18.41.42,B4-2E-99-5A-E1-C2,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-12,************,B4-2E-99-5A-EB-33,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-09,************,B4-2E-99-29-3C-B6,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-10,************,B4-2E-99-29-40-6B,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-08,*************,B4-2E-99-29-3F-AF,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_3_05,***********,E0-D5-5E-C1-2B-4C,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_31,***********1,18-C0-4D-BB-C6-4E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE-PC-SDhigh-01,************,E0-D5-5E-9F-6B-9C,[{低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-26,***********9,B4-2E-99-5A-E2-F1,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-06,*************,B4-2E-99-29-3F-59,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_3_11,************,E0-D5-5E-C1-30-6A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE-PC-SDhigh-20,************,B4-2E-99-5A-EC-20,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-22,***********5,B4-2E-99-5A-EA-81,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-14,*************,B4-2E-99-5A-EA-F0,[{Mars=4}, {低温=4}, {高温=4}, {年限2=-1}] }, { GE-PC-SDhigh-21,*************,B4-2E-99-5A-EB-2F,[{Mars=4}, {高温=4}, {年限2=-1}] }, { GE2_1_14,*************,18-C0-4D-BA-04-33,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_1_18,*************,18-C0-4D-BA-04-89,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_27,***********7,2C-F0-5D-40-F8-F2,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE2_2_24,************6,2C-F0-5D-40-F5-3D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{温循=4}, {高温=4}, {Mars=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{高温=4}, {低温=4}, {Mars=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{温循=4}, {高温=4}, {Mars=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { GE2_1_30,************,18-C0-4D-BB-C6-AC,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {年限1=-1}, {FIO=4}] }, { GE3_2_04,***********0,18-C0-4D-BA-23-11,[{性能=4}, {年限1=-1}] }, { GE3_2_17,************3,0C-9D-92-75-B0-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE2_3_09,*************,E0-D5-5E-99-46-D5,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE3_2_05,************,18-C0-4D-B5-45-91,[{性能=4}, {年限1=-1}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE2_3_22,***********40,E0-D5-5E-9D-12-FA,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_23,************,E0-D5-5E-9D-86-FB,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_24,***********49,B4-2E-99-29-3B-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_25,************,E0-D5-5E-9D-BB-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=4}] }, { GE2_3_27,************,B4-2E-99-29-40-6A,[{DUT=8}, {年限2=-1}] }, { GE2_3_28,*************,B4-2E-99-29-40-53,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {年限2=-1}] }, { GE2_3_31,*************,E0-D5-5E-9F-63-7F,[{DUT=8}, {年限2=-1}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { GE-PC-SDhigh-32,************,D8-5E-D3-59-EB-B1,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-29,************8,D8-5E-D3-51-8F-12,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-28,************,D8-5E-D3-51-87-5A,[{Mars=4}, {高温=4}] }, { GE-PC-SDhigh-36,************,18-C0-4D-AB-1E-33,[{Mars=4}, {高温=4}] }, { GE_PC_010,*************,D8-5E-D3-51-87-5C,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_008,*************,D8-5E-D3-59-E9-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_011,************,D8-5E-D3-59-EB-2E,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {温循=4}, {低温=4}, {高温=4}] }, { GE_PC_012,*************,D8-5E-D3-59-EB-68,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_013,*************,D8-5E-D3-59-EC-60,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_014,*************,D8-5E-D3-51-81-E2,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {温循=4}, {性能=4}, {低温=4}, {高温=4}] }, { GE_PC_015,*************,D8-5E-D3-51-8E-94,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_016,*************,D8-5E-D3-59-E8-AB,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_020,*************,D8-5E-D3-59-E7-55,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_017,*************,D8-5E-D3-51-81-E0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {低温=4}, {高温=4}, {温循=4}, {性能=4}] }, { GE_PC_018,************,D8-5E-D3-59-E8-5F,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_022,*************,D8-5E-D3-51-8E-C2,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_028,*************,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_009,*************,D8-5E-D3-59-EB-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_021,*************,D8-5E-D3-51-8F-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {低温=4}, {高温=4}, {温循=4}, {性能=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}, {DUT=8}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE_PC_019,***********,D8-5E-D3-51-81-E3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}, {低温=4}, {高温=4}, {温循=4}] }, { GE3_2_23,***********3,08-BF-B8-6F-CA-49,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_24,************2,08-BF-B8-6F-C8-B8,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_29,*************,08-BF-B8-6F-CA-DA,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_28,***********07,08-BF-B8-39-8D-08,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_30,************,08-BF-B8-6F-CA-D2,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_32,*************,08-BF-B8-6F-CB-1E,[{FIO=4}, {TCPIP掉电=4}, {性能=4}, {Mars=4}] }, { GE3_2_15,***********31,08-BF-B8-39-68-1D,[{年限3=-1}] }, { GE_PC_055,***********08,A4-0C-66-14-25-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_047,***********12,A4-0C-66-14-28-45,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_049,************8,A4-0C-66-14-28-4D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_050,*************,A4-0C-66-14-2B-FE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_046,*************,A4-0C-66-14-2C-08,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_053,***********1,A4-0C-66-14-27-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_054,************,A4-0C-66-14-26-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_058,************,A4-0C-66-14-25-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_036,*************,A4-0C-66-14-28-EE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_039,************4,A4-0C-66-14-25-B3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_040,***********05,A4-0C-66-14-26-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_041,***********48,A4-0C-66-14-26-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_042,************9,A4-0C-66-14-29-49,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_043,*************,A4-0C-66-14-25-B0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_044,************0,A4-0C-66-14-28-64,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_045,************,A4-0C-66-14-25-B1,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_048,***********48,A4-0C-66-14-28-39,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_051,***********,A4-0C-66-14-28-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_052,************,A4-0C-66-14-2C-42,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_056,************,A4-0C-66-14-26-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_057,***********6,A4-0C-66-14-2C-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_059,************,A4-0C-66-14-28-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_060,***********53,A4-0C-66-14-2A-95,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,***********8,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-67,************5,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.081+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.081+08:00","@version":"1","message":"[6380] Plan22 need Num: 119 to test. YXW-6285ENAB-8T2M-A_32GB left num:119 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.081+08:00","@version":"1","message":"subProduct SD flashName YXW-6285ENAB-8T2M-A_32GB plan [Plan22] attrs Mars belongTo tommie.zheng is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.085+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [GE3_2_16, GE3_2_17, GE_PC_036, GE_PC_039, GE_PC_040, GE_PC_041, GE_PC_042, GE_PC_043, GE_PC_044, GE_PC_045, GE_PC_046, GE_PC_047, GE_PC_048, GE_PC_049, GE_PC_050, GE_PC_051, GE_PC_052, GE_PC_053, GE_PC_054, GE_PC_055, GE_PC_056, GE_PC_057, GE_PC_058, GE_PC_059, GE_PC_060, GE2_1_02, GE2_1_14, GE2_1_17, GE2_1_18, GE2_1_19, GE2_1_20, GE2_1_21, GE2_1_22, GE2_1_23, GE2_1_24, GE2_1_25, GE2_1_26, GE2_1_27, GE2_1_28, GE2_1_29, GE2_1_30, GE2_1_31, GE2_1_32, GE2_2_11, GE2_2_12, GE2_2_15, GE2_2_16, GE2_2_19, GE2_2_20, GE2_2_21, GE2_2_22, GE2_2_23, GE2_2_24, GE2_2_25, GE2_2_26, GE2_2_27, GE2_2_28, GE2_2_29, GE2_2_30, GE2_2_31, GE2_2_32, GE2_3_02, GE2_3_03, GE2_3_05, GE2_3_07, GE2_3_09, GE2_3_10, GE2_3_11, GE2_3_12, GE2_3_15, GE2_3_16, GE2_3_17, GE2_3_18, GE2_3_19, GE2_3_21, GE2_3_22, GE2_3_23, GE2_3_24, GE2_3_25, GE2_3_26, GE2_3_28, GE-PC-SDhigh-67, GE-PC-SDhigh-68, GE-PC-SDhigh-69, GE-PC-SDhigh-70, GE-PC-SDhigh-71, GE-PC-SDhigh-72, GE3_2_23, GE3_2_24, GE3_2_28, GE3_2_29, GE3_2_30, GE3_2_32]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.085+08:00","@version":"1","message":"Plan22 test all sample is true","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.094+08:00","@version":"1","message":"[6380] plan:Plan22 use 30 pc: [{ GE-PC-SDhigh-67,************5,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,***********8,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE3_2_17,************3,0C-9D-92-75-B0-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE_PC_036,*************,A4-0C-66-14-28-EE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_039,************4,A4-0C-66-14-25-B3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_040,***********05,A4-0C-66-14-26-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_041,***********48,A4-0C-66-14-26-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_042,************9,A4-0C-66-14-29-49,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_043,*************,A4-0C-66-14-25-B0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_044,************0,A4-0C-66-14-28-64,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_045,************,A4-0C-66-14-25-B1,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_046,*************,A4-0C-66-14-2C-08,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_047,***********12,A4-0C-66-14-28-45,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_048,***********48,A4-0C-66-14-28-39,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_049,************8,A4-0C-66-14-28-4D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_050,*************,A4-0C-66-14-2B-FE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_051,***********,A4-0C-66-14-28-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_052,************,A4-0C-66-14-2C-42,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_053,***********1,A4-0C-66-14-27-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_054,************,A4-0C-66-14-26-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_055,***********08,A4-0C-66-14-25-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_056,************,A4-0C-66-14-26-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_057,***********6,A4-0C-66-14-2C-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_058,************,A4-0C-66-14-25-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_059,************,A4-0C-66-14-28-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.095+08:00","@version":"1","message":"assignDevices [6380] - find 1 run devices:  {Plan22=[{ GE-PC-SDhigh-67,************5,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,***********8,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE3_2_17,************3,0C-9D-92-75-B0-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE_PC_036,*************,A4-0C-66-14-28-EE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_039,************4,A4-0C-66-14-25-B3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_040,***********05,A4-0C-66-14-26-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_041,***********48,A4-0C-66-14-26-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_042,************9,A4-0C-66-14-29-49,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_043,*************,A4-0C-66-14-25-B0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_044,************0,A4-0C-66-14-28-64,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_045,************,A4-0C-66-14-25-B1,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_046,*************,A4-0C-66-14-2C-08,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_047,***********12,A4-0C-66-14-28-45,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_048,***********48,A4-0C-66-14-28-39,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_049,************8,A4-0C-66-14-28-4D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_050,*************,A4-0C-66-14-2B-FE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_051,***********,A4-0C-66-14-28-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_052,************,A4-0C-66-14-2C-42,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_053,***********1,A4-0C-66-14-27-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_054,************,A4-0C-66-14-26-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_055,***********08,A4-0C-66-14-25-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_056,************,A4-0C-66-14-26-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_057,***********6,A4-0C-66-14-2C-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_058,************,A4-0C-66-14-25-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_059,************,A4-0C-66-14-28-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.115+08:00","@version":"1","message":"工单[6380] flash YXW-6285ENAB-8T2M-A_32GB , 参与此次Plan预分配的plan共有 [Plan22] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.196+08:00","@version":"1","message":" add 30 devices :[{ GE-PC-SDhigh-67,************5,8C-32-23-39-B8-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-68,************,8C-32-23-39-BD-7B,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-69,************,8C-32-23-39-BA-F0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-70,*************,8C-32-23-39-BA-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE-PC-SDhigh-71,***********8,8C-32-23-39-BC-10,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {FIO=4}, {性能=4}] }, { GE-PC-SDhigh-72,************,8C-32-23-39-B8-74,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE3_2_16,************,E0-D5-5E-B7-14-50,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE3_2_17,************3,0C-9D-92-75-B0-AF,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {年限3=-1}] }, { GE_PC_036,*************,A4-0C-66-14-28-EE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_039,************4,A4-0C-66-14-25-B3,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_040,***********05,A4-0C-66-14-26-F6,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_041,***********48,A4-0C-66-14-26-86,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_042,************9,A4-0C-66-14-29-49,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_043,*************,A4-0C-66-14-25-B0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_044,************0,A4-0C-66-14-28-64,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_045,************,A4-0C-66-14-25-B1,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }, { GE_PC_046,*************,A4-0C-66-14-2C-08,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_047,***********12,A4-0C-66-14-28-45,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_048,***********48,A4-0C-66-14-28-39,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_049,************8,A4-0C-66-14-28-4D,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_050,*************,A4-0C-66-14-2B-FE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_051,***********,A4-0C-66-14-28-E5,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_052,************,A4-0C-66-14-2C-42,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_053,***********1,A4-0C-66-14-27-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_054,************,A4-0C-66-14-26-4A,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_055,***********08,A4-0C-66-14-25-AD,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_056,************,A4-0C-66-14-26-15,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_057,***********6,A4-0C-66-14-2C-27,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_058,************,A4-0C-66-14-25-CE,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}, {性能=4}] }, { GE_PC_059,************,A4-0C-66-14-28-D0,[{TCPIP掉电=4}, {Mars=4}, {DUT=4}] }]  to plan:Plan22","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.218+08:00","@version":"1","message":"plan:Plan22 assign info update to ActualDeviceNum: 30, ExceptedSampleNum: 91","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.46+08:00","@version":"1","message":"[YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB] - Plan22 holdDevices  :[PlanDeviceEntity(id=277065, orderId=6380, planId=113638, planName=Plan22, ip=************5, mac=8C-32-23-39-B8-CE, no=GE-PC-SDhigh-67, position=GE3_1_27, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277066, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=8C-32-23-39-BD-7B, no=GE-PC-SDhigh-68, position=GE3_1_28, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277067, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=8C-32-23-39-BA-F0, no=GE-PC-SDhigh-69, position=GE3_1_29, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277068, orderId=6380, planId=113638, planName=Plan22, ip=*************, mac=8C-32-23-39-BA-F6, no=GE-PC-SDhigh-70, position=GE3_1_30, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277069, orderId=6380, planId=113638, planName=Plan22, ip=***********8, mac=8C-32-23-39-BC-10, no=GE-PC-SDhigh-71, position=GE3_1_31, score=310, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277070, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=8C-32-23-39-B8-74, no=GE-PC-SDhigh-72, position=GE3_1_32, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277071, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=E0-D5-5E-B7-14-50, no=GE3_2_16, position=, score=180, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277072, orderId=6380, planId=113638, planName=Plan22, ip=************3, mac=0C-9D-92-75-B0-AF, no=GE3_2_17, position=, score=180, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277073, orderId=6380, planId=113638, planName=Plan22, ip=*************, mac=A4-0C-66-14-28-EE, no=GE_PC_036, position=GE1_1_07, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277074, orderId=6380, planId=113638, planName=Plan22, ip=************4, mac=A4-0C-66-14-25-B3, no=GE_PC_039, position=GE1_1_11, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277075, orderId=6380, planId=113638, planName=Plan22, ip=***********05, mac=A4-0C-66-14-26-F6, no=GE_PC_040, position=GE1_1_12, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277076, orderId=6380, planId=113638, planName=Plan22, ip=***********48, mac=A4-0C-66-14-26-86, no=GE_PC_041, position=GE1_1_13, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277077, orderId=6380, planId=113638, planName=Plan22, ip=************9, mac=A4-0C-66-14-29-49, no=GE_PC_042, position=GE1_1_14, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277078, orderId=6380, planId=113638, planName=Plan22, ip=*************, mac=A4-0C-66-14-25-B0, no=GE_PC_043, position=GE1_1_15, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277079, orderId=6380, planId=113638, planName=Plan22, ip=************0, mac=A4-0C-66-14-28-64, no=GE_PC_044, position=GE1_1_16, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277080, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=A4-0C-66-14-25-B1, no=GE_PC_045, position=GE1_1_17, score=150, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277081, orderId=6380, planId=113638, planName=Plan22, ip=*************, mac=A4-0C-66-14-2C-08, no=GE_PC_046, position=GE1_1_18, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277082, orderId=6380, planId=113638, planName=Plan22, ip=***********12, mac=A4-0C-66-14-28-45, no=GE_PC_047, position=GE1_1_19, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277083, orderId=6380, planId=113638, planName=Plan22, ip=***********48, mac=A4-0C-66-14-28-39, no=GE_PC_048, position=GE1_1_20, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277084, orderId=6380, planId=113638, planName=Plan22, ip=************8, mac=A4-0C-66-14-28-4D, no=GE_PC_049, position=GE1_1_21, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277085, orderId=6380, planId=113638, planName=Plan22, ip=*************, mac=A4-0C-66-14-2B-FE, no=GE_PC_050, position=GE1_1_22, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277086, orderId=6380, planId=113638, planName=Plan22, ip=***********, mac=A4-0C-66-14-28-E5, no=GE_PC_051, position=GE1_1_23, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277087, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=A4-0C-66-14-2C-42, no=GE_PC_052, position=GE1_1_24, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277088, orderId=6380, planId=113638, planName=Plan22, ip=***********1, mac=A4-0C-66-14-27-27, no=GE_PC_053, position=GE1_1_25, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277089, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=A4-0C-66-14-26-4A, no=GE_PC_054, position=GE1_1_26, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277090, orderId=6380, planId=113638, planName=Plan22, ip=***********08, mac=A4-0C-66-14-25-AD, no=GE_PC_055, position=GE1_1_27, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277091, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=A4-0C-66-14-26-15, no=GE_PC_056, position=GE1_1_28, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277092, orderId=6380, planId=113638, planName=Plan22, ip=***********6, mac=A4-0C-66-14-2C-27, no=GE_PC_057, position=GE1_1_29, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277093, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=A4-0C-66-14-25-CE, no=GE_PC_058, position=GE1_1_30, score=220, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277094, orderId=6380, planId=113638, planName=Plan22, ip=************, mac=A4-0C-66-14-28-D0, no=GE_PC_059, position=GE1_1_31, score=150, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.691+08:00","@version":"1","message":"lock device:8C-32-23-39-B8-CE to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.743+08:00","@version":"1","message":"lock device:8C-32-23-39-BD-7B to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.755+08:00","@version":"1","message":"lock device:8C-32-23-39-BA-F0 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.766+08:00","@version":"1","message":"lock device:8C-32-23-39-BA-F6 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.777+08:00","@version":"1","message":"lock device:8C-32-23-39-BC-10 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.786+08:00","@version":"1","message":"lock device:8C-32-23-39-B8-74 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.794+08:00","@version":"1","message":"lock device:E0-D5-5E-B7-14-50 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.802+08:00","@version":"1","message":"lock device:0C-9D-92-75-B0-AF to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.81+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-EE to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.818+08:00","@version":"1","message":"lock device:A4-0C-66-14-25-B3 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.826+08:00","@version":"1","message":"lock device:A4-0C-66-14-26-F6 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.834+08:00","@version":"1","message":"lock device:A4-0C-66-14-26-86 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.842+08:00","@version":"1","message":"lock device:A4-0C-66-14-29-49 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.851+08:00","@version":"1","message":"lock device:A4-0C-66-14-25-B0 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.86+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-64 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.876+08:00","@version":"1","message":"lock device:A4-0C-66-14-25-B1 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.912+08:00","@version":"1","message":"lock device:A4-0C-66-14-2C-08 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.94+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-45 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.95+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-39 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.963+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-4D to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.971+08:00","@version":"1","message":"lock device:A4-0C-66-14-2B-FE to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.979+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-E5 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.985+08:00","@version":"1","message":"lock device:A4-0C-66-14-2C-42 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:03.994+08:00","@version":"1","message":"lock device:A4-0C-66-14-27-27 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.002+08:00","@version":"1","message":"lock device:A4-0C-66-14-26-4A to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.009+08:00","@version":"1","message":"lock device:A4-0C-66-14-25-AD to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.018+08:00","@version":"1","message":"lock device:A4-0C-66-14-26-15 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.03+08:00","@version":"1","message":"lock device:A4-0C-66-14-2C-27 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.036+08:00","@version":"1","message":"lock device:A4-0C-66-14-25-CE to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.044+08:00","@version":"1","message":"lock device:A4-0C-66-14-28-D0 to orderNo:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB planName:Plan22 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.05+08:00","@version":"1","message":"[be0a2e] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.205+08:00","@version":"1","message":"lockDevice with ipList:[************5, ************, ************, *************, ***********8, ************, ************, ************3, *************, ************4, ***********05, ***********48, ************9, *************, ************0, ************, *************, ***********12, ***********48, ************8, *************, ***********, ************, ***********1, ************, ***********08, ************, ***********6, ************, ************] - macList:[8C-32-23-39-B8-CE, 8C-32-23-39-BD-7B, 8C-32-23-39-BA-F0, 8C-32-23-39-BA-F6, 8C-32-23-39-BC-10, 8C-32-23-39-B8-74, E0-D5-5E-B7-14-50, 0C-9D-92-75-B0-AF, A4-0C-66-14-28-EE, A4-0C-66-14-25-B3, A4-0C-66-14-26-F6, A4-0C-66-14-26-86, A4-0C-66-14-29-49, A4-0C-66-14-25-B0, A4-0C-66-14-28-64, A4-0C-66-14-25-B1, A4-0C-66-14-2C-08, A4-0C-66-14-28-45, A4-0C-66-14-28-39, A4-0C-66-14-28-4D, A4-0C-66-14-2B-FE, A4-0C-66-14-28-E5, A4-0C-66-14-2C-42, A4-0C-66-14-27-27, A4-0C-66-14-26-4A, A4-0C-66-14-25-AD, A4-0C-66-14-26-15, A4-0C-66-14-2C-27, A4-0C-66-14-25-CE, A4-0C-66-14-28-D0],no:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.211+08:00","@version":"1","message":"自动分配GE-PC-SDhigh-67(************5),GE-PC-SDhigh-68(************),GE-PC-SDhigh-69(************),GE-PC-SDhigh-70(*************),GE-PC-SDhigh-71(***********8),GE-PC-SDhigh-72(************),GE3_2_16(************),GE3_2_17(************3),GE_PC_036(*************),GE_PC_039(************4),GE_PC_040(***********05),GE_PC_041(***********48),GE_PC_042(************9),GE_PC_043(*************),GE_PC_044(************0),GE_PC_045(************),GE_PC_046(*************),GE_PC_047(***********12),GE_PC_048(***********48),GE_PC_049(************8),GE_PC_050(*************),GE_PC_051(***********),GE_PC_052(************),GE_PC_053(***********1),GE_PC_054(************),GE_PC_055(***********08),GE_PC_056(************),GE_PC_057(***********6),GE_PC_058(************),GE_PC_059(************)给YXW-6285ENAB-8T2M-A_32GB下的Plan22","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.211+08:00","@version":"1","message":"add 30 devices to plan: Plan22 ,expect num: 119, left num: 28","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.212+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6380, flash:YXW-6285ENAB-8T2M-A_32GB, planEntity:OrderPlanEntity(id=113638, orderId=6380, name=Plan22, status=QUEUE, priority=91)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.243+08:00","@version":"1","message":"测试单: YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB plan: Plan22已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.982+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.984+08:00","@version":"1","message":"此次分配flash批次 YXW-6285ENAB-8T2M-A_32GB 下的Plan共消耗 119 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:04.989+08:00","@version":"1","message":"更新Flash:YXW-6285ENAB-8T2M-A_32GB的样片数量从119变更为0","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.6+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:03:02.601+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.604+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.594+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:09:02.597+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.602+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.591+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6380","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:12:02.592+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6618, orderId=6380, flash=YXW-6285ENAB-8T2M-A_32GB, orderFlashNo=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, num=119, leftNum=0)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.593+08:00","@version":"1","message":"[6380] - [YXW-6285ENAB-8T2M-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6380","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:20:06.649+08:00","@version":"1","message":"Flash:YXW-6285ENAB-8T2M-A_32GB下的Plan22","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ceb72667721dee5e","spanId":"ceb72667721dee5e","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:20:06.653+08:00","@version":"1","message":"Flash:YXW-6285ENAB-8T2M-A_32GB下的Plan22使用设备:[***********52, *************, ************5, ************2, ***********2, ************, ************, ************4, ************, ************, ************, ***********6, ***********25, ************, *************, ************4, *************, ************, ***********38, ************, ***********6, ************8, ************, ************3, ***********5, ************4] -[8C-32-23-39-B8-90, 8C-32-23-39-B8-96, 8C-32-23-39-B8-CE, 8C-32-23-39-B9-28, 8C-32-23-39-B9-B9, 8C-32-23-39-BA-E5, 8C-32-23-39-BA-EF, 8C-32-23-39-BB-27, 8C-32-23-39-BB-6B, 8C-32-23-39-BB-CF, 8C-32-23-39-BB-F9, 8C-32-23-39-BC-41, 8C-32-23-39-BC-55, 8C-32-23-39-BC-D4, 8C-32-23-39-BC-FF, 8C-32-23-39-BD-51, 8C-32-23-39-BD-72, 8C-32-23-39-BD-7B, A4-0C-66-13-D6-0F, A4-0C-66-14-27-2D, A4-0C-66-14-27-FC, A4-0C-66-14-28-3A, A4-0C-66-14-28-3B, A4-0C-66-14-28-4C, A4-0C-66-14-2A-5B, A4-0C-66-14-2A-BC] 开始测试!","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ceb72667721dee5e","spanId":"ceb72667721dee5e","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:20:06.653+08:00","@version":"1","message":"startTestPlan with PlanTestParams(planName=Plan22, no=YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB, group=2, bAutoLoc=true, ipList=[***********52, *************, ************5, ************2, ***********2, ************, ************, ************4, ************, ************, ************, ***********6, ***********25, ************, *************, ************4, *************, ************, ***********38, ************, ***********6, ************8, ************, ************3, ***********5, ************4], macList=[8C-32-23-39-B8-90, 8C-32-23-39-B8-96, 8C-32-23-39-B8-CE, 8C-32-23-39-B9-28, 8C-32-23-39-B9-B9, 8C-32-23-39-BA-E5, 8C-32-23-39-BA-EF, 8C-32-23-39-BB-27, 8C-32-23-39-BB-6B, 8C-32-23-39-BB-CF, 8C-32-23-39-BB-F9, 8C-32-23-39-BC-41, 8C-32-23-39-BC-55, 8C-32-23-39-BC-D4, 8C-32-23-39-BC-FF, 8C-32-23-39-BD-51, 8C-32-23-39-BD-72, 8C-32-23-39-BD-7B, A4-0C-66-13-D6-0F, A4-0C-66-14-27-2D, A4-0C-66-14-27-FC, A4-0C-66-14-28-3A, A4-0C-66-14-28-3B, A4-0C-66-14-28-4C, A4-0C-66-14-2A-5B, A4-0C-66-14-2A-BC], product=SD, planPath=\\\\************\\软件工具\\03内部工具版本\\eSee\\GE\\SD\\Mars plan\\6285_V31.02.020.006.plan, marsPath=\\\\************\\软件工具\\03内部工具版本\\eSee\\GE\\SD\\Mars版本\\Mars_40.00.4.37.7z, mpPath=, username=tommie.zheng)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ceb72667721dee5e","spanId":"ceb72667721dee5e","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:20:06.667+08:00","@version":"1","message":"Product:SD testPerson:tommie.zheng order no:YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB plan name:Plan22  macList:[8C-32-23-39-B8-90, 8C-32-23-39-B8-96, 8C-32-23-39-B8-CE, 8C-32-23-39-B9-28, 8C-32-23-39-B9-B9, 8C-32-23-39-BA-E5, 8C-32-23-39-BA-EF, 8C-32-23-39-BB-27, 8C-32-23-39-BB-6B, 8C-32-23-39-BB-CF, 8C-32-23-39-BB-F9, 8C-32-23-39-BC-41, 8C-32-23-39-BC-55, 8C-32-23-39-BC-D4, 8C-32-23-39-BC-FF, 8C-32-23-39-BD-51, 8C-32-23-39-BD-72, 8C-32-23-39-BD-7B, A4-0C-66-13-D6-0F, A4-0C-66-14-27-2D, A4-0C-66-14-27-FC, A4-0C-66-14-28-3A, A4-0C-66-14-28-3B, A4-0C-66-14-28-4C, A4-0C-66-14-2A-5B, A4-0C-66-14-2A-BC]","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ceb72667721dee5e","spanId":"ceb72667721dee5e","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:20:06.669+08:00","@version":"1","message":"[3dc25c4c] HTTP POST http://ereport.yeestor.com/wo/plan/test","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ceb72667721dee5e","spanId":"ceb72667721dee5e","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:20:06.803+08:00","@version":"1","message":"startTestPlan with YS6285##MP#########0199##8T24-F66B_TL250416_Alpha_YXW-6285ENAB-8T2M-A_32GB's Plan22 got data:HandleResp(code=0, data=PlanTestRespModel(successLst=[PlanTestRespModel.DeviceTestResult(ip=***********52, no=GE-PC-SDhigh-64, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-65, reason=), PlanTestRespModel.DeviceTestResult(ip=************5, no=GE-PC-SDhigh-67, reason=), PlanTestRespModel.DeviceTestResult(ip=************2, no=GE-PC-SDhigh-59, reason=), PlanTestRespModel.DeviceTestResult(ip=***********2, no=GE-PC-SDhigh-57, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-63, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-66, reason=), PlanTestRespModel.DeviceTestResult(ip=************4, no=GE-PC-SDHIGH-51, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-62, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDHIGH-55, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-52, reason=), PlanTestRespModel.DeviceTestResult(ip=***********6, no=GE-PC-SDhigh-49, reason=), PlanTestRespModel.DeviceTestResult(ip=***********25, no=GE-PC-SDhigh-54, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-56, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-60, reason=), PlanTestRespModel.DeviceTestResult(ip=************4, no=GE-PC-SDhigh-61, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-50, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-68, reason=), PlanTestRespModel.DeviceTestResult(ip=***********38, no=GE-PC-SDhigh-48, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-42, reason=), PlanTestRespModel.DeviceTestResult(ip=***********6, no=GE-PC-SDHIGH-41, reason=), PlanTestRespModel.DeviceTestResult(ip=************8, no=GE-PC-SDHIGH-46, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-45, reason=), PlanTestRespModel.DeviceTestResult(ip=************3, no=GE-PC-SDhigh-44, reason=), PlanTestRespModel.DeviceTestResult(ip=***********5, no=GE-PC-SDhigh-47, reason=), PlanTestRespModel.DeviceTestResult(ip=************4, no=GE-PC-SDhigh-43, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ceb72667721dee5e","spanId":"ceb72667721dee5e","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:20:06.812+08:00","@version":"1","message":"Plan22 启动测试成功, resp: PlanTestRespModel(successLst=[PlanTestRespModel.DeviceTestResult(ip=***********52, no=GE-PC-SDhigh-64, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-65, reason=), PlanTestRespModel.DeviceTestResult(ip=************5, no=GE-PC-SDhigh-67, reason=), PlanTestRespModel.DeviceTestResult(ip=************2, no=GE-PC-SDhigh-59, reason=), PlanTestRespModel.DeviceTestResult(ip=***********2, no=GE-PC-SDhigh-57, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-63, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-66, reason=), PlanTestRespModel.DeviceTestResult(ip=************4, no=GE-PC-SDHIGH-51, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-62, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDHIGH-55, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-52, reason=), PlanTestRespModel.DeviceTestResult(ip=***********6, no=GE-PC-SDhigh-49, reason=), PlanTestRespModel.DeviceTestResult(ip=***********25, no=GE-PC-SDhigh-54, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-56, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-60, reason=), PlanTestRespModel.DeviceTestResult(ip=************4, no=GE-PC-SDhigh-61, reason=), PlanTestRespModel.DeviceTestResult(ip=*************, no=GE-PC-SDhigh-50, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-68, reason=), PlanTestRespModel.DeviceTestResult(ip=***********38, no=GE-PC-SDhigh-48, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-42, reason=), PlanTestRespModel.DeviceTestResult(ip=***********6, no=GE-PC-SDHIGH-41, reason=), PlanTestRespModel.DeviceTestResult(ip=************8, no=GE-PC-SDHIGH-46, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=GE-PC-SDhigh-45, reason=), PlanTestRespModel.DeviceTestResult(ip=************3, no=GE-PC-SDhigh-44, reason=), PlanTestRespModel.DeviceTestResult(ip=***********5, no=GE-PC-SDhigh-47, reason=), PlanTestRespModel.DeviceTestResult(ip=************4, no=GE-PC-SDhigh-43, reason=)], failLst=[])","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ceb72667721dee5e","spanId":"ceb72667721dee5e","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.577+08:00","@version":"1","message":"execute:AutoAssignDevice_GE_SD  map key:[subProduct, product] value:org.quartz.utils.DirtyFlagMap$DirtyFlagCollection@34b0f3b3 ","logger_name":"com.yeestor.work_order.service.job.AutoAssignDeviceJob","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","context":"QueueService","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.582+08:00","@version":"1","message":"AutoAssignDeviceJob execute product:GE subProduct:SD orderCount:13","logger_name":"com.yeestor.work_order.service.job.AutoAssignDeviceJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","context":"QueueService","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.586+08:00","@version":"1","message":"[SD] start check order's plan . find 7 orders","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","context":"QueueService","no":"6380","traceType":"启动测试","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:40.905+08:00","@version":"1","message":"execute:SD_8C-32-23-39-BC-10_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:tommie.zheng title:手动释放设备后电脑关机 planId:113638 mac:8C-32-23-39-BC-10","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"821f5c4e82277b24","spanId":"821f5c4e82277b24","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:40.907+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-71[8C-32-23-39-BC-10]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"821f5c4e82277b24","spanId":"821f5c4e82277b24","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:40.909+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-71]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"821f5c4e82277b24","spanId":"821f5c4e82277b24","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:40.909+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=tommie.zheng, product=SD, macList=[8C-32-23-39-BC-10])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"821f5c4e82277b24","spanId":"821f5c4e82277b24","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:40.912+08:00","@version":"1","message":"[517fa8ce] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"821f5c4e82277b24","spanId":"821f5c4e82277b24","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:41.067+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=tommie.zheng, product=SD, macList=[8C-32-23-39-BC-10]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=***********8, mac=8C-32-23-39-BC-10, pc_no=GE-PC-SDhigh-71, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"821f5c4e82277b24","spanId":"821f5c4e82277b24","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:41.068+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"821f5c4e82277b24","spanId":"821f5c4e82277b24","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:42.842+08:00","@version":"1","message":"execute:SD_8C-32-23-39-BA-F0_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:tommie.zheng title:手动释放设备后电脑关机 planId:113638 mac:8C-32-23-39-BA-F0","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9432a897a34c157c","spanId":"9432a897a34c157c","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:42.845+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-69[8C-32-23-39-BA-F0]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9432a897a34c157c","spanId":"9432a897a34c157c","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:42.845+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-69]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9432a897a34c157c","spanId":"9432a897a34c157c","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:42.845+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=tommie.zheng, product=SD, macList=[8C-32-23-39-BA-F0])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9432a897a34c157c","spanId":"9432a897a34c157c","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:42.845+08:00","@version":"1","message":"[47e79392] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9432a897a34c157c","spanId":"9432a897a34c157c","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:42.979+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=tommie.zheng, product=SD, macList=[8C-32-23-39-BA-F0]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=8C-32-23-39-BA-F0, pc_no=GE-PC-SDhigh-69, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9432a897a34c157c","spanId":"9432a897a34c157c","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:29:42.98+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9432a897a34c157c","spanId":"9432a897a34c157c","no":"6380","traceType":"TimedShutdownDeviceJob","flash":"YXW-6285ENAB-8T2M-A_32GB"}
