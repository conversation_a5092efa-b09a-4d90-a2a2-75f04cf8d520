package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DeviceStatusChangeParams-DeviceInfo", description = "设备以及样片信息。")
public class DeviceInfo {

    @ApiModelProperty(value = "设备IP")
    private String ip;

    @ApiModelProperty(value = "设备Mac地址")
    private String mac;

    @ApiModelProperty(value = "设备编号")
    private String no;

    @ApiModelProperty(value = "样片数量")
    private int num;

    @ApiModelProperty(value = "样片编号列表")
    private List<String> sampleNoList;

    @ApiModelProperty(value = "系统分配的样片数量")
    private int testNum;

    @JsonAlias("samples")
    @ApiModelProperty(value = "样片详细信息列表")
    private List<SampleInfo> sampleNoLst;
}