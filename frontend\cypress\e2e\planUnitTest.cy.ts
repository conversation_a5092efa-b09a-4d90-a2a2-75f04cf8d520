///<reference types="cypress" />
import { addDevicePlan } from './plan/addDevice.cy';
import { cancelPlan } from './plan/cancel.cy';
import { suspendPlan, apartmentPlan } from './plan/assign.cy';
import { endPlan } from './plan/endTest.cy';
import { confirmAllPlan } from './plan/planConfirm.cy';
import { startPlan } from './plan/start.cy';
// 测试用例
import { orderId, flashName, autoPlanName, manualPlanName, deviceList } from './case';

context('with plan operate unit testing', () => {
  describe('plan-operate', () => {
    beforeEach(() => {
      cy.viewport(1920, 960);
      cy.visit(`http://172.18.11.245:8512/work_order/GE/SD/${orderId}`).wait(5000);
    });

    it('自动Plan测试', () => {
      cy.get('#flash-tab-' + flashName)
        .click()
        .wait(1000);

      // 先开始验证测试，方便后买你All样片的Plan进行排队
      startPlan(manualPlanName);

      /*
                暂缓分配  -->  开始分配  -->  分配环境  -->   开始测试  -->  取消测试
            */
      suspendPlan(autoPlanName);
      cy.wait(8000);
      cy.reload();
      apartmentPlan(autoPlanName);
      cy.wait(80000);
      cy.reload();
      cy.wait(60000);
      cy.reload();
      confirmAllPlan();
      cy.wait(8000);
      cy.reload();
      startPlan(autoPlanName);
      cy.wait(8000);
      cy.reload();
      cancelPlan(autoPlanName);
    });

    it('手动Plan测试', () => {
      cy.get('#flash-tab-' + flashName)
        .click()
        .wait(1000);

      /*
                开始测试  -->  新增电脑  -->  结束测试
            */
      cy.wait(20000);
      cy.reload();
      addDevicePlan(manualPlanName, deviceList);
      cy.wait(8000);
      cy.reload();
      endPlan(manualPlanName);
    });
  });
});
