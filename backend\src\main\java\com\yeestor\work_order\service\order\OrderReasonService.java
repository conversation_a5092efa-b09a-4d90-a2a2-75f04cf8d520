package com.yeestor.work_order.service.order;

import com.yeestor.work_order.entity.order.OrderReasonEntity;
import com.yeestor.work_order.repository.order.OrderReasonRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderReasonService {
    private final OrderReasonRepository orderReasonRepository;

    /**
     * 获取原因信息
     * @param belong 属于工单或者批次
     * @param type 原因类型，取消测试或撤销工单
     * @return 列表
     */
    public List<OrderReasonEntity> findReasonByTypeAndBelong(String belong, String type){
        return orderReasonRepository.findAllByBelongToAndType(belong, type);
    }
}
