package com.yeestor.work_order.model.http.resp.output;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel(value = "DAFlashModel", description = "Flash 批次统计数据列表返回类型")
public class DAFlashModel {
    private long id;

    private long orderId;
    private String orderNo;
    private String product;
    private String subProduct;
    private String chip;

    private String flash;
    private String flashModel;
    private String size;
    private int num;
    private OrderFlashEntity.Status status;
    private Long testStartAt;
    private String testBy;
    private String testPerson;
    private Long testEndAt;
    private Long revokeAt;
    private Long completeAt;
    private Long cancelAt;
    private Long createAt;

    public DAFlashModel(WorkOrderEntity workOrderEntity, OrderFlashEntity flashEntity) {
        this.id = flashEntity.getId();
        this.orderId = workOrderEntity.getId();
        this.product = workOrderEntity.getProduct();
        this.chip = workOrderEntity.getChip();
        this.subProduct = workOrderEntity.getSubProduct();
        this.orderNo = workOrderEntity.getNo();
        this.flashModel = workOrderEntity.getFlash();
        this.flash = flashEntity.getFlash();
        this.size = flashEntity.getSize();
        this.num = flashEntity.getNum();
        this.status = flashEntity.getStatus();
        this.testStartAt = flashEntity.getTestStartAt();
        this.testBy = flashEntity.getTestBy();
        this.testPerson = flashEntity.getTestPerson();
        this.testEndAt = flashEntity.getTestEndAt();
        this.revokeAt = flashEntity.getRevokeAt();
        this.completeAt = flashEntity.getCompleteAt();
        this.cancelAt = flashEntity.getCancelAt();
        this.createAt = flashEntity.getCreatedAt();
    }
}
