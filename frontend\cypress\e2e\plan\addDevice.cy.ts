///<reference types="cypress" />

// context('with plan operate', () => {
//   describe('plan-operate', () => {
//     // 需要操作的工单id
//     const orderId = 363;
//     const flashName = 'flashname02_4GB';
//     const planName = 'Plan3';
//     beforeEach(() => {
//       cy.viewport(1920, 960);
//       cy.visit(`http://172.18.11.245:8512/work_order/GE/SD/${orderId}`).wait(5000);
//     });

//     it('Plan增加设备测试', () => {
//       cy.get('#flash-tab-' + flashName)
//         .click()
//         .wait(1000);
//       addDevicePlan(planName, ['GE3_1_22']);
//     });
//   });
// });
const addDevicePlan = function (name: string, deviceNameList: string[]) {
  cy.get(`a#plan-operate-menu-${name}`)
    .click()
    .get(`button#plan-add-device-${name}`)
    .click()
    .wait(4000);
  // 选择设备
  cy.get(`span.ant-tree-switcher`).click();
  deviceNameList.forEach((deviceName: string) => {
    cy.get(`div#free-device-name-${deviceName}`).click();
  });

  cy.get('#plan-add-device-submit').click().wait(8000);

  //  检验是否开始测试成功
  cy.get(`img#plan-view-${name}`).wait(6000).click();
  deviceNameList.forEach((deviceName: string) => {
    cy.get(`div#plan-device-name-${deviceName}`);
  });

  cy.get(`span#plan-view-title-${name}`).click();
};

export { addDevicePlan };
