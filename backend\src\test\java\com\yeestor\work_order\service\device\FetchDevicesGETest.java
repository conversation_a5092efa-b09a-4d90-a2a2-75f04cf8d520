package com.yeestor.work_order.service.device;

import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.rms.AttrModel;
import com.yeestor.work_order.model.rms.DeviceModel;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.repository.PlanDeviceRepository;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.service.product.ProductContext;
import com.yeestor.work_order.service.product.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith({MockitoExtension.class})
class FetchDevicesGETest {


    QueueService queueService ;

    // 设备匹配服务
    @InjectMocks
    DeviceService deviceService2 ;
    // mock
    @Mock
    DeviceService deviceService ;
    @Mock
    ProductContext productContext ;
    @Mock
    ProductService productService ;


    SecureRandom random = new SecureRandom() ;
    // given info -- global -- start
    WorkOrderEntity orderEntity ;
    OrderPlanEntity planEntity ;

    // given info -- global -- end


    DeviceModel generateDeviceModel(String group, int posInGroup,List<AttrModel> attrModelList) {
        DeviceModel deviceModel = new DeviceModel() ;
        deviceModel.setIp("192.168."+random.nextInt(50)+"." + random.nextInt(255)) ;
        deviceModel.setPcNo(String.format("GE%s_%02d",group,posInGroup)) ;
        deviceModel.setPosition(deviceModel.getPcNo()) ;
        deviceModel.setProduct(orderEntity.getSubProduct()) ;
        deviceModel.setStatus("status-" + random.nextInt(100)) ;
        deviceModel.setAttrModelList(attrModelList) ;
        return deviceModel ;
    }

    @BeforeEach
    void setUp() {
        queueService = new QueueService(
                null,
                null,
                null,
                deviceService,
                null,
                productContext,
                null,
                null,
                null
        );

        orderEntity = new WorkOrderEntity();
        orderEntity.setId(1L);
        orderEntity.setProduct("GE");
        orderEntity.setSubProduct("SD");

        planEntity = new OrderPlanEntity();
        planEntity.setOrderId(orderEntity.getId());
        planEntity.setId(1L);
        planEntity.setName("Plan1");
        planEntity.setType(0);
        planEntity.setTestNum(8);

        lenient().when(productContext.getProductService(orderEntity.getSubProduct())).thenReturn(productService);
        lenient().when(productContext.deviceComparator(orderEntity.getId())).thenReturn(Comparator.comparing(DeviceModel::getScore)
                .reversed()
                .thenComparing(DeviceModel::getPcNo));
        lenient().when(productContext.handleTestAllPlan(any(OrderPlanEntity.class),anyList())).thenReturn(List.of());

    }

    @Test
    @DisplayName("无匹配的设备")
    void fetchPlanRunnableDevices_given_ge_with_no_matchingDevices_shouldReturn_emptyDevices() {

        // given
        planEntity.setAttrs("Mars;高温");
        List<DeviceModel> mockDevices = new ArrayList<>() ;
        // mock
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity,true,mockDevices)).thenReturn(List.of());
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity,false,mockDevices)).thenReturn(List.of());

        // when
        List<DeviceModel> deviceModels = queueService.fetchPlanRunnableDevices(orderEntity,planEntity, List.of(),List.of());

        // then
        assertThat(deviceModels)
                .isEmpty();

    }

    @Test
    @DisplayName("无足够匹配的设备")
    void fetchPlanRunnableDevices_given_ge_with_noEnoughMatchingDevices_shouldReturn_emptyDevices(){

        // given
        planEntity.setAttrs("Mars;高温");
        DeviceModel deviceModel = new DeviceModel();
        deviceModel.setProduct("GE");
        deviceModel.setPcNo("GE5_1_1");
        deviceModel.setIp(String.format("%d.%d.%d.%d", random.nextInt(255), random.nextInt(255), random.nextInt(255), random.nextInt(255)));
        deviceModel.setPosition("GE5_1_1");
        deviceModel.setStatus("在线");
        deviceModel.setAttrModelList(List.of(
                new AttrModel("Mars",4),
                new AttrModel("高温",4)
        ));

        List<DeviceModel> mockDevices = List.of(
                deviceModel
        );

        // mock
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity,true,mockDevices)).thenReturn(List.of());
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity,false,mockDevices)).thenReturn(mockDevices);

        // when
        List<DeviceModel> deviceModels = queueService.fetchPlanRunnableDevices(orderEntity,planEntity, List.of(), mockDevices);

        // then
        assertThat(deviceModels)
                .isEmpty();
    }

    @Test
    @DisplayName("优先确保同机柜的机器")
    void fetchPlanRunnableDevice_given_ge_with_crossGroup_shouldReturn_sameGroupDevices(){

        // given
        planEntity.setAttrs("Mars");
        planEntity.setTestNum(60);

        // device list
        List<DeviceModel> mockDevices = new ArrayList<>();
        // 第一和第二机柜，没有足够 的机器
        for (int i = 0; i < 10; i++) {
            DeviceModel deviceModel = generateDeviceModel("4_1",i, List.of(
                    new AttrModel("Mars",4)
            ));
            mockDevices.add(deviceModel);
            deviceModel = generateDeviceModel("4_2",i, List.of(
                    new AttrModel("Mars",4)
            ));
            mockDevices.add(deviceModel);
        }
        // 第三机柜拥有足够的机器
        for (int i = 0; i < 30; i++) {
            DeviceModel deviceModel = generateDeviceModel("5_1",i, List.of(
                    new AttrModel("Mars",4)
            ));
            mockDevices.add(deviceModel);
        }


        // mock
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity,true, mockDevices)).thenReturn(List.of());
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity,false, mockDevices)).thenReturn(mockDevices);
        // when
        List<DeviceModel> deviceModels = queueService.fetchPlanRunnableDevices(orderEntity,planEntity, List.of(), mockDevices);

        // then
        assertThat(deviceModels)
                .isNotEmpty()
                .allMatch(deviceModel -> deviceModel.getPcNo().startsWith("GE5_1")) ;

    }


    @Test
    @DisplayName("无法同机柜的情况下，尽可能少跨机柜（2个不连续的机柜)")
    void fetchPlanRunnableDevice_given_ge_with_crossGroup_shouldReturn_crossGroupDevices() {

        // given
        planEntity.setAttrs("Mars");
        planEntity.setTestNum(60);

        // device list
        List<DeviceModel> mockDevices = new ArrayList<>();
        // 所有的机柜单独都没有足够的机器
        for (int i = 1; i < 6; i++) {
            int count = 4 + i;
            for (int j = 0; j < count; j++) {
                DeviceModel deviceModel = generateDeviceModel("4_" + i, j, List.of(
                        new AttrModel("Mars", 4)
                ));
                mockDevices.add(deviceModel);
            }
            log.info("机柜4_{}的机器数量为{}", i, count);
        }

        // mock
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity, true, mockDevices)).thenReturn(List.of());
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity, false, mockDevices)).thenReturn(mockDevices);
        // when
        List<DeviceModel> deviceModels = queueService.fetchPlanRunnableDevices(orderEntity, planEntity, List.of(), mockDevices);

        // then
        // 应该分配到机柜4_5 中的所有的设备以及机柜4_4中的前两个设备
        List<DeviceModel> devices_4_5 = deviceModels.stream().filter(deviceModel -> deviceModel.getPcNo().startsWith("GE4_5")).collect(Collectors.toList());

        assertThat(new HashSet<>(deviceModels))
                .hasSize(deviceModels.size());

        assertThat(deviceModels)
                .isNotEmpty()
                .containsAll(devices_4_5)
                .noneMatch(deviceModel -> deviceModel.getPcNo().startsWith("GE4_1"))
                .noneMatch(deviceModel -> deviceModel.getPcNo().startsWith("GE4_3"))
                .noneMatch(deviceModel -> deviceModel.getPcNo().startsWith("GE4_4"))
                .allMatch(deviceModel -> deviceModel.getPcNo().startsWith("GE4_5") || deviceModel.getPcNo().startsWith("GE4_2"));
    }

    @Test
    @DisplayName("无法同机柜的情况下，尽可能少跨机柜（n个机柜)")
    void fetchPlanRunnableDevice_given_ge_with_crossGroup_shouldReturn_crossGroupDevices_n() {

        // given
        planEntity.setAttrs("Mars");
        planEntity.setTestNum(108);

        // device list
        List<DeviceModel> mockDevices = new ArrayList<>();

        // 最多的设备数量为： 单个机柜中有 10 台机器，总计可以测试 10*4 = 40 个样片
        for (int j = 0; j < 10; j++) {
            DeviceModel deviceModel = generateDeviceModel("5_1" , j, List.of(
                    new AttrModel("Mars", 4)
            ));
            mockDevices.add(deviceModel);
        }
        // 其他的4个机柜中，剩余的机器分别为 7，6，5，4 台
        for (int i = 1; i < 5; i++) {
            int count = 3 + i;
            for (int j = 0; j < count; j++) {
                DeviceModel deviceModel = generateDeviceModel("4_" + i, j, List.of(
                        new AttrModel("Mars", 4)
                ));
                mockDevices.add(deviceModel);
            }
            log.info("机柜4_{}的机器数量为{}", i, count);
        }

        // mock
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity, true, mockDevices)).thenReturn(List.of());
        when(deviceService.findSupportDevicesByPlan(orderEntity, planEntity, false, mockDevices)).thenReturn(mockDevices);
        // when
        List<DeviceModel> deviceModels = queueService.fetchPlanRunnableDevices(orderEntity, planEntity, List.of(), mockDevices);

        // then
        // 应该分配到机柜4_5 中的所有的设备以及机柜4_4中的前两个设备
        List<DeviceModel> devices_4_5 = deviceModels.stream().filter(deviceModel -> deviceModel.getPcNo().startsWith("GE4_5")).collect(Collectors.toList());

        assertThat(new HashSet<>(deviceModels))
                .hasSize(deviceModels.size());

        assertThat(deviceModels)
                .isNotEmpty()
                .containsAll(devices_4_5)
                .noneMatch(deviceModel -> deviceModel.getPcNo().startsWith("GE4_2"))
                .contains(mockDevices.stream().filter(deviceModel -> deviceModel.getPcNo().startsWith("GE4_1")).findFirst().orElse(null))
                .contains(mockDevices.stream().filter(deviceModel -> deviceModel.getPcNo().startsWith("GE4_3")).findFirst().orElse(null))
                .contains(mockDevices.stream().filter(deviceModel -> deviceModel.getPcNo().startsWith("GE4_4")).findFirst().orElse(null))
                .contains(mockDevices.stream().filter(deviceModel -> deviceModel.getPcNo().startsWith("GE5_1")).findFirst().orElse(null));
    }


    @Test
    @DisplayName("不带高温属性的Plan 不分配高温属性的电脑.")
    void fetchPlanRunnableDevice_given_ge_without_highTemp_shouldReturn_noHighTempDevices() {

        PlanDeviceRepository planDeviceRepository = Mockito.mock(PlanDeviceRepository.class);
        RMSDeviceService rmsDeviceService = Mockito.mock(RMSDeviceService.class);

        queueService = new QueueService(
                null,
                null,
                null,
                deviceService2,
                null,
                productContext,
                null,
                null,
                null
        );
        // given
        planEntity.setAttrs("Mars");
        planEntity.setTestNum(40);


        // device list
        List<DeviceModel> mockDevices = new ArrayList<>();

        DeviceModel deviceModel = generateDeviceModel("4_1" , 0, List.of(
                new AttrModel("Mars", 4),
                new AttrModel("高温", 2)
        ));
        deviceModel.setScore(100);
        mockDevices.add(deviceModel);

        for (int j = 1; j < 11; j++) {
            deviceModel = generateDeviceModel("4_1" , j, List.of(
                    new AttrModel("Mars", 4)
            ));
            deviceModel.setScore(80);
            mockDevices.add(deviceModel);
        }

        // mock
        when(rmsDeviceService.getAvailableDevices(orderEntity.getSubProduct())).thenReturn(mockDevices);
        given(planDeviceRepository.countByOrderIdAndStatusIn(orderEntity.getId(), Arrays.asList(PlanDeviceEntity.Status.FINISHED_SUCCESS,PlanDeviceEntity.Status.FINISHED_FAILED))).willReturn(0);

        // when

        List<DeviceModel> deviceModels_1 = deviceService2.findSupportDevicesByPlan(orderEntity, planEntity, false);
        List<DeviceModel> deviceModels = queueService.fetchPlanRunnableDevices(orderEntity, planEntity, List.of(), mockDevices);

        // then
        assertThat(new HashSet<>(deviceModels))
                .hasSize(deviceModels.size());
        // 不包含高温属性的设备
        assertThat(deviceModels)
                .isNotEmpty()
                .noneMatch( d -> d.getAttrModelList().stream().anyMatch(attrModel -> attrModel.getAttrName().equals("高温")));
        assertThat(deviceModels_1)
                .isNotEmpty()
                .noneMatch( d -> d.getAttrModelList().stream().anyMatch(attrModel -> attrModel.getAttrName().equals("高温")));

    }


    @Test
    @DisplayName("高温属性的Plan 需要分配高温属性的电脑.")
    void fetchPlanRunnableDevice_given_ge_with_highTemp_shouldReturn_highTempDevices() {

        PlanDeviceRepository planDeviceRepository = Mockito.mock(PlanDeviceRepository.class);
        RMSDeviceService rmsDeviceService = Mockito.mock(RMSDeviceService.class);

        queueService = new QueueService(
                null,
                null,
                null,
                deviceService2,
                null,
                productContext,
                null,
                null,
                null
        );
        // given
        String fixedAttr = "高温";
        planEntity.setAttrs(fixedAttr);
        planEntity.setTestNum(8);


        // device list
        List<DeviceModel> mockDevices = new ArrayList<>();

        for (int j = 1; j < 11; j++) {
            DeviceModel deviceModel = generateDeviceModel("4_1" , j, List.of(
                    new AttrModel("Mars", 4),
                    new AttrModel("高温", 4),
                    new AttrModel("低温", 4)
            ));
            deviceModel.setScore(100);
            mockDevices.add(deviceModel);
        }

        // mock
        when(rmsDeviceService.getAvailableDevices(orderEntity.getSubProduct())).thenReturn(mockDevices);
        given(planDeviceRepository.countByOrderIdAndStatusIn(orderEntity.getId(), Arrays.asList(PlanDeviceEntity.Status.FINISHED_SUCCESS,PlanDeviceEntity.Status.FINISHED_FAILED))).willReturn(0);

        // when
        List<DeviceModel> deviceModels_1 = deviceService2.findSupportDevicesByPlan(orderEntity, planEntity, false);
        List<DeviceModel> deviceModels = queueService.fetchPlanRunnableDevices(orderEntity, planEntity, List.of(), mockDevices);

        // then
        assertThat(new HashSet<>(deviceModels))
                .hasSize(deviceModels.size());
        // 不包含高温属性的设备
        assertThat(deviceModels)
                .isNotEmpty()
                .allMatch( d -> d.getAttrModelList().stream().anyMatch(attrModel -> attrModel.getAttrName().equals(fixedAttr)));
        assertThat(deviceModels_1)
                .isNotEmpty()
                .allMatch( d -> d.getAttrModelList().stream().anyMatch(attrModel -> attrModel.getAttrName().equals(fixedAttr)));

    }


    @Test
    @DisplayName("USB产品， 非第一次测试all的plan，需要与第一次测试的All的Plan 使用的电脑保持一致")
    void fetchPlanRunnableDevice_given_usb_with_firstAllPlan_shouldReturn_firstAllPlanDevices() {

        PlanDeviceRepository planDeviceRepository = Mockito.mock(PlanDeviceRepository.class);
        RMSDeviceService rmsDeviceService = Mockito.mock(RMSDeviceService.class);


        queueService = new QueueService(
                null,
                null,
                null,
                deviceService2,
                null,
                productContext,
                null,
                null,
                null
        );
        // given
        planEntity.setAttrs("Mars");
        planEntity.setTestNum(48);
        planEntity.setTestAll(true);

        orderEntity.setSubProduct("U2");

        // mock device list
        List<DeviceModel> mockDevices = new ArrayList<>();
        for (int i = 0; i < 16; i++) {

            DeviceModel deviceModel = generateDeviceModel("4_1" , i, List.of(
                    new AttrModel("Mars", 16)
            ));
            deviceModel.setScore(80);
            mockDevices.add(deviceModel);
        }

        List<DeviceModel> firstAllPlanDevices = mockDevices.subList(0,3) ;

        List<PlanDeviceEntity> deviceEntityList = firstAllPlanDevices.stream().map(d -> DeviceModel.toEntity(d, 234L, orderEntity.getId())).collect(Collectors.toList());


        // mock
        when(productContext.handleTestAllPlan(any(OrderPlanEntity.class),anyList())).thenReturn(firstAllPlanDevices);
        given(planDeviceRepository.countByOrderIdAndStatusIn(orderEntity.getId(),
                Arrays.asList(PlanDeviceEntity.Status.FINISHED_SUCCESS,PlanDeviceEntity.Status.FINISHED_FAILED)))
                .willReturn(3);
        given(planDeviceRepository.findAllByOrderIdAndStatusIn(orderEntity.getId(), Arrays.asList(PlanDeviceEntity.Status.FINISHED_SUCCESS, PlanDeviceEntity.Status.FINISHED_FAILED)))
                .willReturn(deviceEntityList);


        List<DeviceModel> deviceModels = queueService.fetchPlanRunnableDevices(orderEntity, planEntity, List.of(), mockDevices);


        // 该设备与第一次测试的All的Plan 使用的电脑保持一致
        assertThat(deviceModels)
                .isNotEmpty()
                .containsAll(firstAllPlanDevices);

    }
}