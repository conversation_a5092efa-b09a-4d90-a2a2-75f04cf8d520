{"@timestamp":"2025-07-23T16:00:03.58+08:00","@version":"1","message":"Flash:CY-1583-8T2F-A_8GB下的Plan17","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0005afaefd1a6ea4","spanId":"0005afaefd1a6ea4","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:00:03.679+08:00","@version":"1","message":"Flash:CY-1583-8T2F-A_8GB下的Plan17使用设备:[************] -[B4-2E-99-5A-E4-8F] 开始测试!","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0005afaefd1a6ea4","spanId":"0005afaefd1a6ea4","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:00:03.686+08:00","@version":"1","message":"startTestPlan with PlanTestParams(planName=Plan17, no=YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB, group=0, bAutoLoc=false, ipList=[************], macList=[B4-2E-99-5A-E4-8F], product=U2, planPath=\\\\************\\软件工具\\03内部工具版本\\eSee\\GE\\U2\\Mars plan\\TestPlanV31.**********-svn1747.plan, marsPath=\\\\************\\软件工具\\03内部工具版本\\eSee\\GE\\U2\\Mars版本\\Mars_V40.00.2.222_3443.zip, mpPath=\\\\************\\软件工具\\03内部工具版本\\eSee\\GE\\U2\\MPTool\\(I)MPTools V1.8.9.3.110_250711Alpha(1583)-92%.rar, username=Milly.Huang)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0005afaefd1a6ea4","spanId":"0005afaefd1a6ea4","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:00:03.808+08:00","@version":"1","message":"Product:U2 testPerson:Milly.Huang order no:YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB plan name:Plan17  macList:[B4-2E-99-5A-E4-8F]","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0005afaefd1a6ea4","spanId":"0005afaefd1a6ea4","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:00:03.812+08:00","@version":"1","message":"[5b1a910a] HTTP POST http://ereport.yeestor.com/wo/plan/test","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0005afaefd1a6ea4","spanId":"0005afaefd1a6ea4","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:00:04.03+08:00","@version":"1","message":"startTestPlan with YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB's Plan17 got data:HandleResp(code=0, data=PlanTestRespModel(successLst=[PlanTestRespModel.DeviceTestResult(ip=************, no=GE5_1_14, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0005afaefd1a6ea4","spanId":"0005afaefd1a6ea4","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:00:04.046+08:00","@version":"1","message":"Plan17 启动测试成功, resp: PlanTestRespModel(successLst=[PlanTestRespModel.DeviceTestResult(ip=************, no=GE5_1_14, reason=)], failLst=[])","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0005afaefd1a6ea4","spanId":"0005afaefd1a6ea4","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:05:21.138+08:00","@version":"1","message":"execute:AutoAssignDevice_IND_IND_SATA  map key:[subProduct, product] value:org.quartz.utils.DirtyFlagMap$DirtyFlagCollection@2761ff2e ","logger_name":"com.yeestor.work_order.service.job.AutoAssignDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be251268ea90f288","spanId":"be251268ea90f288","context":"QueueService","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:05:21.142+08:00","@version":"1","message":"AutoAssignDeviceJob execute product:IND subProduct:IND_SATA orderCount:3","logger_name":"com.yeestor.work_order.service.job.AutoAssignDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be251268ea90f288","spanId":"be251268ea90f288","context":"QueueService","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:05:21.146+08:00","@version":"1","message":"[IND_SATA] start check order's plan . find 3 orders","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be251268ea90f288","spanId":"be251268ea90f288","context":"QueueService","no":"6369","traceType":"启动测试","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-24T14:09:02.601+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c24f8b6dbddce9af","spanId":"c24f8b6dbddce9af","context":"QueueService","no":"6369","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:09:02.611+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6606, orderId=6369, flash=CY-1583-8T2F-A_8GB, orderFlashNo=YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB, num=80, leftNum=64)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c24f8b6dbddce9af","spanId":"c24f8b6dbddce9af","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.632+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c24f8b6dbddce9af","spanId":"c24f8b6dbddce9af","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c24f8b6dbddce9af","spanId":"c24f8b6dbddce9af","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.632+08:00","@version":"1","message":"[6369] - [CY-1583-8T2F-A_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c24f8b6dbddce9af","spanId":"c24f8b6dbddce9af","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.643+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c24f8b6dbddce9af","spanId":"c24f8b6dbddce9af","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.601+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc0ffd12a4678ed7","spanId":"bc0ffd12a4678ed7","context":"QueueService","no":"6369","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:12:02.605+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6606, orderId=6369, flash=CY-1583-8T2F-A_8GB, orderFlashNo=YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB, num=80, leftNum=64)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc0ffd12a4678ed7","spanId":"bc0ffd12a4678ed7","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.625+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc0ffd12a4678ed7","spanId":"bc0ffd12a4678ed7","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.625+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc0ffd12a4678ed7","spanId":"bc0ffd12a4678ed7","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.626+08:00","@version":"1","message":"[6369] - [CY-1583-8T2F-A_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc0ffd12a4678ed7","spanId":"bc0ffd12a4678ed7","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.626+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc0ffd12a4678ed7","spanId":"bc0ffd12a4678ed7","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.598+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06a992fdf8337793","spanId":"06a992fdf8337793","context":"QueueService","no":"6369","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.599+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6606, orderId=6369, flash=CY-1583-8T2F-A_8GB, orderFlashNo=YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB, num=80, leftNum=64)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06a992fdf8337793","spanId":"06a992fdf8337793","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.62+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06a992fdf8337793","spanId":"06a992fdf8337793","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.62+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06a992fdf8337793","spanId":"06a992fdf8337793","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.621+08:00","@version":"1","message":"[6369] - [CY-1583-8T2F-A_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06a992fdf8337793","spanId":"06a992fdf8337793","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.621+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06a992fdf8337793","spanId":"06a992fdf8337793","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:02.893+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"944cf1c27a20c1a9","spanId":"944cf1c27a20c1a9","context":"QueueService","no":"6369","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:00:03.003+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6606, orderId=6369, flash=CY-1583-8T2F-A_8GB, orderFlashNo=YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB, num=80, leftNum=64)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"944cf1c27a20c1a9","spanId":"944cf1c27a20c1a9","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.167+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"944cf1c27a20c1a9","spanId":"944cf1c27a20c1a9","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.219+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"944cf1c27a20c1a9","spanId":"944cf1c27a20c1a9","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.22+08:00","@version":"1","message":"[6369] - [CY-1583-8T2F-A_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"944cf1c27a20c1a9","spanId":"944cf1c27a20c1a9","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.221+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"944cf1c27a20c1a9","spanId":"944cf1c27a20c1a9","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.597+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0350cf5860f181","spanId":"0b0350cf5860f181","context":"QueueService","no":"6369","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:03:02.598+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6606, orderId=6369, flash=CY-1583-8T2F-A_8GB, orderFlashNo=YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB, num=80, leftNum=64)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0350cf5860f181","spanId":"0b0350cf5860f181","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.616+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0350cf5860f181","spanId":"0b0350cf5860f181","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.616+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0350cf5860f181","spanId":"0b0350cf5860f181","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.616+08:00","@version":"1","message":"[6369] - [CY-1583-8T2F-A_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0350cf5860f181","spanId":"0b0350cf5860f181","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.617+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0b0350cf5860f181","spanId":"0b0350cf5860f181","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.599+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0594a0273d65cee","spanId":"d0594a0273d65cee","context":"QueueService","no":"6369","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.601+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6606, orderId=6369, flash=CY-1583-8T2F-A_8GB, orderFlashNo=YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB, num=80, leftNum=64)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0594a0273d65cee","spanId":"d0594a0273d65cee","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.62+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0594a0273d65cee","spanId":"d0594a0273d65cee","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.62+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0594a0273d65cee","spanId":"d0594a0273d65cee","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.621+08:00","@version":"1","message":"[6369] - [CY-1583-8T2F-A_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0594a0273d65cee","spanId":"d0594a0273d65cee","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.63+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0594a0273d65cee","spanId":"d0594a0273d65cee","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.598+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eddc8823340ec292","spanId":"eddc8823340ec292","context":"QueueService","no":"6369","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:09:02.599+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6606, orderId=6369, flash=CY-1583-8T2F-A_8GB, orderFlashNo=YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB, num=80, leftNum=64)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eddc8823340ec292","spanId":"eddc8823340ec292","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.621+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eddc8823340ec292","spanId":"eddc8823340ec292","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.622+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eddc8823340ec292","spanId":"eddc8823340ec292","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.622+08:00","@version":"1","message":"[6369] - [CY-1583-8T2F-A_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eddc8823340ec292","spanId":"eddc8823340ec292","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.622+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"eddc8823340ec292","spanId":"eddc8823340ec292","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.602+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72b0af089cf4123f","spanId":"72b0af089cf4123f","context":"QueueService","no":"6369","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:24:02.603+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6606, orderId=6369, flash=CY-1583-8T2F-A_8GB, orderFlashNo=YS1583##MP#########3110##8T2F-other-4250022_Alpha_CY-1583-8T2F-A_8GB, num=80, leftNum=64)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72b0af089cf4123f","spanId":"72b0af089cf4123f","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.624+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72b0af089cf4123f","spanId":"72b0af089cf4123f","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113786, orderId=6369, name=Plan4, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72b0af089cf4123f","spanId":"72b0af089cf4123f","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.624+08:00","@version":"1","message":"[6369] - [CY-1583-8T2F-A_8GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72b0af089cf4123f","spanId":"72b0af089cf4123f","context":"QueueService","no":"6369","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.624+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72b0af089cf4123f","spanId":"72b0af089cf4123f","context":"QueueService","no":"6369","traceType":"分配设备"}
