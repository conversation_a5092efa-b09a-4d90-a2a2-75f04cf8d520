package com.yeestor.work_order.entity.history;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.ToString;

import javax.persistence.*;

@Setter
@Getter
@ToString
@Entity(name = "PlanDeviceHistory")
@Table(name = "wo_plan_device_history")
public class PlanDeviceHistoryEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    /**
     * 标题，用来简单描述这个历史主要记录了什么内容.
     */
    private String title ;

    /**
     * 工单id
     */
    @Column(name = "order_id")
    private long orderId ;
    /**
     * plan id
     */
    @Column(name = "plan_id")
    private long planId ;
    /**
     * 当前历史的版本
     */
    @Column(name = "version")
    private int version ;

    /**
     * 详细信息，使用Json 来存储。
     */
    @Lob
    @Column(name = "detail_info")
    private String detailInfo ;

    @Column(name = "created_at")
    private Long createdAt ;

    /**
     * 将设备转换成json 存储到历史中
     * @param deviceEntity 设备实体
     * @return 历史实体类
     */
    @SneakyThrows
    public static PlanDeviceHistoryEntity device2History(PlanDeviceEntity deviceEntity){
        ObjectMapper mapper = new ObjectMapper() ;
        PlanDeviceHistoryEntity historyEntity = new PlanDeviceHistoryEntity();
        historyEntity.orderId = deviceEntity.getOrderId() ;
        historyEntity.planId = deviceEntity.getPlanId() ;
        historyEntity.version = deviceEntity.getVersion() ;
        historyEntity.detailInfo = mapper.writeValueAsString(deviceEntity) ;
        historyEntity.createdAt = System.currentTimeMillis();
        return historyEntity ;
    }

    /**
     * 将历史转换成设备
     * @return 设备实体
     */
    @SneakyThrows
    public PlanDeviceEntity history2Device(){
        ObjectMapper mapper = new ObjectMapper() ;
        return mapper.readValue(detailInfo,PlanDeviceEntity.class) ;
    }

}
