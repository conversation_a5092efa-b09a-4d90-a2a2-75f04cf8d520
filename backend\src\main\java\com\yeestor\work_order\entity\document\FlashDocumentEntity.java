package com.yeestor.work_order.entity.document;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;

/**
 * 用于存储flash批次下的文档的实体类
 */
@Getter
@Setter
@ToString
@Entity(name = "FlashDocument")
@Table(name = "wo_order_flash_doc")
public class FlashDocumentEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    /**
     * 工单id
     */
    @Column(name = "order_id")
    private long orderId ;

    /**
     * flash批次的名称、
     */
    @Column(name = "flash")
    private String flash ;

    /**
     * flash实体类的 id
     */
    @Column(name = "flash_id")
    private long flashId ;

    /**
     * 文件的类型，是错误日志报表还是报告
     */
    @Column(name = "type" )
    private String type ;


    /**
     * 文档id
     */
    @Column(name = "document_id")
    private long documentId ;


    /**
     * 文档来源， 表示是从RMS还是从本地上传的。
     * 如果是从RMS上传的，则为RMS，如果是从本地上传的，则为LOCAL
     */
    @Column(name = "source")
    private String source ;

    @Version
    @ColumnDefault("0") // 默认值为0
    private Integer version;

}
