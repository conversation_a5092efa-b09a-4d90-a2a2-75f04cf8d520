package com.yeestor.work_order.model.http.req;

import com.yeestor.work_order.model.http.req.order.OrderEnvConfirmReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "DeviceReleaseParams", description = "释放设备的参数")
public class DeviceReleaseParams {


    @ApiModelProperty("工单id")
    @Min(value = 1, message = "无效的工单id")
    private long orderId ;

    @ApiModelProperty("Plan 下的设备列表")
    @NotEmpty(message = "Plan设备列表为空")
    private List<OrderEnvConfirmReq.PlanDeviceInfo> planDeviceList ;

    @ApiModelProperty("释放原因描述")
    private String reason ;

}
