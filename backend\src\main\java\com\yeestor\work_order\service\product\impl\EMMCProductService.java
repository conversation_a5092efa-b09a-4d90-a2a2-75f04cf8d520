package com.yeestor.work_order.service.product.impl;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.service.product.ProductContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
@RequiredArgsConstructor
public class EMMCProductService extends DefaultProductService{

    private final ProductContext productContext;

    @PostConstruct
    private void init(){
        productContext.registerProductService("UFS",this);
        productContext.registerProductService("eMMC",this);
    }
    @Override
    public List<OrderPlanEntity> fetchNeedTestPlanList(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {
//        return planService.fetchNeedTestPlanList(orderEntity.getSubProduct(), orderEntity.getId(), flashEntity.getFlash(), (allPlanEntityList, leftFlashNum) ->
//                allPlanEntityList.stream()
//                        .filter(planEntity -> planEntity.getStatus() == OrderPlanEntity.Status.QUEUE)
//                        .collect(Collectors.toList()));

        long orderId = orderEntity.getId();
        String flash = flashEntity.getFlash();
        String subProduct = orderEntity.getSubProduct();

        return planService.fetchNeedTestPlanList(subProduct, orderId, flash);
    }
}
