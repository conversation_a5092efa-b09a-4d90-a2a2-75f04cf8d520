package com.yeestor.work_order.utils;

import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

@Component
@RequiredArgsConstructor
public class PathUtils {

    private final Environment environment;

    private static final String developUrl = "http://*************:8512";

    private static final String productUrl = "http://************:8512";

    private static final String alphaUrl = "http://************:8512";
    private final List<String> supportEnvList = Arrays.asList("prod", "alpha");
    private static final String CORP_ID = "ding31d78c632c4953fa35c2f4657eb6378f";
    private static final String APP_ID = "1368250694";

    /**
     * 获取前端系统根路径方法
     *
     * @return 前端页面根路径
     */
    private String getHostURL() {
        if (Arrays.asList(environment.getActiveProfiles()).contains("prod")) {
            return productUrl;
        } else if (Arrays.asList(environment.getActiveProfiles()).contains("alpha")) {
            return alphaUrl;
        }
        return developUrl;
    }

    /**
     * 构建访问 eSee 工单 url
     *
     * @param product    产品线
     * @param subProduct 产品
     * @param orderId    工单id
     * @param flashName  flash名称
     * @return url地址
     */
    public String getFlashPageUrl(String product, String subProduct, Long orderId, String flashName) {
        return getHostURL() + "/work_order/" + product + "/" + subProduct + "/" + orderId + ((flashName == null) ? "" : "?flash=" + flashName);
    }

    /**
     * 获取Plan详情页面url地址
     *
     * @param subProduct 产品
     * @param orderId    工单id
     * @param flashName  flash名称
     * @param planEntity plan实体类
     * @return url地址
     */
    public String getPlanPageUrl(String subProduct, Long orderId, String flashName, OrderPlanEntity planEntity) {
        return getHostURL() + "/work_order/" + subProduct + "/" + orderId + "/" + flashName + "/" + planEntity.getId() + "/" + planEntity.getName() + "/" + planEntity.getType() + "/detail";
    }

    @SneakyThrows
    private String encodeURL(String url) {
        return URLEncoder.encode(url, StandardCharsets.UTF_8);
    }

    /**
     * 获取工单plan详情页面钉钉地址
     *
     * @param orderEntity 工单信息
     * @param flash       flash名称
     * @param planEntity  plan信息
     * @return 地址
     */
    public String getPlanPageDingUrl(WorkOrderEntity orderEntity, String flash, OrderPlanEntity planEntity) {
        String url = getPlanPageUrl(orderEntity.getSubProduct(), orderEntity.getId(), flash, planEntity);
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(supportEnvList::contains)) {
            return "dingtalk://dingtalkclient/page/link?url=" + encodeURL(url);
        }
        return buildDingtalkAppUrl(url);
    }

    /**
     * 获取工单Flash详情页面钉钉地址
     *
     * @param orderEntity 工单实体类
     * @param flash       flash名称
     * @return url地址
     */
    public String getFlashDingUrl(WorkOrderEntity orderEntity, String flash) {
        String url = getFlashPageUrl(orderEntity.getProduct(), orderEntity.getSubProduct(), orderEntity.getId(), flash);
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(supportEnvList::contains)) {
            return "dingtalk://dingtalkclient/page/link?url=" + encodeURL(url);
        }
        return buildDingtalkAppUrl(url);
    }

    public String buildDingtalkAppUrl(String uri) {
        if (Arrays.stream(environment.getActiveProfiles()).noneMatch(supportEnvList::contains)) {
            return uri;
        }
        return "dingtalk://dingtalkclient/action/openapp?corpid=" + CORP_ID + "&container_type=work_platform&app_id=0_" + getAppId() + "&redirect_type=jump&redirect_url=" + encodeURL(uri);
    }

    public String getAppId() {
        if (Arrays.stream(environment.getActiveProfiles()).anyMatch("prod"::equalsIgnoreCase)) {
            return APP_ID;
        }
        return "1445820031";
    }
}
