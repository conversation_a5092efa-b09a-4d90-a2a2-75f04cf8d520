package com.yeestor.work_order.model.http.req.config;

import com.yeestor.work_order.entity.config.TimeoutConfigEntity;
import com.yeestor.work_order.model.http.req.Person;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "TimeoutConfigParams", description = "超时配置参数")
public class TimeoutConfigParams {

    @ApiModelProperty(value = "产品线", required = true)
    @NotBlank(message = "产品线不能为空")
    private String product;

    @ApiModelProperty(value = "产品", required = true)
    @NotBlank(message = "产品不能为空")
    private String subProduct;


    @ApiModelProperty(value = "类型", required = true)
    private TimeoutConfigEntity.Type type;

    @ApiModelProperty(value = "阶段", required = true)
    @NotBlank(message = "阶段不能为空")
    private String phase;

    @ApiModelProperty(value = "超时时间, 单位为小时", required = true)
    private int timeout; // -1 为不超时


    @ApiModelProperty(value = "监听人", required = true)
    @NotEmpty(message = "监听人不能为空")
    private List<Person> listeners ;


    public TimeoutConfigEntity convert2Entity(){
        TimeoutConfigEntity entity = new TimeoutConfigEntity();
        entity.setProduct(product);
        entity.setSubProduct(subProduct);
        entity.setType(type);
        entity.setPhase(phase);
        entity.setTimeout(timeout);
        entity.setListeners(listeners);
        return entity;
    }


}
