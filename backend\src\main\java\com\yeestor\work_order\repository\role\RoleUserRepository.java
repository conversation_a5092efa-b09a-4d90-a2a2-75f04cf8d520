package com.yeestor.work_order.repository.role;

import com.yeestor.work_order.entity.role.RoleUserEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface RoleUserRepository extends JpaRepository<RoleUserEntity, Long> {

    List<RoleUserEntity> findAllByUserId(String userId) ;

    void deleteAllByRoleId(long roleId);

    List<RoleUserEntity> findAllByRoleId(long id);

    List<RoleUserEntity> findAllByRoleIdIn(List<Long> roleId);

    boolean existsByUserIdAndRoleIdIn(String userId, List<Long> roleId);

}
