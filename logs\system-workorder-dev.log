2025-07-31 15:06:50.006 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/output/**'] 
2025-07-31 15:06:50.024 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/device/machine/info'] 
2025-07-31 15:06:50.027 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/actuator/**'] 
2025-07-31 15:06:50.031 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/flow/**/flash_info'] 
2025-07-31 15:06:50.031 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/api-docs'] 
2025-07-31 15:06:50.031 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-resources'] 
2025-07-31 15:06:50.032 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/device/status'] 
2025-07-31 15:06:50.032 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/plan/status'] 
2025-07-31 15:06:50.032 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/report/status'] 
2025-07-31 15:06:50.032 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/order/import'] 
2025-07-31 15:06:50.034 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/device/disk/callback'] 
2025-07-31 15:06:50.038 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/flash/model'] 
2025-07-31 15:06:50.038 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/terminal/plan/status'] 
2025-07-31 15:06:50.038 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/terminal/plan/case/start'] 
2025-07-31 15:06:50.043 DEBUG [system-workorder-dev,,] [unknown]  15424 --- edFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for Ant [pattern='/**'] 
2025-07-31 15:06:50.108  INFO [system-workorder-dev,,] [unknown]  15424 --- o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4df30827, org.springframework.security.web.context.SecurityContextPersistenceFilter@657b6510, org.springframework.security.web.header.HeaderWriterFilter@4afdb7da, org.springframework.security.web.authentication.logout.LogoutFilter@6a57365, org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter@817e0aa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3779b701, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4548f47, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4008caaf, org.springframework.security.web.session.SessionManagementFilter@40c925a1, org.springframework.security.web.access.ExceptionTranslationFilter@6e11dd6c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@e94908c] 
