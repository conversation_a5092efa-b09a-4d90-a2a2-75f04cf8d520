package com.yeestor.work_order.entity.order;

import lombok.Data;

import javax.persistence.*;

/**
 * 工单数据统计邮箱基础信息
 */
@Data
@Entity(name = "OrderStat")
@Table(name = "wo_order_stat")
public class OrderStatInfoEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    /**
     * 产品线
     */
    @Column(name = "product")
    private String product;

    /**
     * 产品
     */
    @Column(name = "sub_product")
    private String subProduct;

    /**
     * 接收邮箱
     */
    @Column(name = "to_email")
    private String toEmail;

    /**
     * 抄送邮箱
     */
    @Column(name = "ccMail", length = 512)
    private String ccMail;

    /**
     * 数据统计到当前第n小时之前
     */
    @Column(name = "hours")
    private int hours;

    /**
     * 邮件标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 邮件主题
     */
    @Column(name = "description")
    private String description;

}
