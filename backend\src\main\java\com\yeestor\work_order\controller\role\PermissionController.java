package com.yeestor.work_order.controller.role;

import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.utils.Enums;
import com.yeestor.work_order.entity.role.RolePermissionEntity;
import com.yeestor.work_order.entity.role.RoleUserEntity;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.model.permission.PermissionGroup;
import com.yeestor.work_order.model.permission.PermissionModel;
import com.yeestor.work_order.model.permission.UserPermissionVO;
import com.yeestor.work_order.repository.role.RolePermissionRepository;
import com.yeestor.work_order.repository.role.RoleUserRepository;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/permission")
@Api(value = "权限相关的接口", tags = "权限管理")
public class PermissionController {

    private final RoleUserRepository roleUserRepository ;
    private final RolePermissionRepository rolePermissionRepository ;
    
    @GetMapping("/list")
    @ApiOperation(value = "获取权限列表", notes = "获取所有的权限列表。")
    public HandleResp<List<PermissionModel>> listPermissions(){
        // 查询所有权限组
        Map<PermissionGroup, List<Permission>> listHashMap = Arrays.stream(Permission.values())
                .filter(p ->
                        Optional.of(p).map(Permission::getGroup).isPresent()
                )
                .collect(Collectors.groupingBy(p -> Optional.of(p).map(Permission::getGroup).orElse(PermissionGroup.WORK_ORDER)));

        // 将 listHashMap 转换成 PermissionController.PermissionModel 的列表 。
        List<PermissionModel> permissionModels = new ArrayList<>();
        listHashMap.forEach((k,v) -> {
            PermissionModel permissionModel = new PermissionModel();
            permissionModel.setGroup(PermissionModel.LocaleLanguage.builder()
                    .name(k.name())
                    .locale(k.getLocale())
                    .build());
            permissionModel.setPermissions(
                    v.stream()
                            .map(p -> PermissionModel.LocaleLanguage
                                    .builder()
                                    .name(p.name())
                                    .locale(p.getLocale())
                                    .build())
                            .collect(Collectors.toList())
            );
            permissionModels.add(permissionModel);

        });

        return HandleResp.ok(permissionModels, "获取权限列表成功！");

    }


    @GetMapping("/current")
    @ApiOperation(value = "获取当前用户的权限", notes = "获取当前用户在工单系统中的权限列表")
    public HandleResp<UserPermissionVO> fetchCurrentUserPermissions(
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        if(userDetail == null){
            return HandleResp.failed("获取当前用户的权限失败！");
        }

        // 获取当前用户对应的角色。
        // 获取用户-角色关系
        List<RoleUserEntity> roleUserEntityList = roleUserRepository.findAllByUserId(userDetail.getUid());
        // 获取角色ID
        List<Long> roleIds = roleUserEntityList.stream().map(RoleUserEntity::getRoleId).collect(Collectors.toList());

        // 获取对应角色的权限列表。
        // 获取角色-权限关系
        List<RolePermissionEntity> permissionEntityList = rolePermissionRepository.findAllByRoleIdIn(roleIds);

        // 遍历 角色的权限。按照 产品 来分组。
        HashMap<String,List<String>> productPermissionMap = new HashMap<>();

        // 与 产品无关的权限列表
        HashSet<String> rolePermissions = new HashSet<>();
        permissionEntityList.forEach(p -> {
            String product = p.getProduct();
            List<String> permissions = Arrays.stream(p.getPermission().split(";")).map(String::trim).collect(Collectors.toList());
            List<String> oldPermissions = productPermissionMap.getOrDefault(product, new ArrayList<>()) ;

            oldPermissions.addAll(permissions);
            // 去重


            oldPermissions = oldPermissions.stream()
                    .distinct()
                    .collect(Collectors.toList());

            final List<String> newPermissions = new ArrayList<>(oldPermissions);
            // 对于角色组的权限列表，不需要添加到 映射中。
            oldPermissions.stream()
                    .filter(s -> Enums.getIfPresent(Permission.class, s)
                            .orElse(Permission.CONFIRM_FLASH).getGroup() == PermissionGroup.ROLE)
                    .forEach(s-> {
                        rolePermissions.add(s);
                        newPermissions.remove(s);
                    });
            productPermissionMap.put(product, newPermissions);
        });

        // 将 productPermissionMap 转换成 UserPermissionVO 的列表 。
        List<UserPermissionVO.ProductPermission> productPermissions = new ArrayList<>();
        productPermissionMap.forEach((k,v) -> productPermissions.add(
                        UserPermissionVO.ProductPermission.builder()
                                .product(k)
                                .permission(v)
                                .build()
                )
        );
        return HandleResp.ok(UserPermissionVO.builder()
                .productPermissions(productPermissions)
                .permissions(new ArrayList<>(rolePermissions))
                .build(), "获取当前用户的权限成功！");



    }



}
