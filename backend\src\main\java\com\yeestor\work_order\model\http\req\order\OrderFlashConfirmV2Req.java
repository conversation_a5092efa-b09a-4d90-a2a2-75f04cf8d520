package com.yeestor.work_order.model.http.req.order;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.yeestor.work_order.entity.OrderDetailEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@JsonAutoDetect
@ApiModel(value="OrderFlashConfirmV2Req",description = "工单确认Flash信息时提交的信息")
public class OrderFlashConfirmV2Req {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "OrderFlashConfirmV2Req.PlanItem", description = "确认Flash信息时,plan列表中的一项")
    public static class PlanItem {
        @ApiModelProperty(value = "name", example = "Plan1", required = true)
        private String name;

        @ApiModelProperty(value = "阶段", required = true)
        private OrderPlanEntity.Phase phase;

        @ApiModelProperty(value = "是否为临时Plan")
        private boolean temporal = false;

        @ApiModelProperty(value = "测试负责人姓名。使用英文名", required = true)
        private String testUserName;

        @ApiModelProperty(value = "测试负责人ID", required = true)
        private String testUserID;

        /**
         * Plan 所属类型
         *  general: 读卡器接入测试
         *  terminal: 终端测试
         */
        @ApiModelProperty(value = "Plan类型", required = true)
        private String type;
    }



    @ApiModelProperty("订单ID")
    @Min(value = 1,message = "工单id不能小于1")
    private long orderId ;

    /**
     * 主管确认时，填入的Flash信息。
     */
    @ApiModelProperty(value = "主管确认时，填入的Flash信息。", required = true)
    @NotEmpty(message = "填入的Flash信息不能为空")
    @Valid
    private List<FlashInfo> flashInfoList ;

    @ApiModelProperty("优先级")
    private int priority ;

    @ApiModelProperty("RMS通知群组")
    private String group ;

    @ApiModelProperty("Mars 路径")
    private String marsPath ;

    @ApiModelProperty("Mars Plan 路径")
    private String planPath ;

    @ApiModelProperty("scrcpy 路径")
    private String scrcpyPath;

    @ApiModelProperty("scrcpy Plan 路径")
    private String scrcpyPlan;

    @ApiModelProperty("mpTool 路径")
    private String mpToolPath ;

    @ApiModelProperty(value = "测试要求")
    private String requirement;

    public void applyData(WorkOrderEntity orderEntity, OrderDetailEntity detailEntity) {

        orderEntity.setRmsGroup(this.getGroup());
        orderEntity.setMarsPath(this.getMarsPath());
        orderEntity.setPlanPath(this.getPlanPath());
        orderEntity.setMpToolPath(this.getMpToolPath());
        orderEntity.setScrcpyPath(this.getScrcpyPath());
        orderEntity.setScrcpyPlan(this.getScrcpyPlan());

        // 如果 confirmReq 的 priority 和 orderEntity的 priority 不一样的话，就更新优先级、
        if (this.getPriority() != orderEntity.getPriority()) {
            orderEntity.setPriority(this.getPriority());
        }

        // 更新detail 中信息
        detailEntity.setRequirement(this.getRequirement());

    }

}
