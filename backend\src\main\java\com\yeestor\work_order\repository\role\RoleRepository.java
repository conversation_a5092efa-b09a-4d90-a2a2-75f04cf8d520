package com.yeestor.work_order.repository.role;

import com.yeestor.work_order.entity.role.RoleEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface RoleRepository extends JpaRepository<RoleEntity, Long> {
//    @Query(value = "SELECT * FROM wo_role WHERE if(?1 != '',name=?1,1=1) AND IF(?2 !='', description=?2,1=1) ",nativeQuery = true)
//    List<RoleEntity> selRoleList(String name, String description);


    List<RoleEntity> findAllByNameContainingAndDescContaining(String name, String description);

    Page<RoleEntity> findAllByNameContainingAndDescContaining(String name, String description, Pageable pageable);

}

