{"@timestamp":"2025-07-23T10:42:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:42:02.634+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=69)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.634+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.653+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.689+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.717+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.732+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.747+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.763+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.778+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.793+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.807+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.822+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113596, orderId=6374, name=Plan65, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.837+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113597, orderId=6374, name=Plan70, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.838+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113596, orderId=6374, name=Plan65, status=QUEUE, priority=50), OrderPlanEntity(id=113597, orderId=6374, name=Plan70, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.838+08:00","@version":"1","message":"执行预分配前共有69颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.838+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.839+08:00","@version":"1","message":" getValidPlanList sumList: [2, 4] leftFlashNum:69 index:2 plans:[OrderPlanEntity(id=113596, orderId=6374, name=Plan65, status=QUEUE, priority=50), OrderPlanEntity(id=113597, orderId=6374, name=Plan70, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:42:02.84+08:00","@version":"1","message":"[预分配]分配给测试人[clara.lou]的Plan有 [Plan65, Plan70], 一共使用了 4 颗样片, 还剩下样片 65 颗","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.84+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.84+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.84+08:00","@version":"1","message":"assignList: [OrderPlanEntity(id=113596, orderId=6374, name=Plan65, status=QUEUE, priority=50), OrderPlanEntity(id=113597, orderId=6374, name=Plan70, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.84+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 2 plans: [OrderPlanEntity(id=113596, orderId=6374, name=Plan65, status=QUEUE, priority=50), OrderPlanEntity(id=113597, orderId=6374, name=Plan70, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.841+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113596, orderId=6374, name=Plan65, status=QUEUE, priority=50), OrderPlanEntity(id=113597, orderId=6374, name=Plan70, status=QUEUE, priority=50)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.917+08:00","@version":"1","message":"[2b7ffd83] HTTP GET http://ereport.yeestor.com/wo/device/list?p=PCIe","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.044+08:00","@version":"1","message":"getAllDeviceList with PCIe got data DeviceListResp(code=0, data=[{ SS-PC-MB0521,172.18.21.22,18-C0-4D-A6-72-5E,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0784,172.18.30.120,04-7C-16-00-2A-55,[{性能=1}] }, { SS-PC-MB0534,*************,18-C0-4D-A5-7F-8B,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0734,172.18.30.26,D8-5E-D3-73-DC-C6,[{系统掉电=1}] }, { SS-PC-MB0753,172.18.30.2,50-EB-F6-9C-B0-19,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0375,172.18.20.141,00-E0-70-C4-2D-27,[{MARS单盘=1}] }, { SS-PC-MB0478,172.18.20.146,A8-5E-45-9E-D5-73,[{MARS单盘=1}] }, { SS-PC-MB0545,172.18.20.147,F0-2F-74-2E-69-26,[{MARS单盘=1}] }, { SS-PC-MB0540,*************,F0-2F-74-2E-71-E0,[{MARS单盘=1}] }, { SS-PC-MB0536,172.18.20.155,18-C0-4D-A6-71-D5,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0544,172.18.21.113,F0-2F-74-2E-66-5B,[{MARS单盘=1}] }, { SS-PC-MB0535,172.18.20.226,18-C0-4D-A6-72-D8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0741,172.18.30.101,D8-BB-C1-D1-E2-DE,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0769,172.18.30.105,18-C0-4D-A9-A4-6D,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0532,172.18.20.44,18-C0-4D-A6-71-A3,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0539,172.18.20.48,18-C0-4D-A6-72-11,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0554,172.18.20.50,F0-2F-74-2E-74-33,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0378,172.18.20.149,A8-A1-59-27-3E-92,[{系统掉电=1}] }, { SS-PC-MB0513,172.18.40.174,A4-0C-66-02-AE-83,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0471,172.18.41.123,B4-2E-99-FD-D5-21,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0469,172.18.41.181,B4-2E-99-FD-CE-0E,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0470,172.18.40.22,B4-2E-99-FD-D5-30,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0384,172.18.21.187,A8-A1-59-26-0A-92,[{系统掉电=1}] }, { SS-PC-MB0476,172.18.20.28,A8-5E-45-9E-D9-10,[{MARS单盘=1}] }, { SS-PC-MB0406,172.18.21.160,F0-2F-74-8B-94-DC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0514,172.18.41.40,A4-0C-66-07-03-D0,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0376,172.18.21.210,00-E0-70-C4-2C-84,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0377,172.18.20.189,00-E0-70-C4-2B-4D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0541,************,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0424,172.18.20.197,F0-2F-74-34-0C-BC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0372,172.18.20.205,00-E0-70-C4-2A-EF,[{MARS单盘=1}] }, { SS-PC-MB0371,172.18.20.208,00-E0-70-C4-2B-A9,[{MARS单盘=1}] }, { SS-PC-MB0533,************,18-C0-4D-A6-71-53,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0386,172.18.21.42,A8-A1-59-26-10-8B,[{系统掉电=1}] }, { SS-PC-MB0543,************,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }, { SS-PC-MB0537,172.18.20.113,18-C0-4D-A6-74-26,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0515,172.18.41.120,A4-0C-66-07-03-01,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0512,172.18.41.148,A4-0C-66-07-01-DE,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0390,172.18.21.244,18-C0-4D-6C-68-0B,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0397,172.18.21.25,18-C0-4D-6C-66-B8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0368,*************,00-E0-70-C4-2B-11,[{MARS单盘=1}] }, { SS-PC-MB0383,172.18.21.27,A8-A1-59-26-87-0E,[{系统掉电=1}] }, { SS-PC-MB0394,172.18.20.239,18-C0-4D-6C-67-FB,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0538,172.18.20.60,18-C0-4D-A6-72-BB,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0396,172.18.20.129,18-C0-4D-6C-67-F1,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0425,172.18.20.135,F0-2F-74-33-FD-E0,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0520,172.18.20.192,18-C0-4D-A7-7E-79,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0389,172.18.21.181,18-C0-4D-6C-68-0C,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0392,172.18.21.193,18-C0-4D-6C-68-0A,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0754,172.18.30.66,50-EB-F6-9C-AD-F2,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0756,172.18.30.70,50-EB-F6-9C-AF-DB,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0787,172.18.30.71,D8-5E-D3-73-DD-39,[{系统掉电=1}] }, { SS-PC-MB0752,172.18.30.8,50-EB-F6-9C-B0-03,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0755,172.18.30.81,50-EB-F6-9C-AE-4E,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0709,172.18.31.172,D8-BB-C1-9D-AA-10,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0711,172.18.30.119,D8-BB-C1-9D-AA-01,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0713,172.18.31.174,D8-BB-C1-9D-AA-53,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0714,172.18.30.243,D8-BB-C1-9D-A9-D9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0715,172.18.31.24,D8-BB-C1-9D-A9-E9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0716,172.18.31.243,D8-BB-C1-9D-A9-FA,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0719,************,D8-BB-C1-9D-A9-7A,[{低温=4}] }, { SS-PC-MB0720,172.18.31.47,D8-BB-C1-9D-AA-30,[{低温=4}] }, { SS-PC-MB0722,172.18.31.147,D8-BB-C1-9D-AA-A0,[{低温=4}] }, { SS-PC-MB0723,172.18.31.94,D8-BB-C1-9D-AA-40,[{低温=4}] }, { SS-PC-MB0775,172.18.30.231,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0793,172.18.31.36,3C-EC-EF-B0-84-95,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0806,172.18.12.233,3C-EC-EF-B0-8A-75,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0794,172.18.31.102,3C-EC-EF-B0-8A-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0763,172.18.30.79,2C-F0-5D-CC-E7-DB,[{DOS=1}] }, { SS-PC-MB0764,172.18.30.92,2C-F0-5D-CC-E7-C8,[{DOS=1}] }, { SS-PC-MB0751,172.18.30.80,FC-34-97-C2-12-10,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0792,172.18.30.251,3C-EC-EF-B0-84-93,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0759,172.18.31.110,2C-F0-5D-CC-E7-D2,[{DOS=1}] }, { SS-PC-MB0783,172.18.30.13,04-7C-16-00-2A-66,[{性能=1}] }, { SS-PC-MB0799,172.18.31.43,3C-EC-EF-B0-84-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0800,172.18.30.124,3C-EC-EF-B0-89-C5,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0804,172.18.30.236,3C-EC-EF-B0-8A-9D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0395,172.18.20.34,18-C0-4D-6C-66-B2,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0782,172.18.30.179,18-C0-4D-A9-A8-D6,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0761,172.18.30.113,2C-F0-5D-CC-E7-CB,[{DOS=1}] }, { SS-PC-MB0731,172.18.30.21,D8-5E-D3-73-DC-7C,[{系统掉电=1}] }, { SS-PC-MB0739,172.18.30.6,D8-BB-C1-D1-E2-DC,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0740,172.18.30.1,D8-BB-C1-D1-D1-C3,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0735,172.18.30.28,D8-BB-C1-D7-23-28,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0797,172.18.30.174,3C-EC-EF-B0-84-7F,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0385,172.18.20.229,A8-A1-59-27-C9-DB,[{系统掉电=1}] }, { SS-PC-MB0786,172.18.30.4,D8-5E-D3-73-DD-47,[{系统掉电=1}] }, { SS-PC-MB0732,172.18.30.111,D8-5E-D3-73-D0-27,[{系统掉电=1}] }, { SS-PC-MB0758,172.18.30.115,50-EB-F6-9C-AF-A0,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0555,172.18.20.114,F0-2F-74-2E-66-7D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0737,172.18.30.24,D8-BB-C1-D1-D1-C7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0738,172.18.30.104,D8-BB-C1-D1-D1-CD,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0798,172.18.31.235,3C-EC-EF-B0-8A-7D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0718,172.18.30.117,D8-BB-C1-9D-AD-44,[{低温=4}] }, { SS-PC-MB0803,172.18.31.74,3C-EC-EF-B0-8A-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0730,172.18.30.12,D8-5E-D3-73-DD-84,[{系统掉电=1}] }, { SS-PC-MB0733,172.18.30.97,D8-5E-D3-73-E0-F2,[{系统掉电=1}] }, { SS-PC-MB0785,172.18.30.3,D8-5E-D3-73-DD-48,[{系统掉电=1}] }, { SS-PC-MB0774,172.18.30.20,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0388,172.18.21.84,18-C0-4D-6C-66-B5,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0772,172.18.30.52,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0773,172.18.30.14,18-C0-4D-AA-06-FF,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0771,172.18.30.50,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0776,172.18.30.67,18-C0-4D-A9-A2-B6,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0780,172.18.30.39,18-C0-4D-A9-A4-93,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0779,172.18.30.18,18-C0-4D-A9-A4-71,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0742,172.18.30.107,D8-BB-C1-D1-E2-D7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0736,172.18.30.46,D8-BB-C1-D1-E2-CA,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0369,*************,00-E0-70-C4-2C-4D,[{MARS单盘=1}] }, { SS-PC-MB0370,172.18.21.240,00-E0-70-C4-2B-A3,[{MARS单盘=1}] }, { SS-PC-MB0380,172.18.20.193,A8-A1-59-26-8B-34,[{系统掉电=1}] }, { SS-PC-MB0807,172.18.31.152,3C-EC-EF-B0-89-57,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0728,172.18.31.241,3C-EC-EF-B0-2F-C7,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00010,172.18.31.57,04-7C-16-B9-36-FD,[{低温=4}] }, { PCIE-PC-MB00003,*************,04-7C-16-B9-36-E9,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00006,172.18.30.153,04-7C-16-B9-3D-56,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00015,172.18.31.135,04-7C-16-B9-3D-62,[{低温=4}] }, { PCIE-PC-MB00012,172.18.31.191,04-7C-16-B9-36-F9,[{低温=4}] }, { SS-PC-MB0805,172.18.31.20,3C-EC-EF-B5-09-37,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00008,172.18.30.123,04-7C-16-B9-3D-63,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00018,172.18.31.183,3C-EC-EF-B0-B4-CD,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00020,172.18.30.238,3C-EC-EF-B0-B4-D9,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00019,172.18.30.230,3C-EC-EF-B0-B4-D1,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00023,172.18.30.194,3C-EC-EF-B0-B4-D7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00022,172.18.31.5,3C-EC-EF-B0-B2-AF,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00021,172.18.30.138,3C-EC-EF-B0-B8-B7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00016,172.18.31.6,04-7C-16-B9-36-E8,[{低温=4}] }, { PCIE-PC-MB00026,172.18.31.109,3C-EC-EF-B0-B4-9F,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00027,172.18.30.253,3C-EC-EF-B0-B4-E3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00028,172.18.31.84,3C-EC-EF-B0-B8-B3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00035,172.18.31.213,08-BF-B8-39-E9-AB,[{性能=1}] }, { PCIE-PC-MB00030,172.18.31.112,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00031,172.18.12.35,3C-EC-EF-B0-B2-D3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00013,172.18.31.29,04-7C-16-B9-3D-E1,[{低温=4}] }, { PCIE-PC-MB00029,172.18.31.71,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0468,172.18.40.197,B4-2E-99-FD-D5-3A,[{品质测试_高温=4}, {高温=1}] }, { PCIE-PC-MB00014,172.18.31.69,04-7C-16-B9-36-EB,[{低温=4}] }, { SS-PC-MB0766,\f仚w\u0002,2C-F0-5D-CC-E7-CA,[{DOS=1}] }, { SS-PC-MB0762,172.18.30.112,2C-F0-5D-CC-E7-CD,[{DOS=1}] }, { SS-PC-MB0760,172.18.30.96,2C-F0-5D-CC-E8-1F,[{DOS=1}] }, { PCIE-PC-MB00041,172.18.31.89,7C-C2-55-7D-6A-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00042,172.18.30.178,7C-C2-55-7D-6C-D1,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00043,172.18.31.13,7C-C2-55-7D-74-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00045,172.18.30.135,7C-C2-55-7D-6D-4D,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00047,172.18.30.208,7C-C2-55-7D-71-0B,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00046,172.18.30.130,7C-C2-55-7D-6D-CD,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00044,172.18.31.8,7C-C2-55-7D-69-B9,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00032,172.18.30.242,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00055,172.18.31.158,C8-7F-54-C5-F2-1F,[{性能=1}] }, { PCIE-PC-MB00036,172.18.30.255,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }], msg=测试机信息获取成功！, workPcLst=[{ SS-PC-MB0407,172.18.20.29,F0-2F-74-8B-94-FD,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.097+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6374 flash: SSV7_4X1_256G-4X4_1T_256GB available device: [{ SS-PC-MB0521,172.18.21.22,18-C0-4D-A6-72-5E,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0784,172.18.30.120,04-7C-16-00-2A-55,[{性能=1}] }, { SS-PC-MB0534,*************,18-C0-4D-A5-7F-8B,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0734,172.18.30.26,D8-5E-D3-73-DC-C6,[{系统掉电=1}] }, { SS-PC-MB0753,172.18.30.2,50-EB-F6-9C-B0-19,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0375,172.18.20.141,00-E0-70-C4-2D-27,[{MARS单盘=1}] }, { SS-PC-MB0478,172.18.20.146,A8-5E-45-9E-D5-73,[{MARS单盘=1}] }, { SS-PC-MB0545,172.18.20.147,F0-2F-74-2E-69-26,[{MARS单盘=1}] }, { SS-PC-MB0540,*************,F0-2F-74-2E-71-E0,[{MARS单盘=1}] }, { SS-PC-MB0536,172.18.20.155,18-C0-4D-A6-71-D5,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0544,172.18.21.113,F0-2F-74-2E-66-5B,[{MARS单盘=1}] }, { SS-PC-MB0535,172.18.20.226,18-C0-4D-A6-72-D8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0741,172.18.30.101,D8-BB-C1-D1-E2-DE,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0769,172.18.30.105,18-C0-4D-A9-A4-6D,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0532,172.18.20.44,18-C0-4D-A6-71-A3,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0539,172.18.20.48,18-C0-4D-A6-72-11,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0554,172.18.20.50,F0-2F-74-2E-74-33,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0378,172.18.20.149,A8-A1-59-27-3E-92,[{系统掉电=1}] }, { SS-PC-MB0513,172.18.40.174,A4-0C-66-02-AE-83,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0471,172.18.41.123,B4-2E-99-FD-D5-21,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0469,172.18.41.181,B4-2E-99-FD-CE-0E,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0470,172.18.40.22,B4-2E-99-FD-D5-30,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0384,172.18.21.187,A8-A1-59-26-0A-92,[{系统掉电=1}] }, { SS-PC-MB0476,172.18.20.28,A8-5E-45-9E-D9-10,[{MARS单盘=1}] }, { SS-PC-MB0406,172.18.21.160,F0-2F-74-8B-94-DC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0514,172.18.41.40,A4-0C-66-07-03-D0,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0376,172.18.21.210,00-E0-70-C4-2C-84,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0377,172.18.20.189,00-E0-70-C4-2B-4D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0541,************,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0424,172.18.20.197,F0-2F-74-34-0C-BC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0372,172.18.20.205,00-E0-70-C4-2A-EF,[{MARS单盘=1}] }, { SS-PC-MB0371,172.18.20.208,00-E0-70-C4-2B-A9,[{MARS单盘=1}] }, { SS-PC-MB0533,************,18-C0-4D-A6-71-53,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0386,172.18.21.42,A8-A1-59-26-10-8B,[{系统掉电=1}] }, { SS-PC-MB0543,************,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }, { SS-PC-MB0537,172.18.20.113,18-C0-4D-A6-74-26,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0515,172.18.41.120,A4-0C-66-07-03-01,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0512,172.18.41.148,A4-0C-66-07-01-DE,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0390,172.18.21.244,18-C0-4D-6C-68-0B,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0397,172.18.21.25,18-C0-4D-6C-66-B8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0368,*************,00-E0-70-C4-2B-11,[{MARS单盘=1}] }, { SS-PC-MB0383,172.18.21.27,A8-A1-59-26-87-0E,[{系统掉电=1}] }, { SS-PC-MB0394,172.18.20.239,18-C0-4D-6C-67-FB,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0538,172.18.20.60,18-C0-4D-A6-72-BB,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0396,172.18.20.129,18-C0-4D-6C-67-F1,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0425,172.18.20.135,F0-2F-74-33-FD-E0,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0520,172.18.20.192,18-C0-4D-A7-7E-79,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0389,172.18.21.181,18-C0-4D-6C-68-0C,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0392,172.18.21.193,18-C0-4D-6C-68-0A,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0754,172.18.30.66,50-EB-F6-9C-AD-F2,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0756,172.18.30.70,50-EB-F6-9C-AF-DB,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0787,172.18.30.71,D8-5E-D3-73-DD-39,[{系统掉电=1}] }, { SS-PC-MB0752,172.18.30.8,50-EB-F6-9C-B0-03,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0755,172.18.30.81,50-EB-F6-9C-AE-4E,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0709,172.18.31.172,D8-BB-C1-9D-AA-10,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0711,172.18.30.119,D8-BB-C1-9D-AA-01,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0713,172.18.31.174,D8-BB-C1-9D-AA-53,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0714,172.18.30.243,D8-BB-C1-9D-A9-D9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0715,172.18.31.24,D8-BB-C1-9D-A9-E9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0716,172.18.31.243,D8-BB-C1-9D-A9-FA,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0719,************,D8-BB-C1-9D-A9-7A,[{低温=4}] }, { SS-PC-MB0720,172.18.31.47,D8-BB-C1-9D-AA-30,[{低温=4}] }, { SS-PC-MB0722,172.18.31.147,D8-BB-C1-9D-AA-A0,[{低温=4}] }, { SS-PC-MB0723,172.18.31.94,D8-BB-C1-9D-AA-40,[{低温=4}] }, { SS-PC-MB0775,172.18.30.231,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0793,172.18.31.36,3C-EC-EF-B0-84-95,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0806,172.18.12.233,3C-EC-EF-B0-8A-75,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0794,172.18.31.102,3C-EC-EF-B0-8A-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0763,172.18.30.79,2C-F0-5D-CC-E7-DB,[{DOS=1}] }, { SS-PC-MB0764,172.18.30.92,2C-F0-5D-CC-E7-C8,[{DOS=1}] }, { SS-PC-MB0751,172.18.30.80,FC-34-97-C2-12-10,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0792,172.18.30.251,3C-EC-EF-B0-84-93,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0759,172.18.31.110,2C-F0-5D-CC-E7-D2,[{DOS=1}] }, { SS-PC-MB0783,172.18.30.13,04-7C-16-00-2A-66,[{性能=1}] }, { SS-PC-MB0799,172.18.31.43,3C-EC-EF-B0-84-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0800,172.18.30.124,3C-EC-EF-B0-89-C5,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0804,172.18.30.236,3C-EC-EF-B0-8A-9D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0395,172.18.20.34,18-C0-4D-6C-66-B2,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0782,172.18.30.179,18-C0-4D-A9-A8-D6,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0761,172.18.30.113,2C-F0-5D-CC-E7-CB,[{DOS=1}] }, { SS-PC-MB0731,172.18.30.21,D8-5E-D3-73-DC-7C,[{系统掉电=1}] }, { SS-PC-MB0739,172.18.30.6,D8-BB-C1-D1-E2-DC,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0740,172.18.30.1,D8-BB-C1-D1-D1-C3,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0735,172.18.30.28,D8-BB-C1-D7-23-28,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0797,172.18.30.174,3C-EC-EF-B0-84-7F,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0385,172.18.20.229,A8-A1-59-27-C9-DB,[{系统掉电=1}] }, { SS-PC-MB0786,172.18.30.4,D8-5E-D3-73-DD-47,[{系统掉电=1}] }, { SS-PC-MB0732,172.18.30.111,D8-5E-D3-73-D0-27,[{系统掉电=1}] }, { SS-PC-MB0758,172.18.30.115,50-EB-F6-9C-AF-A0,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0555,172.18.20.114,F0-2F-74-2E-66-7D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0737,172.18.30.24,D8-BB-C1-D1-D1-C7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0738,172.18.30.104,D8-BB-C1-D1-D1-CD,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0798,172.18.31.235,3C-EC-EF-B0-8A-7D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0718,172.18.30.117,D8-BB-C1-9D-AD-44,[{低温=4}] }, { SS-PC-MB0803,172.18.31.74,3C-EC-EF-B0-8A-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0730,172.18.30.12,D8-5E-D3-73-DD-84,[{系统掉电=1}] }, { SS-PC-MB0733,172.18.30.97,D8-5E-D3-73-E0-F2,[{系统掉电=1}] }, { SS-PC-MB0785,172.18.30.3,D8-5E-D3-73-DD-48,[{系统掉电=1}] }, { SS-PC-MB0774,172.18.30.20,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0388,172.18.21.84,18-C0-4D-6C-66-B5,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0772,172.18.30.52,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0773,172.18.30.14,18-C0-4D-AA-06-FF,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0771,172.18.30.50,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0776,172.18.30.67,18-C0-4D-A9-A2-B6,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0780,172.18.30.39,18-C0-4D-A9-A4-93,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0779,172.18.30.18,18-C0-4D-A9-A4-71,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0742,172.18.30.107,D8-BB-C1-D1-E2-D7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0736,172.18.30.46,D8-BB-C1-D1-E2-CA,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0369,*************,00-E0-70-C4-2C-4D,[{MARS单盘=1}] }, { SS-PC-MB0370,172.18.21.240,00-E0-70-C4-2B-A3,[{MARS单盘=1}] }, { SS-PC-MB0380,172.18.20.193,A8-A1-59-26-8B-34,[{系统掉电=1}] }, { SS-PC-MB0807,172.18.31.152,3C-EC-EF-B0-89-57,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0728,172.18.31.241,3C-EC-EF-B0-2F-C7,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00010,172.18.31.57,04-7C-16-B9-36-FD,[{低温=4}] }, { PCIE-PC-MB00003,*************,04-7C-16-B9-36-E9,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00006,172.18.30.153,04-7C-16-B9-3D-56,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00015,172.18.31.135,04-7C-16-B9-3D-62,[{低温=4}] }, { PCIE-PC-MB00012,172.18.31.191,04-7C-16-B9-36-F9,[{低温=4}] }, { SS-PC-MB0805,172.18.31.20,3C-EC-EF-B5-09-37,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00008,172.18.30.123,04-7C-16-B9-3D-63,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00018,172.18.31.183,3C-EC-EF-B0-B4-CD,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00020,172.18.30.238,3C-EC-EF-B0-B4-D9,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00019,172.18.30.230,3C-EC-EF-B0-B4-D1,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00023,172.18.30.194,3C-EC-EF-B0-B4-D7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00022,172.18.31.5,3C-EC-EF-B0-B2-AF,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00021,172.18.30.138,3C-EC-EF-B0-B8-B7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00016,172.18.31.6,04-7C-16-B9-36-E8,[{低温=4}] }, { PCIE-PC-MB00026,172.18.31.109,3C-EC-EF-B0-B4-9F,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00027,172.18.30.253,3C-EC-EF-B0-B4-E3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00028,172.18.31.84,3C-EC-EF-B0-B8-B3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00035,172.18.31.213,08-BF-B8-39-E9-AB,[{性能=1}] }, { PCIE-PC-MB00030,172.18.31.112,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00031,172.18.12.35,3C-EC-EF-B0-B2-D3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00013,172.18.31.29,04-7C-16-B9-3D-E1,[{低温=4}] }, { PCIE-PC-MB00029,172.18.31.71,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0468,172.18.40.197,B4-2E-99-FD-D5-3A,[{品质测试_高温=4}, {高温=1}] }, { PCIE-PC-MB00014,172.18.31.69,04-7C-16-B9-36-EB,[{低温=4}] }, { SS-PC-MB0766,\f仚w\u0002,2C-F0-5D-CC-E7-CA,[{DOS=1}] }, { SS-PC-MB0762,172.18.30.112,2C-F0-5D-CC-E7-CD,[{DOS=1}] }, { SS-PC-MB0760,172.18.30.96,2C-F0-5D-CC-E8-1F,[{DOS=1}] }, { PCIE-PC-MB00041,172.18.31.89,7C-C2-55-7D-6A-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00042,172.18.30.178,7C-C2-55-7D-6C-D1,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00043,172.18.31.13,7C-C2-55-7D-74-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00045,172.18.30.135,7C-C2-55-7D-6D-4D,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00047,172.18.30.208,7C-C2-55-7D-71-0B,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00046,172.18.30.130,7C-C2-55-7D-6D-CD,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00044,172.18.31.8,7C-C2-55-7D-69-B9,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00032,172.18.30.242,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00055,172.18.31.158,C8-7F-54-C5-F2-1F,[{性能=1}] }, { PCIE-PC-MB00036,172.18.30.255,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.098+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.098+08:00","@version":"1","message":"[6374] Plan65 need Num: 2 to test. SSV7_4X1_256G-4X4_1T_256GB left num:69 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.099+08:00","@version":"1","message":"subProduct PCIe flashName SSV7_4X1_256G-4X4_1T_256GB plan [Plan65] attrs MARS单盘 belongTo clara.lou is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.101+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [SS-PC-MB0769, SS-PC-MB0771, SS-PC-MB0772, SS-PC-MB0773, SS-PC-MB0775, SS-PC-MB0779, SS-PC-MB0780, SS-PC-MB0782, SS-PC-MB0368, SS-PC-MB0369, SS-PC-MB0370, SS-PC-MB0371, SS-PC-MB0372, SS-PC-MB0375, SS-PC-MB0476, SS-PC-MB0478, SS-PC-MB0540, SS-PC-MB0541, SS-PC-MB0543, SS-PC-MB0544, SS-PC-MB0545, SS-PC-MB0388, SS-PC-MB0389, SS-PC-MB0390, SS-PC-MB0392, SS-PC-MB0394, SS-PC-MB0395, SS-PC-MB0396, SS-PC-MB0397, SS-PC-MB0424, SS-PC-MB0425, SS-PC-MB0406, SS-PC-MB0376, SS-PC-MB0377, SS-PC-MB0520, SS-PC-MB0521, SS-PC-MB0554, SS-PC-MB0555, SS-PC-MB0532, SS-PC-MB0533, SS-PC-MB0534, SS-PC-MB0535, SS-PC-MB0536, SS-PC-MB0537, SS-PC-MB0538, SS-PC-MB0539]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.101+08:00","@version":"1","message":"PCIe Plan65不是测试所有样片的Plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.102+08:00","@version":"1","message":"sortedEntries: [SSD5_4=4, PCIE_3=4, SSD5_5=2]","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.109+08:00","@version":"1","message":"Plan65已存在使用机柜，在机柜SSD5_4下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.11+08:00","@version":"1","message":"Plan65在机柜SSD5_4下找到设备历史中常用的连号设备：[F0-2F-74-2E-6B-5C, F0-2F-74-2E-6B-4D]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.111+08:00","@version":"1","message":"[6374] plan:Plan65 use 2 pc: [{ SS-PC-MB0541,************,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0543,************,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.111+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ SS-PC-MB0541,************,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0543,************,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.111+08:00","@version":"1","message":"[6374] Plan70 need Num: 2 to test. SSV7_4X1_256G-4X4_1T_256GB left num:67 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.111+08:00","@version":"1","message":"subProduct PCIe flashName SSV7_4X1_256G-4X4_1T_256GB plan [Plan70] attrs MARS单盘 belongTo clara.lou is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.112+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [SS-PC-MB0769, SS-PC-MB0771, SS-PC-MB0772, SS-PC-MB0773, SS-PC-MB0775, SS-PC-MB0779, SS-PC-MB0780, SS-PC-MB0782, SS-PC-MB0368, SS-PC-MB0369, SS-PC-MB0370, SS-PC-MB0371, SS-PC-MB0372, SS-PC-MB0375, SS-PC-MB0476, SS-PC-MB0478, SS-PC-MB0540, SS-PC-MB0544, SS-PC-MB0545, SS-PC-MB0388, SS-PC-MB0389, SS-PC-MB0390, SS-PC-MB0392, SS-PC-MB0394, SS-PC-MB0395, SS-PC-MB0396, SS-PC-MB0397, SS-PC-MB0424, SS-PC-MB0425, SS-PC-MB0406, SS-PC-MB0376, SS-PC-MB0377, SS-PC-MB0520, SS-PC-MB0521, SS-PC-MB0554, SS-PC-MB0555, SS-PC-MB0532, SS-PC-MB0533, SS-PC-MB0534, SS-PC-MB0535, SS-PC-MB0536, SS-PC-MB0537, SS-PC-MB0538, SS-PC-MB0539]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.113+08:00","@version":"1","message":"PCIe Plan70不是测试所有样片的Plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.114+08:00","@version":"1","message":"sortedEntries: [SSD5_4=4, PCIE_3=4, SSD5_5=2]","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.12+08:00","@version":"1","message":"Plan70已存在使用机柜，在机柜SSD5_4下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.12+08:00","@version":"1","message":"Plan70已存在使用机柜，在机柜PCIE_3下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.12+08:00","@version":"1","message":"Plan70已存在使用机柜，在机柜SSD5_5下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.12+08:00","@version":"1","message":"rackList: [SSD5_4=4, PCIE_3=4, SSD5_5=2]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.12+08:00","@version":"1","message":"devices: [SSD5_4_01, SSD5_4_02, SSD5_4_03, SSD5_4_04, SSD5_4_05, SSD5_4_08, SSD5_4_09, SSD5_4_11, SSD5_4_12, SSD5_4_15, SSD5_4_16]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.12+08:00","@version":"1","message":"for index: 0 size: 2 sum: 2 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.12+08:00","@version":"1","message":"wait testNum: 2 assign sum: 2 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.121+08:00","@version":"1","message":"Plan70已分配的机柜范围内：在机柜SSD5_4下找到空闲的可使用设备：[SSD5_4_01, SSD5_4_02]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.121+08:00","@version":"1","message":"[6374] plan:Plan70 use 2 pc: [{ SS-PC-MB0368,*************,00-E0-70-C4-2B-11,[{MARS单盘=1}] }, { SS-PC-MB0369,*************,00-E0-70-C4-2C-4D,[{MARS单盘=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.121+08:00","@version":"1","message":"assignDevices [6374] - find 2 run devices:  {Plan70=[{ SS-PC-MB0368,*************,00-E0-70-C4-2B-11,[{MARS单盘=1}] }, { SS-PC-MB0369,*************,00-E0-70-C4-2C-4D,[{MARS单盘=1}] }], Plan65=[{ SS-PC-MB0541,************,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0543,************,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.122+08:00","@version":"1","message":"工单[6374] flash SSV7_4X1_256G-4X4_1T_256GB , 参与此次Plan预分配的plan共有 [Plan65, Plan70] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.14+08:00","@version":"1","message":" add 2 devices :[{ SS-PC-MB0368,*************,00-E0-70-C4-2B-11,[{MARS单盘=1}] }, { SS-PC-MB0369,*************,00-E0-70-C4-2C-4D,[{MARS单盘=1}] }]  to plan:Plan70","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.158+08:00","@version":"1","message":"plan:Plan70 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 2","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.171+08:00","@version":"1","message":"[YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB] - Plan70 holdDevices  :[PlanDeviceEntity(id=276967, orderId=6374, planId=113597, planName=Plan70, ip=*************, mac=00-E0-70-C4-2B-11, no=SS-PC-MB0368, position=SSD5_4_01, score=100, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=276968, orderId=6374, planId=113597, planName=Plan70, ip=*************, mac=00-E0-70-C4-2C-4D, no=SS-PC-MB0369, position=SSD5_4_02, score=100, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.208+08:00","@version":"1","message":"lock device:00-E0-70-C4-2B-11 to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan70 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.229+08:00","@version":"1","message":"lock device:00-E0-70-C4-2C-4D to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan70 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.233+08:00","@version":"1","message":"[7973ab33] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.405+08:00","@version":"1","message":"lockDevice with ipList:[*************, *************] - macList:[00-E0-70-C4-2B-11, 00-E0-70-C4-2C-4D],no:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.411+08:00","@version":"1","message":"自动分配SS-PC-MB0368(*************),SS-PC-MB0369(*************)给SSV7_4X1_256G-4X4_1T_256GB下的Plan70","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.411+08:00","@version":"1","message":"add 2 devices to plan: Plan70 ,expect num: 2, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.411+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6374, flash:SSV7_4X1_256G-4X4_1T_256GB, planEntity:OrderPlanEntity(id=113597, orderId=6374, name=Plan70, status=QUEUE, priority=50)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.426+08:00","@version":"1","message":"测试单: YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB plan: Plan70已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.845+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.857+08:00","@version":"1","message":" add 2 devices :[{ SS-PC-MB0541,************,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0543,************,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }]  to plan:Plan65","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.876+08:00","@version":"1","message":"plan:Plan65 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 2","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.88+08:00","@version":"1","message":"[YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB] - Plan65 holdDevices  :[PlanDeviceEntity(id=276969, orderId=6374, planId=113596, planName=Plan65, ip=************, mac=F0-2F-74-2E-6B-4D, no=SS-PC-MB0541, position=SSD5_4_13, score=100, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=276970, orderId=6374, planId=113596, planName=Plan65, ip=************, mac=F0-2F-74-2E-6B-5C, no=SS-PC-MB0543, position=SSD5_4_14, score=100, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.883+08:00","@version":"1","message":"lock device:F0-2F-74-2E-6B-4D to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan65 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.888+08:00","@version":"1","message":"lock device:F0-2F-74-2E-6B-5C to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan65 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:03.892+08:00","@version":"1","message":"[4b8b733c] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:04.041+08:00","@version":"1","message":"lockDevice with ipList:[************, ************] - macList:[F0-2F-74-2E-6B-4D, F0-2F-74-2E-6B-5C],no:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:04.041+08:00","@version":"1","message":"自动分配SS-PC-MB0541(************),SS-PC-MB0543(************)给SSV7_4X1_256G-4X4_1T_256GB下的Plan65","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:04.041+08:00","@version":"1","message":"add 2 devices to plan: Plan65 ,expect num: 2, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:04.041+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6374, flash:SSV7_4X1_256G-4X4_1T_256GB, planEntity:OrderPlanEntity(id=113596, orderId=6374, name=Plan65, status=QUEUE, priority=50)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:04.043+08:00","@version":"1","message":"测试单: YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB plan: Plan65已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:04.229+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:04.229+08:00","@version":"1","message":"此次分配flash批次 SSV7_4X1_256G-4X4_1T_256GB 下的Plan共消耗 4 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:04.232+08:00","@version":"1","message":"更新Flash:SSV7_4X1_256G-4X4_1T_256GB的样片数量从69变更为65","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:04.264+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"715b124b2eb27959","spanId":"715b124b2eb27959","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:02.637+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=65)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.637+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.71+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.726+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.743+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.758+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.773+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.794+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.808+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.823+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.823+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.823+08:00","@version":"1","message":"执行预分配前共有65颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.824+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.824+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.824+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.824+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.824+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.824+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4cefd32a72584dda","spanId":"4cefd32a72584dda","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:02:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=65)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.613+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.634+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.663+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.686+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.706+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.72+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.735+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.749+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.764+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.764+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.764+08:00","@version":"1","message":"执行预分配前共有65颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.765+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.765+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.766+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.766+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.766+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:02:02.766+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0f04650b95962b3e","spanId":"0f04650b95962b3e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.623+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:06:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=65)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.624+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.64+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.655+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.707+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.735+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.749+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.763+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.777+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.791+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.805+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.805+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.805+08:00","@version":"1","message":"执行预分配前共有65颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.805+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.805+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.805+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.805+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.805+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.805+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c8179b1ae128eb4d","spanId":"c8179b1ae128eb4d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:14:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=65)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.613+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.633+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.648+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.683+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.698+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.712+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.726+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.74+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.754+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.754+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.755+08:00","@version":"1","message":"执行预分配前共有65颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.755+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.755+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.755+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.755+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.755+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:14:02.755+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5d549fc2ed4d559","spanId":"a5d549fc2ed4d559","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.616+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:18:02.617+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=65)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.617+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.676+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.708+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.725+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.741+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.755+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.769+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.783+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.799+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.8+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.8+08:00","@version":"1","message":"执行预分配前共有65颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.8+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.801+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.801+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.802+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.802+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.802+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa11ad65e125ad38","spanId":"fa11ad65e125ad38","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:34:02.637+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=65)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.638+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.686+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.705+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.731+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.75+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.766+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.784+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.799+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.815+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.839+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.841+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.841+08:00","@version":"1","message":"执行预分配前共有65颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.846+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.849+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.849+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.85+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.85+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:34:02.85+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa412a46f360669e","spanId":"aa412a46f360669e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:02.627+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=65)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.627+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.665+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.695+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.714+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.728+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.742+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.761+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.777+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.793+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.807+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.808+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.808+08:00","@version":"1","message":"执行预分配前共有65颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.809+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.809+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.81+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.81+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.81+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.81+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"461b97c64ce31369","spanId":"461b97c64ce31369","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:46:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=67)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.613+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.635+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.652+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.666+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.681+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.695+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.709+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.726+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.743+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.758+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.758+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.758+08:00","@version":"1","message":"执行预分配前共有67颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.758+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.758+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.759+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.759+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.759+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:46:02.759+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d3ad684aac05ce67","spanId":"d3ad684aac05ce67","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:47:04.633+08:00","@version":"1","message":"execute:PCIe_D8-BB-C1-9D-AA-03_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:Abel.Zou title:重新选择电脑后电脑关机 planId:113575 mac:D8-BB-C1-9D-AA-03","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"877def45155ac09b","spanId":"877def45155ac09b","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:47:04.635+08:00","@version":"1","message":"设备D8-BB-C1-9D-AA-03不在计划113575中，忽略","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"877def45155ac09b","spanId":"877def45155ac09b","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:04.573+08:00","@version":"1","message":"execute:PCIe_F0-2F-74-2E-71-E0_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113596 mac:F0-2F-74-2E-71-E0","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4bf6e1eb90f1eef4","spanId":"4bf6e1eb90f1eef4","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:04.574+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0540[F0-2F-74-2E-71-E0]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4bf6e1eb90f1eef4","spanId":"4bf6e1eb90f1eef4","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:04.575+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0540]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4bf6e1eb90f1eef4","spanId":"4bf6e1eb90f1eef4","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:04.575+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[F0-2F-74-2E-71-E0])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4bf6e1eb90f1eef4","spanId":"4bf6e1eb90f1eef4","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:04.575+08:00","@version":"1","message":"[769b20f7] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4bf6e1eb90f1eef4","spanId":"4bf6e1eb90f1eef4","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:04.709+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[F0-2F-74-2E-71-E0]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=F0-2F-74-2E-71-E0, pc_no=MB0540, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4bf6e1eb90f1eef4","spanId":"4bf6e1eb90f1eef4","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:04.71+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4bf6e1eb90f1eef4","spanId":"4bf6e1eb90f1eef4","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:59.96+08:00","@version":"1","message":"execute:PCIe_F0-2F-74-2E-6B-4D_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113596 mac:F0-2F-74-2E-6B-4D","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"34244e7180499b99","spanId":"34244e7180499b99","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:59.961+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0541[F0-2F-74-2E-6B-4D]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"34244e7180499b99","spanId":"34244e7180499b99","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:59.961+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0541]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"34244e7180499b99","spanId":"34244e7180499b99","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:59.961+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[F0-2F-74-2E-6B-4D])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"34244e7180499b99","spanId":"34244e7180499b99","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:53:59.961+08:00","@version":"1","message":"[1aa569a3] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"34244e7180499b99","spanId":"34244e7180499b99","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:54:00.103+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[F0-2F-74-2E-6B-4D]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=F0-2F-74-2E-6B-4D, pc_no=MB0540, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"34244e7180499b99","spanId":"34244e7180499b99","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:54:00.103+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"34244e7180499b99","spanId":"34244e7180499b99","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T11:54:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:54:02.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=67)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.619+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.656+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.696+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.714+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.728+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.742+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.757+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.772+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.786+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.799+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.8+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.8+08:00","@version":"1","message":"执行预分配前共有67颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.8+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.8+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.8+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.8+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.8+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.8+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2610be989219ae61","spanId":"2610be989219ae61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:06:02.62+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=71)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.62+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.67+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.684+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.71+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.723+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.738+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.751+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.766+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.78+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.78+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113579, orderId=6374, name=Plan61, status=QUEUE, priority=85), OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85), OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.78+08:00","@version":"1","message":"执行预分配前共有71颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.78+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.78+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.78+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.781+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:71 index:1 plans:[OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:06:02.781+08:00","@version":"1","message":"[预分配]分配给测试人[Abel.Zou]的Plan有 [Plan63], 一共使用了 2 颗样片, 还剩下样片 69 颗","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.781+08:00","@version":"1","message":"assignList: [OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.781+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 1 plans: [OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.781+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.827+08:00","@version":"1","message":"[6f930c03] HTTP GET http://ereport.yeestor.com/wo/device/list?p=PCIe","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.057+08:00","@version":"1","message":"getAllDeviceList with PCIe got data DeviceListResp(code=0, data=[{ SS-PC-MB0521,172.18.21.22,18-C0-4D-A6-72-5E,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0784,172.18.30.120,04-7C-16-00-2A-55,[{性能=1}] }, { SS-PC-MB0534,*************,18-C0-4D-A5-7F-8B,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0734,172.18.30.26,D8-5E-D3-73-DC-C6,[{系统掉电=1}] }, { SS-PC-MB0753,172.18.30.2,50-EB-F6-9C-B0-19,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0375,172.18.20.141,00-E0-70-C4-2D-27,[{MARS单盘=1}] }, { SS-PC-MB0478,172.18.20.146,A8-5E-45-9E-D5-73,[{MARS单盘=1}] }, { SS-PC-MB0545,172.18.20.147,F0-2F-74-2E-69-26,[{MARS单盘=1}] }, { SS-PC-MB0540,*************,F0-2F-74-2E-71-E0,[{MARS单盘=1}] }, { SS-PC-MB0536,172.18.20.155,18-C0-4D-A6-71-D5,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0544,172.18.21.113,F0-2F-74-2E-66-5B,[{MARS单盘=1}] }, { SS-PC-MB0535,172.18.20.226,18-C0-4D-A6-72-D8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0741,172.18.30.101,D8-BB-C1-D1-E2-DE,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0769,172.18.30.105,18-C0-4D-A9-A4-6D,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0532,172.18.20.44,18-C0-4D-A6-71-A3,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0539,172.18.20.48,18-C0-4D-A6-72-11,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0554,172.18.20.50,F0-2F-74-2E-74-33,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0378,172.18.20.149,A8-A1-59-27-3E-92,[{系统掉电=1}] }, { SS-PC-MB0513,172.18.40.174,A4-0C-66-02-AE-83,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0471,172.18.41.123,B4-2E-99-FD-D5-21,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0469,172.18.41.181,B4-2E-99-FD-CE-0E,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0470,172.18.40.22,B4-2E-99-FD-D5-30,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0384,172.18.21.187,A8-A1-59-26-0A-92,[{系统掉电=1}] }, { SS-PC-MB0476,172.18.20.28,A8-5E-45-9E-D9-10,[{MARS单盘=1}] }, { SS-PC-MB0406,172.18.21.160,F0-2F-74-8B-94-DC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0514,172.18.41.40,A4-0C-66-07-03-D0,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0376,172.18.21.210,00-E0-70-C4-2C-84,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0377,172.18.20.189,00-E0-70-C4-2B-4D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0541,************,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0424,172.18.20.197,F0-2F-74-34-0C-BC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0372,172.18.20.205,00-E0-70-C4-2A-EF,[{MARS单盘=1}] }, { SS-PC-MB0371,172.18.20.208,00-E0-70-C4-2B-A9,[{MARS单盘=1}] }, { SS-PC-MB0533,************,18-C0-4D-A6-71-53,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0386,172.18.21.42,A8-A1-59-26-10-8B,[{系统掉电=1}] }, { SS-PC-MB0543,************,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }, { SS-PC-MB0537,172.18.20.113,18-C0-4D-A6-74-26,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0515,172.18.41.120,A4-0C-66-07-03-01,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0512,172.18.41.148,A4-0C-66-07-01-DE,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0390,172.18.21.244,18-C0-4D-6C-68-0B,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0397,172.18.21.25,18-C0-4D-6C-66-B8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0383,172.18.21.27,A8-A1-59-26-87-0E,[{系统掉电=1}] }, { SS-PC-MB0394,172.18.20.239,18-C0-4D-6C-67-FB,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0538,172.18.20.60,18-C0-4D-A6-72-BB,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0396,172.18.20.129,18-C0-4D-6C-67-F1,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0425,172.18.20.135,F0-2F-74-33-FD-E0,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0520,172.18.20.192,18-C0-4D-A7-7E-79,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0389,172.18.21.181,18-C0-4D-6C-68-0C,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0392,172.18.21.193,18-C0-4D-6C-68-0A,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0754,172.18.30.66,50-EB-F6-9C-AD-F2,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0756,172.18.30.70,50-EB-F6-9C-AF-DB,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0787,172.18.30.71,D8-5E-D3-73-DD-39,[{系统掉电=1}] }, { SS-PC-MB0752,172.18.30.8,50-EB-F6-9C-B0-03,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0755,172.18.30.81,50-EB-F6-9C-AE-4E,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0709,172.18.31.172,D8-BB-C1-9D-AA-10,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0711,172.18.30.119,D8-BB-C1-9D-AA-01,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0713,172.18.31.174,D8-BB-C1-9D-AA-53,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0714,172.18.30.243,D8-BB-C1-9D-A9-D9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0715,172.18.31.24,D8-BB-C1-9D-A9-E9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0716,172.18.31.243,D8-BB-C1-9D-A9-FA,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0717,172.18.31.9,D8-BB-C1-9D-AA-51,[{低温=4}] }, { SS-PC-MB0719,************,D8-BB-C1-9D-A9-7A,[{低温=4}] }, { SS-PC-MB0720,172.18.31.47,D8-BB-C1-9D-AA-30,[{低温=4}] }, { SS-PC-MB0407,172.18.20.29,F0-2F-74-8B-94-FD,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0775,172.18.30.231,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0793,172.18.31.36,3C-EC-EF-B0-84-95,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0789,172.18.30.175,3C-EC-EF-B0-62-43,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0806,172.18.12.233,3C-EC-EF-B0-8A-75,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0790,172.18.31.166,3C-EC-EF-B0-8A-91,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0791,172.18.30.140,3C-EC-EF-B0-84-83,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0794,172.18.31.102,3C-EC-EF-B0-8A-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0763,172.18.30.79,2C-F0-5D-CC-E7-DB,[{DOS=1}] }, { SS-PC-MB0764,172.18.30.92,2C-F0-5D-CC-E7-C8,[{DOS=1}] }, { SS-PC-MB0751,172.18.30.80,FC-34-97-C2-12-10,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0792,172.18.30.251,3C-EC-EF-B0-84-93,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0759,172.18.31.110,2C-F0-5D-CC-E7-D2,[{DOS=1}] }, { SS-PC-MB0783,172.18.30.13,04-7C-16-00-2A-66,[{性能=1}] }, { SS-PC-MB0799,172.18.31.43,3C-EC-EF-B0-84-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0800,172.18.30.124,3C-EC-EF-B0-89-C5,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0804,172.18.30.236,3C-EC-EF-B0-8A-9D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0395,172.18.20.34,18-C0-4D-6C-66-B2,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0788,172.18.31.223,3C-EC-EF-B0-84-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0782,172.18.30.179,18-C0-4D-A9-A8-D6,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0761,172.18.30.113,2C-F0-5D-CC-E7-CB,[{DOS=1}] }, { SS-PC-MB0731,172.18.30.21,D8-5E-D3-73-DC-7C,[{系统掉电=1}] }, { SS-PC-MB0739,172.18.30.6,D8-BB-C1-D1-E2-DC,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0740,172.18.30.1,D8-BB-C1-D1-D1-C3,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0735,172.18.30.28,D8-BB-C1-D7-23-28,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0797,172.18.30.174,3C-EC-EF-B0-84-7F,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0385,172.18.20.229,A8-A1-59-27-C9-DB,[{系统掉电=1}] }, { SS-PC-MB0786,172.18.30.4,D8-5E-D3-73-DD-47,[{系统掉电=1}] }, { SS-PC-MB0732,172.18.30.111,D8-5E-D3-73-D0-27,[{系统掉电=1}] }, { SS-PC-MB0758,172.18.30.115,50-EB-F6-9C-AF-A0,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0555,172.18.20.114,F0-2F-74-2E-66-7D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0737,172.18.30.24,D8-BB-C1-D1-D1-C7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0738,172.18.30.104,D8-BB-C1-D1-D1-CD,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0798,172.18.31.235,3C-EC-EF-B0-8A-7D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0718,172.18.30.117,D8-BB-C1-9D-AD-44,[{低温=4}] }, { SS-PC-MB0803,172.18.31.74,3C-EC-EF-B0-8A-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0730,172.18.30.12,D8-5E-D3-73-DD-84,[{系统掉电=1}] }, { SS-PC-MB0733,172.18.30.97,D8-5E-D3-73-E0-F2,[{系统掉电=1}] }, { SS-PC-MB0724,172.18.30.158,D8-BB-C1-9D-AA-03,[{低温=4}] }, { SS-PC-MB0785,172.18.30.3,D8-5E-D3-73-DD-48,[{系统掉电=1}] }, { SS-PC-MB0774,172.18.30.20,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0388,172.18.21.84,18-C0-4D-6C-66-B5,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0772,172.18.30.52,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0773,172.18.30.14,18-C0-4D-AA-06-FF,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0771,172.18.30.50,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0776,172.18.30.67,18-C0-4D-A9-A2-B6,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0780,172.18.30.39,18-C0-4D-A9-A4-93,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0742,172.18.30.107,D8-BB-C1-D1-E2-D7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0736,172.18.30.46,D8-BB-C1-D1-E2-CA,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0370,172.18.21.240,00-E0-70-C4-2B-A3,[{MARS单盘=1}] }, { SS-PC-MB0380,172.18.20.193,A8-A1-59-26-8B-34,[{系统掉电=1}] }, { SS-PC-MB0807,172.18.31.152,3C-EC-EF-B0-89-57,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0728,172.18.31.241,3C-EC-EF-B0-2F-C7,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00010,172.18.31.57,04-7C-16-B9-36-FD,[{低温=4}] }, { PCIE-PC-MB00003,*************,04-7C-16-B9-36-E9,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00015,172.18.31.135,04-7C-16-B9-3D-62,[{低温=4}] }, { PCIE-PC-MB00012,172.18.31.191,04-7C-16-B9-36-F9,[{低温=4}] }, { SS-PC-MB0805,172.18.31.20,3C-EC-EF-B5-09-37,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00034,*************,08-BF-B8-32-90-B4,[{性能=1}] }, { PCIE-PC-MB00018,172.18.31.183,3C-EC-EF-B0-B4-CD,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00020,172.18.30.238,3C-EC-EF-B0-B4-D9,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00019,172.18.30.230,3C-EC-EF-B0-B4-D1,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00023,172.18.30.194,3C-EC-EF-B0-B4-D7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00021,172.18.30.138,3C-EC-EF-B0-B8-B7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00016,172.18.31.6,04-7C-16-B9-36-E8,[{低温=4}] }, { PCIE-PC-MB00035,172.18.31.213,08-BF-B8-39-E9-AB,[{性能=1}] }, { PCIE-PC-MB00030,172.18.31.112,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00031,172.18.12.35,3C-EC-EF-B0-B2-D3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00013,172.18.31.29,04-7C-16-B9-3D-E1,[{低温=4}] }, { PCIE-PC-MB00029,172.18.31.71,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0468,172.18.40.197,B4-2E-99-FD-D5-3A,[{品质测试_高温=4}, {高温=1}] }, { PCIE-PC-MB00014,172.18.31.69,04-7C-16-B9-36-EB,[{低温=4}] }, { SS-PC-MB0766,\f仚w\u0002,2C-F0-5D-CC-E7-CA,[{DOS=1}] }, { SS-PC-MB0762,172.18.30.112,2C-F0-5D-CC-E7-CD,[{DOS=1}] }, { SS-PC-MB0760,172.18.30.96,2C-F0-5D-CC-E8-1F,[{DOS=1}] }, { PCIE-PC-MB00041,172.18.31.89,7C-C2-55-7D-6A-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00042,172.18.30.178,7C-C2-55-7D-6C-D1,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00043,172.18.31.13,7C-C2-55-7D-74-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00045,172.18.30.135,7C-C2-55-7D-6D-4D,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00047,172.18.30.208,7C-C2-55-7D-71-0B,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00046,172.18.30.130,7C-C2-55-7D-6D-CD,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00044,172.18.31.8,7C-C2-55-7D-69-B9,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00032,172.18.30.242,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00054,*************,10-7C-61-0A-72-01,[{性能=1}] }, { PCIE-PC-MB00055,172.18.31.158,C8-7F-54-C5-F2-1F,[{性能=1}] }, { PCIE-PC-MB00036,172.18.30.255,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }], msg=测试机信息获取成功！, workPcLst=[])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.109+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6374 flash: SSV7_4X1_256G-4X4_1T_256GB available device: [{ SS-PC-MB0521,172.18.21.22,18-C0-4D-A6-72-5E,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0784,172.18.30.120,04-7C-16-00-2A-55,[{性能=1}] }, { SS-PC-MB0534,*************,18-C0-4D-A5-7F-8B,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0734,172.18.30.26,D8-5E-D3-73-DC-C6,[{系统掉电=1}] }, { SS-PC-MB0753,172.18.30.2,50-EB-F6-9C-B0-19,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0375,172.18.20.141,00-E0-70-C4-2D-27,[{MARS单盘=1}] }, { SS-PC-MB0478,172.18.20.146,A8-5E-45-9E-D5-73,[{MARS单盘=1}] }, { SS-PC-MB0545,172.18.20.147,F0-2F-74-2E-69-26,[{MARS单盘=1}] }, { SS-PC-MB0540,*************,F0-2F-74-2E-71-E0,[{MARS单盘=1}] }, { SS-PC-MB0536,172.18.20.155,18-C0-4D-A6-71-D5,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0544,172.18.21.113,F0-2F-74-2E-66-5B,[{MARS单盘=1}] }, { SS-PC-MB0535,172.18.20.226,18-C0-4D-A6-72-D8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0741,172.18.30.101,D8-BB-C1-D1-E2-DE,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0769,172.18.30.105,18-C0-4D-A9-A4-6D,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0532,172.18.20.44,18-C0-4D-A6-71-A3,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0539,172.18.20.48,18-C0-4D-A6-72-11,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0554,172.18.20.50,F0-2F-74-2E-74-33,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0378,172.18.20.149,A8-A1-59-27-3E-92,[{系统掉电=1}] }, { SS-PC-MB0513,172.18.40.174,A4-0C-66-02-AE-83,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0471,172.18.41.123,B4-2E-99-FD-D5-21,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0469,172.18.41.181,B4-2E-99-FD-CE-0E,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0470,172.18.40.22,B4-2E-99-FD-D5-30,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0384,172.18.21.187,A8-A1-59-26-0A-92,[{系统掉电=1}] }, { SS-PC-MB0476,172.18.20.28,A8-5E-45-9E-D9-10,[{MARS单盘=1}] }, { SS-PC-MB0406,172.18.21.160,F0-2F-74-8B-94-DC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0514,172.18.41.40,A4-0C-66-07-03-D0,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0376,172.18.21.210,00-E0-70-C4-2C-84,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0377,172.18.20.189,00-E0-70-C4-2B-4D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0541,************,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0424,172.18.20.197,F0-2F-74-34-0C-BC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0372,172.18.20.205,00-E0-70-C4-2A-EF,[{MARS单盘=1}] }, { SS-PC-MB0371,172.18.20.208,00-E0-70-C4-2B-A9,[{MARS单盘=1}] }, { SS-PC-MB0533,************,18-C0-4D-A6-71-53,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0386,172.18.21.42,A8-A1-59-26-10-8B,[{系统掉电=1}] }, { SS-PC-MB0543,************,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }, { SS-PC-MB0537,172.18.20.113,18-C0-4D-A6-74-26,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0515,172.18.41.120,A4-0C-66-07-03-01,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0512,172.18.41.148,A4-0C-66-07-01-DE,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0390,172.18.21.244,18-C0-4D-6C-68-0B,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0397,172.18.21.25,18-C0-4D-6C-66-B8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0383,172.18.21.27,A8-A1-59-26-87-0E,[{系统掉电=1}] }, { SS-PC-MB0394,172.18.20.239,18-C0-4D-6C-67-FB,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0538,172.18.20.60,18-C0-4D-A6-72-BB,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0396,172.18.20.129,18-C0-4D-6C-67-F1,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0425,172.18.20.135,F0-2F-74-33-FD-E0,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0520,172.18.20.192,18-C0-4D-A7-7E-79,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0389,172.18.21.181,18-C0-4D-6C-68-0C,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0392,172.18.21.193,18-C0-4D-6C-68-0A,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0754,172.18.30.66,50-EB-F6-9C-AD-F2,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0756,172.18.30.70,50-EB-F6-9C-AF-DB,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0787,172.18.30.71,D8-5E-D3-73-DD-39,[{系统掉电=1}] }, { SS-PC-MB0752,172.18.30.8,50-EB-F6-9C-B0-03,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0755,172.18.30.81,50-EB-F6-9C-AE-4E,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0709,172.18.31.172,D8-BB-C1-9D-AA-10,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0711,172.18.30.119,D8-BB-C1-9D-AA-01,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0713,172.18.31.174,D8-BB-C1-9D-AA-53,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0714,172.18.30.243,D8-BB-C1-9D-A9-D9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0715,172.18.31.24,D8-BB-C1-9D-A9-E9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0716,172.18.31.243,D8-BB-C1-9D-A9-FA,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0717,172.18.31.9,D8-BB-C1-9D-AA-51,[{低温=4}] }, { SS-PC-MB0719,************,D8-BB-C1-9D-A9-7A,[{低温=4}] }, { SS-PC-MB0720,172.18.31.47,D8-BB-C1-9D-AA-30,[{低温=4}] }, { SS-PC-MB0407,172.18.20.29,F0-2F-74-8B-94-FD,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0775,172.18.30.231,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0793,172.18.31.36,3C-EC-EF-B0-84-95,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0789,172.18.30.175,3C-EC-EF-B0-62-43,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0806,172.18.12.233,3C-EC-EF-B0-8A-75,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0790,172.18.31.166,3C-EC-EF-B0-8A-91,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0791,172.18.30.140,3C-EC-EF-B0-84-83,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0794,172.18.31.102,3C-EC-EF-B0-8A-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0763,172.18.30.79,2C-F0-5D-CC-E7-DB,[{DOS=1}] }, { SS-PC-MB0764,172.18.30.92,2C-F0-5D-CC-E7-C8,[{DOS=1}] }, { SS-PC-MB0751,172.18.30.80,FC-34-97-C2-12-10,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0792,172.18.30.251,3C-EC-EF-B0-84-93,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0759,172.18.31.110,2C-F0-5D-CC-E7-D2,[{DOS=1}] }, { SS-PC-MB0783,172.18.30.13,04-7C-16-00-2A-66,[{性能=1}] }, { SS-PC-MB0799,172.18.31.43,3C-EC-EF-B0-84-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0800,172.18.30.124,3C-EC-EF-B0-89-C5,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0804,172.18.30.236,3C-EC-EF-B0-8A-9D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0395,172.18.20.34,18-C0-4D-6C-66-B2,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0788,172.18.31.223,3C-EC-EF-B0-84-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0782,172.18.30.179,18-C0-4D-A9-A8-D6,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0761,172.18.30.113,2C-F0-5D-CC-E7-CB,[{DOS=1}] }, { SS-PC-MB0731,172.18.30.21,D8-5E-D3-73-DC-7C,[{系统掉电=1}] }, { SS-PC-MB0739,172.18.30.6,D8-BB-C1-D1-E2-DC,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0740,172.18.30.1,D8-BB-C1-D1-D1-C3,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0735,172.18.30.28,D8-BB-C1-D7-23-28,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0797,172.18.30.174,3C-EC-EF-B0-84-7F,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0385,172.18.20.229,A8-A1-59-27-C9-DB,[{系统掉电=1}] }, { SS-PC-MB0786,172.18.30.4,D8-5E-D3-73-DD-47,[{系统掉电=1}] }, { SS-PC-MB0732,172.18.30.111,D8-5E-D3-73-D0-27,[{系统掉电=1}] }, { SS-PC-MB0758,172.18.30.115,50-EB-F6-9C-AF-A0,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0555,172.18.20.114,F0-2F-74-2E-66-7D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0737,172.18.30.24,D8-BB-C1-D1-D1-C7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0738,172.18.30.104,D8-BB-C1-D1-D1-CD,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0798,172.18.31.235,3C-EC-EF-B0-8A-7D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0718,172.18.30.117,D8-BB-C1-9D-AD-44,[{低温=4}] }, { SS-PC-MB0803,172.18.31.74,3C-EC-EF-B0-8A-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0730,172.18.30.12,D8-5E-D3-73-DD-84,[{系统掉电=1}] }, { SS-PC-MB0733,172.18.30.97,D8-5E-D3-73-E0-F2,[{系统掉电=1}] }, { SS-PC-MB0724,172.18.30.158,D8-BB-C1-9D-AA-03,[{低温=4}] }, { SS-PC-MB0785,172.18.30.3,D8-5E-D3-73-DD-48,[{系统掉电=1}] }, { SS-PC-MB0774,172.18.30.20,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0388,172.18.21.84,18-C0-4D-6C-66-B5,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0772,172.18.30.52,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0773,172.18.30.14,18-C0-4D-AA-06-FF,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0771,172.18.30.50,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0776,172.18.30.67,18-C0-4D-A9-A2-B6,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0780,172.18.30.39,18-C0-4D-A9-A4-93,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0742,172.18.30.107,D8-BB-C1-D1-E2-D7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0736,172.18.30.46,D8-BB-C1-D1-E2-CA,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0370,172.18.21.240,00-E0-70-C4-2B-A3,[{MARS单盘=1}] }, { SS-PC-MB0380,172.18.20.193,A8-A1-59-26-8B-34,[{系统掉电=1}] }, { SS-PC-MB0807,172.18.31.152,3C-EC-EF-B0-89-57,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0728,172.18.31.241,3C-EC-EF-B0-2F-C7,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00010,172.18.31.57,04-7C-16-B9-36-FD,[{低温=4}] }, { PCIE-PC-MB00003,*************,04-7C-16-B9-36-E9,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00015,172.18.31.135,04-7C-16-B9-3D-62,[{低温=4}] }, { PCIE-PC-MB00012,172.18.31.191,04-7C-16-B9-36-F9,[{低温=4}] }, { SS-PC-MB0805,172.18.31.20,3C-EC-EF-B5-09-37,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00034,*************,08-BF-B8-32-90-B4,[{性能=1}] }, { PCIE-PC-MB00018,172.18.31.183,3C-EC-EF-B0-B4-CD,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00020,172.18.30.238,3C-EC-EF-B0-B4-D9,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00019,172.18.30.230,3C-EC-EF-B0-B4-D1,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00023,172.18.30.194,3C-EC-EF-B0-B4-D7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00021,172.18.30.138,3C-EC-EF-B0-B8-B7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00016,172.18.31.6,04-7C-16-B9-36-E8,[{低温=4}] }, { PCIE-PC-MB00035,172.18.31.213,08-BF-B8-39-E9-AB,[{性能=1}] }, { PCIE-PC-MB00030,172.18.31.112,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00031,172.18.12.35,3C-EC-EF-B0-B2-D3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00013,172.18.31.29,04-7C-16-B9-3D-E1,[{低温=4}] }, { PCIE-PC-MB00029,172.18.31.71,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0468,172.18.40.197,B4-2E-99-FD-D5-3A,[{品质测试_高温=4}, {高温=1}] }, { PCIE-PC-MB00014,172.18.31.69,04-7C-16-B9-36-EB,[{低温=4}] }, { SS-PC-MB0766,\f仚w\u0002,2C-F0-5D-CC-E7-CA,[{DOS=1}] }, { SS-PC-MB0762,172.18.30.112,2C-F0-5D-CC-E7-CD,[{DOS=1}] }, { SS-PC-MB0760,172.18.30.96,2C-F0-5D-CC-E8-1F,[{DOS=1}] }, { PCIE-PC-MB00041,172.18.31.89,7C-C2-55-7D-6A-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00042,172.18.30.178,7C-C2-55-7D-6C-D1,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00043,172.18.31.13,7C-C2-55-7D-74-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00045,172.18.30.135,7C-C2-55-7D-6D-4D,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00047,172.18.30.208,7C-C2-55-7D-71-0B,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00046,172.18.30.130,7C-C2-55-7D-6D-CD,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00044,172.18.31.8,7C-C2-55-7D-69-B9,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00032,172.18.30.242,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00054,*************,10-7C-61-0A-72-01,[{性能=1}] }, { PCIE-PC-MB00055,172.18.31.158,C8-7F-54-C5-F2-1F,[{性能=1}] }, { PCIE-PC-MB00036,172.18.30.255,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.11+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.11+08:00","@version":"1","message":"[6374] Plan63 need Num: 2 to test. SSV7_4X1_256G-4X4_1T_256GB left num:71 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.11+08:00","@version":"1","message":"subProduct PCIe flashName SSV7_4X1_256G-4X4_1T_256GB plan [Plan63] attrs 高温 belongTo Abel.Zou is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.112+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [PCIE-PC-MB00003, SS-PC-MB0709, SS-PC-MB0711, SS-PC-MB0713, SS-PC-MB0714, SS-PC-MB0715, SS-PC-MB0716, SS-PC-MB0468, SS-PC-MB0469, SS-PC-MB0470, SS-PC-MB0471, SS-PC-MB0512, SS-PC-MB0513, SS-PC-MB0514, SS-PC-MB0515]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.112+08:00","@version":"1","message":"PCIe Plan63不是测试所有样片的Plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.113+08:00","@version":"1","message":"sortedEntries: [SSD5_5=6, PCIE_6_1=4, PCIE_5_1=4, PCIE_6_2=1, PCIE_5_2=1]","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.119+08:00","@version":"1","message":"Plan63已存在使用机柜，在机柜SSD5_5下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.119+08:00","@version":"1","message":"Plan63已存在使用机柜，在机柜PCIE_6_1下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.119+08:00","@version":"1","message":"Plan63已存在使用机柜，在机柜PCIE_5_1下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.119+08:00","@version":"1","message":"Plan63已存在使用机柜，在机柜PCIE_6_2下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.119+08:00","@version":"1","message":"Plan63已存在使用机柜，在机柜PCIE_5_2下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.119+08:00","@version":"1","message":"rackList: [SSD5_5=6, PCIE_6_1=4, PCIE_5_1=4, PCIE_6_2=1, PCIE_5_2=1]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.119+08:00","@version":"1","message":"devices: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.12+08:00","@version":"1","message":"devices: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.12+08:00","@version":"1","message":"devices: [PCIE_5_1_03]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.12+08:00","@version":"1","message":"for index: 0 size: 1 sum: 4 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.12+08:00","@version":"1","message":"wait testNum: 2 assign sum: 4 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.12+08:00","@version":"1","message":"Plan63已分配的机柜范围内：在机柜PCIE_5_1下找到空闲的可使用设备：[PCIE_5_1_03]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.12+08:00","@version":"1","message":"[6374] plan:Plan63 use 1 pc: [{ PCIE-PC-MB00003,*************,04-7C-16-B9-36-E9,[{高温=4}, {品质测试_高温=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.12+08:00","@version":"1","message":"assignDevices [6374] - find 1 run devices:  {Plan63=[{ PCIE-PC-MB00003,*************,04-7C-16-B9-36-E9,[{高温=4}, {品质测试_高温=1}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.121+08:00","@version":"1","message":"工单[6374] flash SSV7_4X1_256G-4X4_1T_256GB , 参与此次Plan预分配的plan共有 [Plan63] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.138+08:00","@version":"1","message":" add 1 devices :[{ PCIE-PC-MB00003,*************,04-7C-16-B9-36-E9,[{高温=4}, {品质测试_高温=1}] }]  to plan:Plan63","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.159+08:00","@version":"1","message":"plan:Plan63 assign info update to ActualDeviceNum: 1, ExceptedSampleNum: 2","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.169+08:00","@version":"1","message":"[YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB] - Plan63 holdDevices  :[PlanDeviceEntity(id=276984, orderId=6374, planId=113581, planName=Plan63, ip=*************, mac=04-7C-16-B9-36-E9, no=PCIE-PC-MB00003, position=PCIE_5_1_03, score=200, owner=, testNum=2, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.198+08:00","@version":"1","message":"lock device:04-7C-16-B9-36-E9 to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan63 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.215+08:00","@version":"1","message":"[c5b0ef0] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.384+08:00","@version":"1","message":"lockDevice with ipList:[*************] - macList:[04-7C-16-B9-36-E9],no:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.392+08:00","@version":"1","message":"自动分配PCIE-PC-MB00003(*************)给SSV7_4X1_256G-4X4_1T_256GB下的Plan63","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.392+08:00","@version":"1","message":"add 1 devices to plan: Plan63 ,expect num: 2, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.392+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6374, flash:SSV7_4X1_256G-4X4_1T_256GB, planEntity:OrderPlanEntity(id=113581, orderId=6374, name=Plan63, status=QUEUE, priority=85)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.418+08:00","@version":"1","message":"测试单: YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB plan: Plan63已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.75+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.751+08:00","@version":"1","message":"此次分配flash批次 SSV7_4X1_256G-4X4_1T_256GB 下的Plan共消耗 2 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.755+08:00","@version":"1","message":"更新Flash:SSV7_4X1_256G-4X4_1T_256GB的样片数量从71变更为69","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:03.778+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7ba78e07c600b0bb","spanId":"7ba78e07c600b0bb","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:07:18.802+08:00","@version":"1","message":"Flash:SSV7_4X1_256G-4X4_1T_256GB下的Plan57","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06cd7266c4d63021","spanId":"06cd7266c4d63021","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:07:18.802+08:00","@version":"1","message":"Flash:SSV7_4X1_256G-4X4_1T_256GB下的Plan57使用设备:[*************, ************] -[18-C0-4D-A5-7F-8B, 18-C0-4D-A6-71-53] 开始测试!","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06cd7266c4d63021","spanId":"06cd7266c4d63021","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:07:18.802+08:00","@version":"1","message":"startTestPlan with PlanTestParams(planName=Plan57, no=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, group=0, bAutoLoc=true, ipList=[*************, ************], macList=[18-C0-4D-A5-7F-8B, 18-C0-4D-A6-71-53], product=PCIe, planPath=\\\\************\\软件工具\\03内部工具版本\\eSee\\SSD\\PCIe\\Mars plan\\TestPlanV31.05.005-PCIe9205-V9.0-timeout100.plan, marsPath=\\\\************\\软件工具\\03内部工具版本\\eSee\\SSD\\PCIe\\Mars版本\\Mars_40.01.4.29_0712.zip, mpPath=, username=Abel.Zou)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06cd7266c4d63021","spanId":"06cd7266c4d63021","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:07:18.811+08:00","@version":"1","message":"Product:PCIe testPerson:Abel.Zou order no:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB plan name:Plan57  macList:[18-C0-4D-A5-7F-8B, 18-C0-4D-A6-71-53]","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06cd7266c4d63021","spanId":"06cd7266c4d63021","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:07:18.812+08:00","@version":"1","message":"[5f5908a8] HTTP POST http://ereport.yeestor.com/wo/plan/test","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06cd7266c4d63021","spanId":"06cd7266c4d63021","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:07:19.013+08:00","@version":"1","message":"startTestPlan with YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB's Plan57 got data:HandleResp(code=0, data=PlanTestRespModel(successLst=[PlanTestRespModel.DeviceTestResult(ip=*************, no=MB0534, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=MB0533, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06cd7266c4d63021","spanId":"06cd7266c4d63021","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:07:19.015+08:00","@version":"1","message":"Plan57 启动测试成功, resp: PlanTestRespModel(successLst=[PlanTestRespModel.DeviceTestResult(ip=*************, no=MB0534, reason=), PlanTestRespModel.DeviceTestResult(ip=************, no=MB0533, reason=)], failLst=[])","logger_name":"com.yeestor.work_order.service.job.StartTestJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06cd7266c4d63021","spanId":"06cd7266c4d63021","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:11:53.565+08:00","@version":"1","message":"execute:PCIe_10-7C-61-0A-72-01_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113588 mac:10-7C-61-0A-72-01","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d247318902847da3","spanId":"d247318902847da3","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:11:53.566+08:00","@version":"1","message":"倒计时结束，即将释放设备PCIE-PC-MB00054[10-7C-61-0A-72-01]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d247318902847da3","spanId":"d247318902847da3","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:11:53.566+08:00","@version":"1","message":"需要关机的设备编号有: [PCIE-PC-MB00054]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d247318902847da3","spanId":"d247318902847da3","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:11:53.566+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[10-7C-61-0A-72-01])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d247318902847da3","spanId":"d247318902847da3","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:11:53.566+08:00","@version":"1","message":"[73b3b16f] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d247318902847da3","spanId":"d247318902847da3","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:11:53.704+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[10-7C-61-0A-72-01]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=10-7C-61-0A-72-01, pc_no=MB00054, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d247318902847da3","spanId":"d247318902847da3","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:11:53.705+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d247318902847da3","spanId":"d247318902847da3","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.589+08:00","@version":"1","message":"execute:AutoAssignDevice_GE_U2  map key:[subProduct, product] value:org.quartz.utils.DirtyFlagMap$DirtyFlagCollection@da60e4f ","logger_name":"com.yeestor.work_order.service.job.AutoAssignDeviceJob","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8371e10285d74802","spanId":"8371e10285d74802","context":"QueueService","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.593+08:00","@version":"1","message":"AutoAssignDeviceJob execute product:GE subProduct:U2 orderCount:3","logger_name":"com.yeestor.work_order.service.job.AutoAssignDeviceJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8371e10285d74802","spanId":"8371e10285d74802","context":"QueueService","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.596+08:00","@version":"1","message":"[U2] start check order's plan . find 0 orders","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8371e10285d74802","spanId":"8371e10285d74802","context":"QueueService","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.596+08:00","@version":"1","message":"[U2] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8371e10285d74802","spanId":"8371e10285d74802","context":"QueueService","no":"6374","traceType":"启动测试","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.849+08:00","@version":"1","message":"execute:PCIe_08-BF-B8-32-90-B4_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113588 mac:08-BF-B8-32-90-B4","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"221bd0f5d846f78b","spanId":"221bd0f5d846f78b","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.851+08:00","@version":"1","message":"倒计时结束，即将释放设备PCIE-PC-MB00034[08-BF-B8-32-90-B4]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"221bd0f5d846f78b","spanId":"221bd0f5d846f78b","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.851+08:00","@version":"1","message":"需要关机的设备编号有: [PCIE-PC-MB00034]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"221bd0f5d846f78b","spanId":"221bd0f5d846f78b","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.851+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[08-BF-B8-32-90-B4])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"221bd0f5d846f78b","spanId":"221bd0f5d846f78b","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.851+08:00","@version":"1","message":"[4ecac523] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"221bd0f5d846f78b","spanId":"221bd0f5d846f78b","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.986+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[08-BF-B8-32-90-B4]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=08-BF-B8-32-90-B4, pc_no=MB00034, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"221bd0f5d846f78b","spanId":"221bd0f5d846f78b","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:12:02.986+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"221bd0f5d846f78b","spanId":"221bd0f5d846f78b","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:13:50.406+08:00","@version":"1","message":"execute:PCIe_D8-BB-C1-9D-A9-7A_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113580 mac:D8-BB-C1-9D-A9-7A","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d5ddb3c50e8c335","spanId":"0d5ddb3c50e8c335","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:13:50.407+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0719[D8-BB-C1-9D-A9-7A]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d5ddb3c50e8c335","spanId":"0d5ddb3c50e8c335","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:13:50.407+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0719]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d5ddb3c50e8c335","spanId":"0d5ddb3c50e8c335","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:13:50.407+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[D8-BB-C1-9D-A9-7A])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d5ddb3c50e8c335","spanId":"0d5ddb3c50e8c335","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:13:50.408+08:00","@version":"1","message":"[64367b47] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d5ddb3c50e8c335","spanId":"0d5ddb3c50e8c335","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:13:50.547+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[D8-BB-C1-9D-A9-7A]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=D8-BB-C1-9D-A9-7A, pc_no=MB0719, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d5ddb3c50e8c335","spanId":"0d5ddb3c50e8c335","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:13:50.547+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0d5ddb3c50e8c335","spanId":"0d5ddb3c50e8c335","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:14:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:14:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=71)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.61+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.634+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.65+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.665+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.679+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.693+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.709+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.723+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.724+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.724+08:00","@version":"1","message":"执行预分配前共有71颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.724+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.724+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.724+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.724+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.724+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:14:02.724+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"77a01f6e1cf7fa91","spanId":"77a01f6e1cf7fa91","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:17:45.87+08:00","@version":"1","message":"execute:PCIe_D8-BB-C1-9D-A9-D7_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113578 mac:D8-BB-C1-9D-A9-D7","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"efa254bcb095eb77","spanId":"efa254bcb095eb77","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:17:45.872+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0712[D8-BB-C1-9D-A9-D7]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"efa254bcb095eb77","spanId":"efa254bcb095eb77","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:17:45.873+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0712]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"efa254bcb095eb77","spanId":"efa254bcb095eb77","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:17:45.873+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[D8-BB-C1-9D-A9-D7])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"efa254bcb095eb77","spanId":"efa254bcb095eb77","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:17:45.874+08:00","@version":"1","message":"[1eb8823] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"efa254bcb095eb77","spanId":"efa254bcb095eb77","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:17:46.014+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[D8-BB-C1-9D-A9-D7]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=D8-BB-C1-9D-A9-D7, pc_no=MB0712, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"efa254bcb095eb77","spanId":"efa254bcb095eb77","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:17:46.014+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"efa254bcb095eb77","spanId":"efa254bcb095eb77","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:18:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:18:02.637+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=71)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.637+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.683+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.7+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.715+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.729+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.745+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.745+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.745+08:00","@version":"1","message":"执行预分配前共有71颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.746+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.746+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.746+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.746+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.746+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.746+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ed6f84f7efb0b316","spanId":"ed6f84f7efb0b316","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.617+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:22:02.62+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=71)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.62+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.641+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.655+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.67+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.684+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.698+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.714+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.728+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.729+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.729+08:00","@version":"1","message":"执行预分配前共有71颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.729+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.729+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.729+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.729+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.729+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:22:02.729+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af43a025a6804d74","spanId":"af43a025a6804d74","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.62+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:02.621+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=71)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.621+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.659+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.682+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.705+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.719+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.733+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.746+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.76+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.76+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.76+08:00","@version":"1","message":"执行预分配前共有71颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.76+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.76+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.76+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.76+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.76+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.761+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f506890ca152090","spanId":"2f506890ca152090","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:34:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=71)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.61+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.642+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.656+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.671+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.685+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.698+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.716+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.73+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.73+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.73+08:00","@version":"1","message":"执行预分配前共有71颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.73+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.73+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.73+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.73+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.73+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:34:02.73+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91ec21d49d9546c7","spanId":"91ec21d49d9546c7","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:02.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=72)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.619+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.637+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.683+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.701+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.717+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.73+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.744+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.758+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.758+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.758+08:00","@version":"1","message":"执行预分配前共有72颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.758+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.759+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.759+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.759+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.759+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.759+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9d1a2c266b899c1d","spanId":"9d1a2c266b899c1d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:04.738+08:00","@version":"1","message":"execute:PCIe_18-C0-4D-A5-7F-8B_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113577 mac:18-C0-4D-A5-7F-8B","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ca4c87d159fa346","spanId":"2ca4c87d159fa346","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:46:04.739+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0534[18-C0-4D-A5-7F-8B]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ca4c87d159fa346","spanId":"2ca4c87d159fa346","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:46:04.739+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0534]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ca4c87d159fa346","spanId":"2ca4c87d159fa346","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:46:04.74+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[18-C0-4D-A5-7F-8B])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ca4c87d159fa346","spanId":"2ca4c87d159fa346","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:46:04.74+08:00","@version":"1","message":"[1f942001] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ca4c87d159fa346","spanId":"2ca4c87d159fa346","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:46:04.878+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[18-C0-4D-A5-7F-8B]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=18-C0-4D-A5-7F-8B, pc_no=MB0534, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ca4c87d159fa346","spanId":"2ca4c87d159fa346","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:46:04.878+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ca4c87d159fa346","spanId":"2ca4c87d159fa346","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T12:50:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:50:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=72)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.61+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.627+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.663+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.691+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.705+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.719+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.719+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.719+08:00","@version":"1","message":"执行预分配前共有72颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.72+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.72+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.72+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.72+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.72+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:50:02.72+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38a7723f99628718","spanId":"38a7723f99628718","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:54:02.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=72)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.619+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.661+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.684+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.699+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.713+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.727+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.742+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.758+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.758+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.758+08:00","@version":"1","message":"执行预分配前共有72颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.758+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.759+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.759+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.759+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.761+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.761+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"28c39a3ebaa5fd2c","spanId":"28c39a3ebaa5fd2c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:06:02.631+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=72)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.631+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.67+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.685+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.713+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.731+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.747+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.761+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.761+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.761+08:00","@version":"1","message":"执行预分配前共有72颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.762+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.762+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.762+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.763+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.763+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.763+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"39016753d3fa5416","spanId":"39016753d3fa5416","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.611+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:14:02.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=72)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.613+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.643+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.657+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.671+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.686+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.706+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.726+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.744+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.744+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.744+08:00","@version":"1","message":"执行预分配前共有72颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.744+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.744+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.744+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.744+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.745+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:14:02.745+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49047582946d2c59","spanId":"49047582946d2c59","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.623+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.648+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.694+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.709+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.725+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.74+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.755+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.756+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.756+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.756+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.757+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.757+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.757+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.757+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.758+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8af2ff3975e38143","spanId":"8af2ff3975e38143","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:25:24.631+08:00","@version":"1","message":"execute:PCIe_18-C0-4D-A6-71-53_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113577 mac:18-C0-4D-A6-71-53","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a999c783cedc7ba0","spanId":"a999c783cedc7ba0","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T13:25:24.632+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0533[18-C0-4D-A6-71-53]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a999c783cedc7ba0","spanId":"a999c783cedc7ba0","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T13:25:24.633+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0533]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a999c783cedc7ba0","spanId":"a999c783cedc7ba0","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T13:25:24.633+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[18-C0-4D-A6-71-53])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a999c783cedc7ba0","spanId":"a999c783cedc7ba0","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T13:25:24.633+08:00","@version":"1","message":"[3a93d3d] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a999c783cedc7ba0","spanId":"a999c783cedc7ba0","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T13:25:24.778+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[18-C0-4D-A6-71-53]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=18-C0-4D-A6-71-53, pc_no=MB0533, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a999c783cedc7ba0","spanId":"a999c783cedc7ba0","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T13:25:24.778+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a999c783cedc7ba0","spanId":"a999c783cedc7ba0","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T13:30:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:02.622+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.622+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.691+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.705+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.719+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.732+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.746+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.761+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.761+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.761+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.761+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.761+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.761+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.762+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.762+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.762+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3a24bd896ece13c4","spanId":"3a24bd896ece13c4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:38:02.614+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.614+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.635+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.652+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.67+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.684+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.699+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.713+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.731+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.731+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.731+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.731+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.731+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.732+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.732+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.732+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:38:02.732+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26ae8745f7732467","spanId":"26ae8745f7732467","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:02.622+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.622+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.646+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.676+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.693+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.709+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.723+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.737+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.754+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.754+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.754+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.754+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.754+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.754+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.754+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.754+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.755+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f48cc58b37a23a61","spanId":"f48cc58b37a23a61","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:50:02.611+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.611+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.628+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.647+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.691+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.705+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.727+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.727+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.727+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.728+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.728+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.728+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.728+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.728+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:50:02.728+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65242d7d6b24c2fa","spanId":"65242d7d6b24c2fa","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.618+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:54:02.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.619+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.635+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.663+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.677+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.696+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.711+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.724+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.725+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.725+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.725+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.725+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.725+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.725+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.725+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.725+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d73920a296344782","spanId":"d73920a296344782","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:02:02.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.612+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.635+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.679+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.694+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.711+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.726+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.726+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.726+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.727+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.727+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.727+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.727+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.727+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:02:02.728+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fa0a59d5523747d4","spanId":"fa0a59d5523747d4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:02.627+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.627+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.661+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.683+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.704+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.719+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.734+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.75+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.763+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.764+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.764+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.764+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.764+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.764+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.764+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.764+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.764+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a32132686db929c1","spanId":"a32132686db929c1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.611+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:10:02.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.612+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.669+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.684+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.698+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.715+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.729+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.729+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.73+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.73+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.73+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.73+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.73+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.73+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:10:02.73+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"92cfab563abe5bae","spanId":"92cfab563abe5bae","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:14:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=73)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.613+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.637+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.653+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.667+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.681+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.696+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.712+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.726+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.727+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.727+08:00","@version":"1","message":"执行预分配前共有73颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.727+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.727+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.727+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.727+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.728+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:14:02.728+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fae155a015f4e7a4","spanId":"fae155a015f4e7a4","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:18:02.628+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=77)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.628+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.651+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.676+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.696+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.711+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.725+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.739+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.754+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.754+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113592, orderId=6374, name=Plan19, status=QUEUE, priority=50), OrderPlanEntity(id=113593, orderId=6374, name=Plan24, status=QUEUE, priority=50), OrderPlanEntity(id=113594, orderId=6374, name=Plan25, status=QUEUE, priority=50), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.754+08:00","@version":"1","message":"执行预分配前共有77颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.754+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.754+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.754+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.754+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.754+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.755+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d069eecd946f8306","spanId":"d069eecd946f8306","context":"QueueService","no":"6374","traceType":"分配设备"}
