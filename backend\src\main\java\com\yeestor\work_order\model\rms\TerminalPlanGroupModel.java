package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "TerminalPlanGroupModel", description = "终端测试Plan合集的信息，参考现有的Plan合集方式")
public class TerminalPlanGroupModel {
    @ApiModelProperty("合集的名称,可能会根据名字来取默认合集,需要唯一")
    private String name;

    @ApiModelProperty("合集版本,当合集名称与版本一致时,则表示这个合集为默认合集.")
    private String version;

    @ApiModelProperty("合集包含的Plan 列表。")
    private List<TerminalPlanModel> plans;
}
