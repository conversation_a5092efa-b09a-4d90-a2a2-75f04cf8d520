package com.yeestor.work_order.model.http.req.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(value = "AddDeviceParams", description = "Plan 增加电脑的参数")
public class AddDeviceParams {

    @Min(value=1,message = "无效的工单ID")
    @ApiModelProperty("工单ID")
    private long orderId;

    @NotBlank(message = "flash 批次号不能为空")
    @ApiModelProperty("flash批次")
    private String flash;


    @Min(value=1,message = "无效的Plan ID")
    @ApiModelProperty("Plan ID")
    private long planId ;

    @ApiModelProperty("设备列表")
    @NotEmpty(message = "选择的设备 不能为空")
    private List<String > devices;

    @ApiModelProperty("设备的Mac列表")
    @NotEmpty(message = "选择的设备 不能为空")
    private List<String > macList;


}
