package com.yeestor.work_order.service.product.impl;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.http.req.Person;
import com.yeestor.work_order.model.rms.DeviceModel;
import com.yeestor.work_order.service.product.ProductContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class SATAProductService
        extends DefaultProductService
{

    private final ProductContext productContext;

    @PostConstruct
    private void init(){
        productContext.registerProductService("PCIe",this);
        productContext.registerProductService("SATA",this);
    }


    @Override
    public void peekDeviceOnPlanStatusChange(OrderFlashEntity flashEntity, OrderPlanEntity planEntity, PlanDeviceEntity deviceEntity) {
        super.peekDeviceOnPlanStatusChange(flashEntity, planEntity, deviceEntity);
        if(deviceEntity.getStatus() == PlanDeviceEntity.Status.FINISHED_SUCCESS){
            deviceEntity.setReleaseAt(System.currentTimeMillis());
        }
    }


    @Override
    public void autoCompletePlan(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, OrderPlanEntity planEntity) {
        // SATA 暂时不需要释放设备的任务。
        planService.checkDeviceReleaseStatus(orderEntity,flashEntity, planEntity);
    }

    @Override
    public Comparator<DeviceModel> deviceComparator() {
        return Comparator.comparing(DeviceModel::getScore)
                .thenComparing(DeviceModel::getPosition) ;
    }

    @Override
    public List<OrderPlanEntity> fetchNeedTestPlanList(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {
        log.info("SSD assign subProduct: {} ", orderEntity.getSubProduct());
        List<Person> testerIdList = new ArrayList<>(flashEntity.getTesters());
        // 查出这个批次下所有的plan列表。
        List<OrderPlanEntity> allPlanEntityList = planService.fetchAllPlanByFlash(orderEntity.getId(), flashEntity.getFlash());

        // 此次分配进程中涉及到的Flash批次下所有可分配Plan
        List<OrderPlanEntity> canAssignList = queueService.fetchCanAssignPlanList(allPlanEntityList);

        // Flash 剩余样片数量
        final AtomicInteger leftFlashNum = new AtomicInteger(flashEntity.getLeftNum());
        log.info("执行预分配前共有{}颗样片", leftFlashNum.get());
        List<OrderPlanEntity> assignList = new ArrayList<>();

        testerIdList.forEach(user -> {
            // 此次分配的进程中，某个测试人员剩余的Plan可分配额度
            int leftPlanNum = planService.leftPlanCountByUserId(allPlanEntityList, user.getId());
            // 此次分配进程中，某一个测试人员所有可分配的Plan
            List<OrderPlanEntity> userCanAssignList = canAssignList.stream()
                    .filter(p -> user.getId().equals(p.getBelongTo()))
                    .filter(p -> !p.isDisabled()).collect(Collectors.toList());
            log.info("此次Plan分配的可分配额度为0！");
            // 参与此次预分配的Plan
            List<OrderPlanEntity> testList = queueService.getValidPlanList(allPlanEntityList, leftFlashNum.get(), leftPlanNum, userCanAssignList);
            // 某一个测试人员此次分配消耗的Plan数量
            int testUseNum = testList.stream().mapToInt(OrderPlanEntity::getTestNum).sum();
            leftFlashNum.addAndGet(-testUseNum);
            assignList.addAll(testList);
            if (testList.size() > 0) {
                log.info("[预分配]分配给测试人[{}]的Plan有 {}, 一共使用了 {} 颗样片, 还剩下样片 {} 颗",
                        user.getName(),
                        testList.stream().map(OrderPlanEntity::getName).collect(Collectors.toList()),
                        testUseNum,
                        leftFlashNum.get());
            }
        });
        log.info("assignList: {}", assignList);
        return assignList;
    }
}
