package com.yeestor.work_order.service.plan;

import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.req.order.OrderFlashConfirmV2Req;
import com.yeestor.work_order.model.http.req.terminal.AddTerminalPlanParams;
import com.yeestor.work_order.model.http.req.terminal.PlanPlatformParams;
import com.yeestor.work_order.model.http.req.terminal.PlatformInfo;
import com.yeestor.work_order.model.http.resp.order.PlanCaseItemVO;
import com.yeestor.work_order.model.http.resp.order.PlanDetailVO;
import com.yeestor.work_order.model.http.resp.order.PlatformDetailVO;
import com.yeestor.work_order.model.rms.*;
import com.yeestor.work_order.repository.*;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.device.ScrcpyService;
import com.yeestor.work_order.service.job.CheckTerminalPlanStartStatusJob;
import com.yeestor.work_order.service.job.JobService;
import com.yeestor.work_order.service.job.StartTerminalTestJob;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.utils.DingTalkUtils;
import com.yeestor.work_order.utils.RMSApis;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.quartz.DateBuilder;
import org.quartz.JobDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

import static com.yeestor.work_order.entity.PlanPlatformEntity.COMPLETED_STATUS_LIST;
import static com.yeestor.work_order.entity.PlanPlatformEntity.RUN_STATUS_LIST;
import static com.yeestor.work_order.model.rms.TerminalDeviceModel.FREE_STATUS;


@Slf4j
@Service
@RequiredArgsConstructor
public class TerminalService {
    @Setter(onMethod_ = {@Autowired, @Lazy})
    private PlanService planService;

    private final OrderService orderService;
    private final OrderPlanRepository planRepository;
    private final DataChangeListener dataChangeListener;
    private final TempPlanRepository tempPlanRepository;
    private final PlanPlatformRepository planPlatformRepository;

    @Setter(onMethod_ = {@Autowired, @Lazy})
    private ScrcpyService scrcpyService;
    private final FlashService flashService;
    private final JobService jobService;
    private final RMSApis rmsApis;
    private final PlanHistoryRepository planHistoryRepository ;
    private final PlatformCaseRepository platformCaseRepository;
    private final NotificationService notificationService;
    public List<PlanPlatformEntity> fetchPlatformListByPlanId(long planId){
        return planPlatformRepository.findAllByPlanId(planId);
    }

    /**
     * 获取终端测试Case列表信息
     * @param product 产品线
     * @param subProduct 子产品
     * @return case列表
     */
    public List<CaseModel> fetchTerminalCaseList(String product, String subProduct) {
        return rmsApis.getTerminalCaseList(product, subProduct);
    }

    /**
     * 在RMS中获取指定产品的对应Plan信息
     *
     * @param product    产品线
     * @param subProduct 子产品
     * @return plan列表
     */
    public List<TerminalPlanModel> fetchTerminalPlanList(String product, String subProduct, String tool) {
        return rmsApis.getTerminalPlanList(product, subProduct, tool);
    }

    public List<TerminalPlanGroupModel> fetchTerminalPlanGroups(String product, String subProduct, String tool) {
        return rmsApis.getTerminalPlanGroups(product, subProduct, tool);
    }

    /**
     * 获取产品线终端测试中的空闲平台信息
     *
     * @param subProduct 产品线
     * @return 空闲平台
     */
    public List<TerminalDeviceModel> fetchPlatformFreeList(String subProduct) {
        List<TerminalDeviceModel> freeList = rmsApis.getAllPlatformList(subProduct);
        return freeList.stream().filter(p -> FREE_STATUS.contains(p.getStatus()))
                .collect(Collectors.toList());
    }

    public List<TerminalDeviceModel> fetchPlanTestSupportList(List<String> attrs, List<TerminalDeviceModel> modelList){
        return modelList.stream().filter(model -> {
            Set<String> attrModelList = new HashSet<>(model.getAttrModelList());
            if(attrs.size() == 0){
                return true;
            }
            return attrModelList.containsAll(attrs);
        }).collect(Collectors.toList());
    }

    /**
     * 获取满足当前属性测试的空闲测试平台
     *
     * @param platformList 当前可用的空闲测试平台
     * @param attrs        测试属性
     * @return　条件满足的测试平台
     */
    public List<TerminalDeviceModel> fetchPlanFreePlatformList(List<TerminalDeviceModel> platformList, List<String> attrs) {
        if (attrs.size() == 0) {
            return platformList;
        }
        return fetchPlanTestSupportList(attrs, platformList);
    }

    /**
     * 通过plan id、Mac、number 查询对应的平台信息
     *
     * @param planId plan id
     * @param mac    Mac地址
     * @param number 终端平台编号
     * @return 平台信息
     */
    public PlanPlatformEntity fetchPlatformByIdAndMacAndNumber(long planId, String mac, String number) {

        List<PlanPlatformEntity> platformEntities = planPlatformRepository.findAllByPlanIdAndMacAndNumber(planId, mac, number);
        String msg = "PlanID: " + planId + " mac: " + mac + " number: " + number ;
        if (platformEntities.isEmpty()) {
            throw new DataNotFoundException(msg + "下找不到平台信息！");
        }
        if (platformEntities.size() > 1) {
            // 已经 release 的 platform 需要过滤掉
            platformEntities = platformEntities.stream().filter(p -> !p.isRelease()).collect(Collectors.toList());

            if (platformEntities.size() > 1) {
                String errMsg = msg + "下存在多个设备!";
                log.warn(errMsg);
                notificationService.sendErrorNotification(errMsg);
                throw new DataNotFoundException(errMsg);
            }
        }

        return platformEntities.get(0);
    }

    /**
     * 通过平台信息获取测试参数
     *
     * @param platformEntityList 平台信息列表
     * @return 测试参数
     */
    public List<TerminalModel> fetchTestModelByPlatformList(List<PlanPlatformEntity> platformEntityList) {
        // 将运行中需要停止的平台信息
        Map<String, List<PlanPlatformEntity>> macToEntitiesMap = platformEntityList.stream()
                .collect(Collectors.groupingBy(PlanPlatformEntity::getMac));

        // 获取测试中的终端参数
        List<TerminalModel> terminalList = new ArrayList<>();
        for (Map.Entry<String, List<PlanPlatformEntity>> entry : macToEntitiesMap.entrySet()) {
            String mac = entry.getKey();
            List<String> numberList = entry.getValue().stream().map(PlanPlatformEntity::getNumber).collect(Collectors.toList());
            TerminalModel model = new TerminalModel();
            model.setNumberList(numberList);
            model.setMac(mac);
            terminalList.add(model);
        }
        return terminalList;
    }

    /**
     * 添加终端测试Plan
     *
     * @param userDetail    操作人信息
     * @param orderId       工单id
     * @param flashEntity   flash实体类
     * @param planList      需要添加的Plan列表
     * @param planModelList plan模板列表
     */
    public void addTerminalPlanToFlash(
            OAuthUserDetail userDetail,
            long orderId,
            OrderFlashEntity flashEntity,
            List<OrderFlashConfirmV2Req.PlanItem> planList,
            List<TerminalPlanModel> planModelList
    ) {
        // 将 planList 转换为基于 name 的 Map，以便快速查找
        Map<String, OrderFlashConfirmV2Req.PlanItem> planItemMap = planList.stream()
                .collect(Collectors.toMap(
                        OrderFlashConfirmV2Req.PlanItem::getName,
                        item -> item,
                        (existing, replacement) -> existing // 处理重复键的情况
                ));

        // 使用 map 进行匹配，并设置属性
        List<TerminalPlanModel> allPlanList = planModelList.stream()
                .filter(planModel -> planItemMap.containsKey(planModel.getName()))
                .peek(planModel -> {
                    OrderFlashConfirmV2Req.PlanItem item = planItemMap.get(planModel.getName());
                    planModel.setBelongTo(item.getTestUserID());
                    planModel.setBelongToPerson(item.getTestUserName());
                    planModel.setType(item.getType());
                    planModel.setPhase(item.getPhase());
                })
                .collect(Collectors.toList());

        for (TerminalPlanModel planModel : allPlanList) {
            OrderPlanEntity planEntity = planModel.toEntity(orderId, flashEntity);
            planEntity.setAddedBy(userDetail.getUid());
            planEntity.setAddedPerson(userDetail.getUsername());
            OrderPlanEntity result = planRepository.save(planEntity);

            dataChangeListener.onDataChange(DataChangeEvent.builder()
                    .type(DataChangeEvent.Type.PLAN_ADDED)
                    .orderId(orderId)
                    .flash(flashEntity.getFlash())
                    .planId(result.getId())
                    .build()
            );
        }
    }

    public List<PlatformDetailVO> getPlanTerminalVO(OrderPlanEntity planEntity){
        List<PlanPlatformEntity> platformEntityList = fetchPlatformListByPlanId(planEntity.getId());
        return platformEntityList.stream()
                .map(entity -> {
                    PlatformDetailVO detailVO = new PlatformDetailVO();
                    detailVO.setInfo(entity);
                    List<PlatformCaseEntity> caseEntityList = platformCaseRepository.findByPlatformId(entity.getId());
                    List<PlanCaseItemVO> caseItemVOList = caseEntityList.stream().map(item -> {
                        PlanCaseItemVO caseItemVO = new PlanCaseItemVO();
                        caseItemVO.setCaseInfo(item);
                        return caseItemVO;
                    }).collect(Collectors.toList());
                    detailVO.setCaseList(caseItemVOList);
                    return detailVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取当前终端测试Plan的信息
     * @param planEntity plan实体类
     * @return 需要返回的信息
     */
    public PlanDetailVO fetchTerminalPlanInfo(OrderPlanEntity planEntity) {
        PlanDetailVO planDetail = PlanDetailVO.of(planEntity);

        List<PlatformDetailVO> platformDetailVOS = getPlanTerminalVO(planEntity);
        planDetail.setPlatforms(platformDetailVOS);

        // 查询临时plan
        if (planEntity.getTempPlanId() != null) {
            TempPlanEntity tempPlanEntity = tempPlanRepository.findById(planEntity.getTempPlanId()).orElse(null);
            assert tempPlanEntity != null;
            planDetail.setTitle(tempPlanEntity.getTitle());
            planDetail.setOwnerName(tempPlanEntity.getOwnerName());
        }

        return planDetail;
    }

    /**
     * 获取 plan 的详细信息数据.
     *
     * @param temPlan     设备归属产品线
     * @param orderEntity 工单信息
     * @param userDetail  用户登录信息
     */
    @Transactional
    public TempPlanEntity saveTerminalTemPlan(AddTerminalPlanParams temPlan, WorkOrderEntity orderEntity, OAuthUserDetail userDetail) {
        // 统计今日指定产品已经创建了多少个plan.
        int count = tempPlanRepository.countBySubProductAndCreatedAtBetween(
                orderEntity.getSubProduct(),
                DateTime.now().withTimeAtStartOfDay().toDate().getTime(),
                DateTime.now().toDate().getTime()
        );
        String name = String.format("%s%02d", DateTime.now().toString("yyyMMdd"), count + 1);
        log.info("{}下今日已有{}个临时Plan, 生成Plan的名称为:{}", temPlan.getSubProduct(), count, name);

        TempPlanEntity tempPlanEntity = new TempPlanEntity();
        tempPlanEntity.setTitle(temPlan.getTitle());
        tempPlanEntity.setName(name);
        tempPlanEntity.setFeature(temPlan.getDesc());
        tempPlanEntity.setOrderId(temPlan.getOrderId());
        tempPlanEntity.setPriority(0);
        tempPlanEntity.setType(1); // 临时Plan 都是手动plan
        tempPlanEntity.setProduct(orderEntity.getProduct());
        tempPlanEntity.setSubProduct(orderEntity.getSubProduct());
        tempPlanEntity.setTestNum(0);
        tempPlanEntity.setVersionType(orderEntity.getVersionType());
        tempPlanEntity.setScripts(String.join(";", Optional.ofNullable(temPlan.getCaseList()).orElse(new ArrayList<>())));
        if (temPlan.getOwner() != null) {
            tempPlanEntity.setOwnerId(temPlan.getOwner().getId());
            tempPlanEntity.setOwnerName(temPlan.getOwner().getName());
        }

        tempPlanEntity.setAddedBy(userDetail.getUid());
        tempPlanEntity.setAddedPerson(userDetail.getUsername());
        tempPlanEntity.setCreatedAt(System.currentTimeMillis());
        tempPlanEntity.setUpdatedAt(System.currentTimeMillis());

        return tempPlanRepository.save(tempPlanEntity);
    }

    /**
     * Plan添加手机平台信息
     *
     * @param orderEntity 工单信息
     * @param flashEntity     Flash实体类
     * @param planEntity       plan实体类
     * @param platformInfoList 需要添加的平台列表
     * @param userDetail       添加操作人员
     */
    @Transactional
    public void addPlatformToPlan(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            List<PlatformInfo> platformInfoList,
            OAuthUserDetail userDetail
    ) {
        List<TerminalDeviceModel> platformList = fetchPlatformFreeList(orderEntity.getSubProduct());
        // 创建基于 mac 和 number 组合的查找表
        Set<String> platformSet = platformInfoList.stream()
                .map(info -> info.getMac() + ":" + info.getNumber())
                .collect(Collectors.toSet());

        // Plan下的旧终端平台信息
        List<PlanPlatformEntity> oldEntityList = planPlatformRepository.findAllByPlanId(planEntity.getId());
        List<PlanPlatformEntity> platformEntityList = platformList.stream()
                .filter(device -> platformSet.contains(device.getMac() + ":" + device.getNo()))
                .map(model -> {
                    log.info("orderFlashNo: {} number: {}", flashEntity.getOrderFlashNo(), model.getNo());
                    PlanPlatformEntity entity = oldEntityList
                            .stream()
                            .filter(d-> d.isSamePlatform(model.getMac(), model.getNo()))
                            .findFirst()
                            .map(PlanPlatformEntity::duplicateEmpty)
                            .orElse(TerminalDeviceModel.toEntity(model));

                    entity.setStatus(PlanPlatformEntity.Status.OCCUPIED);
                    entity.setOrderId(planEntity.getOrderId());
                    entity.setPlanId(planEntity.getId());
                    entity.setPlanName(planEntity.getName());
                    entity.setStartAt(null);
                    entity.setEndAt(null);
                    entity.setFailReason(null);
                    entity.setAddedBy(userDetail.getUid());
                    entity.setAddedPerson(userDetail.getUsername());
                    entity.setCreatedAt(System.currentTimeMillis());
                    entity.setUpdatedAt(System.currentTimeMillis());
                    return entity;
                }).collect(Collectors.toList());

        List<TerminalModel> terminalList = fetchTestModelByPlatformList(platformEntityList);
        // 锁定此处测试过程中的平台
        scrcpyService.holdLockPlatform(
                flashEntity.getOrderFlashNo(),
                terminalList,
                userDetail
        );

        if (planEntity.getStatus() == OrderPlanEntity.Status.COMPLETED) {
            planEntity.clearEndStatus();
            planEntity.setStatus(OrderPlanEntity.Status.RUNNING);

            planEntity.setStartAt(System.currentTimeMillis());
            planEntity.setStartBy(userDetail.getUid());
            planEntity.setStartPerson(userDetail.getUsername());
            planRepository.save(planEntity);
        }

        List<PlanPlatformEntity> newEntityList = planPlatformRepository.saveAll(platformEntityList);
        insertPlatformCase(planEntity, newEntityList);
    }

    /**
     * 释放设备，并保存释放信息
     *  暂未使用
     *
     * @param flashEntity       flash实体类
     * @param platformEntityList 需要释放的平台信息
     * @param userDetail         操作人信息
     */
    @Transactional
    public void releasePlanPlatform(
            OrderFlashEntity flashEntity,
            List<PlanPlatformEntity> platformEntityList,
            OAuthUserDetail userDetail
    ) {
        List<TerminalModel> terminalList = fetchTestModelByPlatformList(platformEntityList);
        // 释放此处测试过程中被取消的平台
        scrcpyService.holdUnLockPlatform(
                flashEntity.getOrderFlashNo(),
                terminalList,
                userDetail.getUsername()
        );
        List<PlanPlatformEntity> resultList = platformEntityList.stream()
                .peek(entity -> {
                    PlanPlatformEntity.Status status = entity.getStatus();
                    if (entity.isOccupied()) {
                        entity.setStatus(PlanPlatformEntity.Status.CANCELED);
                    } else if (status == PlanPlatformEntity.Status.FINISHED_SUCCESS) {
                        entity.setStatus(PlanPlatformEntity.Status.FINISHED_SUCCESS);
                    } else {
                        entity.setStatus(PlanPlatformEntity.Status.FINISHED_FAILED);
                    }
                    entity.setUpdatedAt(System.currentTimeMillis());
                    entity.setReleaseAt(System.currentTimeMillis());
                    entity.setReleaseBy(userDetail.getUid());
                    entity.setReleasePerson(userDetail.getUsername());
                }).collect(Collectors.toList());

        // 保存释放操作信息
        planPlatformRepository.saveAll(resultList);
    }

    /**
     * 启动Plan测试
     *
     * @param orderEntity        order实体类
     * @param flashEntity        flash实体类
     * @param planEntity         plan实体类
     * @param platformEntityList 手机平台信息
     * @param userDetail         操作人
     */
    public void startTestTerminalPlan(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            List<PlanPlatformEntity> platformEntityList,
            OAuthUserDetail userDetail
    ) {
        //IMPORT: 只要用户点击了plan的开始，则表示plan已经开始了，如果出现了部分设备没有启动成功，也认为plan已经开始了。
        planService.updatePlanStatusToRunning(orderEntity, flashEntity, planEntity, userDetail);
        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_STARTED)
                .orderId(planEntity.getOrderId())
                .flash(planEntity.getFlash())
                .planId(planEntity.getId())
                .build()
        );

        log.info("Flash:{}下的终端Plan {}使用终端平台:{} 开始测试!", flashEntity.getFlash(), planEntity.getName(),
                platformEntityList.stream().map(
                        d -> String.format("%s(%s-%s)", d.getNo(), d.getMac(), d.getNumber())
                ).collect(Collectors.joining(",")));

        // 定时任务启动平台
        timeStartTestPlatform(orderEntity, planEntity, platformEntityList, userDetail);
    }

    /**
     * 启动一个10分钟后的定时检测终端Plan启动状态的任务。
     *
     * @param orderId      工单id
     * @param orderFlashNo RMS中完整的工单号
     * @param planEntity   plan 实体类
     * @param terminalList 需要检测终端平台
     */
    public void startCheckTerminalPlanStartStatusJob(
            long orderId,
            String orderFlashNo,
            OrderPlanEntity planEntity,
            List<TerminalModel> terminalList
    ) {
        String jobName = orderFlashNo + "_" + planEntity.getName() + "_CheckTerminalPlanStartStatusJob";
        jobService.startJobAfter(
                jobName,
                CheckTerminalPlanStartStatusJob.JOB_GROUP_NAME,
                CheckTerminalPlanStartStatusJob.buildJobDataMap(
                        orderId,
                        orderFlashNo,
                        planEntity.getId(),
                        terminalList
                ),
                CheckTerminalPlanStartStatusJob.class,
                600
        );
    }

    /**
     * 取消Plan测试
     *
     * @param orderEntity        工单实体
     * @param flashEntity        flash实体类
     * @param planEntity         plan实体类
     * @param platformEntityList plan 下的所有平台
     * @param userDetail         操作人信息
     */
    public void stopTestTerminalPlan(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            List<PlanPlatformEntity> platformEntityList,
            OAuthUserDetail userDetail
    ) {
        // 更新Plan的状态。
        log.info("更新{}状态至停止!", planEntity.getName());
        // 不管plan 之前是处于什么状态，
        OrderPlanEntity.Status lastStatus = planEntity.getStatus();

        planEntity.setUpdatedAt(System.currentTimeMillis());
        planEntity.setStatus(OrderPlanEntity.Status.STOPPED);
        planEntity.setEndStatus(OrderPlanEntity.END_STATUS_TERMINATED);
        planEntity.setTerminateAt(System.currentTimeMillis());
        planEntity.setTerminateBy(userDetail.getUid());
        planEntity.setTerminatePerson(userDetail.getUsername());
        planRepository.save(planEntity);

        log.info("取消{}关联的所有任务", planEntity.getName());
        cancelPlanJob(flashEntity, planEntity);

        // 进行中的平台信息
        List<PlanPlatformEntity> runningPlatformList = platformEntityList.stream()
                .filter(d -> !COMPLETED_STATUS_LIST.contains(d.getStatus()))
                .collect(Collectors.toList());
        int total = platformEntityList.size();
        int completed = total - runningPlatformList.size();

        // 如果 完成的设备数量等于总设备数量,则表示所有设备都已经完成了,则不需要再进行处理了.
        log.info("终端Plan:{}下的平台总数:{}, 已完成数量:{}, 未完成数量:{}", planEntity.getName(), total, completed, runningPlatformList.size());
        if (lastStatus == OrderPlanEntity.Status.CONFIRMED || lastStatus == OrderPlanEntity.Status.RUNNING) {
            // 获取测试中的终端参数
            List<TerminalModel> terminalList = fetchTestModelByPlatformList(runningPlatformList);

            // 理论上 Plan 不处于(RUNNING,CONFIRMED)状态的时候,是不需要处理设备的.
            if (lastStatus == OrderPlanEntity.Status.RUNNING) {
                log.info(
                        "调用RMS停止终端Plan: {}下的平台：{}",
                        planEntity.getName(),
                        runningPlatformList.stream().map(d -> d.getNo() + "_" + d.getNumber())
                                .collect(Collectors.joining(","))
                );

                // 发起停止测试请求
                stopTestPlatformAndCheck(flashEntity.getOrderFlashNo(), planEntity, terminalList);

                // 释放此处测试过程中被取消的平台
                scrcpyService.holdUnLockPlatform(
                        flashEntity.getOrderFlashNo(),
                        terminalList,
                        userDetail.getUsername()
                );

                // 获取测试失败，但是并未释放的平台
                List<PlanPlatformEntity> failPlatformList = platformEntityList.stream()
                        .filter(d -> d.getStatus() == PlanPlatformEntity.Status.FINISHED_FAILED && d.getReleaseAt() == null)
                        .collect(Collectors.toList());
                if (failPlatformList.size() > 0) {
                    List<TerminalModel> failTerminalList = fetchTestModelByPlatformList(failPlatformList);
                    // 解锁平台
                    scrcpyService.holdUnLockPlatform(
                            flashEntity.getOrderFlashNo(),
                            failTerminalList,
                            userDetail.getUsername()
                    );
                }
            }

            //检查释放存在锁定状态下的平台设备
            List<PlanPlatformEntity> occupiedList = platformEntityList.stream()
                    .filter(d -> PlanPlatformEntity.Status.OCCUPIED == d.getStatus())
                    .collect(Collectors.toList());

            log.info("occupiedList:{}", occupiedList);

            // 如果存在锁定状态下的平台，则直接释放掉该平台
            if(occupiedList.size() > 0){
                log.info("终端{} 已经添加设备，但是尚未开始运行,可以直接释放终端平台即可.", planEntity.getName());
                List<TerminalModel> unlockList = fetchTestModelByPlatformList(occupiedList);

                List<PlanPlatformEntity> saveList = platformEntityList.stream()
                        .peek(d -> {
                            d.setTerminateAt(System.currentTimeMillis());
                            d.setTerminateBy(userDetail.getUid());
                            d.setTerminatePerson(userDetail.getUsername());
                            d.setStopReason("取消终端Plan测试");
                            d.setReleaseAt(System.currentTimeMillis());         // 撤销所有的设备信息
                            d.setReleaseBy(userDetail.getUid());
                            d.setReleasePerson(userDetail.getUsername());
                            d.setStatus(PlanPlatformEntity.Status.CANCELED);
                        }).collect(Collectors.toList());
                planPlatformRepository.saveAll(saveList);

                scrcpyService.holdUnLockPlatform(
                        flashEntity.getOrderFlashNo(),
                        unlockList,
                        userDetail.getUsername()
                );
            }
        }

        // 如果是直接释放设备的话,则同时也需要检查状态.
        flashService.checkWaitMerge(orderEntity, flashEntity);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_STOPPED)
                .orderId(planEntity.getOrderId())
                .flash(planEntity.getFlash())
                .planId(planEntity.getId())
                .build()
        );
    }

    /**
     * 保存执行停止测试后的终端平台回调处理
     *
     * @param planId   planId
     * @param respList 返回的测试数据
     * @param status   需要处理成的状态
     */
    public void savePlatformListByTestParams(
            long planId,
            List<TerminalPlatformStatusResp.PlatformStatusResult> respList,
            PlanPlatformEntity.Status status
    ) {
        List<PlanPlatformEntity> resultList = new ArrayList<>();
        for (TerminalPlatformStatusResp.PlatformStatusResult model : respList) {
            PlanPlatformEntity entity = fetchPlatformByIdAndMacAndNumber(planId, model.getMac(), model.getNumber());

            entity.setStatus(status);

            if (status == PlanPlatformEntity.Status.FINISHED_FAILED) {
                entity.setFailReason(model.getReason());
            } else if (status == PlanPlatformEntity.Status.CANCELED) {
                entity.setReleaseAt(System.currentTimeMillis());
                entity.setReleasePerson(DingTalkUtils.getCurrentUserName());
                entity.setReleaseBy(DingTalkUtils.getCurrentUserDingTalkID());
            }
            resultList.add(entity);
        }
        planPlatformRepository.saveAll(resultList);
    }

    /**
     * 终端平台停止测试，并处理回调信息
     *
     * @param no              工单号
     * @param orderPlanEntity plan实体类
     * @param terminalList    需要停止测试的平台参数
     */
    public void stopTestPlatformAndCheck(
            String no,
            OrderPlanEntity orderPlanEntity,
            List<TerminalModel> terminalList
    ) {
        if (terminalList.isEmpty()) {
            return;
        }
        TerminalPlanStopParams stopParams = new TerminalPlanStopParams();
        stopParams.setNo(no);
        stopParams.setPlan(orderPlanEntity.getName());
        stopParams.setUsername(DingTalkUtils.getCurrentUserName());
        stopParams.setTerminalList(terminalList);

        // 更新相关的case信息
        updatePlatformCaseAtCancel(orderPlanEntity, terminalList);
        HandleResp<TerminalPlatformStatusResp> result = scrcpyService.stopTestPlatforms(stopParams);

        if (result.getCode() == 0) {
            TerminalPlatformStatusResp model = result.getData();
            List<TerminalPlatformStatusResp.PlatformStatusResult> failLst = model.getFailLst();
            List<TerminalPlatformStatusResp.PlatformStatusResult> successLst = model.getSuccessLst();

            // 处理返回结果
            // 参与此次停止测试的平台中失败的平台
             savePlatformListByTestParams(orderPlanEntity.getId(), failLst, PlanPlatformEntity.Status.FINISHED_FAILED);

            // 参与此次停止测试的平台中成功的平台
            savePlatformListByTestParams(orderPlanEntity.getId(), successLst, PlanPlatformEntity.Status.CANCELED);
        }
    }

    /**
     * 释放测试平台
     *
     * @param orderEntity      工单实体类
     * @param flashEntity      Flash实体类
     * @param planEntity       Plan实体类
     * @param platformInfoList 需要释放的测试平台
     * @param userDetail       操作人
     */
    public void releasePlatform(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            List<PlatformInfo> platformInfoList,
            OAuthUserDetail userDetail
    ) {
        // 需要解锁的测试平台
        List<TerminalModel> terminalList = new ArrayList<>();
        for (PlatformInfo platformInfo : platformInfoList) {
            PlanPlatformEntity platformEntity = fetchPlatformByIdAndMacAndNumber(planEntity.getId(), platformInfo.getMac(), platformInfo.getNumber());

            // 将当前平台信息组合成一个终端平台信息
            TerminalModel terminalModel = new TerminalModel();
            terminalModel.setMac(platformEntity.getMac());
            terminalModel.setNumberList(Collections.singletonList(platformEntity.getNumber()));

            PlanPlatformEntity.Status status = platformEntity.getStatus();
            if (status == PlanPlatformEntity.Status.OCCUPIED) {
                log.info("{}下需要解锁的终端平台: {}",
                        planEntity.getName(),
                        platformEntity.getNo() + "_" + platformEntity.getNumber());
                terminalList.add(terminalModel);

                // 仅占用状态需要处理成已取消
                platformEntity.setStatus(PlanPlatformEntity.Status.CANCELED);
            } else if (status == PlanPlatformEntity.Status.FINISHED_FAILED) {
                // 失败状态且尚未释放的终端平台
                if (platformEntity.getReleaseAt() == null) {
                    terminalList.add(terminalModel);
                }
            } else {
                throw new DataNotFoundException("此次需要释放的平台不在锁定状态!");
            }

            // 处理case信息
            updatePlatformCaseAtCancel(planEntity, terminalList);
            // 统一对测试平台进行解锁
            scrcpyService.holdUnLockPlatform(
                    flashEntity.getOrderFlashNo(),
                    terminalList,
                    userDetail.getUsername()
            );
            // 处理终端平台信息
            platformEntity.setUpdatedAt(System.currentTimeMillis());
            platformEntity.setReleaseAt(System.currentTimeMillis());
            platformEntity.setReleaseBy(userDetail.getUid());
            platformEntity.setReleasePerson(userDetail.getUsername());
            planPlatformRepository.save(platformEntity);

            dataChangeListener.onDataChange(DataChangeEvent.builder()
                    .type(DataChangeEvent.Type.DEVICE_RELEASED)
                    .orderId(orderEntity.getId())
                    .flash(planEntity.getFlash())
                    .planId(planEntity.getId())
                    .build());
        }

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.DEVICE_RELEASED)
                .orderId(orderEntity.getId())
                .flash(planEntity.getFlash())
                .planId(planEntity.getId())
                .build());
        checkDeviceReleaseStatus(orderEntity, flashEntity, planEntity);
    }

    /**
     * 启动终端测试平台
     *
     * @param orderEntity    工单实体类
     * @param flashEntity    Flash实体类
     * @param planEntity     Plan实体类
     * @param platformEntity 终端测试平台信息
     * @param userDetail     操作人
     */
    public void startTestPlatform(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            PlanPlatformEntity platformEntity,
            OAuthUserDetail userDetail
    ) {
        log.info(
                "[{}]启动终端测试平台: {}!",
                planEntity.getName(),
                String.format("%s(%s-%s)", platformEntity.getNo(), platformEntity.getMac(), platformEntity.getNumber())
        );

        platformEntity.setStatus(PlanPlatformEntity.Status.RUNNING);
        platformEntity.setStartAt(System.currentTimeMillis());
        platformEntity.setStartBy(userDetail.getUid());
        platformEntity.setStartPerson(userDetail.getUsername());
        platformEntity.setTerminateAt(null);
        platformEntity.setTerminateBy(null);
        platformEntity.setTerminatePerson(null);
        platformEntity.setFailReason(null);
        platformEntity.setStopReason(null);
        planPlatformRepository.save(platformEntity);
        // 定时任务启动平台
        timeStartTestPlatform(orderEntity, planEntity, Collections.singletonList(platformEntity), userDetail);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_STARTED)
                .orderId(planEntity.getOrderId())
                .flash(flashEntity.getFlash())
                .planId(planEntity.getId())
                .build());
    }

    /**
     * 启动一个测试任务启动以下终端平台
     *
     * @param orderEntity        工单实体
     * @param planEntity         plan实体类
     * @param platformEntityList 需要启动的设备信息
     * @param userDetail         操作人
     */
    private void timeStartTestPlatform(
            WorkOrderEntity orderEntity,
            OrderPlanEntity planEntity,
            List<PlanPlatformEntity> platformEntityList,
            OAuthUserDetail userDetail
    ) {
        log.info("此次需要启动测试的终端平台有:{} ", platformEntityList.stream()
                .map(d -> String.format("%s(%s %s)", d.getNo(), d.getMac(), d.getNumber()))
                .collect(Collectors.joining(",")));

        // 任务名
        String jobName = StartTerminalTestJob.buildJobName(orderEntity.getId(), planEntity.getId());

        JobDetail jobDetail = StartTerminalTestJob.buildJobDetail(
                jobName,
                planEntity.getOrderId(),
                planEntity.getFlash(),
                planEntity.getId(),
                userDetail.getUsername(),
                platformEntityList
        );

        // 定时任务启动平台
        jobService.startJob(
                jobDetail,
                jobName,
                StartTerminalTestJob.GROUP_NAME,
                DateBuilder.futureDate(5, DateBuilder.IntervalUnit.SECOND)
        );
    }

    /**
     * 停止测试平台的测试任务
     *
     * @param orderEntity 工单实体类
     * @param flashEntity Flash实体类
     * @param planEntity  Plan实体类
     * @param params      需要停止的测试平台信息
     * @param userDetail  操作人
     */
    public void stopTestPlatform(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            PlanPlatformParams params,
            OAuthUserDetail userDetail
    ) {
        List<PlanPlatformEntity> platformEntityList = new ArrayList<>();
        for (PlatformInfo platformInfo : params.getPlatformList()) {
            String mac = platformInfo.getMac();
            String number = platformInfo.getNumber();
            PlanPlatformEntity platformEntity = fetchPlatformByIdAndMacAndNumber(planEntity.getId(), mac, number);
            platformEntity.setStopReason(params.getReason());
            platformEntity.setTerminateAt(System.currentTimeMillis());
            platformEntity.setTerminateBy(DingTalkUtils.getCurrentUserDingTalkID());
            platformEntity.setTerminatePerson(DingTalkUtils.getCurrentUserName());
            platformEntityList.add(platformEntity);
        }
        planPlatformRepository.saveAll(platformEntityList);

        // 获取测试中的终端参数
        List<TerminalModel> terminalList = fetchTestModelByPlatformList(platformEntityList);

        // 发起停止测试请求，并处理停止后的终端平台信息
        stopTestPlatformAndCheck(flashEntity.getOrderFlashNo(), planEntity, terminalList);

        // 释放此处测试过程中被取消的平台
        scrcpyService.holdUnLockPlatform(
                flashEntity.getOrderFlashNo(),
                terminalList,
                userDetail.getUsername()
        );

        // 检查Plan 下的终端平台情况。
        checkDeviceReleaseStatus(orderEntity, flashEntity, planEntity);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.PLAN_STOPPED)
                .orderId(planEntity.getOrderId())
                .flash(planEntity.getFlash())
                .planId(planEntity.getId())
                .build()
        );
    }

    /**
     * 重测已经取消的Plan
     *
     * @param planEntity 需要重测的Plan
     * @param userDetail 操作人
     */
    public void retryCanceledPlan(OrderPlanEntity planEntity, OAuthUserDetail userDetail) {
        PlanHistoryEntity historyEntity = PlanHistoryEntity.of(planEntity);
        planHistoryRepository.save(historyEntity);

        log.info("终端Plan重测完成：更新{} 状态至等待开始!", planEntity.getName());
        // 移除所有终端平台记录
        planPlatformRepository.deleteAllByPlanId(planEntity.getId());

        planEntity.clearEndStatus();
        planEntity.setStatus(OrderPlanEntity.Status.CONFIRMED);
        planEntity.setReTestAt(System.currentTimeMillis());
        planEntity.setReTestBy(userDetail.getUid());
        planEntity.setReTestPerson(userDetail.getUsername());
        planRepository.save(planEntity);

        // 发送变更到前端.
        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .orderId(planEntity.getOrderId())
                        .flash(planEntity.getFlash())
                        .planId(planEntity.getId())
                        .type(DataChangeEvent.Type.PLAN_RETEST)
                        .build()
        );

    }

    /**
     * 检查plan下的设备释放状态。
     *
     * @param orderEntity 工单实体类
     * @param planEntity  plan 实体类
     * @param flashEntity flash 实体类
     */
    @Transactional
    public void checkDeviceReleaseStatus(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, OrderPlanEntity planEntity) {
        // 找出plan 下的所有设备
        List<PlanPlatformEntity> platformEntityList = planPlatformRepository.findAllByPlanId(planEntity.getId());
        // 判断工单下的终端平台是不是所有的都被释放了。
        int releasedPlatformCount = planPlatformRepository.countByPlanIdAndReleaseAtNotNull(planEntity.getId());

        if (platformEntityList.size() != releasedPlatformCount) {
            log.info("{}下的终端平台未释放完成,目前释放进度: {}/{}", planEntity.getName(), releasedPlatformCount, platformEntityList.size());
            return;
        }
        log.info("{}下的{}台设备都已释放,将状态更新至已完成", planEntity.getName(), platformEntityList.size());

        // 如果所有的设备都已经释放了,则移除Plan对应的任务.
        cancelPlanJob(flashEntity, planEntity);
        if (planEntity.getStatus() != OrderPlanEntity.Status.STOPPED) {
            // 如果所有的设备都被释放了， 就更新 Plan 的状态
            planEntity.setUpdatedAt(System.currentTimeMillis());
            planEntity.setStatus(OrderPlanEntity.Status.COMPLETED);
            planEntity.setEndAt(System.currentTimeMillis());
            planRepository.save(planEntity);
            log.info("更新：{}的状态至完成", planEntity.getName());
        }
        // 判断，并更新对应批次，工单的状态。
        flashService.checkWaitMerge(orderEntity, flashEntity);
    }

    /**
     * 检查一下点击启动测试之后，本地生成的检测测试请求成功失败 的本地任务是否还存在，存在则取消。
     *
     * @param flashEntity flash 实体类
     * @param planEntity  plan 实体类
     */
    public void cancelPlanJob(OrderFlashEntity flashEntity, OrderPlanEntity planEntity) {
        log.info("取消{}下的{}的所有任务!", flashEntity.getFlash(), planEntity.getName());
        // 取消启动后的启动成功失败的状态检测任务
        jobService.cancelJobIfExist(
                CheckTerminalPlanStartStatusJob.buildJobName(flashEntity.getOrderFlashNo(), planEntity.getName()),
                CheckTerminalPlanStartStatusJob.JOB_GROUP_NAME
        );
    }

    /**
     * 检查当前平台是否被重复占用
     *
     * @param mac    设备MAC地址
     * @param number 平台编号
     */
    public void checkInsertPlatform(String mac, String number) {
        log.info("[平台锁定前]检查平台是否已被其他Plan占用,需要检查的平台:{} mac: {}", number, mac);
        List<PlanPlatformEntity> platformList = planPlatformRepository.findAllByMacAndNumberAndStatusIn(mac, number, RUN_STATUS_LIST);
        int length = platformList.size();

        if (length > 1) {
            PlanPlatformEntity platform = platformList.get(0);
            log.warn("发现电脑{}下的平台: {} 还在运行中!", platform.getNo(), number);
            String msg = "平台: " + number + " 已经被其他Plan占用!";
            throw new IllegalArgumentException(msg);
        }
    }

    /**
     * 检查当前设备是否正在测试中
     *
     * @param mac    终端电脑MAC
     * @param number 平台编号
     * @param planId Plan id
     */
    public void checkPlatformHasUsed(String mac, String number, long planId) {
        log.info("[平台解锁前]检查平台是否已在测试中,需要检查的平台:{} mac: {}", number, mac);
        List<PlanPlatformEntity> platformList = planPlatformRepository.findAllByMacAndNumberAndStatusIn(mac, number, List.of(PlanPlatformEntity.Status.RUNNING, PlanPlatformEntity.Status.OCCUPIED));
        int length = platformList.size();
        if (length == 0) {
            log.warn("未发现平台: {} 被任何Plan使用!", number);
            String msg = "平台: " + number + " 并不存在占用列表中!";
            throw new IllegalArgumentException(msg);
        }

        PlanPlatformEntity platform = platformList.get(0);

        boolean flag = platformList.stream().anyMatch(entity -> entity.getPlanId() != planId);
        if (flag) {
            log.warn("发现电脑{}下的测试平台: {} 正在被其他Plan使用!", platform.getNo(), number);
            String msg = "平台: " + number + " 正在被其他Plan占用中无法直接释放!";
            throw new IllegalArgumentException(msg);
        }

        if (length > 1) {
            log.warn("发现电脑{}下的平台: {} 被重复占用", platform.getNo(), number);
            String msg = "平台: " + number + " 被重复占用，无法直接释放!";
            throw new IllegalArgumentException(msg);
        }
    }

    /**
     * 检查当前手机平台，是否满足启动或者释放条件
     *
     * @param mac    设备MAC地址
     * @param number 平台编号
     * @param planId Plan id
     */
    public void checkTestPlatform(String mac, String number, long planId) {
        log.info("[平台测试前]检查平台是否已被其他Plan占用,需要检查的平台:{}", number);
        List<PlanPlatformEntity> platformList = planPlatformRepository.findAllByMacAndNumberAndStatusIn(mac, number, RUN_STATUS_LIST);
        int length = platformList.size();
        if (length == 0) {
            log.warn("未发现平台: {} 被任何Plan使用!", number);
            String msg = "平台: " + number + " 并不存在占用列表中!";
            throw new IllegalArgumentException(msg);
        }
        PlanPlatformEntity platform = platformList.get(0);

        boolean flag = platformList.stream().anyMatch(entity -> entity.getPlanId() != planId);
        if (!flag) {
            log.warn("发现平台: {} 已被其他Plan使用!", number);
            String msg = "平台: " + number + " 执行操作的Plan和占用的Plan不一致!";
            throw new IllegalArgumentException(msg);
        }

        if (length > 1) {
            log.warn("发现电脑{}下的平台: {} 还在运行中!", platform.getNo(), number);
            String msg = "平台: " + number + " 同时被其他Plan占用!";
            throw new IllegalArgumentException(msg);
        }
    }

    /**
     * 增加case信息存储
     * @param planEntity plan实体类
     * @param platformEntityList 终端平台实体类
     */
    public void insertPlatformCase(OrderPlanEntity planEntity, List<PlanPlatformEntity> platformEntityList){
        for(PlanPlatformEntity platformEntity : platformEntityList){
            // 获取case列表并转换为List<String>
            List<String> caseList = Optional.ofNullable(planEntity.getScripts())
                    .map(scripts -> Arrays.asList(scripts.split(";")))
                    .orElse(Collections.emptyList());
            List<PlatformCaseEntity> caseEntities = new ArrayList<>();
            log.info("insertPlatformCase caseList: {}", caseList);
            for (int i = 0; i < caseList.size(); i++){
                caseEntities.add(createPlatformCaseEntity(planEntity, platformEntity, caseList.get(i), i + 1));
            }

            platformCaseRepository.saveAll(caseEntities);
        }
    }

    private PlatformCaseEntity createPlatformCaseEntity(
            OrderPlanEntity planEntity,
            PlanPlatformEntity platformEntity,
            String caseName,
            Integer caseStep
    ) {
        Optional<PlatformCaseEntity> existingEntityOpt = platformCaseRepository.findByPlanIdAndMacAndNumberAndCaseNameAndStep(
                planEntity.getId(),
                platformEntity.getMac(),
                platformEntity.getNumber(),
                caseName,
                caseStep
        );

        PlatformCaseEntity entity = existingEntityOpt.orElseGet(PlatformCaseEntity::new);

        entity.setOrderId(planEntity.getOrderId());
        entity.setPlanId(planEntity.getId());
        entity.setPlatformId(platformEntity.getId());
        entity.setPlatformName(platformEntity.getName());
        entity.setCaseName(caseName);
        entity.setFailReason(null);
        entity.setEndAt(null);
        entity.setStatus(PlatformCaseEntity.Status.WAITING);
        entity.setCaseStep(caseStep);
        return entity;
    }

    /**
     * 记录case启动时间
     * @param planEntity plan实体类
     * @param params TerminalCaseStartParams 请求参数
     */
    public void updateCaseStartCallBack(OrderPlanEntity planEntity, TerminalCaseStartParams params){
        PlatformCaseEntity entity = platformCaseRepository.findByPlanIdAndMacAndNumberAndCaseNameAndStep(
                        planEntity.getId(), params.getMac(), params.getNumber(), params.getCaseName(), params.getCaseStep()
                )
                .orElseThrow(() -> new DataNotFoundException("未找到相关平台的case测试信息！"));
        entity.setStatus(PlatformCaseEntity.Status.RUNNING);
        entity.setStartAt(System.currentTimeMillis());
        platformCaseRepository.save(entity);
    }

    /**
     * case状态回调时,进行case信息更新
     * @param flashEntity flash实体类
     * @param planEntity plan实体类
     * @param caseList 需要更新的case列表
     * @param result 测试结果
     */
    public void updatePlatformCaseAtCallBack(
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            List<TerminalCaseStatusChangeParams.CaseInfo> caseList, boolean result
    ) {
        // 获取所有需要更新的caseId集合
        Set<Long> caseIdsToUpdate = caseList.stream()
                .map(caseInfo -> {
                    Optional<PlatformCaseEntity> existingEntityOpt = platformCaseRepository.findByPlanIdAndMacAndNumberAndCaseNameAndStep(
                            planEntity.getId(), caseInfo.getMac(), caseInfo.getNumber(), caseInfo.getCaseName(), caseInfo.getCaseStep()
                    );
                    PlatformCaseEntity entity = existingEntityOpt.orElseGet(PlatformCaseEntity::new);
                    return entity.getId();
                })
                .collect(Collectors.toSet());
        // 批量更新已完成的测试用例状态
        List<PlatformCaseEntity> updatedCases = platformCaseRepository.findAllById(caseIdsToUpdate);
        for (PlatformCaseEntity caseEntity : updatedCases) {
            caseEntity.setStatus(result ? PlatformCaseEntity.Status.FINISHED_SUCCESS : PlatformCaseEntity.Status.FINISHED_FAILED);
            caseEntity.setFailReason(caseEntity.getFailReason());
            caseEntity.setEndAt(System.currentTimeMillis());
        }
        platformCaseRepository.saveAll(updatedCases);

        // 如果当前case失败了，则取消后续的所有case信息
        if (!result) {
            // 取消同一计划下所有等待中的测试用例
            List<PlatformCaseEntity> waitingCases = platformCaseRepository.findByPlanIdAndMacAndNumberAndStatus(
                    planEntity.getId(), caseList.get(0).getMac(), caseList.get(0).getNumber(), PlatformCaseEntity.Status.WAITING);

            for (PlatformCaseEntity entity : waitingCases) {
                entity.setStatus(PlatformCaseEntity.Status.CANCELED);
                entity.setEndAt(System.currentTimeMillis());
            }
            platformCaseRepository.saveAll(waitingCases);
        }

        // 检查终端平台的所有case测试是否已完成
        checkPlatformStatus(flashEntity, planEntity);

        // 检查当前Plan是否测试完成
        checkTerminalPlanStatus(flashEntity, planEntity);
        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.DEVICE_SAMPLE_CHANGED)
                        .orderId(flashEntity.getOrderId())
                        .flash(flashEntity.getFlash())
                        .planId(planEntity.getId())
                        .build()
        );
    }

    /**
     * 检查当前Plan 下的所有终端平台的所有case测试是否已完成
     * @param planEntity plan实体类
     */
    public void checkPlatformStatus(OrderFlashEntity flashEntity, OrderPlanEntity planEntity) {
        List<PlanPlatformEntity> platformEntityList = planPlatformRepository.findAllByPlanIdAndStatusIn(planEntity.getId(), RUN_STATUS_LIST);

        List<TerminalModel> terminalList = new ArrayList<>();
        for (PlanPlatformEntity entity : platformEntityList) {
            List<PlatformCaseEntity> caseEntityList = platformCaseRepository.findByPlanIdAndMacAndNumber(
                    planEntity.getId(),
                    entity.getMac(),
                    entity.getNumber()
            );
            long failedCount = caseEntityList.stream().filter(PlatformCaseEntity::isFailed).count();
            int waitingCount = platformCaseRepository.countByPlanIdAndMacAndNumberAndStatus(
                    planEntity.getId(),
                    entity.getMac(),
                    entity.getNumber(),
                    PlatformCaseEntity.Status.WAITING
            );
            if (waitingCount == 0) {
                if(failedCount > 0 ){
                    entity.setStatus(PlanPlatformEntity.Status.FINISHED_FAILED);
                }else{
                    entity.setStatus(PlanPlatformEntity.Status.FINISHED_SUCCESS);
                    entity.setReleaseAt(System.currentTimeMillis());
                    entity.setReleasePerson("eSee.system");
                    entity.setReleaseBy("0");
                    TerminalModel model = new TerminalModel();
                    model.setMac(entity.getMac());
                    model.setNumberList(Collections.singletonList(entity.getNumber()));
                    terminalList.add(model);
                }
            }
        }
        planPlatformRepository.saveAll(platformEntityList);
        // 释放当前状态需要更新为成功的测试平台
        scrcpyService.holdUnLockPlatform(flashEntity.getOrderFlashNo(), terminalList, "eSee.system");

        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.DEVICE_SAMPLE_CHANGED)
                        .orderId(flashEntity.getOrderId())
                        .flash(flashEntity.getFlash())
                        .planId(planEntity.getId())
                        .build()
        );
    }

    /**
     * 检查当前Plan下的所有终端测试平台是否都完成测试
     * @param flashEntity Flash实体类
     * @param planEntity plan实体类
     */
    public void checkTerminalPlanStatus(OrderFlashEntity flashEntity, OrderPlanEntity planEntity) {
        List<PlanPlatformEntity> platformEntityList = planPlatformRepository.findAllByPlanId(planEntity.getId());
        long failedCount = platformEntityList.stream().filter(PlanPlatformEntity::isFailed).count();
        long finishCount = platformEntityList.stream().filter(PlanPlatformEntity::isRelease).count();
        log.info("{}下共有{}个平台, 其中已释放的测试平台{}个", planEntity.getName(), platformEntityList.size(), finishCount);

        if (finishCount == platformEntityList.size()) {
            planEntity.setUpdatedAt(System.currentTimeMillis());
            planEntity.setStatus(OrderPlanEntity.Status.COMPLETED);
            planEntity.setEndStatus(failedCount > 0 ? OrderPlanEntity.END_STATUS_FAIL : OrderPlanEntity.END_STATUS_SUCCESS);
            planEntity.setEndAt(System.currentTimeMillis());
            planRepository.save(planEntity);
            log.info("更新：终端{}的状态至完成",planEntity.getName());
        }
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        flashService.checkWaitMerge(orderEntity, flashEntity);
    }

    /**
     * 平台取消测试时,进行case信息更新
     * @param planEntity plan实体类
     * @param terminalList 需要更新的平台信息
     */
    public void updatePlatformCaseAtCancel(OrderPlanEntity planEntity, List<TerminalModel> terminalList) {
        for (TerminalModel model : terminalList) {
            List<PlatformCaseEntity> waitingCases = platformCaseRepository.findByPlanIdAndMacAndNumberInAndStatus(
                    planEntity.getId(), model.getMac(), model.getNumberList(), PlatformCaseEntity.Status.WAITING);

            for (PlatformCaseEntity entity : waitingCases) {
                entity.setStatus(PlatformCaseEntity.Status.CANCELED);
                entity.setEndAt(System.currentTimeMillis());
            }
            platformCaseRepository.saveAll(waitingCases);
        }
    }

}
