package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.model.base.DeviceBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备模块
 */
@Slf4j
@Data
@JsonAutoDetect
@ApiModel(value = "DeviceModel",description = "RMS中的设备")
public class DeviceModel implements DeviceBaseInfo {

    public static final String ATTR_HIGH_TEMP = "高温";
    public static final String ATTR_LOW_TEMP = "低温";
    public static final String ATTR_TEMP_CYCLE = "温循";
    public static final String ATTR_QUALITY_HIGH_TEMP = "品质测试高温";
    public static final String ATTR_QUALITY_NORMAL_TEMP = "品质测试常温";
    public static final String ATTR_QUALITY_NORMAL_MARS_TEMP = "品质测试-常温MARS";
    public static final List<String> FIXED_ATTRS = List.of(
            ATTR_HIGH_TEMP,
            ATTR_LOW_TEMP,
            ATTR_TEMP_CYCLE,
            ATTR_QUALITY_NORMAL_TEMP,
            ATTR_QUALITY_NORMAL_MARS_TEMP
    );

    public enum Status {
        IDLE,
        READY,
        IN_USE
    }

    /**
     * 产品线, SD , U2 , U3 等等, 对应着系统中的subProduct
     */
    @ApiModelProperty("产品线")
    private String product ;

    @ApiModelProperty("PC 的编号")
    private String pcNo ;

    /**
     * 测试机的ip 地址
     */
    @ApiModelProperty("测试机的IP地址")
    private String ip ;

    /**
     * 测试机的mac 地址
     */
    @ApiModelProperty("测试机的MAC地址")
    private String mac ;

    @ApiModelProperty("测试机的位置")
    private String position ;

    @ApiModelProperty("测试机所属的人员")
    private String owner ;

    /**
     * 设备的状态
     */
    @ApiModelProperty("测试机的状态")
    private String status ;

    /**
     * 机器的属性列表
     */
    @ApiModelProperty("测试机所包含的属性列表")
    private List<AttrModel> attrModelList ;

    /**
     * 测试机的分数，分数越高，表示机器越稀有，越不容易被抢占
     */
    @ApiModelProperty("测试机的分数；分数越高，表示机器越稀有，越不容易被抢占")
    private int score ;

    @ApiModelProperty("设备归属地，SH代表深圳，HF代表合肥")
    private String region;


    /**
     * 通过属性名称来获取这个设备最大能测试的样片数量 ；
     * 理论上来说一台机器相同的属性只有一个，如果有多个相同的属性的时候，则认为最少的样片数量是正确的数量。
     * @param attr 属性名称。
     * @return 这个设备最少能测试的样片数量
     */
    public int getTestNumByAttr(String attr){
        return attrModelList.stream()
                .filter(attrModel -> Objects.equals(attrModel.getAttrName(),attr))
                .map(AttrModel::getNum)
                .min(Comparator.comparingInt(Integer::intValue))
                .orElse(0);
    }

    public static PlanDeviceEntity toEntity(
            DeviceModel deviceModel,
            long planId,
            long orderId
    ){
        PlanDeviceEntity planDeviceEntity = new PlanDeviceEntity();
        planDeviceEntity.setPlanId(planId);
        planDeviceEntity.setOrderId(orderId);
        planDeviceEntity.setIp(deviceModel.getIp());
        planDeviceEntity.setMac(deviceModel.getMac());
        planDeviceEntity.setNo(deviceModel.getPcNo());
        planDeviceEntity.setPosition(deviceModel.getPosition());
        planDeviceEntity.setOwner(deviceModel.getOwner());
        planDeviceEntity.setScore(deviceModel.getScore());
        planDeviceEntity.setStatus(PlanDeviceEntity.Status.OCCUPIED);
        return planDeviceEntity;
    }

    @Override
    public String toString() {
        return String.format("{ %s,%s,%s,%s }",pcNo,ip, mac,attrModelList);

    }

    /**
     * 获取机器编号
     * @return 返回编号
     */
    public int getNoNumber(){
        if(!position.contains("_")){
            return Integer.MAX_VALUE;
        }
        int lastIndex = position.lastIndexOf("_");
        return Integer.parseInt(position.substring(lastIndex + 1));
    }

    // 获取设备机柜信息
    public String getCabinetPosition() {
        if(!position.contains("_")){
            return position;
        }
        int firstUnderscoreIndex = position.indexOf('_');
        int secondUnderscoreIndex = position.indexOf('_', firstUnderscoreIndex + 1);
        if (secondUnderscoreIndex != -1) {
            return position.substring(0, secondUnderscoreIndex);
        }
        return position;
    }
}
