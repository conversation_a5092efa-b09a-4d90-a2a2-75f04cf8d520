{"@timestamp":"2025-07-24T15:54:02.628+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.641+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6637, orderId=6402, flash=29525_X3-9060_128GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29525_X3####250198_29525_X3-9060_128GB, num=50, leftNum=32)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.642+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.666+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.686+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.728+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.764+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.764+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85), OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.764+08:00","@version":"1","message":"执行预分配前共有32颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.764+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.765+08:00","@version":"1","message":" getValidPlanList sumList: [4] leftFlashNum:32 index:1 plans:[OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.765+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.765+08:00","@version":"1","message":"[6402] - [29525_X3-9060_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.631+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6637, orderId=6402, flash=29525_X3-9060_128GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29525_X3####250198_29525_X3-9060_128GB, num=50, leftNum=32)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.632+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.656+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.682+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.713+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.739+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.741+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85), OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.741+08:00","@version":"1","message":"执行预分配前共有32颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.742+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.742+08:00","@version":"1","message":" getValidPlanList sumList: [4] leftFlashNum:32 index:1 plans:[OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.743+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.743+08:00","@version":"1","message":"[6402] - [29525_X3-9060_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6637, orderId=6402, flash=29525_X3-9060_128GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29525_X3####250198_29525_X3-9060_128GB, num=50, leftNum=32)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.613+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.634+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.653+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.682+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.683+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85), OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.683+08:00","@version":"1","message":"执行预分配前共有32颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.683+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.683+08:00","@version":"1","message":" getValidPlanList sumList: [4] leftFlashNum:32 index:1 plans:[OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:02.683+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.683+08:00","@version":"1","message":"[6402] - [29525_X3-9060_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6637, orderId=6402, flash=29525_X3-9060_128GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29525_X3####250198_29525_X3-9060_128GB, num=50, leftNum=32)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.623+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.669+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.714+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.76+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.76+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85), OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.761+08:00","@version":"1","message":"执行预分配前共有32颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.761+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.761+08:00","@version":"1","message":" getValidPlanList sumList: [4] leftFlashNum:32 index:1 plans:[OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.761+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.761+08:00","@version":"1","message":"[6402] - [29525_X3-9060_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6637, orderId=6402, flash=29525_X3-9060_128GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29525_X3####250198_29525_X3-9060_128GB, num=50, leftNum=32)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.613+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.634+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.679+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.68+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85), OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.68+08:00","@version":"1","message":"执行预分配前共有32颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.68+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.68+08:00","@version":"1","message":" getValidPlanList sumList: [4] leftFlashNum:32 index:1 plans:[OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:02.681+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.682+08:00","@version":"1","message":"[6402] - [29525_X3-9060_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:02.887+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.039+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6637, orderId=6402, flash=29525_X3-9060_128GB, orderFlashNo=YS9082HPMPHP5310B3#01131#29525_X3####250198_29525_X3-9060_128GB, num=50, leftNum=32)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.106+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.263+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.298+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.34+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.394+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.394+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85), OrderPlanEntity(id=113856, orderId=6402, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113852, orderId=6402, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113854, orderId=6402, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.395+08:00","@version":"1","message":"执行预分配前共有32颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.395+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.399+08:00","@version":"1","message":" getValidPlanList sumList: [4] leftFlashNum:32 index:1 plans:[OrderPlanEntity(id=113853, orderId=6402, name=Plan506, status=QUEUE, priority=85)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.417+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.417+08:00","@version":"1","message":"[6402] - [29525_X3-9060_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6402","traceType":"分配设备"}
