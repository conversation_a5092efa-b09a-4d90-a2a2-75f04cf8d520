package com.yeestor.work_order.entity.document;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString
@Entity(name = "Document")
@Table(name = "wo_doc")
public class DocumentEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    /**
     * 文档的url
     */
    @Column(name = "url", length = 2048)
    private String url ;

    /**
     * 文档的名称
     */
    @Column(name = "name")
    private String name ;

    /**
     * 文件的ContentType
     * 文档的类型, 例如: pdf, doc, ppt, xls, txt, jpg, jpeg, gif, bmp, png
     */
    @Column(name = "content_type")
    private String contentType ;

    /**
     * 文档的大小, 单位为KB。
     */
    @Column(name = "size")
    private long size ;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private long createdAt = System.currentTimeMillis();

    /**
     * 创建人，钉钉id。可以为空，为空时表示系统自动生成
     */
    @Column(name = "created_by")
    private String createdBy ;

    /**
     * 创建人的名称
     */
    @Column(name = "created_p")
    private String createdPerson ;


}
