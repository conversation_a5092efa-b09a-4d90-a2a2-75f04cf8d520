package com.yeestor.work_order.model.http.req.terminal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PlatformReplaceParams", description = "平台替换参数信息")
public class PlatformReplaceParams {
    @ApiModelProperty(value = "工单id", required = true)
    private long orderId;

    @ApiModelProperty(value = "plan id", required = true)
    private long planId ;

    @ApiModelProperty(value = "替换之前的设备mac", required = true)
    private String oldMac;

    @ApiModelProperty(value = "替换之后的设备mac", required = true)
    private String newMac;

    @ApiModelProperty(value = "替换之前的平台编号", required = true)
    private String oldNumber;

    @ApiModelProperty(value = "替换之后的平台编号", required = true)
    private String newNumber;
}
