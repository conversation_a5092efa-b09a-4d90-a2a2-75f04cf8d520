package com.yeestor.work_order.utils;

import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import static org.junit.jupiter.api.Assertions.*;

class LogUtilsTest {

    @Test
    void setAndClearTracePoint() {
        LogUtils.setOrderTracePoint(1, "test");
        assertEquals("1", MDC.get(LogUtils.MDC_KEY_ORDER_NUMBER));
        assertEquals("test", MDC.get(LogUtils.MDC_KEY_TRACE_TYPE));
        LogUtils.clearTracePoint();
        assertNull(MDC.get(LogUtils.MDC_KEY_ORDER_NUMBER));
        assertNull(MDC.get(LogUtils.MDC_KEY_TRACE_TYPE));
    }

}