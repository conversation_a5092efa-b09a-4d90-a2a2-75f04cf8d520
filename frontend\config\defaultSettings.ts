import { Settings as LayoutSettings } from '@ant-design/pro-layout';
import moment from 'moment';
const { NODE_ENV } = process.env;

function getHostName(): string {
  if (typeof window !== 'undefined') {
    return window.location.hostname;
  }
  return '************';
}

function getPort(): string {
  if (getIsAlpha()) {
    return '8513';
  }
  return '8512';
}
function getIsAlpha(): boolean {
  if (typeof window !== 'undefined') {
    return window.location.port == '8513';
  }
  return false;
}

console.log('isAlpha: ', getIsAlpha());
console.log('NODE_ENV: ', NODE_ENV);

const Settings: LayoutSettings & {
  version: string;
  pwa?: boolean;
  dingQRAppId?: string;
  authUrl: string;
  apiHost: string;
  corpId: string;
  profile?: string;
  apiUrl: string;
  dingId: string;
  isAlpha: boolean;
  minWidth: number;
  ebuildHost: string;
  eReportHost: string;
} = {
  version: "v1." + moment().format('MMDD') + '.01-alpha',
  navTheme: 'light',
  primaryColor: '#1890ff',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: 'eSee',
  pwa: false,
  dingQRAppId: 'dingipd0xwcswudroa5i',
  iconfontUrl: '',
  apiHost: `http://************:8789`,
  authUrl: `http://${getHostName()}:${getPort()}/user/auth`,
  isAlpha: getIsAlpha(),
  apiUrl: getIsAlpha() ? '/w1' : NODE_ENV === 'development' ? '/w0' : '/wo',
  corpId: 'ding31d78c632c4953fa35c2f4657eb6378f',
  profile: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  dingId: getIsAlpha() ? '1445820031' : '1368250694',
  minWidth: 1920,
  ebuildHost:
    NODE_ENV === 'development' || getIsAlpha()
      ? 'http://ebuildin.yeestor.com:8789' 
      : 'http://ebuildin.yeestor.com:8789',
  eReportHost: 'http://ereport.yeestor.com', // 获取详情设备信息与plan信息地址
};

export default Settings;
