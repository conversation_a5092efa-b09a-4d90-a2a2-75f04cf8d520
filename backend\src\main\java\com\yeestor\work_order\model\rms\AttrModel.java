package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.AttributeConverter;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * 设计到的属性
 */
@Data
@JsonAutoDetect
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("设备与测试相关的属性")
public class AttrModel {


    @ApiModelProperty(value = "属性名称",notes = "属性的名称，需要与plan中的对应的属性名称对应。")
    private String attrName ;

    @ApiModelProperty(value = "数量",notes = "指对应的机器在此属性上能测多少个样片")
    private int num ;

    @Override
    public String toString() {
        return "{" +
               attrName + '=' + num +
                '}';
    }


    public static class AttrModelConverter implements AttributeConverter<List<AttrModel>, String> {
        private static final ObjectMapper mapper = new ObjectMapper();

        static {
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        }
        @Override
        public String convertToDatabaseColumn(List<AttrModel> attribute) {
            try {
                return mapper.writeValueAsString(attribute);
            } catch (JsonProcessingException ex) {
                return null;
            }
        }

        @Override
        public List<AttrModel> convertToEntityAttribute(String dbData) {
            try {
                return mapper.readValue(dbData, new TypeReference<>() {
                });
            } catch (IOException ex) {
                return Collections.emptyList();
            }
        }
    }
}
