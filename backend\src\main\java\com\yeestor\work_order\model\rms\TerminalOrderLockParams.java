package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;


@Data
@Builder
@ApiModel("工单给电脑下的终端加锁")
public class TerminalOrderLockParams {
    @ApiModelProperty("工单号")
    private String no;

    @ApiModelProperty("发起测试的用户")
    private String username;

    @ApiModelProperty("终端列表")
    private List<TerminalModel> terminalList;
}
