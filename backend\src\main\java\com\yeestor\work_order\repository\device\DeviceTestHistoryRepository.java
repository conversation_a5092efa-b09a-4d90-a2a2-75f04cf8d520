package com.yeestor.work_order.repository.device;

import com.yeestor.work_order.entity.device.DeviceTestHistoryEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface DeviceTestHistoryRepository extends JpaRepository<DeviceTestHistoryEntity, Long> {
    boolean existsBySubProductAndPlanNameAndMacAndUserId(String subProduct, String planName, String mac, String userId);

    Optional<DeviceTestHistoryEntity> findBySubProductAndPlanNameAndMacAndUserId(String subProduct, String planName, String mac, String userId);

    List<DeviceTestHistoryEntity> findBySubProductAndPlanNameAndUserIdOrderByPositionAscNoAsc(String subProduct, String planName, String userId);
}
