package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.model.http.resp.output.DAFlashPlanModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface OrderPlanRepository extends JpaRepository<OrderPlanEntity, Long> {


    int countByOrderIdAndIdIn(long orderId, List<Long> idList) ;

    boolean existsByIdIn(List<Long> ids);
    boolean existsByOrderIdAndFlashAndName(long orderId, String flash, String name);

    int countByOrderId(long orderId) ;
    int countByOrderIdAndFlash(long orderId, String flash) ;
    int countByOrderIdAndFlashAndStatus(long orderId, String flash, OrderPlanEntity.Status status) ;
    int countByOrderIdAndFlashAndStatusIn(long orderId, String flash, List<OrderPlanEntity.Status> statusList) ;

    List<OrderPlanEntity> findAllByOrderId(long orderId);
    List<OrderPlanEntity> findAllByOrderIdAndType(long orderId, int type);


    List<OrderPlanEntity> findAllByOrderIdAndFlashAndType(long orderId, String flash, int type);

    List<OrderPlanEntity> findAllByOrderIdAndFlashAndTypeAndStatusIn(long orderId, String flash, int type, List<OrderPlanEntity.Status> statusList);

    OrderPlanEntity findByOrderIdAndFlashAndName(long orderId, String flash, String plan);

    void deleteAllByOrderIdAndFlash(long orderId, String flash);

    void deleteAllByOrderId(long id);

    @Query(value = "select p from OrderPlan p where p.orderId = :orderId  and p.flash=:flash and (p.disabled = false  or p.disabled is null)")
    List<OrderPlanEntity> findAllByOrderIdAndFlashAndNotDisabled(long orderId, String flash);


    @Query(value = "select p from OrderPlan p " +
            "where p.orderId = :orderId  " +
            "and p.flash=:flash " +
            "and p.confirmedAt is not null " +
            "and p.type=:type " +
            "and (p.disabled = false  or p.disabled is null) "+
            "order by p.confirmedAt")
    List<OrderPlanEntity> findFirstConfirmedPlanList(long orderId, String flash, int type);


    /**
     * 更新指定Plan的预期结束时间
     * @param planId Plan 的ID
     * @param expectedEndTime 预期结束时间
     */
    @Modifying
    @Transactional
    @Query(value = "update OrderPlan p set p.expectedEndTime = :expectedEndTime where p.id = :planId")
    void updateExpectedEndTime(long planId, Long expectedEndTime);

    /**
     * 更新指定Plan的自动结束时间。
     * @param planId Plan 的ID
     * @param time autoEndTime
     */
    @Modifying
    @Transactional
    @Query(value = "update OrderPlan p set p.autoEndAt = :time where p.id = :planId")
    void updateAutoEndTime(long planId, Long time);

    List<OrderPlanEntity> findAllByOrderIdAndFlash(long orderId, String flash);

    @Query(value = "select p from OrderPlan p where p.orderId = :orderId  and p.flash = :flash and p.status in :statusList")
    List<OrderPlanEntity> findAllByOrderIdAndFlashAndStatus(long orderId, String flash, List<OrderPlanEntity.Status> statusList);

    List<OrderPlanEntity> findAllByIdIn(List<Long> planIds);

//    @Query(value = "select p from OrderPlan p where p.name = :name and p.belongTo = :belongTo and p.feature = :feature and p.status = :status")
    List<OrderPlanEntity> findAllByNameAndBelongToAndFeatureAndStatus(String name, String belongTo, String feature, OrderPlanEntity.Status status);

    List<OrderPlanEntity> findAllByStatusAndBelongToNotNull(OrderPlanEntity.Status status);

    @Query("select new com.yeestor.work_order.model.http.resp.output.DAFlashPlanModel(o, f, p) from WorkOrder o, OrderFlash f, OrderPlan p " +
            "where o.subProduct = :subProduct and o.chip like %:chip% and o.flash like %:flash% and o.fullVersion like %:fullVersion% " +
            "and o.id = f.orderId and f.createdAt >= :afterAt and f.createdAt <= :beforeAt and p.orderId = o.id and p.flash = f.flash ")
    List<DAFlashPlanModel> findOrderPlanBySubProductAndChipAndFlashAndTime(
            String subProduct,
            String chip,
            String flash,
            String fullVersion,
            long afterAt,
            long beforeAt
    );
}
