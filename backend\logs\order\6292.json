{"@timestamp":"2025-07-23T10:33:02.687+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a43e1d9c9ea80826","spanId":"a43e1d9c9ea80826","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:33:02.713+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a43e1d9c9ea80826","spanId":"a43e1d9c9ea80826","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.757+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a43e1d9c9ea80826","spanId":"a43e1d9c9ea80826","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.76+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a43e1d9c9ea80826","spanId":"a43e1d9c9ea80826","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.766+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a43e1d9c9ea80826","spanId":"a43e1d9c9ea80826","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.77+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a43e1d9c9ea80826","spanId":"a43e1d9c9ea80826","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.775+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a43e1d9c9ea80826","spanId":"a43e1d9c9ea80826","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.775+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a43e1d9c9ea80826","spanId":"a43e1d9c9ea80826","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.775+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a43e1d9c9ea80826","spanId":"a43e1d9c9ea80826","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:36:02.628+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71971fe657d9e9c3","spanId":"71971fe657d9e9c3","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:36:02.63+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71971fe657d9e9c3","spanId":"71971fe657d9e9c3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:36:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71971fe657d9e9c3","spanId":"71971fe657d9e9c3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:36:02.634+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71971fe657d9e9c3","spanId":"71971fe657d9e9c3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:36:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71971fe657d9e9c3","spanId":"71971fe657d9e9c3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:36:02.639+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71971fe657d9e9c3","spanId":"71971fe657d9e9c3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:36:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71971fe657d9e9c3","spanId":"71971fe657d9e9c3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:36:02.643+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71971fe657d9e9c3","spanId":"71971fe657d9e9c3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:36:02.643+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71971fe657d9e9c3","spanId":"71971fe657d9e9c3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.628+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0266a3f0d0ab0ee3","spanId":"0266a3f0d0ab0ee3","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:39:02.629+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0266a3f0d0ab0ee3","spanId":"0266a3f0d0ab0ee3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0266a3f0d0ab0ee3","spanId":"0266a3f0d0ab0ee3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.634+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0266a3f0d0ab0ee3","spanId":"0266a3f0d0ab0ee3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0266a3f0d0ab0ee3","spanId":"0266a3f0d0ab0ee3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.64+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0266a3f0d0ab0ee3","spanId":"0266a3f0d0ab0ee3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0266a3f0d0ab0ee3","spanId":"0266a3f0d0ab0ee3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.644+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0266a3f0d0ab0ee3","spanId":"0266a3f0d0ab0ee3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.644+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0266a3f0d0ab0ee3","spanId":"0266a3f0d0ab0ee3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5a250f42242382b3","spanId":"5a250f42242382b3","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:45:02.628+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5a250f42242382b3","spanId":"5a250f42242382b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5a250f42242382b3","spanId":"5a250f42242382b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.63+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5a250f42242382b3","spanId":"5a250f42242382b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5a250f42242382b3","spanId":"5a250f42242382b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.633+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5a250f42242382b3","spanId":"5a250f42242382b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5a250f42242382b3","spanId":"5a250f42242382b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.636+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5a250f42242382b3","spanId":"5a250f42242382b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.636+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5a250f42242382b3","spanId":"5a250f42242382b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"741b3c38fe691bcd","spanId":"741b3c38fe691bcd","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:48:02.625+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"741b3c38fe691bcd","spanId":"741b3c38fe691bcd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"741b3c38fe691bcd","spanId":"741b3c38fe691bcd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"741b3c38fe691bcd","spanId":"741b3c38fe691bcd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"741b3c38fe691bcd","spanId":"741b3c38fe691bcd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.63+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"741b3c38fe691bcd","spanId":"741b3c38fe691bcd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"741b3c38fe691bcd","spanId":"741b3c38fe691bcd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.632+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"741b3c38fe691bcd","spanId":"741b3c38fe691bcd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.632+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"741b3c38fe691bcd","spanId":"741b3c38fe691bcd","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.647+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3c64437d86a3d2c","spanId":"b3c64437d86a3d2c","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:51:02.648+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3c64437d86a3d2c","spanId":"b3c64437d86a3d2c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.652+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3c64437d86a3d2c","spanId":"b3c64437d86a3d2c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.653+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3c64437d86a3d2c","spanId":"b3c64437d86a3d2c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.657+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3c64437d86a3d2c","spanId":"b3c64437d86a3d2c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.657+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3c64437d86a3d2c","spanId":"b3c64437d86a3d2c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.661+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3c64437d86a3d2c","spanId":"b3c64437d86a3d2c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.661+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3c64437d86a3d2c","spanId":"b3c64437d86a3d2c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.662+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3c64437d86a3d2c","spanId":"b3c64437d86a3d2c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:57:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd2bd06c10484642","spanId":"cd2bd06c10484642","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:57:02.625+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd2bd06c10484642","spanId":"cd2bd06c10484642","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:57:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd2bd06c10484642","spanId":"cd2bd06c10484642","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:57:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd2bd06c10484642","spanId":"cd2bd06c10484642","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:57:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd2bd06c10484642","spanId":"cd2bd06c10484642","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:57:02.631+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd2bd06c10484642","spanId":"cd2bd06c10484642","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:57:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd2bd06c10484642","spanId":"cd2bd06c10484642","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:57:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd2bd06c10484642","spanId":"cd2bd06c10484642","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:57:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd2bd06c10484642","spanId":"cd2bd06c10484642","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3f8a4ed8da80058","spanId":"b3f8a4ed8da80058","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:03:02.625+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3f8a4ed8da80058","spanId":"b3f8a4ed8da80058","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3f8a4ed8da80058","spanId":"b3f8a4ed8da80058","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.627+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3f8a4ed8da80058","spanId":"b3f8a4ed8da80058","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3f8a4ed8da80058","spanId":"b3f8a4ed8da80058","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.63+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3f8a4ed8da80058","spanId":"b3f8a4ed8da80058","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3f8a4ed8da80058","spanId":"b3f8a4ed8da80058","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.632+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3f8a4ed8da80058","spanId":"b3f8a4ed8da80058","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.632+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b3f8a4ed8da80058","spanId":"b3f8a4ed8da80058","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f46faa362d7d26ab","spanId":"f46faa362d7d26ab","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:09:02.622+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f46faa362d7d26ab","spanId":"f46faa362d7d26ab","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f46faa362d7d26ab","spanId":"f46faa362d7d26ab","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.624+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f46faa362d7d26ab","spanId":"f46faa362d7d26ab","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f46faa362d7d26ab","spanId":"f46faa362d7d26ab","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.627+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f46faa362d7d26ab","spanId":"f46faa362d7d26ab","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f46faa362d7d26ab","spanId":"f46faa362d7d26ab","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.629+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f46faa362d7d26ab","spanId":"f46faa362d7d26ab","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.629+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f46faa362d7d26ab","spanId":"f46faa362d7d26ab","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d8bf097f5b9781d","spanId":"2d8bf097f5b9781d","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:12:02.627+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d8bf097f5b9781d","spanId":"2d8bf097f5b9781d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d8bf097f5b9781d","spanId":"2d8bf097f5b9781d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.63+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d8bf097f5b9781d","spanId":"2d8bf097f5b9781d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d8bf097f5b9781d","spanId":"2d8bf097f5b9781d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.632+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d8bf097f5b9781d","spanId":"2d8bf097f5b9781d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d8bf097f5b9781d","spanId":"2d8bf097f5b9781d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.635+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d8bf097f5b9781d","spanId":"2d8bf097f5b9781d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.635+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d8bf097f5b9781d","spanId":"2d8bf097f5b9781d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.628+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ae234283919af8bb","spanId":"ae234283919af8bb","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:15:02.629+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ae234283919af8bb","spanId":"ae234283919af8bb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ae234283919af8bb","spanId":"ae234283919af8bb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.631+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ae234283919af8bb","spanId":"ae234283919af8bb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ae234283919af8bb","spanId":"ae234283919af8bb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.633+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ae234283919af8bb","spanId":"ae234283919af8bb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ae234283919af8bb","spanId":"ae234283919af8bb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.636+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ae234283919af8bb","spanId":"ae234283919af8bb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.636+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ae234283919af8bb","spanId":"ae234283919af8bb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:33:02.633+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aaf46907e7591f09","spanId":"aaf46907e7591f09","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:33:02.639+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aaf46907e7591f09","spanId":"aaf46907e7591f09","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:33:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aaf46907e7591f09","spanId":"aaf46907e7591f09","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:33:02.644+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aaf46907e7591f09","spanId":"aaf46907e7591f09","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:33:02.649+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aaf46907e7591f09","spanId":"aaf46907e7591f09","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:33:02.649+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aaf46907e7591f09","spanId":"aaf46907e7591f09","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:33:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aaf46907e7591f09","spanId":"aaf46907e7591f09","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:33:02.654+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aaf46907e7591f09","spanId":"aaf46907e7591f09","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:33:02.654+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aaf46907e7591f09","spanId":"aaf46907e7591f09","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:36:31.163+08:00","@version":"1","message":"execute:IND_EMMC_9C-6B-00-17-EE-E3_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:IND_EMMC userName:stary.wang title:手动释放设备后电脑关机 planId:112526 mac:9C-6B-00-17-EE-E3","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f9de9dd7e2909ec","spanId":"9f9de9dd7e2909ec","no":"6292","traceType":"TimedShutdownDeviceJob","flash":"GDGDX2_32GB"}
{"@timestamp":"2025-07-23T11:36:31.228+08:00","@version":"1","message":"倒计时结束，即将释放设备EM4_9_22[9C-6B-00-17-EE-E3]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f9de9dd7e2909ec","spanId":"9f9de9dd7e2909ec","no":"6292","traceType":"TimedShutdownDeviceJob","flash":"GDGDX2_32GB"}
{"@timestamp":"2025-07-23T11:36:31.236+08:00","@version":"1","message":"需要关机的设备编号有: [EM4_9_22]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f9de9dd7e2909ec","spanId":"9f9de9dd7e2909ec","no":"6292","traceType":"TimedShutdownDeviceJob","flash":"GDGDX2_32GB"}
{"@timestamp":"2025-07-23T11:36:31.236+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=stary.wang, product=IND_EMMC, macList=[9C-6B-00-17-EE-E3])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f9de9dd7e2909ec","spanId":"9f9de9dd7e2909ec","no":"6292","traceType":"TimedShutdownDeviceJob","flash":"GDGDX2_32GB"}
{"@timestamp":"2025-07-23T11:36:31.285+08:00","@version":"1","message":"[49fded6] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f9de9dd7e2909ec","spanId":"9f9de9dd7e2909ec","no":"6292","traceType":"TimedShutdownDeviceJob","flash":"GDGDX2_32GB"}
{"@timestamp":"2025-07-23T11:36:41.613+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=stary.wang, product=IND_EMMC, macList=[9C-6B-00-17-EE-E3]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=9C-6B-00-17-EE-E3, pc_no=EM4-9-22, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f9de9dd7e2909ec","spanId":"9f9de9dd7e2909ec","no":"6292","traceType":"TimedShutdownDeviceJob","flash":"GDGDX2_32GB"}
{"@timestamp":"2025-07-23T11:36:41.614+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9f9de9dd7e2909ec","spanId":"9f9de9dd7e2909ec","no":"6292","traceType":"TimedShutdownDeviceJob","flash":"GDGDX2_32GB"}
{"@timestamp":"2025-07-23T11:39:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"37f27220ce55d1f8","spanId":"37f27220ce55d1f8","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:39:02.631+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"37f27220ce55d1f8","spanId":"37f27220ce55d1f8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"37f27220ce55d1f8","spanId":"37f27220ce55d1f8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.635+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"37f27220ce55d1f8","spanId":"37f27220ce55d1f8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"37f27220ce55d1f8","spanId":"37f27220ce55d1f8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.638+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"37f27220ce55d1f8","spanId":"37f27220ce55d1f8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"37f27220ce55d1f8","spanId":"37f27220ce55d1f8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.641+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"37f27220ce55d1f8","spanId":"37f27220ce55d1f8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.641+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"37f27220ce55d1f8","spanId":"37f27220ce55d1f8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.665+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88d6bd0f1e4965f9","spanId":"88d6bd0f1e4965f9","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:02.666+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88d6bd0f1e4965f9","spanId":"88d6bd0f1e4965f9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88d6bd0f1e4965f9","spanId":"88d6bd0f1e4965f9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.669+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88d6bd0f1e4965f9","spanId":"88d6bd0f1e4965f9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88d6bd0f1e4965f9","spanId":"88d6bd0f1e4965f9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.671+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88d6bd0f1e4965f9","spanId":"88d6bd0f1e4965f9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.674+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88d6bd0f1e4965f9","spanId":"88d6bd0f1e4965f9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.674+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88d6bd0f1e4965f9","spanId":"88d6bd0f1e4965f9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.674+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"88d6bd0f1e4965f9","spanId":"88d6bd0f1e4965f9","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c2b362c7427a889c","spanId":"c2b362c7427a889c","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:45:02.626+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c2b362c7427a889c","spanId":"c2b362c7427a889c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c2b362c7427a889c","spanId":"c2b362c7427a889c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.629+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c2b362c7427a889c","spanId":"c2b362c7427a889c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c2b362c7427a889c","spanId":"c2b362c7427a889c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.631+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c2b362c7427a889c","spanId":"c2b362c7427a889c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c2b362c7427a889c","spanId":"c2b362c7427a889c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c2b362c7427a889c","spanId":"c2b362c7427a889c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c2b362c7427a889c","spanId":"c2b362c7427a889c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:48:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c1892b8a424b8ed","spanId":"8c1892b8a424b8ed","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:48:02.633+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c1892b8a424b8ed","spanId":"8c1892b8a424b8ed","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:48:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c1892b8a424b8ed","spanId":"8c1892b8a424b8ed","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:48:02.64+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c1892b8a424b8ed","spanId":"8c1892b8a424b8ed","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:48:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c1892b8a424b8ed","spanId":"8c1892b8a424b8ed","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:48:02.647+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c1892b8a424b8ed","spanId":"8c1892b8a424b8ed","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:48:02.653+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c1892b8a424b8ed","spanId":"8c1892b8a424b8ed","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:48:02.653+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c1892b8a424b8ed","spanId":"8c1892b8a424b8ed","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:48:02.654+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8c1892b8a424b8ed","spanId":"8c1892b8a424b8ed","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.632+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4d76d895047bf8b3","spanId":"4d76d895047bf8b3","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:51:02.633+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4d76d895047bf8b3","spanId":"4d76d895047bf8b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4d76d895047bf8b3","spanId":"4d76d895047bf8b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.635+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4d76d895047bf8b3","spanId":"4d76d895047bf8b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4d76d895047bf8b3","spanId":"4d76d895047bf8b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.637+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4d76d895047bf8b3","spanId":"4d76d895047bf8b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4d76d895047bf8b3","spanId":"4d76d895047bf8b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.64+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4d76d895047bf8b3","spanId":"4d76d895047bf8b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.64+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4d76d895047bf8b3","spanId":"4d76d895047bf8b3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.657+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ff6052d5caacc3a","spanId":"0ff6052d5caacc3a","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:54:02.658+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ff6052d5caacc3a","spanId":"0ff6052d5caacc3a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ff6052d5caacc3a","spanId":"0ff6052d5caacc3a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.66+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ff6052d5caacc3a","spanId":"0ff6052d5caacc3a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.662+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ff6052d5caacc3a","spanId":"0ff6052d5caacc3a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.662+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ff6052d5caacc3a","spanId":"0ff6052d5caacc3a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ff6052d5caacc3a","spanId":"0ff6052d5caacc3a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.665+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ff6052d5caacc3a","spanId":"0ff6052d5caacc3a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.665+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ff6052d5caacc3a","spanId":"0ff6052d5caacc3a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:57:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21f3eb7ae9f6cb18","spanId":"21f3eb7ae9f6cb18","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:57:02.625+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21f3eb7ae9f6cb18","spanId":"21f3eb7ae9f6cb18","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:57:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21f3eb7ae9f6cb18","spanId":"21f3eb7ae9f6cb18","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:57:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21f3eb7ae9f6cb18","spanId":"21f3eb7ae9f6cb18","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:57:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21f3eb7ae9f6cb18","spanId":"21f3eb7ae9f6cb18","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:57:02.63+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21f3eb7ae9f6cb18","spanId":"21f3eb7ae9f6cb18","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:57:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21f3eb7ae9f6cb18","spanId":"21f3eb7ae9f6cb18","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:57:02.632+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21f3eb7ae9f6cb18","spanId":"21f3eb7ae9f6cb18","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:57:02.633+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21f3eb7ae9f6cb18","spanId":"21f3eb7ae9f6cb18","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"336f6815b5df41ee","spanId":"336f6815b5df41ee","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:00:02.623+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"336f6815b5df41ee","spanId":"336f6815b5df41ee","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.625+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"336f6815b5df41ee","spanId":"336f6815b5df41ee","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.626+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"336f6815b5df41ee","spanId":"336f6815b5df41ee","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"336f6815b5df41ee","spanId":"336f6815b5df41ee","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.628+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"336f6815b5df41ee","spanId":"336f6815b5df41ee","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"336f6815b5df41ee","spanId":"336f6815b5df41ee","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.63+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"336f6815b5df41ee","spanId":"336f6815b5df41ee","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.63+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"336f6815b5df41ee","spanId":"336f6815b5df41ee","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91b1e82ab9f22411","spanId":"91b1e82ab9f22411","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:03:02.628+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91b1e82ab9f22411","spanId":"91b1e82ab9f22411","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91b1e82ab9f22411","spanId":"91b1e82ab9f22411","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.63+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91b1e82ab9f22411","spanId":"91b1e82ab9f22411","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91b1e82ab9f22411","spanId":"91b1e82ab9f22411","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.632+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91b1e82ab9f22411","spanId":"91b1e82ab9f22411","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91b1e82ab9f22411","spanId":"91b1e82ab9f22411","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91b1e82ab9f22411","spanId":"91b1e82ab9f22411","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"91b1e82ab9f22411","spanId":"91b1e82ab9f22411","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.652+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61882298907050d6","spanId":"61882298907050d6","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:06:02.653+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61882298907050d6","spanId":"61882298907050d6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.655+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61882298907050d6","spanId":"61882298907050d6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.655+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61882298907050d6","spanId":"61882298907050d6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.657+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61882298907050d6","spanId":"61882298907050d6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.657+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61882298907050d6","spanId":"61882298907050d6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.659+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61882298907050d6","spanId":"61882298907050d6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.659+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61882298907050d6","spanId":"61882298907050d6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.659+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61882298907050d6","spanId":"61882298907050d6","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:09:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6f8410c598ca4a7c","spanId":"6f8410c598ca4a7c","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:09:02.626+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6f8410c598ca4a7c","spanId":"6f8410c598ca4a7c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:09:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6f8410c598ca4a7c","spanId":"6f8410c598ca4a7c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:09:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6f8410c598ca4a7c","spanId":"6f8410c598ca4a7c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:09:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6f8410c598ca4a7c","spanId":"6f8410c598ca4a7c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:09:02.63+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6f8410c598ca4a7c","spanId":"6f8410c598ca4a7c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:09:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6f8410c598ca4a7c","spanId":"6f8410c598ca4a7c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:09:02.633+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6f8410c598ca4a7c","spanId":"6f8410c598ca4a7c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:09:02.633+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6f8410c598ca4a7c","spanId":"6f8410c598ca4a7c","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:12:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f4bc90d94e7b6260","spanId":"f4bc90d94e7b6260","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:12:02.625+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f4bc90d94e7b6260","spanId":"f4bc90d94e7b6260","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:12:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f4bc90d94e7b6260","spanId":"f4bc90d94e7b6260","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:12:02.627+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f4bc90d94e7b6260","spanId":"f4bc90d94e7b6260","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:12:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f4bc90d94e7b6260","spanId":"f4bc90d94e7b6260","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:12:02.629+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f4bc90d94e7b6260","spanId":"f4bc90d94e7b6260","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:12:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f4bc90d94e7b6260","spanId":"f4bc90d94e7b6260","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:12:02.632+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f4bc90d94e7b6260","spanId":"f4bc90d94e7b6260","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:12:02.632+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f4bc90d94e7b6260","spanId":"f4bc90d94e7b6260","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56dfbae395837f3b","spanId":"56dfbae395837f3b","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:15:02.625+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56dfbae395837f3b","spanId":"56dfbae395837f3b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56dfbae395837f3b","spanId":"56dfbae395837f3b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56dfbae395837f3b","spanId":"56dfbae395837f3b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56dfbae395837f3b","spanId":"56dfbae395837f3b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.631+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56dfbae395837f3b","spanId":"56dfbae395837f3b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56dfbae395837f3b","spanId":"56dfbae395837f3b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56dfbae395837f3b","spanId":"56dfbae395837f3b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56dfbae395837f3b","spanId":"56dfbae395837f3b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.701+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc2b978c3a7e7cc8","spanId":"cc2b978c3a7e7cc8","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:18:02.71+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc2b978c3a7e7cc8","spanId":"cc2b978c3a7e7cc8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.717+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc2b978c3a7e7cc8","spanId":"cc2b978c3a7e7cc8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.718+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc2b978c3a7e7cc8","spanId":"cc2b978c3a7e7cc8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.724+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc2b978c3a7e7cc8","spanId":"cc2b978c3a7e7cc8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.724+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc2b978c3a7e7cc8","spanId":"cc2b978c3a7e7cc8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.73+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc2b978c3a7e7cc8","spanId":"cc2b978c3a7e7cc8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.731+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc2b978c3a7e7cc8","spanId":"cc2b978c3a7e7cc8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.731+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc2b978c3a7e7cc8","spanId":"cc2b978c3a7e7cc8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:21:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e52f1529c22ba1f4","spanId":"e52f1529c22ba1f4","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:21:02.622+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=193)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e52f1529c22ba1f4","spanId":"e52f1529c22ba1f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:21:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e52f1529c22ba1f4","spanId":"e52f1529c22ba1f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:21:02.624+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e52f1529c22ba1f4","spanId":"e52f1529c22ba1f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:21:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e52f1529c22ba1f4","spanId":"e52f1529c22ba1f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:21:02.627+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e52f1529c22ba1f4","spanId":"e52f1529c22ba1f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:21:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e52f1529c22ba1f4","spanId":"e52f1529c22ba1f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:21:02.629+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e52f1529c22ba1f4","spanId":"e52f1529c22ba1f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:21:02.629+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e52f1529c22ba1f4","spanId":"e52f1529c22ba1f4","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:24:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"462d816148c743cc","spanId":"462d816148c743cc","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:24:02.623+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"462d816148c743cc","spanId":"462d816148c743cc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:24:02.625+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"462d816148c743cc","spanId":"462d816148c743cc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:24:02.625+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"462d816148c743cc","spanId":"462d816148c743cc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:24:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"462d816148c743cc","spanId":"462d816148c743cc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:24:02.627+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"462d816148c743cc","spanId":"462d816148c743cc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:24:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"462d816148c743cc","spanId":"462d816148c743cc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:24:02.629+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"462d816148c743cc","spanId":"462d816148c743cc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:24:02.629+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"462d816148c743cc","spanId":"462d816148c743cc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"090c7382e2e2aac8","spanId":"090c7382e2e2aac8","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:27:02.628+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"090c7382e2e2aac8","spanId":"090c7382e2e2aac8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"090c7382e2e2aac8","spanId":"090c7382e2e2aac8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.63+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"090c7382e2e2aac8","spanId":"090c7382e2e2aac8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"090c7382e2e2aac8","spanId":"090c7382e2e2aac8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.632+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"090c7382e2e2aac8","spanId":"090c7382e2e2aac8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"090c7382e2e2aac8","spanId":"090c7382e2e2aac8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"090c7382e2e2aac8","spanId":"090c7382e2e2aac8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"090c7382e2e2aac8","spanId":"090c7382e2e2aac8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.663+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5820cb08269c38fb","spanId":"5820cb08269c38fb","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:02.664+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5820cb08269c38fb","spanId":"5820cb08269c38fb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.666+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5820cb08269c38fb","spanId":"5820cb08269c38fb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.666+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5820cb08269c38fb","spanId":"5820cb08269c38fb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5820cb08269c38fb","spanId":"5820cb08269c38fb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.668+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5820cb08269c38fb","spanId":"5820cb08269c38fb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.67+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5820cb08269c38fb","spanId":"5820cb08269c38fb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.67+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5820cb08269c38fb","spanId":"5820cb08269c38fb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.67+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5820cb08269c38fb","spanId":"5820cb08269c38fb","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:33:02.623+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a9af7011200ad1ef","spanId":"a9af7011200ad1ef","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:33:02.624+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a9af7011200ad1ef","spanId":"a9af7011200ad1ef","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:33:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a9af7011200ad1ef","spanId":"a9af7011200ad1ef","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:33:02.627+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a9af7011200ad1ef","spanId":"a9af7011200ad1ef","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:33:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a9af7011200ad1ef","spanId":"a9af7011200ad1ef","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:33:02.629+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a9af7011200ad1ef","spanId":"a9af7011200ad1ef","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:33:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a9af7011200ad1ef","spanId":"a9af7011200ad1ef","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:33:02.631+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a9af7011200ad1ef","spanId":"a9af7011200ad1ef","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:33:02.631+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a9af7011200ad1ef","spanId":"a9af7011200ad1ef","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1eb12fd17bc9188a","spanId":"1eb12fd17bc9188a","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:36:02.626+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1eb12fd17bc9188a","spanId":"1eb12fd17bc9188a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1eb12fd17bc9188a","spanId":"1eb12fd17bc9188a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1eb12fd17bc9188a","spanId":"1eb12fd17bc9188a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1eb12fd17bc9188a","spanId":"1eb12fd17bc9188a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.631+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1eb12fd17bc9188a","spanId":"1eb12fd17bc9188a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1eb12fd17bc9188a","spanId":"1eb12fd17bc9188a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.633+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1eb12fd17bc9188a","spanId":"1eb12fd17bc9188a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.633+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1eb12fd17bc9188a","spanId":"1eb12fd17bc9188a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c61a9facf77baba","spanId":"2c61a9facf77baba","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:39:02.626+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c61a9facf77baba","spanId":"2c61a9facf77baba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c61a9facf77baba","spanId":"2c61a9facf77baba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c61a9facf77baba","spanId":"2c61a9facf77baba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c61a9facf77baba","spanId":"2c61a9facf77baba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.63+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c61a9facf77baba","spanId":"2c61a9facf77baba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c61a9facf77baba","spanId":"2c61a9facf77baba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.632+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c61a9facf77baba","spanId":"2c61a9facf77baba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.632+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2c61a9facf77baba","spanId":"2c61a9facf77baba","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.67+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbbfc771e16ef9c8","spanId":"fbbfc771e16ef9c8","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:02.67+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbbfc771e16ef9c8","spanId":"fbbfc771e16ef9c8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.673+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbbfc771e16ef9c8","spanId":"fbbfc771e16ef9c8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.673+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbbfc771e16ef9c8","spanId":"fbbfc771e16ef9c8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.675+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbbfc771e16ef9c8","spanId":"fbbfc771e16ef9c8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.675+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbbfc771e16ef9c8","spanId":"fbbfc771e16ef9c8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.676+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbbfc771e16ef9c8","spanId":"fbbfc771e16ef9c8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.677+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbbfc771e16ef9c8","spanId":"fbbfc771e16ef9c8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.677+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbbfc771e16ef9c8","spanId":"fbbfc771e16ef9c8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"79296073cc6b6d92","spanId":"79296073cc6b6d92","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:45:02.623+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"79296073cc6b6d92","spanId":"79296073cc6b6d92","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.625+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"79296073cc6b6d92","spanId":"79296073cc6b6d92","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.625+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"79296073cc6b6d92","spanId":"79296073cc6b6d92","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"79296073cc6b6d92","spanId":"79296073cc6b6d92","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.627+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"79296073cc6b6d92","spanId":"79296073cc6b6d92","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"79296073cc6b6d92","spanId":"79296073cc6b6d92","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.629+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"79296073cc6b6d92","spanId":"79296073cc6b6d92","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.629+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"79296073cc6b6d92","spanId":"79296073cc6b6d92","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:48:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5cf07716b179b6d3","spanId":"5cf07716b179b6d3","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:48:02.622+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5cf07716b179b6d3","spanId":"5cf07716b179b6d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:48:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5cf07716b179b6d3","spanId":"5cf07716b179b6d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:48:02.624+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5cf07716b179b6d3","spanId":"5cf07716b179b6d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:48:02.626+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5cf07716b179b6d3","spanId":"5cf07716b179b6d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:48:02.627+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5cf07716b179b6d3","spanId":"5cf07716b179b6d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:48:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5cf07716b179b6d3","spanId":"5cf07716b179b6d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:48:02.629+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5cf07716b179b6d3","spanId":"5cf07716b179b6d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:48:02.629+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5cf07716b179b6d3","spanId":"5cf07716b179b6d3","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.628+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc4e5390718b75bf","spanId":"dc4e5390718b75bf","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:51:02.629+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc4e5390718b75bf","spanId":"dc4e5390718b75bf","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc4e5390718b75bf","spanId":"dc4e5390718b75bf","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.631+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc4e5390718b75bf","spanId":"dc4e5390718b75bf","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc4e5390718b75bf","spanId":"dc4e5390718b75bf","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.634+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc4e5390718b75bf","spanId":"dc4e5390718b75bf","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.636+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc4e5390718b75bf","spanId":"dc4e5390718b75bf","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.636+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc4e5390718b75bf","spanId":"dc4e5390718b75bf","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.636+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dc4e5390718b75bf","spanId":"dc4e5390718b75bf","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.652+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4b1110b3c109f559","spanId":"4b1110b3c109f559","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:54:02.654+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4b1110b3c109f559","spanId":"4b1110b3c109f559","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.657+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4b1110b3c109f559","spanId":"4b1110b3c109f559","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.657+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4b1110b3c109f559","spanId":"4b1110b3c109f559","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.659+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4b1110b3c109f559","spanId":"4b1110b3c109f559","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.659+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4b1110b3c109f559","spanId":"4b1110b3c109f559","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.661+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4b1110b3c109f559","spanId":"4b1110b3c109f559","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.661+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4b1110b3c109f559","spanId":"4b1110b3c109f559","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.661+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4b1110b3c109f559","spanId":"4b1110b3c109f559","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:57:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"60e4bdb35283d5d8","spanId":"60e4bdb35283d5d8","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:57:02.627+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"60e4bdb35283d5d8","spanId":"60e4bdb35283d5d8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:57:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"60e4bdb35283d5d8","spanId":"60e4bdb35283d5d8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:57:02.63+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"60e4bdb35283d5d8","spanId":"60e4bdb35283d5d8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:57:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"60e4bdb35283d5d8","spanId":"60e4bdb35283d5d8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:57:02.632+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"60e4bdb35283d5d8","spanId":"60e4bdb35283d5d8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:57:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"60e4bdb35283d5d8","spanId":"60e4bdb35283d5d8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:57:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"60e4bdb35283d5d8","spanId":"60e4bdb35283d5d8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:57:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"60e4bdb35283d5d8","spanId":"60e4bdb35283d5d8","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0da5cce0d9e2fc39","spanId":"0da5cce0d9e2fc39","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:00:02.627+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0da5cce0d9e2fc39","spanId":"0da5cce0d9e2fc39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0da5cce0d9e2fc39","spanId":"0da5cce0d9e2fc39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.629+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0da5cce0d9e2fc39","spanId":"0da5cce0d9e2fc39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0da5cce0d9e2fc39","spanId":"0da5cce0d9e2fc39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.631+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0da5cce0d9e2fc39","spanId":"0da5cce0d9e2fc39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0da5cce0d9e2fc39","spanId":"0da5cce0d9e2fc39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.633+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0da5cce0d9e2fc39","spanId":"0da5cce0d9e2fc39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.633+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0da5cce0d9e2fc39","spanId":"0da5cce0d9e2fc39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:03:02.64+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"372274a44dba9484","spanId":"372274a44dba9484","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:03:02.64+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"372274a44dba9484","spanId":"372274a44dba9484","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:03:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"372274a44dba9484","spanId":"372274a44dba9484","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:03:02.643+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"372274a44dba9484","spanId":"372274a44dba9484","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:03:02.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"372274a44dba9484","spanId":"372274a44dba9484","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:03:02.645+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"372274a44dba9484","spanId":"372274a44dba9484","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:03:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"372274a44dba9484","spanId":"372274a44dba9484","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:03:02.647+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"372274a44dba9484","spanId":"372274a44dba9484","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:03:02.647+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"372274a44dba9484","spanId":"372274a44dba9484","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.662+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5086c48ddefe185","spanId":"a5086c48ddefe185","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:06:02.663+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5086c48ddefe185","spanId":"a5086c48ddefe185","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5086c48ddefe185","spanId":"a5086c48ddefe185","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.665+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5086c48ddefe185","spanId":"a5086c48ddefe185","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5086c48ddefe185","spanId":"a5086c48ddefe185","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.671+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5086c48ddefe185","spanId":"a5086c48ddefe185","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.673+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5086c48ddefe185","spanId":"a5086c48ddefe185","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.673+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5086c48ddefe185","spanId":"a5086c48ddefe185","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.673+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5086c48ddefe185","spanId":"a5086c48ddefe185","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:09:02.641+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b417b0e13377cf37","spanId":"b417b0e13377cf37","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:09:02.642+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b417b0e13377cf37","spanId":"b417b0e13377cf37","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:09:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b417b0e13377cf37","spanId":"b417b0e13377cf37","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:09:02.644+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b417b0e13377cf37","spanId":"b417b0e13377cf37","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:09:02.646+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b417b0e13377cf37","spanId":"b417b0e13377cf37","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:09:02.646+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b417b0e13377cf37","spanId":"b417b0e13377cf37","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:09:02.649+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b417b0e13377cf37","spanId":"b417b0e13377cf37","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:09:02.649+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b417b0e13377cf37","spanId":"b417b0e13377cf37","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:09:02.649+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b417b0e13377cf37","spanId":"b417b0e13377cf37","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.623+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"688d78d68ee53a9e","spanId":"688d78d68ee53a9e","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:12:02.624+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"688d78d68ee53a9e","spanId":"688d78d68ee53a9e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.626+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"688d78d68ee53a9e","spanId":"688d78d68ee53a9e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.626+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"688d78d68ee53a9e","spanId":"688d78d68ee53a9e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"688d78d68ee53a9e","spanId":"688d78d68ee53a9e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.628+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"688d78d68ee53a9e","spanId":"688d78d68ee53a9e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"688d78d68ee53a9e","spanId":"688d78d68ee53a9e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.631+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"688d78d68ee53a9e","spanId":"688d78d68ee53a9e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.631+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"688d78d68ee53a9e","spanId":"688d78d68ee53a9e","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c37cd1a76ff9b32","spanId":"9c37cd1a76ff9b32","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:15:02.626+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c37cd1a76ff9b32","spanId":"9c37cd1a76ff9b32","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c37cd1a76ff9b32","spanId":"9c37cd1a76ff9b32","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c37cd1a76ff9b32","spanId":"9c37cd1a76ff9b32","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c37cd1a76ff9b32","spanId":"9c37cd1a76ff9b32","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.63+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c37cd1a76ff9b32","spanId":"9c37cd1a76ff9b32","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c37cd1a76ff9b32","spanId":"9c37cd1a76ff9b32","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.632+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c37cd1a76ff9b32","spanId":"9c37cd1a76ff9b32","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.632+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c37cd1a76ff9b32","spanId":"9c37cd1a76ff9b32","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.675+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e87055f697ca7893","spanId":"e87055f697ca7893","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:02.676+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e87055f697ca7893","spanId":"e87055f697ca7893","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e87055f697ca7893","spanId":"e87055f697ca7893","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.678+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e87055f697ca7893","spanId":"e87055f697ca7893","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.68+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e87055f697ca7893","spanId":"e87055f697ca7893","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.68+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e87055f697ca7893","spanId":"e87055f697ca7893","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.682+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e87055f697ca7893","spanId":"e87055f697ca7893","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.682+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e87055f697ca7893","spanId":"e87055f697ca7893","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.682+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e87055f697ca7893","spanId":"e87055f697ca7893","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b2ed0e034adac8d","spanId":"7b2ed0e034adac8d","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:21:02.637+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=194)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b2ed0e034adac8d","spanId":"7b2ed0e034adac8d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b2ed0e034adac8d","spanId":"7b2ed0e034adac8d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.639+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b2ed0e034adac8d","spanId":"7b2ed0e034adac8d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.642+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b2ed0e034adac8d","spanId":"7b2ed0e034adac8d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.642+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b2ed0e034adac8d","spanId":"7b2ed0e034adac8d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.644+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b2ed0e034adac8d","spanId":"7b2ed0e034adac8d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.644+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b2ed0e034adac8d","spanId":"7b2ed0e034adac8d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.644+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b2ed0e034adac8d","spanId":"7b2ed0e034adac8d","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:24:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81a3a26d5d3db2f2","spanId":"81a3a26d5d3db2f2","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:24:02.625+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81a3a26d5d3db2f2","spanId":"81a3a26d5d3db2f2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:24:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81a3a26d5d3db2f2","spanId":"81a3a26d5d3db2f2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:24:02.627+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81a3a26d5d3db2f2","spanId":"81a3a26d5d3db2f2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:24:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81a3a26d5d3db2f2","spanId":"81a3a26d5d3db2f2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:24:02.629+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81a3a26d5d3db2f2","spanId":"81a3a26d5d3db2f2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:24:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81a3a26d5d3db2f2","spanId":"81a3a26d5d3db2f2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:24:02.631+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81a3a26d5d3db2f2","spanId":"81a3a26d5d3db2f2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:24:02.631+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81a3a26d5d3db2f2","spanId":"81a3a26d5d3db2f2","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.631+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5ee8d85ea860310","spanId":"a5ee8d85ea860310","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:27:02.632+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5ee8d85ea860310","spanId":"a5ee8d85ea860310","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5ee8d85ea860310","spanId":"a5ee8d85ea860310","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.634+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5ee8d85ea860310","spanId":"a5ee8d85ea860310","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.637+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5ee8d85ea860310","spanId":"a5ee8d85ea860310","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.637+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5ee8d85ea860310","spanId":"a5ee8d85ea860310","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5ee8d85ea860310","spanId":"a5ee8d85ea860310","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.639+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5ee8d85ea860310","spanId":"a5ee8d85ea860310","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.639+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a5ee8d85ea860310","spanId":"a5ee8d85ea860310","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.657+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ddde4404c157706a","spanId":"ddde4404c157706a","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:02.658+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ddde4404c157706a","spanId":"ddde4404c157706a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ddde4404c157706a","spanId":"ddde4404c157706a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.66+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ddde4404c157706a","spanId":"ddde4404c157706a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.662+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ddde4404c157706a","spanId":"ddde4404c157706a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.663+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ddde4404c157706a","spanId":"ddde4404c157706a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ddde4404c157706a","spanId":"ddde4404c157706a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.665+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ddde4404c157706a","spanId":"ddde4404c157706a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.665+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ddde4404c157706a","spanId":"ddde4404c157706a","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a8febeb4f4895ec","spanId":"7a8febeb4f4895ec","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:33:02.627+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a8febeb4f4895ec","spanId":"7a8febeb4f4895ec","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a8febeb4f4895ec","spanId":"7a8febeb4f4895ec","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.629+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a8febeb4f4895ec","spanId":"7a8febeb4f4895ec","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a8febeb4f4895ec","spanId":"7a8febeb4f4895ec","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.631+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a8febeb4f4895ec","spanId":"7a8febeb4f4895ec","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a8febeb4f4895ec","spanId":"7a8febeb4f4895ec","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.633+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a8febeb4f4895ec","spanId":"7a8febeb4f4895ec","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.633+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7a8febeb4f4895ec","spanId":"7a8febeb4f4895ec","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff42e850dab3c27f","spanId":"ff42e850dab3c27f","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:36:02.626+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff42e850dab3c27f","spanId":"ff42e850dab3c27f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff42e850dab3c27f","spanId":"ff42e850dab3c27f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff42e850dab3c27f","spanId":"ff42e850dab3c27f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff42e850dab3c27f","spanId":"ff42e850dab3c27f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.63+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff42e850dab3c27f","spanId":"ff42e850dab3c27f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff42e850dab3c27f","spanId":"ff42e850dab3c27f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.633+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff42e850dab3c27f","spanId":"ff42e850dab3c27f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.633+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ff42e850dab3c27f","spanId":"ff42e850dab3c27f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38f895afad6ba941","spanId":"38f895afad6ba941","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:39:02.627+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38f895afad6ba941","spanId":"38f895afad6ba941","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38f895afad6ba941","spanId":"38f895afad6ba941","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.631+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38f895afad6ba941","spanId":"38f895afad6ba941","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38f895afad6ba941","spanId":"38f895afad6ba941","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.633+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38f895afad6ba941","spanId":"38f895afad6ba941","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38f895afad6ba941","spanId":"38f895afad6ba941","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.636+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38f895afad6ba941","spanId":"38f895afad6ba941","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.636+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"38f895afad6ba941","spanId":"38f895afad6ba941","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.657+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9642cbd12233a437","spanId":"9642cbd12233a437","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:02.658+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9642cbd12233a437","spanId":"9642cbd12233a437","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.661+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9642cbd12233a437","spanId":"9642cbd12233a437","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.661+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9642cbd12233a437","spanId":"9642cbd12233a437","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.664+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9642cbd12233a437","spanId":"9642cbd12233a437","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.664+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9642cbd12233a437","spanId":"9642cbd12233a437","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.667+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9642cbd12233a437","spanId":"9642cbd12233a437","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.667+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9642cbd12233a437","spanId":"9642cbd12233a437","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.667+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9642cbd12233a437","spanId":"9642cbd12233a437","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"126d7a1b8d3cf7a1","spanId":"126d7a1b8d3cf7a1","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:45:02.628+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"126d7a1b8d3cf7a1","spanId":"126d7a1b8d3cf7a1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"126d7a1b8d3cf7a1","spanId":"126d7a1b8d3cf7a1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.63+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"126d7a1b8d3cf7a1","spanId":"126d7a1b8d3cf7a1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"126d7a1b8d3cf7a1","spanId":"126d7a1b8d3cf7a1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.633+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"126d7a1b8d3cf7a1","spanId":"126d7a1b8d3cf7a1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"126d7a1b8d3cf7a1","spanId":"126d7a1b8d3cf7a1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.635+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"126d7a1b8d3cf7a1","spanId":"126d7a1b8d3cf7a1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.635+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"126d7a1b8d3cf7a1","spanId":"126d7a1b8d3cf7a1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78e1f218cbf89671","spanId":"78e1f218cbf89671","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:48:02.625+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78e1f218cbf89671","spanId":"78e1f218cbf89671","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78e1f218cbf89671","spanId":"78e1f218cbf89671","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.628+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78e1f218cbf89671","spanId":"78e1f218cbf89671","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78e1f218cbf89671","spanId":"78e1f218cbf89671","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.63+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78e1f218cbf89671","spanId":"78e1f218cbf89671","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78e1f218cbf89671","spanId":"78e1f218cbf89671","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.633+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78e1f218cbf89671","spanId":"78e1f218cbf89671","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.633+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"78e1f218cbf89671","spanId":"78e1f218cbf89671","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.629+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bca7deeb2f96b98","spanId":"5bca7deeb2f96b98","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:51:02.63+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bca7deeb2f96b98","spanId":"5bca7deeb2f96b98","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bca7deeb2f96b98","spanId":"5bca7deeb2f96b98","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.632+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bca7deeb2f96b98","spanId":"5bca7deeb2f96b98","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bca7deeb2f96b98","spanId":"5bca7deeb2f96b98","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.634+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bca7deeb2f96b98","spanId":"5bca7deeb2f96b98","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bca7deeb2f96b98","spanId":"5bca7deeb2f96b98","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.635+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bca7deeb2f96b98","spanId":"5bca7deeb2f96b98","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.636+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5bca7deeb2f96b98","spanId":"5bca7deeb2f96b98","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.675+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e748927a2d50a39","spanId":"5e748927a2d50a39","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:54:02.676+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e748927a2d50a39","spanId":"5e748927a2d50a39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e748927a2d50a39","spanId":"5e748927a2d50a39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.678+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e748927a2d50a39","spanId":"5e748927a2d50a39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.681+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e748927a2d50a39","spanId":"5e748927a2d50a39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.681+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e748927a2d50a39","spanId":"5e748927a2d50a39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.683+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e748927a2d50a39","spanId":"5e748927a2d50a39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.683+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e748927a2d50a39","spanId":"5e748927a2d50a39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.683+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e748927a2d50a39","spanId":"5e748927a2d50a39","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:57:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1f0bb3d1c0a9443b","spanId":"1f0bb3d1c0a9443b","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:57:02.622+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1f0bb3d1c0a9443b","spanId":"1f0bb3d1c0a9443b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:57:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1f0bb3d1c0a9443b","spanId":"1f0bb3d1c0a9443b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:57:02.624+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1f0bb3d1c0a9443b","spanId":"1f0bb3d1c0a9443b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:57:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1f0bb3d1c0a9443b","spanId":"1f0bb3d1c0a9443b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:57:02.627+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1f0bb3d1c0a9443b","spanId":"1f0bb3d1c0a9443b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:57:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1f0bb3d1c0a9443b","spanId":"1f0bb3d1c0a9443b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:57:02.629+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1f0bb3d1c0a9443b","spanId":"1f0bb3d1c0a9443b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:57:02.629+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1f0bb3d1c0a9443b","spanId":"1f0bb3d1c0a9443b","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71e3232522d780c1","spanId":"71e3232522d780c1","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:00:02.627+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71e3232522d780c1","spanId":"71e3232522d780c1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71e3232522d780c1","spanId":"71e3232522d780c1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.629+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71e3232522d780c1","spanId":"71e3232522d780c1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71e3232522d780c1","spanId":"71e3232522d780c1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.631+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71e3232522d780c1","spanId":"71e3232522d780c1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71e3232522d780c1","spanId":"71e3232522d780c1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71e3232522d780c1","spanId":"71e3232522d780c1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"71e3232522d780c1","spanId":"71e3232522d780c1","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9d5419f0861b679","spanId":"b9d5419f0861b679","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:03:02.628+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9d5419f0861b679","spanId":"b9d5419f0861b679","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9d5419f0861b679","spanId":"b9d5419f0861b679","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.632+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9d5419f0861b679","spanId":"b9d5419f0861b679","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9d5419f0861b679","spanId":"b9d5419f0861b679","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.635+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9d5419f0861b679","spanId":"b9d5419f0861b679","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.639+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9d5419f0861b679","spanId":"b9d5419f0861b679","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.639+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9d5419f0861b679","spanId":"b9d5419f0861b679","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.64+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b9d5419f0861b679","spanId":"b9d5419f0861b679","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.674+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4f496ed45cc9966","spanId":"b4f496ed45cc9966","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:02.676+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4f496ed45cc9966","spanId":"b4f496ed45cc9966","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4f496ed45cc9966","spanId":"b4f496ed45cc9966","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.678+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4f496ed45cc9966","spanId":"b4f496ed45cc9966","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.68+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4f496ed45cc9966","spanId":"b4f496ed45cc9966","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.68+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4f496ed45cc9966","spanId":"b4f496ed45cc9966","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.683+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4f496ed45cc9966","spanId":"b4f496ed45cc9966","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.683+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4f496ed45cc9966","spanId":"b4f496ed45cc9966","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.683+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4f496ed45cc9966","spanId":"b4f496ed45cc9966","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e5988c565e2abd99","spanId":"e5988c565e2abd99","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:09:02.631+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e5988c565e2abd99","spanId":"e5988c565e2abd99","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e5988c565e2abd99","spanId":"e5988c565e2abd99","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.633+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e5988c565e2abd99","spanId":"e5988c565e2abd99","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e5988c565e2abd99","spanId":"e5988c565e2abd99","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.635+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e5988c565e2abd99","spanId":"e5988c565e2abd99","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e5988c565e2abd99","spanId":"e5988c565e2abd99","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.638+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e5988c565e2abd99","spanId":"e5988c565e2abd99","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.638+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e5988c565e2abd99","spanId":"e5988c565e2abd99","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:12:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be99f31f38eb06dc","spanId":"be99f31f38eb06dc","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:12:02.626+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be99f31f38eb06dc","spanId":"be99f31f38eb06dc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:12:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be99f31f38eb06dc","spanId":"be99f31f38eb06dc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:12:02.629+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be99f31f38eb06dc","spanId":"be99f31f38eb06dc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:12:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be99f31f38eb06dc","spanId":"be99f31f38eb06dc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:12:02.632+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be99f31f38eb06dc","spanId":"be99f31f38eb06dc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:12:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be99f31f38eb06dc","spanId":"be99f31f38eb06dc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:12:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be99f31f38eb06dc","spanId":"be99f31f38eb06dc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:12:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be99f31f38eb06dc","spanId":"be99f31f38eb06dc","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7757b8c6eecb31d5","spanId":"7757b8c6eecb31d5","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:15:02.626+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7757b8c6eecb31d5","spanId":"7757b8c6eecb31d5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7757b8c6eecb31d5","spanId":"7757b8c6eecb31d5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.629+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7757b8c6eecb31d5","spanId":"7757b8c6eecb31d5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7757b8c6eecb31d5","spanId":"7757b8c6eecb31d5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.632+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7757b8c6eecb31d5","spanId":"7757b8c6eecb31d5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7757b8c6eecb31d5","spanId":"7757b8c6eecb31d5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.634+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7757b8c6eecb31d5","spanId":"7757b8c6eecb31d5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.634+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7757b8c6eecb31d5","spanId":"7757b8c6eecb31d5","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.679+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cfea6d40c0ee149f","spanId":"cfea6d40c0ee149f","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:18:02.68+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cfea6d40c0ee149f","spanId":"cfea6d40c0ee149f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.683+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cfea6d40c0ee149f","spanId":"cfea6d40c0ee149f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.683+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cfea6d40c0ee149f","spanId":"cfea6d40c0ee149f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.685+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cfea6d40c0ee149f","spanId":"cfea6d40c0ee149f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.685+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cfea6d40c0ee149f","spanId":"cfea6d40c0ee149f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.688+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cfea6d40c0ee149f","spanId":"cfea6d40c0ee149f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.688+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cfea6d40c0ee149f","spanId":"cfea6d40c0ee149f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.688+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cfea6d40c0ee149f","spanId":"cfea6d40c0ee149f","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:21:02.632+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbde27dfee7b8307","spanId":"fbde27dfee7b8307","context":"QueueService","no":"6292","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:21:02.634+08:00","@version":"1","message":"find 3 flash: [OrderFlashEntity(id=6526, orderId=6292, flash=GDGDX1_16GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX1_16GB, num=200, leftNum=200), OrderFlashEntity(id=6527, orderId=6292, flash=GDGDX2_32GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX2_32GB, num=200, leftNum=188), OrderFlashEntity(id=6528, orderId=6292, flash=GDGDX4_64GB, orderFlashNo=YS8293ENMP020103###0150##2D_MLC_GDGD#250082_GDGDX4_64GB, num=200, leftNum=195)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbde27dfee7b8307","spanId":"fbde27dfee7b8307","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:21:02.64+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbde27dfee7b8307","spanId":"fbde27dfee7b8307","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:21:02.641+08:00","@version":"1","message":"[6292] - [GDGDX1_16GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbde27dfee7b8307","spanId":"fbde27dfee7b8307","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:21:02.646+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbde27dfee7b8307","spanId":"fbde27dfee7b8307","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:21:02.647+08:00","@version":"1","message":"[6292] - [GDGDX2_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbde27dfee7b8307","spanId":"fbde27dfee7b8307","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:21:02.651+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbde27dfee7b8307","spanId":"fbde27dfee7b8307","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:21:02.652+08:00","@version":"1","message":"[6292] - [GDGDX4_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbde27dfee7b8307","spanId":"fbde27dfee7b8307","context":"QueueService","no":"6292","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:21:02.652+08:00","@version":"1","message":"[IND_EMMC] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fbde27dfee7b8307","spanId":"fbde27dfee7b8307","context":"QueueService","no":"6292","traceType":"分配设备"}
