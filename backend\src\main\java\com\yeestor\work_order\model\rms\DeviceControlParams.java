package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel(value = "DeviceControlParams", description = "电脑开关机参数")
public class DeviceControlParams {
    @ApiModelProperty("发起测试的用户")
    private String username;

    @ApiModelProperty("设备所属产品")
    private String product;

    @ApiModelProperty("需要打开或者关闭的电脑Mac地址")
    private List<String> macList;
}
