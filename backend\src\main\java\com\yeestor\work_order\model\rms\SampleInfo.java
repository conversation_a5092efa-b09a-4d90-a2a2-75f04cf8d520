package com.yeestor.work_order.model.rms;

import com.yeestor.work_order.entity.DeviceSampleEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "DeviceStatusChangeParams-SampleInfo", description = "样片信息。")
public class SampleInfo {
    @ApiModelProperty(value = "端口号", required = true)
    private int portNo;

    @ApiModelProperty(value = "样片编号", required = true)
    private String sampleNo;

    @ApiModelProperty(value = "详细位置", required = true)
    private String locationPath;

    @ApiModelProperty("物理编号")
    private Integer phyNo ;

    @ApiModelProperty("标准pe")
    private String all_p_e;

    @ApiModelProperty("实际pe")
    private String now_p_e;

    @ApiModelProperty("预测剩余pe")
    private String remind_p_e;

    public DeviceSampleEntity convertToDeviceSampleEntity(PlanDeviceEntity deviceEntity, String orderFlashNo ){
        DeviceSampleEntity sampleEntity = DeviceStatusChangeParams.convertToDeviceSampleEntity(deviceEntity,orderFlashNo,sampleNo);
        sampleEntity.setPort(portNo);
        sampleEntity.setPath(locationPath);
        if(isNumeric(all_p_e)){
            int allPe = Integer.parseInt(all_p_e);
            sampleEntity.setAllPE(allPe);
        }

        if(isNumeric(now_p_e)){
            int usePe = Integer.parseInt(now_p_e);
            sampleEntity.setUsePE(usePe);
        }

        if(isNumeric(remind_p_e)){
            int resPe = Integer.parseInt(remind_p_e);
            sampleEntity.setResidualPE(resPe);
        }

        return sampleEntity;
    }

    public boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return str.matches("\\d+");
    }

}