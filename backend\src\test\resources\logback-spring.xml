<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
<!--    <include resource="org/springframework/boot/logging/logback/file-appender.xml"/>-->

    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <!-- Example for logging into the build folder of your project -->
    <property name="LOG_FILE" value="${BUILD_FOLDER:-logs}/${springAppName}.log"/>

    <!-- You can override this to have a custom pattern -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([%X{no:-unknown}]){yellow}  %clr(${PID:- }){magenta} %clr(---){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m %n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx} "/>
    <property name="STDOUT_PATTERN"
              value="IMPORT-LOG %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <!-- Appender to log to console -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator> <!-- defaults to type ch.qos.logback.classic.boolex.JaninoEventEvaluator -->
                <expression>
                    return
                    logger.contains("QueueService") ||
                    (marker != null &amp;&amp; marker.contains("QueueService")) ||
                    (formattedMessage.contains("eureka/apps/delta")) ||
                    "QueueService".equals((String) mdc.get("context")) ;

                </expression>
            </evaluator>
            <onMismatch>ACCEPT</onMismatch>
            <onMatch>DENY</onMatch>
        </filter>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>


    <root level="INFO">
        <appender-ref ref="console"/>
    </root>
</configuration>
