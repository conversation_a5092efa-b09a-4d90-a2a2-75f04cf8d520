package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.TempPlanEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Date;
import java.util.Optional;

public interface TempPlanRepository extends JpaRepository<TempPlanEntity, Long> {


    int countBySubProductAndCreatedAtBetween(
            String subProduct,
            Long startDate,
            Long endDate
    );

    Optional<TempPlanEntity> findByNameAndSubProduct(String name, String subProduct);
}
