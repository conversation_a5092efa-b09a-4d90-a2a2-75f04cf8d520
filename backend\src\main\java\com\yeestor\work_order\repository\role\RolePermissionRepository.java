package com.yeestor.work_order.repository.role;

import com.yeestor.work_order.entity.role.RolePermissionEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface RolePermissionRepository extends JpaRepository<RolePermissionEntity, Long> {

    List<RolePermissionEntity> findAllByRoleIdIn(List<Long> roleIdList) ;

    void deleteAllByRoleId(long roleId) ;

    List<RolePermissionEntity> findAllByRoleId(long id);

    List<RolePermissionEntity> findAllByProductAndPermissionContaining(String product, String permission);
    List<RolePermissionEntity> findAllByPermissionContaining(String permission);

}
