package com.yeestor.work_order.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

/**
 * 用于描述订单详情的实体类
 */
@Getter
@Setter
@ToString
@Entity(name = "OrderDetail")
@Table(name = "wo_order_detail")
public class OrderDetailEntity {

    @Id
    private long orderId;

    /**
     * 在禅道中的产品
     */
    @Column(name = "zt_product")
    private String zentaoProduct ;


    /**
     * 在禅道中的项目
     */
    @Column(name = "zt_project")
    private String zentaoProject ;

    /**
     * 在禅道中的测试单
     */
    @Column(name = "zt_testtask")
    private String zentaoTestTask ;
    /**
     * 禅道中的版本的id
     */
    @Column(name = "zt_build_id")
    private String zentaoBuildId ;

    /**
     * 禅道中的执行的id
     */
    @Column(name = "zt_execution_id")
    private String zentaoExecutionId ;

    /**
     * 当前禅道版本产生的bug数量
     */
    @Column(name = "generated_bug_num")
    private Integer generatedBugNum;

    /**
     * 当前禅道版本测试验证产生的bug数量
     */
    @Column(name = "sv_bug_num")
    private Integer svBugNum;

    /**
     * 当前禅道版本验证开发(QA)产生的bug数量
     */
    @Column(name = "fv_bug_num")
    private Integer fvBugNum;

    /**
     * firmware svn 路径
     */
    @Lob
    @Column(name= "fw_svn_path", length=512)
    private String fwSvnPath ;

    @Column(name ="fw_svn_version")
    private String fwSvnVersion;

    @Lob
    @Column(name= "version_log", length = 512)
    private String versionLog ;

    @Column(name ="job_name")
    private String jobName ;

    @Column(name ="mptool_path")
    private String mpToolPath ;

    @Column(name ="mptool_version")
    private String mpToolVersion ;

    @Column(name ="cap")
    private String cap;

    @Column(name ="driver_version")
    private String driverVersion ;


    @Lob
    @Column(name="mail_list", length=2048)
    private String mailList ;

    /**
     * 测试要求
     */
    @Lob
    @Column(name= "requirement", length=1024)
    private String requirement ;



    /**
     * 测试点。
     */
    @Lob
    @Column(name= "test_point", length=1024)
    private String testPoint ;

    /**
     * 从持续集成平台拿过来的json ，保存起来
     */
    @Lob
    @Column(name= "ci_json", length=5120)
    private String ciJson ;

    @Column(name= "mpfile_path")
    private String mpFilePath;

    /**
     * 产品版本类型，分为工业级和消费级版本
     * consumer -> 消费级版本
     * industrial -> 工业级版本
     */
    @Column(name= "product_type")
    private String productType;

    @Lob
    @Column(name= "plan_list", length=1024)
    private String planList ;
}
