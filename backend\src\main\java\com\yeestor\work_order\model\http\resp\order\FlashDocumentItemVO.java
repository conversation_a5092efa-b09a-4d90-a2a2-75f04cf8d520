package com.yeestor.work_order.model.http.resp.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yeestor.work_order.entity.document.DocumentEntity;
import com.yeestor.work_order.entity.document.FlashDocumentEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel(value = "FlashDocumentItemVO", description = "Flash 批次文档项")
public class FlashDocumentItemVO {


    @ApiModelProperty(value = "文档关联表的Id")
    private long id ;

    @ApiModelProperty(value = "工单id")
    private long orderId ;

    @ApiModelProperty(value = "Flash批次")
    private String flash ;

    @ApiModelProperty(value = "文档的url")
    private String url ;

    @ApiModelProperty(value = "文档的名称")
    private String name ;

    @ApiModelProperty(value = "文件的ContentType")
    private String contentType ;

    @ApiModelProperty(value = "文档的大小, 单位为KB。")
    private long size ;

    @ApiModelProperty(value = "文档的创建时间")
    private long createdAt ;

    @ApiModelProperty(value = "文档的创建人的名称")
    private String createdPerson ;

    @ApiModelProperty(hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private FlashDocumentEntity flashDocumentEntity ;

    @ApiModelProperty(hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DocumentEntity documentEntity ;


    /**
     * 给到 JpaRepository 中使用的构造方法。
     * @param flashDocumentEntity FlashDocumentEntity
     * @param documentEntity DocumentEntity
     */
    public FlashDocumentItemVO(FlashDocumentEntity flashDocumentEntity, DocumentEntity documentEntity) {
        this.setId(flashDocumentEntity.getId());    // 此处返回关联表的id信息
        this.setOrderId(flashDocumentEntity.getOrderId());
        this.setFlash(flashDocumentEntity.getFlash());
        this.setUrl(documentEntity.getUrl());
        this.setName(documentEntity.getName());
        this.setContentType(documentEntity.getContentType());
        this.setSize(documentEntity.getSize());
        this.setCreatedAt(documentEntity.getCreatedAt());
        this.setCreatedPerson(documentEntity.getCreatedPerson());
    }

}
