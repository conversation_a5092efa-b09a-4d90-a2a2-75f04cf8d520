package com.yeestor.work_order.entity.plan;

import com.yeestor.entity.BaseEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@RequiredArgsConstructor
@Entity(name = "PlanAssignInfo")
@Table(name = "wo_plan_assign_info")
public class PlanAssignInfoEntity extends BaseEntity {


    public enum Status {
        /**
         * 准备状态,指对应Plan还没有开始排队.
         */
        WAITING,
        /**
         * 排队中,指对应Plan已经开始排队.
         */
        QUEUE,
        /**
         * 已完成,指对应Plan已经完成自动分配.
         */
        COMPLETE,
        /**
         * 已取消,指对应Plan已经取消自动分配.通常是因为Plan被手动停止.
         */
        CANCELLED,
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    private long orderId;

    private long planId;

    private String flash;

    @Column(length = 32)
    @Enumerated(EnumType.STRING)
    private Status status;

    /**
     * 需要分配样片的数量
     */
    private int needSampleNum;
    /**
     * 已经分配了的预期样片数量, 是系统预期的样片数量，不一定是实际分配的样片数量。
     */
    private int exceptedSampleNum;

    /**
     * 已经分配样片的数量
     */
    private int actualSampleNum;

    /**
     * 预期需要的设备的数量
     */
    private int needDeviceNum;

    /**
     * 实际已经分配的设备的数量
     */
    private int actualDeviceNum;

    /**
     * 开始自动分配的时间
     */
    private Long startQueueTime;
    /**
     * 结束自动分配的时间, 手动停止时, 也会记录结束时间.
     */
    private Long endQueueTime;


    @Version
    private Integer version;


    public static List<PlanAssignInfoEntity> createAssignInfo(List<OrderPlanEntity> planList) {
        return planList.stream()
                .filter(OrderPlanEntity::isAutoPlan)
                .map(PlanAssignInfoEntity::createAssignInfo)
                .collect(Collectors.toList());
    }

    public static PlanAssignInfoEntity createAssignInfo(OrderPlanEntity plan) {
        PlanAssignInfoEntity assignInfo = new PlanAssignInfoEntity();
        assignInfo.setOrderId(plan.getOrderId());
        assignInfo.setPlanId(plan.getId());
        assignInfo.setFlash(plan.getFlash());
        assignInfo.setStatus(Status.WAITING);
        assignInfo.setNeedSampleNum(plan.getTestNum());
        assignInfo.setNeedDeviceNum(-1);
        assignInfo.setCreatedAt(System.currentTimeMillis());
        assignInfo.setUpdatedAt(System.currentTimeMillis());
        return assignInfo;
    }

    @Transient
    public boolean isWaiting() {
        return status == Status.WAITING;
    }

    public void startQueue() {
        this.status = Status.QUEUE;
        this.setUpdatedAt(System.currentTimeMillis());
        this.startQueueTime = System.currentTimeMillis();
    }


    public void reset() {
        this.status = Status.WAITING;
        this.setUpdatedAt(System.currentTimeMillis());
        this.exceptedSampleNum = 0;
        this.actualSampleNum = 0;
        this.actualDeviceNum = 0;
        this.startQueueTime = null;
        this.endQueueTime = null;
    }

    public void cancelQueue() {
        this.status = Status.CANCELLED;
        this.setUpdatedAt(System.currentTimeMillis());
    }

}
