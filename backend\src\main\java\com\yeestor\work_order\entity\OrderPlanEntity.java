package com.yeestor.work_order.entity;

import com.yeestor.entity.BaseEntity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.*;

/**
 * 工单 - plan
 */
@Getter
@Setter
@ToString(of={
        "id", "name","orderId","priority","status"
})
@RequiredArgsConstructor
@Entity(name = "OrderPlan")
@Table(name="wo_order_plan")
public class OrderPlanEntity extends BaseEntity {

    /**
     * 未知状态
     */
    public static final int END_STATUS_UNKNOWN = 0;
    /**
     * 成功. 指Plan下的所有设备都成功测试完.
     */
    public static final int END_STATUS_SUCCESS = 1;
    /**
     * 失败. 指Plan下的有某些设备测试失败.
     */
    public static final int END_STATUS_FAIL = 2;
    /**
     * 手动终止,指Plan被手动停止.
     */
    public static final int END_STATUS_TERMINATED = 3;

    /**
     * 自动类型的Plan.
     */
    public static final int TYPE_AUTO = 0;

    /**
     * 手动类型的Plan.
     */
    public static final int TYPE_MANUAL = 1;

    /**
     * plan的状态
     * <ul>
     * <li>刚创建</li>
     * <li>排队中</li>
     * <li>资源准备就绪</li>
     * <li>资源确认完成</li>
     * <li>正在进行</li>
     * <li>已完成</li>
     * <li>已停止</li>
     * </ul>
     */
    public enum Status {
        /**
         * 刚创建, Flash 确认前。
         */
        NEW ,

        QUEUE,
        /**
         * 资源准备就绪
         */
        READY,
        /**
         * 资源分配完成
         */
        CONFIRMED,

        /**
         * 开始测试
         */
        RUNNING,
        /**
         * 已完成，进入这种状态后，不能再改变状态
         */
        COMPLETED,
        /**
         * 手动停止、
         */
        STOPPED;

        public boolean greaterThan(Status status) {
            return this.ordinal() >= status.ordinal();
        }
    }

    public enum Phase {
        /**
         * 接收测试
         */
        ACCEPT("接收测试"),

        /**
         * 验证测试
         */
        VERIFY("验证测试"),
        /**
         * 完整测试
         */
        INTEGRITY("完整测试");

        private final String name;
        Phase(String name) {
            this.name = name;
        }

        public boolean isMatch(String str) {
            return (name+"合集").equals(str);
        }

        public static boolean anyMatch(String str) {
            return Arrays.stream(Phase.values()).anyMatch(p -> p.isMatch(str));
        }

        public static Phase findMatch(String str) {
            return Arrays.stream(Phase.values()).filter(p -> p.isMatch(str)).findFirst().orElse(null);
        }

    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "order_id")
    private long orderId ;

    /**
     * plan 名称 , 如: plan1 .
     * 理论上 同一个批次的Flash下的plan 名称不会冲突
     */
    @Column(name = "name")
    private String name ;


    @Column(name="feature")
    private String feature ;

    /**
     * plan的状态
     * <ul>
     * <li>待启动</li>
     * <li>正在进行</li>
     * <li>已完成</li>
     * <li>排队中</li>
     * <li>已停止</li>
     * </ul>
     */
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private Status status ;

    @Column(name = "phase")
    @Enumerated(EnumType.STRING)
    private Phase phase ;

    /**
     * 优先级
     */
    @Column(name = "priority")
    private int priority ;

    /**
     * plan 涉及到的属性, 以分号为分隔符。
     *
     */
    @Column(name = "attrs")
    private String attrs;


    /**
     * Plan 所对应的flash的批次。
     * 每个Plan只能对应一个Flash 样pian。
     */
    @Column(name = "flash")
    private String flash;

    /**
     * 这个plan需要测试的数量 ,为0时表示测试所有的样片。
     */
    @Column(name = "test_num")
    private int testNum ;

    /**
     * 是否需要测试所有的样片。
     */
    @Column(name = "test_all")
    private boolean testAll ;

    /**
     * 关联的Plan,以英文的逗号进行分割.
     */
    @Column(name = "parent_plan")
    private String parentPlan ;

    /**
     * 临时Plan的ID.
     */
    @Column(name = "temp_plan_id")
    private Long tempPlanId ;

    /**
     * 测试类型。 手动或者自动测试。
     */
    @Column(name = "type")
    private int type ;

    /**
     * 结束的时候的状态
     */
    @Column(name = "end_status")
    private int endStatus ;

    /**
     * 手动Plan测试结束描述
     */
    @Column(name = "end_reason")
    private String endReason ;


    /*
     * 创建时间由BaseEntity 中的createdAt 来决定。
     */
    /**
     * Plan 的资源就绪时间， 这个时间只作为数据收集，不产生实际意义。
     */
    @Column(name = "ready_at")
    private Long readyAt ;

    /**
     * plan 的环境确认时间。
     */
    @Column(name = "confirmed_at")
    private Long confirmedAt ;

    /**
     * plan 的环境确认人, 钉钉ID
     */
    @Column(name = "confirmed_by")
    private String confirmedBy ;

    /**
     * plan 的环境确认人员。名称
     */
    @Column(name = "confirmed_p")
    private String confirmedPerson  ;

    /**
     * plan 的负责人，确认Flash时/分配Plan操作人的时候设置，钉钉ID
     */
    @Column(name = "belong_to")
    private String belongTo ;

    /**
     * plan 的负责人，确认Flash时/分配Plan操作人的时候设置, 名称
     */
    @Column(name = "belong_to_p")
    private String belongToPerson ;

    /**
     * Plan 的开始测试时间。
     */
    @Column(name = "start_at")
    private Long startAt ;

    /**
     * 开始测试plan的人员, 钉钉ID
     */
    @Column(name = "start_by")
    private String startBy ;

    /**
     * 开始测试plan的人员。名称
     */
    @Column(name = "start_p")
    private String startPerson  ;

    /**
     * Plan 的结束测试时间. 真正结束的时间，通常是被释放，或者放弃重测了。
     */
    @Column(name = "end_at")
    private Long endAt ;

    /**
     * 结束测试的人员， 只有手动plan 才会有此信息，钉钉ID
     */
    @Column(name = "end_by")
    private String endBy ;

    /**
     * 结束测试的人员 名称， 只有手动plan 才会有此信息
     */
    @Column(name = "end_p")
    private String endPerson ;

    /**
     * Plan 自动结束的时间。如果一直在重测的话，这个时间会一直更新。
     */
    @Column(name = "auto_end_at")
    private Long autoEndAt ;

    /**
     * 终止plan的时间。
     */
    @Column(name = "terminate_at")
    private Long terminateAt ;

    /**
     * 终止plan的人员，钉钉ID
     */
    @Column(name = "terminate_by")
    private String terminateBy ;

    /**
     * 终止plan的人员 名称
     */
    @Column(name = "terminate_p")
    private String terminatePerson ;

    /**
     * 由谁添加 ,钉钉ID
     */
    @Column(name = "added_by")
    private String addedBy ;

    /**
     * 由谁添加 ,人员名称
     */
    @Column(name = "added_p")
    private String addedPerson ;


    @ColumnDefault("1")
    @Column(name = "need_report")
    private Boolean needReport ;

    @ColumnDefault("0")
    @Column(name = "disabled")
    private Boolean disabled ;

    @ColumnDefault("0")
    @Column(name = "version")
    private Long version ;

    /**
     * 取消Plan启动重测的时间。
     */
    @Column(name = "reTest_at")
    private Long reTestAt ;

    /**
     * 取消Plan启动重测的人员的钉钉ID
     */
    @Column(name = "reTest_by")
    private String reTestBy ;

    /**
     * 取消Plan启动重测的人员的名称。
     */
    @Column(name = "reTest_p")
    private String reTestPerson ;

    /**
     * Plan的启动方式
     * extendNumber -> 继承编号
     * renumber -> 重新编号
     */
    @Column(name = "start_type")
    private String startType;

    /**
     * Plan的类型
     * general -> 读卡器类型Plan
     * terminal -> 终端测试类型Plan
     */
    @Column(name = "plan_type")
    private String planType;

    /**
     * plan 涉及到的脚本名称, 以分号为分隔符。
     *
     */
    @Column(name = "scripts")
    private String scripts;

    /**
     * Plan 的预期结束时间，通常是 即将被自动释放时间。
     */
    @Column(name = "expected_end_time")
    private Long expectedEndTime ;

    public boolean isDisabled() {
        return disabled != null && disabled;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        OrderPlanEntity that = (OrderPlanEntity) o;
        return id != 0 && Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    public OrderPlanEntity duplicate(){
        OrderPlanEntity planEntity = new OrderPlanEntity();
        planEntity.orderId = this.orderId;
        planEntity.name    = this.name ;
        planEntity.attrs   = this.attrs ;
        planEntity.phase   = this.phase ;
        planEntity.feature = this.feature ;
        planEntity.type    = this.type ;
        planEntity.priority= this.priority ;
        planEntity.testAll = this.testAll ;
        planEntity.status  = this.status ;
        planEntity.needReport = this.needReport ;
        planEntity.parentPlan = this.parentPlan;
        planEntity.belongTo = this.belongTo ;
        planEntity.belongToPerson = this.belongToPerson ;
        return planEntity ;
    }
    /**
     * 是否是手动plan .
     * 如果type == {@value TYPE_MANUAL}  ， 则表示是手动plan
     * @return true : 手动plan ，false： 不是手动plan
     */
    @Transient
    public boolean isManualPlan(){
        return type == TYPE_MANUAL ;
    }

    /**
     * 是否是自动plan
     * 如果type == {@value TYPE_AUTO} ， 则表示是自动plan
     * @return  true : 自动plan ，false： 不是自动plan
     */
    @Transient
    public boolean isAutoPlan(){
        return type == TYPE_AUTO ;
    }

    public void clearEndStatus() {
        this.endStatus = END_STATUS_UNKNOWN ;
        this.startAt = null ;
        this.startBy = null ;
        this.startPerson = null ;
        this.endAt = null ;
        this.endBy = null ;
        this.endPerson = null ;
        this.autoEndAt = null ;
        this.reTestAt = null;
        this.reTestBy = null;
        this.reTestPerson = null;
        this.terminateAt = null;
        this.terminatePerson = null;
        this.terminateBy = null;
        this.version = Optional.ofNullable(version).orElse(0L) + 1 ;
        this.setUpdatedAt(System.currentTimeMillis());
    }

    /**
     * 该Plan是否已经结束
     * @return 是否已经结束
     */
    @Transient
    public boolean isFinish(){
        return this.status == Status.STOPPED || this.status == Status.COMPLETED ;
    }

    /**
     * 该Plan是否正在排队
     * @return 是否需要分配
     */
    @Transient
    public boolean isQueue(){
        return this.status == Status.QUEUE;
    }

    /**
     * 该Plan是否包含某个attr属性
     * @param attr 属性名称
     * @return 是否包含
     */
    @Transient
    public boolean hasContainAttr(String attr){
        List<String> attrs = Arrays.asList(this.attrs.split(";"));
        return attrs.contains(attr);
    }

    /**
     * plan结束时通知人员
     * 获取通知人员列表，若开始测试人员、测试负责人、确认环境的人都不一样则都通知
     * @return 钉钉id
     */
    @Transient
    public List<String> getTestEndNoticePerson(){
        Set<String> userIdSet = new HashSet<>();
        userIdSet.add(this.startBy);
        userIdSet.add(this.belongTo);
        userIdSet.add(this.confirmedBy);
        return new ArrayList<>(userIdSet);
    }
}
