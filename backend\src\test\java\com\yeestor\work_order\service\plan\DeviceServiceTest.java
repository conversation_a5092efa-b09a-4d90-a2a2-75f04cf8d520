package com.yeestor.work_order.service.plan;

import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.rms.AttrModel;
import com.yeestor.work_order.model.rms.DeviceModel;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.service.order.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import static com.yeestor.work_order.model.rms.DeviceModel.FIXED_ATTRS;


@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
class DeviceServiceTest {

    @Autowired
    OrderPlanRepository orderPlanRepository;

@Autowired
    OrderService orderService;

    @Autowired
    DeviceService deviceService;

    @Test
    void check(){
        long planId = 8022;
        OrderPlanEntity planEntity = orderPlanRepository.findById(planId).orElseThrow(() -> new RuntimeException("id:" + planId + "的plan不存在"));

        // 获取工单实体类
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());

        // 获取所有支持的设备
        List<DeviceModel> supportDevices = deviceService.findSupportDevicesByPlan(orderEntity, planEntity, false);
        log.info("supportDevices: {}", supportDevices);
    }


    @Test
    void cc(){

        long planId = 64199;
        OrderPlanEntity planEntity = orderPlanRepository.findById(planId).orElseThrow(() -> new RuntimeException("id:" + planId + "的plan不存在"));

        // 获取工单实体类
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());
        List<String> attrs = new ArrayList<>(Arrays.asList(planEntity.getAttrs().split(";")));
        DeviceModel model = new DeviceModel();
        model.setAttrModelList(List.of(
                 new AttrModel("MRAS单盘",1), new AttrModel("MARS多盘",2)
        ));

        List<String> supportAttrs = model.getAttrModelList().stream()
                .map(AttrModel::getAttrName).collect(Collectors.toList());

        // 在SATA 产品线中, 如果是品质验证的工单, 则高温的Plan 可以用 品质高温 的设备来测试 , 具体参考: #165
        if ("SATA".equalsIgnoreCase(orderEntity.getSubProduct()) &&
                (orderEntity.getFeature() & WorkOrderEntity.FEATURE_VERIFY_FLASH) == WorkOrderEntity.FEATURE_VERIFY_FLASH &&
                (attrs.contains(DeviceModel.ATTR_HIGH_TEMP) && supportAttrs.contains(DeviceModel.ATTR_QUALITY_HIGH_TEMP))
        ) {
            // 如果Plan 中包含高温属性,并且设备支持品质高温, 则直接抛开这两个属性,看看Plan以及设备的其他属性是否匹配
            attrs.remove(DeviceModel.ATTR_HIGH_TEMP);
            log.info(" new HashSet<>(supportAttrs).containsAll(attrs): {}", new HashSet<>(supportAttrs).containsAll(attrs));

        }

        // 找出attrs 与 FIXED_ATTRS 的交集
        HashSet<String> intersection = new HashSet<>(attrs);
        intersection.retainAll(FIXED_ATTRS);

        // 如果交集为空,则表明attrs 中没有高温或者低温属性.
        // 如果此时supportAttrs 中包含高温或者低温属性,则表明这个设备不能测试attrs 中的属性,因为高温和低温属性是属于独占的.
        // 即 Plan 中不含高温,但是设备支持高温.
        if (intersection.isEmpty() && supportAttrs.stream().anyMatch(FIXED_ATTRS::contains)) {
            log.info("---false");

        }

        log.info(" new HashSet<>(supportAttrs).containsAll(attrs)2: {}", new HashSet<>(supportAttrs).containsAll(attrs));
    }
}