{"@timestamp":"2025-07-23T15:08:44.442+08:00","@version":"1","message":"execute:SD_8C-32-23-39-B9-B9_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:8C-32-23-39-B9-B9","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"621e42a3d4204f72","spanId":"621e42a3d4204f72","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:08:44.573+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-57[8C-32-23-39-B9-B9]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"621e42a3d4204f72","spanId":"621e42a3d4204f72","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:08:44.588+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-57]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"621e42a3d4204f72","spanId":"621e42a3d4204f72","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:08:44.589+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-B9-B9])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"621e42a3d4204f72","spanId":"621e42a3d4204f72","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:08:44.661+08:00","@version":"1","message":"[7a0397e1] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"621e42a3d4204f72","spanId":"621e42a3d4204f72","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:08:45.112+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-B9-B9]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=8C-32-23-39-B9-B9, pc_no=GE-PC-SDhigh-57, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"621e42a3d4204f72","spanId":"621e42a3d4204f72","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:08:45.113+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"621e42a3d4204f72","spanId":"621e42a3d4204f72","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:09:03.058+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:09:03.061+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=52)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.085+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.085+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.085+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:44.559+08:00","@version":"1","message":"execute:SD_8C-32-23-39-BB-CF_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:8C-32-23-39-BB-CF","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e2c899a62d9f1ffd","spanId":"e2c899a62d9f1ffd","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:09:44.561+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-55[8C-32-23-39-BB-CF]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e2c899a62d9f1ffd","spanId":"e2c899a62d9f1ffd","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:09:44.562+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-55]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e2c899a62d9f1ffd","spanId":"e2c899a62d9f1ffd","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:09:44.562+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BB-CF])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e2c899a62d9f1ffd","spanId":"e2c899a62d9f1ffd","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:09:44.563+08:00","@version":"1","message":"[4b9c05cd] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e2c899a62d9f1ffd","spanId":"e2c899a62d9f1ffd","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:09:44.696+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BB-CF]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=8C-32-23-39-BB-CF, pc_no=GE-PC-SDHIGH-55, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e2c899a62d9f1ffd","spanId":"e2c899a62d9f1ffd","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:09:44.697+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e2c899a62d9f1ffd","spanId":"e2c899a62d9f1ffd","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:10:24.154+08:00","@version":"1","message":"execute:SD_8C-32-23-39-BA-EF_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:8C-32-23-39-BA-EF","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a06f1a2828410821","spanId":"a06f1a2828410821","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:10:24.156+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-66[8C-32-23-39-BA-EF]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a06f1a2828410821","spanId":"a06f1a2828410821","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:10:24.156+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-66]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a06f1a2828410821","spanId":"a06f1a2828410821","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:10:24.156+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BA-EF])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a06f1a2828410821","spanId":"a06f1a2828410821","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:10:24.157+08:00","@version":"1","message":"[5eefe917] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a06f1a2828410821","spanId":"a06f1a2828410821","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:10:24.286+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BA-EF]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=8C-32-23-39-BA-EF, pc_no=GE-PC-SDhigh-66, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a06f1a2828410821","spanId":"a06f1a2828410821","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:10:24.286+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a06f1a2828410821","spanId":"a06f1a2828410821","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:11:55.214+08:00","@version":"1","message":"execute:SD_8C-32-23-39-BC-41_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:8C-32-23-39-BC-41","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b090849f7f60133","spanId":"3b090849f7f60133","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:11:55.216+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-49[8C-32-23-39-BC-41]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b090849f7f60133","spanId":"3b090849f7f60133","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:11:55.216+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-49]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b090849f7f60133","spanId":"3b090849f7f60133","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:11:55.216+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BC-41])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b090849f7f60133","spanId":"3b090849f7f60133","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:11:55.217+08:00","@version":"1","message":"[615711ee] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b090849f7f60133","spanId":"3b090849f7f60133","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:11:55.358+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BC-41]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=8C-32-23-39-BC-41, pc_no=GE-PC-SDhigh-49, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b090849f7f60133","spanId":"3b090849f7f60133","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:11:55.358+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3b090849f7f60133","spanId":"3b090849f7f60133","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:12:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:12:02.636+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=60)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.654+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:23.458+08:00","@version":"1","message":"execute:SD_A4-0C-66-13-D6-0F_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:A4-0C-66-13-D6-0F","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b96b792211887c8","spanId":"7b96b792211887c8","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:12:23.461+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-48[A4-0C-66-13-D6-0F]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b96b792211887c8","spanId":"7b96b792211887c8","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:12:23.461+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-48]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b96b792211887c8","spanId":"7b96b792211887c8","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:12:23.461+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[A4-0C-66-13-D6-0F])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b96b792211887c8","spanId":"7b96b792211887c8","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:12:23.461+08:00","@version":"1","message":"[5bb1b8b7] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b96b792211887c8","spanId":"7b96b792211887c8","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:12:23.602+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[A4-0C-66-13-D6-0F]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=A4-0C-66-13-D6-0F, pc_no=GE-PC-SDhigh-48, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b96b792211887c8","spanId":"7b96b792211887c8","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:12:23.603+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b96b792211887c8","spanId":"7b96b792211887c8","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:13:12.774+08:00","@version":"1","message":"execute:SD_8C-32-23-39-BD-72_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:8C-32-23-39-BD-72","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"357a05e540b8167c","spanId":"357a05e540b8167c","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:13:12.776+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-50[8C-32-23-39-BD-72]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"357a05e540b8167c","spanId":"357a05e540b8167c","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:13:12.776+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-50]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"357a05e540b8167c","spanId":"357a05e540b8167c","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:13:12.776+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BD-72])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"357a05e540b8167c","spanId":"357a05e540b8167c","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:13:12.776+08:00","@version":"1","message":"[f4de502] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"357a05e540b8167c","spanId":"357a05e540b8167c","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:13:12.916+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BD-72]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=8C-32-23-39-BD-72, pc_no=GE-PC-SDhigh-50, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"357a05e540b8167c","spanId":"357a05e540b8167c","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:13:12.916+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"357a05e540b8167c","spanId":"357a05e540b8167c","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:15:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:15:02.637+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=64)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.654+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.654+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:17:54.207+08:00","@version":"1","message":"execute:SD_A4-0C-66-14-2A-BC_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:A4-0C-66-14-2A-BC","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"427b4e78fc46f0ad","spanId":"427b4e78fc46f0ad","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:17:54.208+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-43[A4-0C-66-14-2A-BC]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"427b4e78fc46f0ad","spanId":"427b4e78fc46f0ad","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:17:54.208+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-43]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"427b4e78fc46f0ad","spanId":"427b4e78fc46f0ad","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:17:54.209+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[A4-0C-66-14-2A-BC])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"427b4e78fc46f0ad","spanId":"427b4e78fc46f0ad","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:17:54.209+08:00","@version":"1","message":"[1ab38c17] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"427b4e78fc46f0ad","spanId":"427b4e78fc46f0ad","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:17:54.341+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[A4-0C-66-14-2A-BC]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=A4-0C-66-14-2A-BC, pc_no=GE-PC-SDhigh-43, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"427b4e78fc46f0ad","spanId":"427b4e78fc46f0ad","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:17:54.341+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"427b4e78fc46f0ad","spanId":"427b4e78fc46f0ad","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:18:02.645+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.647+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=68)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.679+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.68+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.68+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:28.744+08:00","@version":"1","message":"execute:SD_8C-32-23-39-BA-E5_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:8C-32-23-39-BA-E5","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a6dc3c842d80ca9","spanId":"1a6dc3c842d80ca9","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:18:28.746+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-63[8C-32-23-39-BA-E5]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a6dc3c842d80ca9","spanId":"1a6dc3c842d80ca9","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:18:28.747+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-63]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a6dc3c842d80ca9","spanId":"1a6dc3c842d80ca9","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:18:28.747+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BA-E5])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a6dc3c842d80ca9","spanId":"1a6dc3c842d80ca9","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:18:28.747+08:00","@version":"1","message":"[2e7a832] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a6dc3c842d80ca9","spanId":"1a6dc3c842d80ca9","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:18:28.882+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BA-E5]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=8C-32-23-39-BA-E5, pc_no=GE-PC-SDhigh-63, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a6dc3c842d80ca9","spanId":"1a6dc3c842d80ca9","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:18:28.883+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a6dc3c842d80ca9","spanId":"1a6dc3c842d80ca9","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:14.221+08:00","@version":"1","message":"execute:SD_8C-32-23-39-BC-D4_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:8C-32-23-39-BC-D4","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d903a998994e7039","spanId":"d903a998994e7039","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:14.223+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-56[8C-32-23-39-BC-D4]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d903a998994e7039","spanId":"d903a998994e7039","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:14.223+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-56]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d903a998994e7039","spanId":"d903a998994e7039","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:14.223+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BC-D4])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d903a998994e7039","spanId":"d903a998994e7039","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:14.224+08:00","@version":"1","message":"[6711b1be] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d903a998994e7039","spanId":"d903a998994e7039","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:14.359+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BC-D4]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=8C-32-23-39-BC-D4, pc_no=GE-PC-SDhigh-56, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d903a998994e7039","spanId":"d903a998994e7039","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:14.359+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d903a998994e7039","spanId":"d903a998994e7039","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:28.695+08:00","@version":"1","message":"execute:SD_8C-32-23-39-BD-51_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:SD userName:eSee.system title:电脑自动释放后电脑关机 planId:113559 mac:8C-32-23-39-BD-51","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc93a9fa2f6dd7d3","spanId":"cc93a9fa2f6dd7d3","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:28.697+08:00","@version":"1","message":"倒计时结束，即将释放设备GE-PC-SDhigh-61[8C-32-23-39-BD-51]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc93a9fa2f6dd7d3","spanId":"cc93a9fa2f6dd7d3","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:28.697+08:00","@version":"1","message":"需要关机的设备编号有: [GE-PC-SDhigh-61]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc93a9fa2f6dd7d3","spanId":"cc93a9fa2f6dd7d3","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:28.697+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BD-51])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc93a9fa2f6dd7d3","spanId":"cc93a9fa2f6dd7d3","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:28.698+08:00","@version":"1","message":"[439fccc3] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc93a9fa2f6dd7d3","spanId":"cc93a9fa2f6dd7d3","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:28.828+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=SD, macList=[8C-32-23-39-BD-51]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=8C-32-23-39-BD-51, pc_no=GE-PC-SDhigh-61, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc93a9fa2f6dd7d3","spanId":"cc93a9fa2f6dd7d3","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:20:28.828+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cc93a9fa2f6dd7d3","spanId":"cc93a9fa2f6dd7d3","no":"6377","traceType":"TimedShutdownDeviceJob","flash":"XCCB-6285DA-9T25-A_64GB"}
{"@timestamp":"2025-07-23T15:24:02.643+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:24:02.645+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.662+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.663+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.663+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.683+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:51:02.761+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.785+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.785+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.786+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:57:02.627+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.648+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.648+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.045+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:00:05.05+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.069+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.07+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.07+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.604+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:03:02.606+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.628+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.629+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.602+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:09:02.604+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.631+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.632+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.594+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:12:02.595+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.618+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.618+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.618+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.615+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6377","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:24:02.626+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6377","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6377","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.646+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6377","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.646+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6377","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:30:02.588+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.589+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.605+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.605+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.605+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:33:02.616+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.644+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.645+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.645+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.589+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:36:02.59+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.61+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.61+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.61+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.59+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:39:02.592+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.61+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.61+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.61+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.595+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:48:02.597+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.621+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.621+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.621+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.59+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6377","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:51:02.591+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6610, orderId=6377, flash=XCCB-6285DA-9T25-A_64GB, orderFlashNo=YS6285##MP#########0198##9X25-453E_TL250415_Alpha_XCCB-6285DA-9T25-A_64GB, num=120, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.608+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.608+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113562, orderId=6377, name=Plan47, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6377","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.608+08:00","@version":"1","message":"[6377] - [XCCB-6285DA-9T25-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6377","traceType":"分配设备"}
