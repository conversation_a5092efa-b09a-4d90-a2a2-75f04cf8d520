{"@timestamp":"2025-07-24T14:00:02.948+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81f2e752d72110b0","spanId":"81f2e752d72110b0","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:00:03.117+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=120), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81f2e752d72110b0","spanId":"81f2e752d72110b0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.281+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81f2e752d72110b0","spanId":"81f2e752d72110b0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.286+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81f2e752d72110b0","spanId":"81f2e752d72110b0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.299+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81f2e752d72110b0","spanId":"81f2e752d72110b0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.317+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81f2e752d72110b0","spanId":"81f2e752d72110b0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:00:03.317+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"81f2e752d72110b0","spanId":"81f2e752d72110b0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.62+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c008d4498c0bfc0","spanId":"0c008d4498c0bfc0","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:03:02.622+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=120), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c008d4498c0bfc0","spanId":"0c008d4498c0bfc0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.625+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c008d4498c0bfc0","spanId":"0c008d4498c0bfc0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.625+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c008d4498c0bfc0","spanId":"0c008d4498c0bfc0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c008d4498c0bfc0","spanId":"0c008d4498c0bfc0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.629+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c008d4498c0bfc0","spanId":"0c008d4498c0bfc0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:03:02.629+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0c008d4498c0bfc0","spanId":"0c008d4498c0bfc0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.67+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21d00ce1de5a3a91","spanId":"21d00ce1de5a3a91","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.672+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=120), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21d00ce1de5a3a91","spanId":"21d00ce1de5a3a91","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21d00ce1de5a3a91","spanId":"21d00ce1de5a3a91","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.678+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21d00ce1de5a3a91","spanId":"21d00ce1de5a3a91","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.682+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21d00ce1de5a3a91","spanId":"21d00ce1de5a3a91","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.682+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21d00ce1de5a3a91","spanId":"21d00ce1de5a3a91","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.682+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21d00ce1de5a3a91","spanId":"21d00ce1de5a3a91","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c386f61f4417eb4d","spanId":"c386f61f4417eb4d","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:09:02.627+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=120), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c386f61f4417eb4d","spanId":"c386f61f4417eb4d","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c386f61f4417eb4d","spanId":"c386f61f4417eb4d","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.632+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c386f61f4417eb4d","spanId":"c386f61f4417eb4d","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.638+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c386f61f4417eb4d","spanId":"c386f61f4417eb4d","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.643+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c386f61f4417eb4d","spanId":"c386f61f4417eb4d","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:09:02.643+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c386f61f4417eb4d","spanId":"c386f61f4417eb4d","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.637+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b3a27a4dff91be0","spanId":"2b3a27a4dff91be0","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:12:02.639+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=120), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b3a27a4dff91be0","spanId":"2b3a27a4dff91be0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b3a27a4dff91be0","spanId":"2b3a27a4dff91be0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.644+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b3a27a4dff91be0","spanId":"2b3a27a4dff91be0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.648+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b3a27a4dff91be0","spanId":"2b3a27a4dff91be0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.648+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b3a27a4dff91be0","spanId":"2b3a27a4dff91be0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:12:02.648+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b3a27a4dff91be0","spanId":"2b3a27a4dff91be0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:15:02.629+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=120), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=120)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.652+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113675, orderId=6387, name=Plan6, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.67+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113676, orderId=6387, name=Plan7, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113675, orderId=6387, name=Plan6, status=QUEUE, priority=75), OrderPlanEntity(id=113676, orderId=6387, name=Plan7, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.671+08:00","@version":"1","message":" getValidPlanList sumList: [3, 6] leftFlashNum:120 index:2 plans:[OrderPlanEntity(id=113675, orderId=6387, name=Plan6, status=QUEUE, priority=75), OrderPlanEntity(id=113676, orderId=6387, name=Plan7, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:15:02.672+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 2 plans: [OrderPlanEntity(id=113675, orderId=6387, name=Plan6, status=QUEUE, priority=75), OrderPlanEntity(id=113676, orderId=6387, name=Plan7, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.672+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113675, orderId=6387, name=Plan6, status=QUEUE, priority=75), OrderPlanEntity(id=113676, orderId=6387, name=Plan7, status=QUEUE, priority=75)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.778+08:00","@version":"1","message":"[4136c1fa] HTTP GET http://ereport.yeestor.com/wo/device/list?p=IND_SD","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:02.98+08:00","@version":"1","message":"getAllDeviceList with IND_SD got data DeviceListResp(code=0, data=[{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_01,************,8C-32-23-21-99-16,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_06,*************,8C-32-23-21-97-C4,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_05,*************,8C-32-23-21-98-D3,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_04,************,8C-32-23-21-9C-96,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_02,*************,8C-32-23-21-97-64,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_03,************,8C-32-23-21-99-4C,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }], msg=测试机信息获取成功！, workPcLst=[{ EM6_1_16,************,B4-2E-99-EA-B3-D0,[{低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_15,************,B4-2E-99-EA-22-87,[{低温=4}, {温循=4}, {Mars=4}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.02+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6387 flash: YS-6297I-WTx1-A_32GB available device: [{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_01,************,8C-32-23-21-99-16,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_06,*************,8C-32-23-21-97-C4,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_05,*************,8C-32-23-21-98-D3,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_04,************,8C-32-23-21-9C-96,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_02,*************,8C-32-23-21-97-64,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_03,************,8C-32-23-21-99-4C,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.02+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.02+08:00","@version":"1","message":"[6387] Plan6 need Num: 3 to test. YS-6297I-WTx1-A_32GB left num:120 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.021+08:00","@version":"1","message":"subProduct IND_SD flashName YS-6297I-WTx1-A_32GB plan [Plan6] attrs 性能;FIO belongTo Claire.xiong is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.023+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM7_3_08, EM7_4_01, EM7_4_02, EM7_4_03, EM7_4_04, EM7_4_05, EM7_4_06, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16, GE2_2_30]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.024+08:00","@version":"1","message":"Plan6 test all sample is false","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.025+08:00","@version":"1","message":"devices: [EM7_3_08]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.025+08:00","@version":"1","message":"for index: 0 size: 1 sum: 4 testNum: 3","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.025+08:00","@version":"1","message":"wait testNum: 3 assign sum: 4 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.025+08:00","@version":"1","message":"[6387] plan:Plan6 use 1 pc: [{ EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.025+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.025+08:00","@version":"1","message":"[6387] Plan7 need Num: 3 to test. YS-6297I-WTx1-A_32GB left num:117 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.026+08:00","@version":"1","message":"subProduct IND_SD flashName YS-6297I-WTx1-A_32GB plan [Plan7] attrs Mars;性能 belongTo Claire.xiong is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.027+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM7_4_01, EM7_4_02, EM7_4_03, EM7_4_04, EM7_4_05, EM7_4_06, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16, GE2_2_30]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.028+08:00","@version":"1","message":"Plan7 test all sample is false","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.028+08:00","@version":"1","message":"devices: [EM7_4_01, EM7_4_02, EM7_4_03, EM7_4_04, EM7_4_05, EM7_4_06, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.028+08:00","@version":"1","message":"for index: 0 size: 1 sum: 4 testNum: 3","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.028+08:00","@version":"1","message":"wait testNum: 3 assign sum: 4 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.028+08:00","@version":"1","message":"[6387] plan:Plan7 use 1 pc: [{ EM7_4_01,************,8C-32-23-21-99-16,[{Mars=4}, {FIO=4}, {性能=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.028+08:00","@version":"1","message":"assignDevices [6387] - find 2 run devices:  {Plan6=[{ EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }], Plan7=[{ EM7_4_01,************,8C-32-23-21-99-16,[{Mars=4}, {FIO=4}, {性能=4}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.031+08:00","@version":"1","message":"工单[6387] flash YS-6297I-WTx1-A_32GB , 参与此次Plan预分配的plan共有 [Plan6, Plan7] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.067+08:00","@version":"1","message":" add 1 devices :[{ EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }]  to plan:Plan6","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.091+08:00","@version":"1","message":"plan:Plan6 assign info update to ActualDeviceNum: 1, ExceptedSampleNum: 3","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.128+08:00","@version":"1","message":"[YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB] - Plan6 holdDevices  :[PlanDeviceEntity(id=277372, orderId=6387, planId=113675, planName=Plan6, ip=************, mac=9C-6B-00-49-02-95, no=EM7_3_08, position=EM7_3_08, score=240, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.242+08:00","@version":"1","message":"lock device:9C-6B-00-49-02-95 to orderNo:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB planName:Plan6 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.291+08:00","@version":"1","message":"[4aea7766] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.459+08:00","@version":"1","message":"lockDevice with ipList:[************] - macList:[9C-6B-00-49-02-95],no:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.463+08:00","@version":"1","message":"自动分配EM7_3_08(************)给YS-6297I-WTx1-A_32GB下的Plan6","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.463+08:00","@version":"1","message":"add 1 devices to plan: Plan6 ,expect num: 3, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.463+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6387, flash:YS-6297I-WTx1-A_32GB, planEntity:OrderPlanEntity(id=113675, orderId=6387, name=Plan6, status=QUEUE, priority=75)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:03.49+08:00","@version":"1","message":"测试单: YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB plan: Plan6已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.276+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.29+08:00","@version":"1","message":" add 1 devices :[{ EM7_4_01,************,8C-32-23-21-99-16,[{Mars=4}, {FIO=4}, {性能=4}] }]  to plan:Plan7","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.313+08:00","@version":"1","message":"plan:Plan7 assign info update to ActualDeviceNum: 1, ExceptedSampleNum: 3","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.317+08:00","@version":"1","message":"[YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB] - Plan7 holdDevices  :[PlanDeviceEntity(id=277373, orderId=6387, planId=113676, planName=Plan7, ip=************, mac=8C-32-23-21-99-16, no=EM7_4_01, position=EM7_4_01, score=190, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.32+08:00","@version":"1","message":"lock device:8C-32-23-21-99-16 to orderNo:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB planName:Plan7 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.325+08:00","@version":"1","message":"[89b9090] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.537+08:00","@version":"1","message":"lockDevice with ipList:[************] - macList:[8C-32-23-21-99-16],no:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.538+08:00","@version":"1","message":"自动分配EM7_4_01(************)给YS-6297I-WTx1-A_32GB下的Plan7","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.538+08:00","@version":"1","message":"add 1 devices to plan: Plan7 ,expect num: 3, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.538+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6387, flash:YS-6297I-WTx1-A_32GB, planEntity:OrderPlanEntity(id=113676, orderId=6387, name=Plan7, status=QUEUE, priority=75)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.541+08:00","@version":"1","message":"测试单: YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB plan: Plan7已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.723+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.724+08:00","@version":"1","message":"此次分配flash批次 YS-6297I-WTx1-A_32GB 下的Plan共消耗 6 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.727+08:00","@version":"1","message":"更新Flash:YS-6297I-WTx1-A_32GB的样片数量从120变更为114","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.833+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113686, orderId=6387, name=Plan6, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.848+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.849+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113686, orderId=6387, name=Plan6, status=QUEUE, priority=75), OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.849+08:00","@version":"1","message":" getValidPlanList sumList: [3, 6] leftFlashNum:120 index:2 plans:[OrderPlanEntity(id=113686, orderId=6387, name=Plan6, status=QUEUE, priority=75), OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:15:04.849+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 2 plans: [OrderPlanEntity(id=113686, orderId=6387, name=Plan6, status=QUEUE, priority=75), OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.85+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113686, orderId=6387, name=Plan6, status=QUEUE, priority=75), OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:04.875+08:00","@version":"1","message":"[3973e0b9] HTTP GET http://ereport.yeestor.com/wo/device/list?p=IND_SD","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.038+08:00","@version":"1","message":"getAllDeviceList with IND_SD got data DeviceListResp(code=0, data=[{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_06,*************,8C-32-23-21-97-C4,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_05,*************,8C-32-23-21-98-D3,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_04,************,8C-32-23-21-9C-96,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_02,*************,8C-32-23-21-97-64,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_03,************,8C-32-23-21-99-4C,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }], msg=测试机信息获取成功！, workPcLst=[{ EM6_1_16,************,B4-2E-99-EA-B3-D0,[{低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_15,************,B4-2E-99-EA-22-87,[{低温=4}, {温循=4}, {Mars=4}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.177+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6387 flash: YS-6297I-WTx2-A_64GB available device: [{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_06,*************,8C-32-23-21-97-C4,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_05,*************,8C-32-23-21-98-D3,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_04,************,8C-32-23-21-9C-96,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_02,*************,8C-32-23-21-97-64,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_03,************,8C-32-23-21-99-4C,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.178+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.178+08:00","@version":"1","message":"[6387] Plan6 need Num: 3 to test. YS-6297I-WTx2-A_64GB left num:120 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.178+08:00","@version":"1","message":"subProduct IND_SD flashName YS-6297I-WTx2-A_64GB plan [Plan6] attrs 性能;FIO belongTo Claire.xiong is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.181+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM7_4_02, EM7_4_03, EM7_4_04, EM7_4_05, EM7_4_06, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16, GE2_2_30]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.181+08:00","@version":"1","message":"Plan6 test all sample is false","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.181+08:00","@version":"1","message":"devices: [EM7_4_02, EM7_4_03, EM7_4_04, EM7_4_05, EM7_4_06, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.181+08:00","@version":"1","message":"for index: 0 size: 1 sum: 4 testNum: 3","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.181+08:00","@version":"1","message":"wait testNum: 3 assign sum: 4 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.182+08:00","@version":"1","message":"[6387] plan:Plan6 use 1 pc: [{ EM7_4_02,*************,8C-32-23-21-97-64,[{Mars=4}, {FIO=4}, {性能=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.182+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ EM7_4_02,*************,8C-32-23-21-97-64,[{Mars=4}, {FIO=4}, {性能=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.182+08:00","@version":"1","message":"[6387] Plan7 need Num: 3 to test. YS-6297I-WTx2-A_64GB left num:117 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.182+08:00","@version":"1","message":"subProduct IND_SD flashName YS-6297I-WTx2-A_64GB plan [Plan7] attrs Mars;性能 belongTo Claire.xiong is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.184+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM7_4_03, EM7_4_04, EM7_4_05, EM7_4_06, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16, GE2_2_30]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.184+08:00","@version":"1","message":"Plan7 test all sample is false","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.185+08:00","@version":"1","message":"devices: [EM7_4_03, EM7_4_04, EM7_4_05, EM7_4_06, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.185+08:00","@version":"1","message":"for index: 0 size: 1 sum: 4 testNum: 3","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.185+08:00","@version":"1","message":"wait testNum: 3 assign sum: 4 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.185+08:00","@version":"1","message":"[6387] plan:Plan7 use 1 pc: [{ EM7_4_03,************,8C-32-23-21-99-4C,[{Mars=4}, {FIO=4}, {性能=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.185+08:00","@version":"1","message":"assignDevices [6387] - find 2 run devices:  {Plan6=[{ EM7_4_02,*************,8C-32-23-21-97-64,[{Mars=4}, {FIO=4}, {性能=4}] }], Plan7=[{ EM7_4_03,************,8C-32-23-21-99-4C,[{Mars=4}, {FIO=4}, {性能=4}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.186+08:00","@version":"1","message":"工单[6387] flash YS-6297I-WTx2-A_64GB , 参与此次Plan预分配的plan共有 [Plan6, Plan7] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.193+08:00","@version":"1","message":" add 1 devices :[{ EM7_4_02,*************,8C-32-23-21-97-64,[{Mars=4}, {FIO=4}, {性能=4}] }]  to plan:Plan6","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.209+08:00","@version":"1","message":"plan:Plan6 assign info update to ActualDeviceNum: 1, ExceptedSampleNum: 3","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.213+08:00","@version":"1","message":"[YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB] - Plan6 holdDevices  :[PlanDeviceEntity(id=277374, orderId=6387, planId=113686, planName=Plan6, ip=*************, mac=8C-32-23-21-97-64, no=EM7_4_02, position=EM7_4_02, score=190, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.215+08:00","@version":"1","message":"lock device:8C-32-23-21-97-64 to orderNo:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB planName:Plan6 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.221+08:00","@version":"1","message":"[3107c455] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.362+08:00","@version":"1","message":"lockDevice with ipList:[*************] - macList:[8C-32-23-21-97-64],no:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.363+08:00","@version":"1","message":"自动分配EM7_4_02(*************)给YS-6297I-WTx2-A_64GB下的Plan6","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.363+08:00","@version":"1","message":"add 1 devices to plan: Plan6 ,expect num: 3, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.363+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6387, flash:YS-6297I-WTx2-A_64GB, planEntity:OrderPlanEntity(id=113686, orderId=6387, name=Plan6, status=QUEUE, priority=75)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.365+08:00","@version":"1","message":"测试单: YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB plan: Plan6已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.566+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.575+08:00","@version":"1","message":" add 1 devices :[{ EM7_4_03,************,8C-32-23-21-99-4C,[{Mars=4}, {FIO=4}, {性能=4}] }]  to plan:Plan7","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.596+08:00","@version":"1","message":"plan:Plan7 assign info update to ActualDeviceNum: 1, ExceptedSampleNum: 3","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.6+08:00","@version":"1","message":"[YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB] - Plan7 holdDevices  :[PlanDeviceEntity(id=277375, orderId=6387, planId=113687, planName=Plan7, ip=************, mac=8C-32-23-21-99-4C, no=EM7_4_03, position=EM7_4_03, score=190, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.603+08:00","@version":"1","message":"lock device:8C-32-23-21-99-4C to orderNo:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB planName:Plan7 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.61+08:00","@version":"1","message":"[5f75d6ec] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.773+08:00","@version":"1","message":"lockDevice with ipList:[************] - macList:[8C-32-23-21-99-4C],no:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.773+08:00","@version":"1","message":"自动分配EM7_4_03(************)给YS-6297I-WTx2-A_64GB下的Plan7","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.774+08:00","@version":"1","message":"add 1 devices to plan: Plan7 ,expect num: 3, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.774+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6387, flash:YS-6297I-WTx2-A_64GB, planEntity:OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.781+08:00","@version":"1","message":"测试单: YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB plan: Plan7已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.974+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.975+08:00","@version":"1","message":"此次分配flash批次 YS-6297I-WTx2-A_64GB 下的Plan共消耗 6 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.977+08:00","@version":"1","message":"更新Flash:YS-6297I-WTx2-A_64GB的样片数量从120变更为114","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:15:05.993+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6c77a4fb0e6ad5a4","spanId":"6c77a4fb0e6ad5a4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.677+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61440d2fe6f99e46","spanId":"61440d2fe6f99e46","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.678+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=114)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61440d2fe6f99e46","spanId":"61440d2fe6f99e46","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.681+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61440d2fe6f99e46","spanId":"61440d2fe6f99e46","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.681+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61440d2fe6f99e46","spanId":"61440d2fe6f99e46","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.685+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61440d2fe6f99e46","spanId":"61440d2fe6f99e46","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.685+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61440d2fe6f99e46","spanId":"61440d2fe6f99e46","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.685+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"61440d2fe6f99e46","spanId":"61440d2fe6f99e46","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:21:02.628+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=114)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.635+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.655+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.655+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.655+08:00","@version":"1","message":" getValidPlanList sumList: [3] leftFlashNum:114 index:1 plans:[OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:21:02.655+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 1 plans: [OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.656+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.676+08:00","@version":"1","message":"[7505d275] HTTP GET http://ereport.yeestor.com/wo/device/list?p=IND_SD","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.827+08:00","@version":"1","message":"getAllDeviceList with IND_SD got data DeviceListResp(code=0, data=[{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_06,*************,8C-32-23-21-97-C4,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_05,*************,8C-32-23-21-98-D3,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_04,************,8C-32-23-21-9C-96,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }], msg=测试机信息获取成功！, workPcLst=[{ EM6_1_16,************,B4-2E-99-EA-B3-D0,[{低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_15,************,B4-2E-99-EA-22-87,[{低温=4}, {温循=4}, {Mars=4}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.872+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6387 flash: YS-6297I-WTx2-A_64GB available device: [{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_06,*************,8C-32-23-21-97-C4,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_05,*************,8C-32-23-21-98-D3,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_04,************,8C-32-23-21-9C-96,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.872+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.873+08:00","@version":"1","message":"[6387] Plan7 need Num: 3 to test. YS-6297I-WTx2-A_64GB left num:114 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.873+08:00","@version":"1","message":"subProduct IND_SD flashName YS-6297I-WTx2-A_64GB plan [Plan7] attrs Mars;性能 belongTo Claire.xiong is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.874+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM7_3_08, EM7_4_04, EM7_4_05, EM7_4_06, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16, GE2_2_30]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.875+08:00","@version":"1","message":"Plan7 test all sample is false","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.875+08:00","@version":"1","message":"devices: [EM7_3_08]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.875+08:00","@version":"1","message":"for index: 0 size: 1 sum: 4 testNum: 3","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.875+08:00","@version":"1","message":"wait testNum: 3 assign sum: 4 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.875+08:00","@version":"1","message":"[6387] plan:Plan7 use 1 pc: [{ EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.875+08:00","@version":"1","message":"assignDevices [6387] - find 1 run devices:  {Plan7=[{ EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.876+08:00","@version":"1","message":"工单[6387] flash YS-6297I-WTx2-A_64GB , 参与此次Plan预分配的plan共有 [Plan7] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.886+08:00","@version":"1","message":" add 1 devices :[{ EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }]  to plan:Plan7","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.901+08:00","@version":"1","message":"plan:Plan7 assign info update to ActualDeviceNum: 1, ExceptedSampleNum: 3","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.904+08:00","@version":"1","message":"[YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB] - Plan7 holdDevices  :[PlanDeviceEntity(id=277377, orderId=6387, planId=113687, planName=Plan7, ip=************, mac=9C-6B-00-49-02-95, no=EM7_3_08, position=EM7_3_08, score=240, owner=, testNum=3, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.906+08:00","@version":"1","message":"lock device:9C-6B-00-49-02-95 to orderNo:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB planName:Plan7 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:02.91+08:00","@version":"1","message":"[27e0a4c9] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:03.07+08:00","@version":"1","message":"lockDevice with ipList:[************] - macList:[9C-6B-00-49-02-95],no:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:03.07+08:00","@version":"1","message":"自动分配EM7_3_08(************)给YS-6297I-WTx2-A_64GB下的Plan7","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:03.07+08:00","@version":"1","message":"add 1 devices to plan: Plan7 ,expect num: 3, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:03.07+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6387, flash:YS-6297I-WTx2-A_64GB, planEntity:OrderPlanEntity(id=113687, orderId=6387, name=Plan7, status=QUEUE, priority=75)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:03.072+08:00","@version":"1","message":"测试单: YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB plan: Plan7已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:03.284+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:03.285+08:00","@version":"1","message":"此次分配flash批次 YS-6297I-WTx2-A_64GB 下的Plan共消耗 3 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:03.287+08:00","@version":"1","message":"更新Flash:YS-6297I-WTx2-A_64GB的样片数量从114变更为111","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:21:03.295+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80478ea0eaadd6dc","spanId":"80478ea0eaadd6dc","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.62+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6ffdcf6f5f6851d8","spanId":"6ffdcf6f5f6851d8","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:24:02.622+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=111)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6ffdcf6f5f6851d8","spanId":"6ffdcf6f5f6851d8","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6ffdcf6f5f6851d8","spanId":"6ffdcf6f5f6851d8","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.627+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6ffdcf6f5f6851d8","spanId":"6ffdcf6f5f6851d8","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6ffdcf6f5f6851d8","spanId":"6ffdcf6f5f6851d8","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.632+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6ffdcf6f5f6851d8","spanId":"6ffdcf6f5f6851d8","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:24:02.632+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6ffdcf6f5f6851d8","spanId":"6ffdcf6f5f6851d8","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.62+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75fb5b364d66d539","spanId":"75fb5b364d66d539","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:27:02.622+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=111)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75fb5b364d66d539","spanId":"75fb5b364d66d539","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.626+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75fb5b364d66d539","spanId":"75fb5b364d66d539","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.627+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75fb5b364d66d539","spanId":"75fb5b364d66d539","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75fb5b364d66d539","spanId":"75fb5b364d66d539","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.632+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75fb5b364d66d539","spanId":"75fb5b364d66d539","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:27:02.632+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"75fb5b364d66d539","spanId":"75fb5b364d66d539","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.738+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80531992778354c4","spanId":"80531992778354c4","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.74+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=111)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80531992778354c4","spanId":"80531992778354c4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.745+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80531992778354c4","spanId":"80531992778354c4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.745+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80531992778354c4","spanId":"80531992778354c4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.751+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80531992778354c4","spanId":"80531992778354c4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.751+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80531992778354c4","spanId":"80531992778354c4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.751+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"80531992778354c4","spanId":"80531992778354c4","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d2d3ec942707c67","spanId":"5d2d3ec942707c67","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:33:02.625+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=111)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d2d3ec942707c67","spanId":"5d2d3ec942707c67","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d2d3ec942707c67","spanId":"5d2d3ec942707c67","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.63+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d2d3ec942707c67","spanId":"5d2d3ec942707c67","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d2d3ec942707c67","spanId":"5d2d3ec942707c67","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.633+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d2d3ec942707c67","spanId":"5d2d3ec942707c67","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:33:02.633+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5d2d3ec942707c67","spanId":"5d2d3ec942707c67","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.619+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83c494bbe030de5c","spanId":"83c494bbe030de5c","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:36:02.62+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=111)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83c494bbe030de5c","spanId":"83c494bbe030de5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.624+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83c494bbe030de5c","spanId":"83c494bbe030de5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.624+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83c494bbe030de5c","spanId":"83c494bbe030de5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.629+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83c494bbe030de5c","spanId":"83c494bbe030de5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.629+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83c494bbe030de5c","spanId":"83c494bbe030de5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:36:02.629+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83c494bbe030de5c","spanId":"83c494bbe030de5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a912f36abedce15c","spanId":"a912f36abedce15c","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:51:02.639+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=111)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a912f36abedce15c","spanId":"a912f36abedce15c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.646+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a912f36abedce15c","spanId":"a912f36abedce15c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.646+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a912f36abedce15c","spanId":"a912f36abedce15c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.653+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a912f36abedce15c","spanId":"a912f36abedce15c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.668+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a912f36abedce15c","spanId":"a912f36abedce15c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:51:02.669+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a912f36abedce15c","spanId":"a912f36abedce15c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.724+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06680ee020145bb6","spanId":"06680ee020145bb6","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.729+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=111)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06680ee020145bb6","spanId":"06680ee020145bb6","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.737+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06680ee020145bb6","spanId":"06680ee020145bb6","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.738+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06680ee020145bb6","spanId":"06680ee020145bb6","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.745+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06680ee020145bb6","spanId":"06680ee020145bb6","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.745+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06680ee020145bb6","spanId":"06680ee020145bb6","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.745+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06680ee020145bb6","spanId":"06680ee020145bb6","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:02.893+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:00:02.962+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=111)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.117+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.12+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.16+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113790, orderId=6387, name=Plan20, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.229+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113790, orderId=6387, name=Plan20, status=QUEUE, priority=70)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.233+08:00","@version":"1","message":" getValidPlanList sumList: [8] leftFlashNum:111 index:1 plans:[OrderPlanEntity(id=113790, orderId=6387, name=Plan20, status=QUEUE, priority=70)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:00:03.233+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 1 plans: [OrderPlanEntity(id=113790, orderId=6387, name=Plan20, status=QUEUE, priority=70)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.235+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113790, orderId=6387, name=Plan20, status=QUEUE, priority=70)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:03.528+08:00","@version":"1","message":"[61815ac6] HTTP GET http://ereport.yeestor.com/wo/device/list?p=IND_SD","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.195+08:00","@version":"1","message":"getAllDeviceList with IND_SD got data DeviceListResp(code=0, data=[{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_06,*************,8C-32-23-21-97-C4,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_02,*************,8C-32-23-21-99-46,[{Mars=4}, {FIO=4}, {性能=4}] }], msg=测试机信息获取成功！, workPcLst=[{ EM6_1_16,************,B4-2E-99-EA-B3-D0,[{低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_15,************,B4-2E-99-EA-22-87,[{低温=4}, {温循=4}, {Mars=4}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.732+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6387 flash: YS-6297I-WTx2-A_64GB available device: [{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_06,*************,8C-32-23-21-97-C4,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_02,*************,8C-32-23-21-99-46,[{Mars=4}, {FIO=4}, {性能=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.739+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.739+08:00","@version":"1","message":"[6387] Plan20 need Num: 8 to test. YS-6297I-WTx2-A_64GB left num:111 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.76+08:00","@version":"1","message":"subProduct IND_SD flashName YS-6297I-WTx2-A_64GB plan [Plan20] attrs Mars belongTo Claire.xiong is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.898+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM7_3_01, EM7_3_02, EM7_3_03, EM7_3_04, EM7_3_05, EM7_3_06, EM7_3_07, EM7_3_08, EM7_3_09, EM7_3_10, EM7_3_11, EM7_3_12, EM7_3_13, EM7_3_14, EM7_4_02, EM7_4_06, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16, GE2_2_19, GE2_2_20, GE2_2_21, GE2_2_22, GE2_2_23, GE2_2_24, GE2_2_25, GE2_2_26, GE2_2_27, GE2_2_28, GE2_2_29, GE2_2_30, GE2_2_31, GE2_2_32, GE_PC_023, GE_PC_024, GE_PC_025, GE_PC_026, GE_PC_027, GE_PC_028, GE_PC_029, GE_PC_030]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.899+08:00","@version":"1","message":"Plan20 test all sample is false","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.901+08:00","@version":"1","message":"devices: [EM7_3_01, EM7_3_02, EM7_3_03, EM7_3_04, EM7_3_05, EM7_3_06, EM7_3_07, EM7_3_08, EM7_3_09, EM7_3_10, EM7_3_11, EM7_3_12, EM7_3_13, EM7_3_14]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.903+08:00","@version":"1","message":"for index: 0 size: 2 sum: 8 testNum: 8","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.904+08:00","@version":"1","message":"wait testNum: 8 assign sum: 8 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.904+08:00","@version":"1","message":"[6387] plan:Plan20 use 2 pc: [{ EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.905+08:00","@version":"1","message":"assignDevices [6387] - find 1 run devices:  {Plan20=[{ EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.908+08:00","@version":"1","message":"工单[6387] flash YS-6297I-WTx2-A_64GB , 参与此次Plan预分配的plan共有 [Plan20] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:04.988+08:00","@version":"1","message":" add 2 devices :[{ EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }]  to plan:Plan20","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.017+08:00","@version":"1","message":"plan:Plan20 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 8","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.27+08:00","@version":"1","message":"[YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB] - Plan20 holdDevices  :[PlanDeviceEntity(id=277418, orderId=6387, planId=113790, planName=Plan20, ip=*************, mac=9C-6B-00-48-CC-11, no=EM7_3_01, position=EM7_3_01, score=170, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277419, orderId=6387, planId=113790, planName=Plan20, ip=*************, mac=9C-6B-00-48-CC-F6, no=EM7_3_02, position=EM7_3_02, score=170, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.595+08:00","@version":"1","message":"lock device:9C-6B-00-48-CC-11 to orderNo:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB planName:Plan20 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.675+08:00","@version":"1","message":"lock device:9C-6B-00-48-CC-F6 to orderNo:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB planName:Plan20 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.688+08:00","@version":"1","message":"[306e3332] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.945+08:00","@version":"1","message":"lockDevice with ipList:[*************, *************] - macList:[9C-6B-00-48-CC-11, 9C-6B-00-48-CC-F6],no:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.957+08:00","@version":"1","message":"自动分配EM7_3_01(*************),EM7_3_02(*************)给YS-6297I-WTx2-A_64GB下的Plan20","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.958+08:00","@version":"1","message":"add 2 devices to plan: Plan20 ,expect num: 8, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.958+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6387, flash:YS-6297I-WTx2-A_64GB, planEntity:OrderPlanEntity(id=113790, orderId=6387, name=Plan20, status=QUEUE, priority=70)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:05.994+08:00","@version":"1","message":"测试单: YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB plan: Plan20已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:06.798+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:06.803+08:00","@version":"1","message":"此次分配flash批次 YS-6297I-WTx2-A_64GB 下的Plan共消耗 8 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:06.809+08:00","@version":"1","message":"更新Flash:YS-6297I-WTx2-A_64GB的样片数量从111变更为103","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:00:06.883+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daa2503819075d49","spanId":"daa2503819075d49","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.623+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1a176ecf4f97ec9","spanId":"c1a176ecf4f97ec9","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:03:02.626+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1a176ecf4f97ec9","spanId":"c1a176ecf4f97ec9","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.63+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1a176ecf4f97ec9","spanId":"c1a176ecf4f97ec9","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.63+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1a176ecf4f97ec9","spanId":"c1a176ecf4f97ec9","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1a176ecf4f97ec9","spanId":"c1a176ecf4f97ec9","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.633+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1a176ecf4f97ec9","spanId":"c1a176ecf4f97ec9","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:03:02.633+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1a176ecf4f97ec9","spanId":"c1a176ecf4f97ec9","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.683+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee9d290aae4e9de0","spanId":"ee9d290aae4e9de0","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.686+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee9d290aae4e9de0","spanId":"ee9d290aae4e9de0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.693+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee9d290aae4e9de0","spanId":"ee9d290aae4e9de0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.693+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee9d290aae4e9de0","spanId":"ee9d290aae4e9de0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.7+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee9d290aae4e9de0","spanId":"ee9d290aae4e9de0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.701+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee9d290aae4e9de0","spanId":"ee9d290aae4e9de0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.701+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee9d290aae4e9de0","spanId":"ee9d290aae4e9de0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:09:02.623+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=114), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.641+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113791, orderId=6387, name=Plan20, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.642+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: [OrderPlanEntity(id=113791, orderId=6387, name=Plan20, status=QUEUE, priority=70)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.642+08:00","@version":"1","message":" getValidPlanList sumList: [8] leftFlashNum:114 index:1 plans:[OrderPlanEntity(id=113791, orderId=6387, name=Plan20, status=QUEUE, priority=70)]","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:09:02.642+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 1 plans: [OrderPlanEntity(id=113791, orderId=6387, name=Plan20, status=QUEUE, priority=70)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.642+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113791, orderId=6387, name=Plan20, status=QUEUE, priority=70)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.662+08:00","@version":"1","message":"[2f025e31] HTTP GET http://ereport.yeestor.com/wo/device/list?p=IND_SD","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.864+08:00","@version":"1","message":"getAllDeviceList with IND_SD got data DeviceListResp(code=0, data=[{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }], msg=测试机信息获取成功！, workPcLst=[{ EM6_1_16,************,B4-2E-99-EA-B3-D0,[{低温=4}, {温循=4}, {Mars=4}] }, { EM6_1_15,************,B4-2E-99-EA-22-87,[{低温=4}, {温循=4}, {Mars=4}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.901+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6387 flash: YS-6297I-WTx1-A_32GB available device: [{ GE2_2_32,************,2C-F0-5D-40-F5-7E,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_21,*************,2C-F0-5D-40-F5-32,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_26,*************,2C-F0-5D-40-F7-DB,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_23,************,2C-F0-5D-40-F5-29,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_31,************,2C-F0-5D-40-F5-1B,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_28,************,2C-F0-5D-40-F5-DF,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_19,*************,2C-F0-5D-40-F5-28,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_22,*************,2C-F0-5D-40-F5-7C,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_25,*************,2C-F0-5D-40-F7-15,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_29,************,2C-F0-5D-40-F8-D0,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_30,************,2C-F0-5D-40-F5-AC,[{Mars=4}, {TCPIP掉电=4}, {FIO=4}, {性能=4}] }, { GE2_2_20,*************,2C-F0-5D-40-F5-7D,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_27,************,2C-F0-5D-40-F8-F2,[{Mars=4}, {TCPIP掉电=4}] }, { GE2_2_24,*************,2C-F0-5D-40-F5-3D,[{Mars=4}, {TCPIP掉电=4}] }, { EM6_2_15,************,A4-0C-66-04-CC-46,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_2_16,************,E0-D5-5E-E7-CF-2F,[{低温=4}, {温循=4}, {Mars=4}, {高温=4}] }, { EM6_3_16,************,E0-D5-5E-9D-12-FB,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_10,*************,F0-2F-74-33-FB-EE,[{Mars=4}, {低温=4}, {高温=4}, {温循=4}] }, { EM6_3_08,************,F0-2F-74-F4-86-81,[{Mars=4}, {高温=4}, {低温=4}, {温循=4}] }, { EM6_3_14,************,B4-2E-99-5A-DF-48,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_15,************,F0-2F-74-F4-86-65,[{高温=4}, {Mars=4}, {温循=4}, {低温=4}] }, { EM6_3_12,************,70-85-C2-82-01-11,[{Mars=4}, {高温=4}, {温循=4}, {低温=4}] }, { EM6_3_09,************,F0-2F-74-4E-A6-60,[{温循=4}, {Mars=4}, {高温=4}, {低温=4}] }, { EM6_4_13,************1,D8-5E-D3-5F-80-90,[{Mars=4}, {高温=4}] }, { EM6_4_14,*************,D8-5E-D3-5F-84-E0,[{Mars=4}, {高温=4}] }, { EM6_4_15,*************,D8-5E-D3-5F-84-45,[{Mars=4}, {高温=4}] }, { EM6_4_16,*************,D8-5E-D3-5B-D0-4F,[{Mars=4}, {高温=4}] }, { GE_PC_029,************,D8-5E-D3-5F-81-9F,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_026,*************,D8-5E-D3-59-EB-AF,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_027,************,D8-5E-D3-59-E8-2D,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_028,************0,D8-5E-D3-5F-7F-D3,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_024,*************,D8-5E-D3-51-81-E4,[{Mars=4}, {TCPIP掉电=4}] }, { GE_PC_023,*************,D8-5E-D3-59-E5-0A,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_025,************,D8-5E-D3-59-E9-11,[{TCPIP掉电=4}, {Mars=4}] }, { GE_PC_030,*************,D8-5E-D3-59-E8-4E,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_13,*************,8C-32-23-21-9D-BD,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM6_5_16,*************,A4-0C-66-15-9C-E6,[] }, { EM6_5_15,*************,A4-0C-66-15-A1-3E,[] }, { EM6_5_14,*************,A4-0C-66-15-9E-CA,[] }, { EM6_5_13,*************,A4-0C-66-17-76-30,[] }, { EM7_3_12,*************,9C-6B-00-49-05-94,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_13,*************,9C-6B-00-48-FE-FE,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_14,*************,9C-6B-00-48-FE-A0,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_11,************,9C-6B-00-49-00-7B,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_10,*************,9C-6B-00-48-FE-A1,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_03,*************,9C-6B-00-48-FE-F7,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_09,*************,9C-6B-00-48-FF-B5,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_04,************,9C-6B-00-48-FE-AD,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_05,************,9C-6B-00-48-FE-A9,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_07,************,9C-6B-00-48-EA-9C,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_3_08,************,9C-6B-00-49-02-95,[{TCPIP掉电=4}, {Mars=4}, {性能=4}, {FIO=4}] }, { EM7_3_06,*************,9C-6B-00-47-3A-92,[{TCPIP掉电=4}, {Mars=4}] }, { EM7_4_09,*************,8C-32-23-21-97-95,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_12,************,8C-32-23-21-9D-0A,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_11,*************,8C-32-23-21-9E-1F,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_10,*************,8C-32-23-21-99-56,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_14,*************,8C-32-23-21-97-90,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_08,*************,8C-32-23-21-99-A2,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_07,*************,8C-32-23-21-97-60,[{Mars=4}, {FIO=4}, {性能=4}] }, { EM7_4_16,*************,8C-32-23-21-9D-0E,[{Mars=4}, {性能=4}, {FIO=4}] }, { EM7_4_15,*************,8C-32-23-21-98-D2,[{Mars=4}, {性能=4}, {FIO=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.902+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.902+08:00","@version":"1","message":"[6387] Plan20 need Num: 8 to test. YS-6297I-WTx1-A_32GB left num:114 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.902+08:00","@version":"1","message":"subProduct IND_SD flashName YS-6297I-WTx1-A_32GB plan [Plan20] attrs Mars belongTo Claire.xiong is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.904+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [EM7_3_01, EM7_3_02, EM7_3_03, EM7_3_04, EM7_3_05, EM7_3_06, EM7_3_07, EM7_3_08, EM7_3_09, EM7_3_10, EM7_3_11, EM7_3_12, EM7_3_13, EM7_3_14, EM7_4_07, EM7_4_08, EM7_4_09, EM7_4_10, EM7_4_11, EM7_4_12, EM7_4_13, EM7_4_14, EM7_4_15, EM7_4_16, GE2_2_19, GE2_2_20, GE2_2_21, GE2_2_22, GE2_2_23, GE2_2_24, GE2_2_25, GE2_2_26, GE2_2_27, GE2_2_28, GE2_2_29, GE2_2_30, GE2_2_31, GE2_2_32, GE_PC_023, GE_PC_024, GE_PC_025, GE_PC_026, GE_PC_027, GE_PC_028, GE_PC_029, GE_PC_030]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.904+08:00","@version":"1","message":"Plan20 test all sample is false","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.904+08:00","@version":"1","message":"devices: [EM7_3_01, EM7_3_02, EM7_3_03, EM7_3_04, EM7_3_05, EM7_3_06, EM7_3_07, EM7_3_08, EM7_3_09, EM7_3_10, EM7_3_11, EM7_3_12, EM7_3_13, EM7_3_14]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.904+08:00","@version":"1","message":"for index: 0 size: 2 sum: 8 testNum: 8","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.904+08:00","@version":"1","message":"wait testNum: 8 assign sum: 8 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.904+08:00","@version":"1","message":"[6387] plan:Plan20 use 2 pc: [{ EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.904+08:00","@version":"1","message":"assignDevices [6387] - find 1 run devices:  {Plan20=[{ EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.905+08:00","@version":"1","message":"工单[6387] flash YS-6297I-WTx1-A_32GB , 参与此次Plan预分配的plan共有 [Plan20] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.912+08:00","@version":"1","message":" add 2 devices :[{ EM7_3_01,*************,9C-6B-00-48-CC-11,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }, { EM7_3_02,*************,9C-6B-00-48-CC-F6,[{TCPIP掉电=4}, {Mars=4}, {FIO=4}] }]  to plan:Plan20","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.926+08:00","@version":"1","message":"plan:Plan20 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 8","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.932+08:00","@version":"1","message":"[YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB] - Plan20 holdDevices  :[PlanDeviceEntity(id=277422, orderId=6387, planId=113791, planName=Plan20, ip=*************, mac=9C-6B-00-48-CC-11, no=EM7_3_01, position=EM7_3_01, score=170, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277423, orderId=6387, planId=113791, planName=Plan20, ip=*************, mac=9C-6B-00-48-CC-F6, no=EM7_3_02, position=EM7_3_02, score=170, owner=, testNum=4, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.934+08:00","@version":"1","message":"lock device:9C-6B-00-48-CC-11 to orderNo:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB planName:Plan20 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.938+08:00","@version":"1","message":"lock device:9C-6B-00-48-CC-F6 to orderNo:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB planName:Plan20 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:02.943+08:00","@version":"1","message":"[18e59add] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.109+08:00","@version":"1","message":"lockDevice with ipList:[*************, *************] - macList:[9C-6B-00-48-CC-11, 9C-6B-00-48-CC-F6],no:YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.11+08:00","@version":"1","message":"自动分配EM7_3_01(*************),EM7_3_02(*************)给YS-6297I-WTx1-A_32GB下的Plan20","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.11+08:00","@version":"1","message":"add 2 devices to plan: Plan20 ,expect num: 8, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.11+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6387, flash:YS-6297I-WTx1-A_32GB, planEntity:OrderPlanEntity(id=113791, orderId=6387, name=Plan20, status=QUEUE, priority=70)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.112+08:00","@version":"1","message":"测试单: YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB plan: Plan20已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.323+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.323+08:00","@version":"1","message":"此次分配flash批次 YS-6297I-WTx1-A_32GB 下的Plan共消耗 8 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.325+08:00","@version":"1","message":"更新Flash:YS-6297I-WTx1-A_32GB的样片数量从114变更为106","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.336+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.336+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:09:03.336+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"42d753a178458bc1","spanId":"42d753a178458bc1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.964+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edd33da18df21ef3","spanId":"edd33da18df21ef3","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:12:02.966+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=106), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edd33da18df21ef3","spanId":"edd33da18df21ef3","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.97+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edd33da18df21ef3","spanId":"edd33da18df21ef3","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.971+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edd33da18df21ef3","spanId":"edd33da18df21ef3","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.976+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edd33da18df21ef3","spanId":"edd33da18df21ef3","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.976+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edd33da18df21ef3","spanId":"edd33da18df21ef3","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:12:02.976+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"edd33da18df21ef3","spanId":"edd33da18df21ef3","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.62+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65bf0a60f0bf4d84","spanId":"65bf0a60f0bf4d84","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:15:02.622+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=106), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65bf0a60f0bf4d84","spanId":"65bf0a60f0bf4d84","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.626+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65bf0a60f0bf4d84","spanId":"65bf0a60f0bf4d84","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.627+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65bf0a60f0bf4d84","spanId":"65bf0a60f0bf4d84","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65bf0a60f0bf4d84","spanId":"65bf0a60f0bf4d84","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.632+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65bf0a60f0bf4d84","spanId":"65bf0a60f0bf4d84","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:15:02.632+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"65bf0a60f0bf4d84","spanId":"65bf0a60f0bf4d84","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.682+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5fdc3f960e8f1ac0","spanId":"5fdc3f960e8f1ac0","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.685+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=106), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5fdc3f960e8f1ac0","spanId":"5fdc3f960e8f1ac0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.689+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5fdc3f960e8f1ac0","spanId":"5fdc3f960e8f1ac0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.689+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5fdc3f960e8f1ac0","spanId":"5fdc3f960e8f1ac0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.693+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5fdc3f960e8f1ac0","spanId":"5fdc3f960e8f1ac0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.693+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5fdc3f960e8f1ac0","spanId":"5fdc3f960e8f1ac0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.693+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5fdc3f960e8f1ac0","spanId":"5fdc3f960e8f1ac0","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"33e510b49d1444cb","spanId":"33e510b49d1444cb","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:21:02.623+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=106), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"33e510b49d1444cb","spanId":"33e510b49d1444cb","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.632+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"33e510b49d1444cb","spanId":"33e510b49d1444cb","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.632+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"33e510b49d1444cb","spanId":"33e510b49d1444cb","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.635+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"33e510b49d1444cb","spanId":"33e510b49d1444cb","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.635+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"33e510b49d1444cb","spanId":"33e510b49d1444cb","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:02.635+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"33e510b49d1444cb","spanId":"33e510b49d1444cb","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:21:48.723+08:00","@version":"1","message":"execute:IND_SD_8C-32-23-21-97-95_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:IND_SD userName:Claire.xiong title:重新选择电脑后电脑关机 planId:113791 mac:8C-32-23-21-97-95","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b86d4d374c16c2c","spanId":"7b86d4d374c16c2c","no":"6387","traceType":"TimedShutdownDeviceJob","flash":"YS-6297I-WTx1-A_32GB"}
{"@timestamp":"2025-07-24T16:21:48.726+08:00","@version":"1","message":"设备8C-32-23-21-97-95不在计划113791中，忽略","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b86d4d374c16c2c","spanId":"7b86d4d374c16c2c","no":"6387","traceType":"TimedShutdownDeviceJob","flash":"YS-6297I-WTx1-A_32GB"}
{"@timestamp":"2025-07-24T16:24:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d71251deab752e5c","spanId":"d71251deab752e5c","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:24:02.624+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=106), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d71251deab752e5c","spanId":"d71251deab752e5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.627+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d71251deab752e5c","spanId":"d71251deab752e5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.627+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d71251deab752e5c","spanId":"d71251deab752e5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.633+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d71251deab752e5c","spanId":"d71251deab752e5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.633+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d71251deab752e5c","spanId":"d71251deab752e5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:24:02.633+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d71251deab752e5c","spanId":"d71251deab752e5c","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.689+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86f9ad5d684e5099","spanId":"86f9ad5d684e5099","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:27:02.691+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=106), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86f9ad5d684e5099","spanId":"86f9ad5d684e5099","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.695+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86f9ad5d684e5099","spanId":"86f9ad5d684e5099","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.695+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86f9ad5d684e5099","spanId":"86f9ad5d684e5099","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.699+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86f9ad5d684e5099","spanId":"86f9ad5d684e5099","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.699+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86f9ad5d684e5099","spanId":"86f9ad5d684e5099","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:27:02.699+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86f9ad5d684e5099","spanId":"86f9ad5d684e5099","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:02.887+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"22baba71352f2ce1","spanId":"22baba71352f2ce1","context":"QueueService","no":"6387","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:02.97+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6624, orderId=6387, flash=YS-6297I-WTx1-A_32GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx1-A_32GB, num=120, leftNum=106), OrderFlashEntity(id=6625, orderId=6387, flash=YS-6297I-WTx2-A_64GB, orderFlashNo=YS6297##MP#########0I####WTS-9BC4_TLC250011_Alpha_YS-6297I-WTx2-A_64GB, num=120, leftNum=103)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"22baba71352f2ce1","spanId":"22baba71352f2ce1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.083+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"22baba71352f2ce1","spanId":"22baba71352f2ce1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.111+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx1-A_32GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"22baba71352f2ce1","spanId":"22baba71352f2ce1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.12+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"22baba71352f2ce1","spanId":"22baba71352f2ce1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.129+08:00","@version":"1","message":"[6387] - [YS-6297I-WTx2-A_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"22baba71352f2ce1","spanId":"22baba71352f2ce1","context":"QueueService","no":"6387","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.13+08:00","@version":"1","message":"[IND_SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"22baba71352f2ce1","spanId":"22baba71352f2ce1","context":"QueueService","no":"6387","traceType":"分配设备"}
