package com.yeestor.work_order.entity.document;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;

/**
 * 用于存储plan下的文档的实体类
 */
@Getter
@Setter
@ToString
@Entity(name = "PlanDocument")
@Table(name = "wo_order_plan_doc")
public class PlanDocumentEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    /**
     * 工单id
     */
    @Column(name = "order_id")
    private long orderId ;

    /**
     * plan id
     */
    @Column(name = "plan_id")
    private long planId ;

    /**
     * 文档id
     */
    @Column(name = "document_id")
    private long documentId ;

    @ColumnDefault("0")
    @Column(name = "is_retest")
    private Boolean isRetest ;

}
