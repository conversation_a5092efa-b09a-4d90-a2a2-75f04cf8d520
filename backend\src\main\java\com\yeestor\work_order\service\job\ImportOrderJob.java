package com.yeestor.work_order.service.job;

import com.yeestor.work_order.model.ci.OrderInfoModel;
import com.yeestor.work_order.service.order.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ImportOrderJob implements Job {
    public static final String GROUP_NAME = "ImportOrderJob";
    private static final String JOB_DATA_KEY_CI_ID = "ciId";
    private static final String JOB_DATA_KEY_ZT_BUILD_ID = "buildId";
    private static final String JOB_DATA_KEY_TEST_TASK_LINK = "testTaskLink";
    private static final String JOB_DATA_KEY_IS_HISTORY = "isHistory";

    private final OrderService orderService ;


    public static String buildJodName(String ciId) {
        return "ImportOrderJob_" + ciId;
    }


    public static JobDetail buildJobDetail(
            String jobName,
            String ciId,
            String testTaskLink,
            String ztBuildId,
            boolean isHistory
    ) {
        return JobBuilder.newJob(ImportOrderJob.class)
                .withIdentity(jobName, GROUP_NAME)
                .usingJobData(JOB_DATA_KEY_CI_ID, ciId)
                .usingJobData(JOB_DATA_KEY_TEST_TASK_LINK, testTaskLink)
                .usingJobData(JOB_DATA_KEY_ZT_BUILD_ID, ztBuildId)
                .usingJobData(JOB_DATA_KEY_IS_HISTORY, isHistory)
                .build();
    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        log.debug("execute:{}  map key:{} value:{} ", context.getJobDetail().getKey().getName(), dataMap.getKeys(), dataMap.values());

        String ciId = dataMap.getString(JOB_DATA_KEY_CI_ID);
        String testTaskLink = dataMap.getString(JOB_DATA_KEY_TEST_TASK_LINK);
        String ztBuildId = dataMap.getString(JOB_DATA_KEY_ZT_BUILD_ID);
        boolean isHistory = dataMap.getBoolean(JOB_DATA_KEY_IS_HISTORY);
        log.info("ImportOrderJob ztBuildId: {}", ztBuildId);

        if (ciId == null) {
            return;
        }
        OrderInfoModel infoModel = orderService.fetchOrderInfoModel(ciId, isHistory);
        orderService.importOrder(testTaskLink, ztBuildId, infoModel);
    }
}
