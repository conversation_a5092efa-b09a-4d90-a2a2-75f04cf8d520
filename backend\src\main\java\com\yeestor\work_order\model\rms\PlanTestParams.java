package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel("开始测试Plan的参数")
public class PlanTestParams {

    @ApiModelProperty("plan的名称")
    private String planName ;

    @ApiModelProperty("工单号")
    private String no;

    @ApiModelProperty("通知群组")
    private String group;

    @ApiModelProperty(value = "自动定位编号", example = "true")
    private boolean bAutoLoc;

    @ApiModelProperty("测试此plan的ip 列表")
    private List<String> ipList ;

    @ApiModelProperty("测试此plan的mac列表, 与ipList二选一,如果都有,以mac为准")
    private List<String> macList ;

    @ApiModelProperty("产线")
    private String product;

    @ApiModelProperty("plan的路径")
    private String planPath;

    @ApiModelProperty("Mars 路径")
    private String marsPath;

    @ApiModelProperty("量产工具路径")
    private String mpPath;

    @ApiModelProperty("发起测试的用户")
    private String username;

}
