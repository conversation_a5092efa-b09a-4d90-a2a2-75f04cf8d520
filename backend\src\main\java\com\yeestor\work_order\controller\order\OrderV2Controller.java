package com.yeestor.work_order.controller.order;

import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.req.ShareInfoParams;
import com.yeestor.work_order.model.http.req.order.OrderFlashConfirmV2Req;
import com.yeestor.work_order.model.http.req.order.ZenTaoImportParams;
import com.yeestor.work_order.model.http.resp.order.BugInfo;
import com.yeestor.work_order.model.http.resp.order.WorkOrderDetailVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.model.zt.TestTaskInfo;
import com.yeestor.work_order.model.zt.UserInfo;
import com.yeestor.work_order.repository.*;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderImportService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.role.RoleService;
import com.yeestor.work_order.service.zentao.ZenTaoService;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.work_order.utils.ZentaoAPI;
import com.yeestor.zentao.model.resp.build.ZtBuildResp;
import com.yeestor.zentao.model.resp.build.ZtBuildBugResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import springfox.documentation.annotations.ApiIgnore;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/order/v2/")
@Api(tags = {"OrderV2"}, value = "处理工单系统的部分更新的接口")
public class OrderV2Controller {


    private final WorkOrderRepository orderRepository;
    private final OrderBugRepository orderBugRepository ;
    private final OrderFlashRepository flashRepository;
    private final OrderPlanRepository planRepository;
    private final OrderService orderService ;
    private final PlanService planService;
    private final FlashService flashService ;
    private final RoleService roleService ;
    private final NotificationService notificationService ;
    private final ZentaoAPI zentaoAPI ;
    private final DataChangeListener dataChangeListener;

    private final ZenTaoService zenTaoService;
    private final OrderImportService orderImportService;

    @PostMapping("/confirm")
    @ApiOperation(value = "确认工单的Flash相关的信息", notes = "测试主管确认工单Flash相关的信息,进行此操作需要测试主管的权限")
    public HandleResp<String> confirmOrderFlashInfo(
            @RequestBody @Valid OrderFlashConfirmV2Req params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "确认Flash" ;
        WorkOrderEntity woe = orderService.findOrderOrElseThrow(params.getOrderId()) ;
        LogUtils.setOrderTracePoint(woe.getId(), operate);
        if(woe.getStatus() != WorkOrderEntity.Status.CREATED) {
            return HandleResp.failed("请不要多次确认工单的Flash!");
        }
        roleService.checkPermission(userDetail, woe.getSubProduct(),operate, Permission.CONFIRM_FLASH);
        orderService.confirmOrderFlashInfoV2(woe,params,userDetail);

        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.ORDER_CONFIRMED_FLASH)
                        .orderId(params.getOrderId())
                        .build()
        );
        log.info("用户[{}] 确认工单[{}]的Flash成功！", userDetail.getUsername(), woe.getNo());
        LogUtils.clearTracePoint();
        return HandleResp.ok("","更新成功") ;
    }

    @ApiOperation(value = "修改工单Mars信息")
    @PostMapping("/{orderId}/mars/change")
    public HandleResp<String> changeOrderMars(
            @ApiParam("工单id") @PathVariable("orderId") @Min(value = 1,message = "无效的工单ID") long orderId,
            @ApiParam("mars路径") @RequestParam(value = "marsPath") String marsPath,
            @ApiParam("Plan脚本路径") @RequestParam(value = "planPath") String planPath,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "修改工单Mars信息";
        log.info("update order[{}] Mars path: {}, plan path: {}", orderId, marsPath, planPath);
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);

        LogUtils.setOrderTracePoint(orderEntity.getId(),operate);
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.UPDATE_MARS);


        orderEntity.setMarsPath(marsPath);
        orderEntity.setPlanPath(planPath);
        orderRepository.save(orderEntity);

        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.ORDER_UPDATE_MARS)
                        .orderId(orderId)
                        .build()
        );
        LogUtils.clearTracePoint();
        return HandleResp.ok("","更新成功") ;
    }

    @PostMapping("/share")
    @ApiOperation(value = "分享链接以及详情")
    public HandleResp<String> shareInfo(
            @RequestBody @Valid ShareInfoParams params,
            @ApiIgnore @AuthenticationPrincipal
                    @NotNull(message = "尚未登录") OAuthUserDetail userDetail
    ){
        log.info("shareInfo params:{}",params);
        return notificationService.sendToConversation(params,userDetail);
    }


    @ApiOperation(value = "暂缓flash批次的分配")
    @PostMapping("/{orderId}/{flash}/pause")
    public HandleResp<String> pauseFlash(
            @ApiParam("工单id") @PathVariable("orderId") @Min(value = 1,message = "无效的工单ID") long orderId,
            @ApiParam("flash批次的名称") @PathVariable("flash") @NotBlank(message = "flash 批次不能为空") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String operate = "暂缓Flash批次分配" ;
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId) ;
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId,flash) ;

        LogUtils.setOrderAndFlashTracePoint(orderEntity.getId(), flash,operate);
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(),operate, Permission.START_TEST, Permission.CONFIRM_FLASH);

        List<OrderPlanEntity> planList = planRepository.findAllByOrderIdAndFlashAndType(orderId,flash,0);
        planList.stream()
                .filter(p -> p.getStatus() == OrderPlanEntity.Status.CONFIRMED ||
                        p.getStatus() == OrderPlanEntity.Status.READY)
                .forEach(p -> {
                    // 释放对应Plan的设备,并且将Plan的状态更新至QUEUE,但是并没有必要将plan disable
                    planService.pausePlan(p,flashEntity);

                    log.debug(" Flash:{} 's Plan: {} need pause, handle success ! ",flash ,p.getName());
                });

        flashService.disableFlash(flashEntity);
        log.debug("pause the [ {} ] flash:{} success", orderEntity.getNo(), flash);

        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.FLASH_PAUSE)
                        .orderId(orderId)
                        .flash(flash)
                        .build()
        );
        LogUtils.clearTracePoint();
        return HandleResp.ok("","更新成功") ;
    }


    @Transactional
    @ApiOperation(value = "恢复flash批次的分配")
    @PostMapping("/{orderId}/{flash}/resume")
    public HandleResp<String> resumeFlash(
            @ApiParam("工单id") @PathVariable("orderId") @Min(value = 1,message = "无效的工单ID") long orderId,
            @ApiParam("flash批次的名称") @PathVariable("flash") @NotBlank(message = "flash 批次不能为空") String flash,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "恢复Flash批次分配" ;
        WorkOrderEntity orderEntity = orderRepository.findById(orderId).orElseThrow(()-> new DataNotFoundException("id为"+orderId+"的工单不存在"));
        OrderFlashEntity flashEntity = flashRepository.findByOrderIdAndFlash(orderId,flash).orElseThrow(()-> new DataNotFoundException("id为"+orderId+"的工单不存在 flash:"+flash));

        LogUtils.setOrderAndFlashTracePoint(orderEntity.getId(), flash,operate);
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(),operate, Permission.START_TEST, Permission.CONFIRM_FLASH);

        List<OrderPlanEntity> planList = planRepository.findAllByOrderIdAndFlashAndType(orderId,flash,0);
        planList.stream()
                .filter(OrderPlanEntity::isDisabled)
                .forEach(p -> {
                    p.setDisabled(false);
                    p.setUpdatedAt(System.currentTimeMillis());
                    planRepository.save(p);
                    log.info("Flash:{}下的{} 已经恢复分配,! ",flash ,p.getName());
                });

        flashEntity.setDisabled(false);
        flashEntity.setUpdatedAt(System.currentTimeMillis());
        flashRepository.save(flashEntity);
        log.info("Flash:{} 已经恢复分配,! ",flash);

        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.FLASH_RESUME)
                        .orderId(orderId)
                        .flash(flash)
                        .build()
        );

        LogUtils.clearTracePoint();
        return HandleResp.ok("","更新成功") ;
    }

    @SneakyThrows
    @Transactional
    @ApiOperation(value = "从禅道导入工单")
    @PostMapping("/zentao/import")
    public HandleResp<String> zentaoImportOrder(
            @Valid @RequestBody ZenTaoImportParams body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        String testTaskId = String.valueOf(body.getTaskId());
        String flash =  body.getFlash() ;

        log.info("---------TASK--------");

        TestTaskInfo taskInfo = zentaoAPI.getTestTaskInfo(body.getTaskId()) ;

        int buildId = taskInfo.getBuild() ;
        String desc = taskInfo.getDesc() ;
        String mailTo = taskInfo.getMailto().stream().map(UserInfo::getRealname).collect(Collectors.joining(",")) ;

        ZtBuildBugResp info = zenTaoService.fetchBuildByBuildId(String.valueOf(buildId));
        log.info("---------BUILD--------");

        log.info("buildInfo:{}",info);
        String product = body.getProduct() ;
        String subProduct = body.getSubProduct();

        orderImportService.importOrderFromZenTao(
                product,
                subProduct,
                testTaskId,
                flash,
                desc,
                mailTo,
                info,
                null
        );
        return HandleResp.ok(null,"从禅道导入成功") ;
    }


    @GetMapping("/sata/builds")
    @ApiOperation(value = "获取SATA产品与工业级SATA下的所有build信息",notes = "尽量想办法替换成eBuild")
    public HandleResp<List<ZtBuildResp>> fetchSataBuilds(){
        // 目前SATA的项目的ID是374,我们目前只需要获取这个项目下的所有build信息.  http://zt.yeestor.com/project-build-380.html
        List<ZtBuildResp> buildList = zenTaoService.fetchBuildsByProjectId("374");
        List<ZtBuildResp> industryBuildList = zenTaoService.fetchBuildsByProjectId("380");
        buildList.addAll(industryBuildList);
        return HandleResp.ok(buildList);
    }

    @GetMapping("/PCIe/builds/{type}")
    @ApiOperation(value = "获取PCIe下的所有build信息",notes = "尽量想办法替换成eBuild")
    public HandleResp<List<ZtBuildResp>> fetchPCIeBuilds(@PathVariable("type") int type){
        List<ZtBuildResp> buildList;
        if (type == 1) {
            buildList = zenTaoService.fetchBuildsByProjectId("450");    // 工业级PCIe
            List<ZtBuildResp> ys9205BuildList = zenTaoService.fetchBuildsByProjectId("1197");   // 9205主控
            buildList.addAll(ys9205BuildList);
        } else {
            buildList = zenTaoService.fetchBuildsByProjectId("375");    // 9203主控
            List<ZtBuildResp> ys9205BuildList = zenTaoService.fetchBuildsByProjectId("499");   // 9205主控
            buildList.addAll(ys9205BuildList);
        }
        return HandleResp.ok(buildList);
    }

    @GetMapping("/eMMC/builds")
    @ApiOperation(value = "获取eMMC 829x的所有build信息,目前仅作为在eBuild 中未支持前使用",notes = "eBuild支持后,此接口会被废弃")
    public HandleResp<List<ZtBuildResp>> fetch_eMMCBuilds(){
        // 目前 eMMC 829x的项目的ID是388,我们目前只需要获取这个项目下的所有build信息.
        List<ZtBuildResp> buildList = zenTaoService.fetchBuildsByProjectId("388");
        return HandleResp.ok(buildList);
    }

    @GetMapping("/IND_EMMC/builds")
    @ApiOperation(value = "获取工业级eMMC相关项目下的禅道版本",notes = "获取工业级eMMC产品相关项目下的禅道版本")
    public HandleResp<List<ZtBuildResp>> fetchINDeMMCBuilds(){
        List<String> projectIdLst = Arrays.asList("1201", "677", "742", "743");
        List<ZtBuildResp> allBuildList = new ArrayList<>();
        for (String projectId : projectIdLst) {
            List<ZtBuildResp> buildList = zenTaoService.fetchBuildsByProjectId(projectId);
            allBuildList.addAll(buildList);
        }
        return HandleResp.ok(allBuildList);
    }


    @ApiOperation(value = "获取指定工单绑定的bug 列表.")
    @GetMapping("/{orderId}/bugs")
    public HandleResp<List<BugInfo>> fetchBugList(
            @ApiParam("工单id") @PathVariable("orderId") @Min(value = 1,message = "无效的工单ID") long orderId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        List<OrderBugEntity> orderBugList = orderBugRepository.findAllByOrderId(orderId);
        List<BugInfo> bugList = orderBugList.stream().map(entity -> BugInfo.toModel(entity)).collect(Collectors.toList());
        log.info("orderId: {}, bugList: {}", orderId, bugList);

        return HandleResp.ok(bugList, "成功获取bug信息.");
    }

    @SneakyThrows
    @ApiOperation(value = "使用EventSource监听工单详情")
    @GetMapping(value = "/{orderId}/detail",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter fetchOrderDetailAsync(
            @ApiParam("工单id") @PathVariable("orderId") @Min(value = 1,message = "无效的工单ID") long orderId
    ){
        SseEmitter sseEmitter = new SseEmitter(-1L);
        dataChangeListener.addEmitter(
                DataChangeListener.EmitterType.Order,
                orderId,
                sseEmitter
        );
        Consumer<Throwable> consumer = e -> {
            dataChangeListener.removeEmitter(
                    DataChangeListener.EmitterType.Order,
                    orderId,
                    sseEmitter
            );
        };
        sseEmitter.onTimeout(()-> consumer.accept(null));
        sseEmitter.onCompletion(()-> consumer.accept(null));
        sseEmitter.onError(consumer);
        new Thread(()->{

            WorkOrderDetailVO entity = orderService.fetchOrderInfo(orderId);
            try {

                sseEmitter.send(SseEmitter.event()
                        .name("order")
                        .id(String.valueOf(System.currentTimeMillis()))
                        .data(entity,MediaType.APPLICATION_JSON)
                );
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }).start();
        return sseEmitter ;
    }

}
