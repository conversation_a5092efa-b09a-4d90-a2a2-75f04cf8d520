package com.yeestor.work_order.model.http.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "Person", description = "人员信息")
public class Person {
    @ApiModelProperty("用户ID")
    private String id;

    @ApiModelProperty("用户名称")
    private String name;
}