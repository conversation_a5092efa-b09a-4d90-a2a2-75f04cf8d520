package com.yeestor.work_order.service.order;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.admin.api.UserFeignClient;
import com.yeestor.admin.model.UserDTO;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.entity.analysis.OrderFailAnalysisEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.exception.ErrorImportOrderException;
import com.yeestor.work_order.model.ci.FaeModel;
import com.yeestor.work_order.model.ci.OrderInfoModel;
import com.yeestor.work_order.model.http.req.order.FlashInfo;
import com.yeestor.work_order.model.http.req.order.OrderEnvConfirmReq;
import com.yeestor.work_order.model.http.req.order.OrderFlashConfirmV2Req;
import com.yeestor.work_order.model.http.resp.order.PlanDetailVO;
import com.yeestor.work_order.model.http.resp.order.WorkOrderDetailVO;
import com.yeestor.work_order.model.http.resp.order.WorkOrderItemVO;
import com.yeestor.work_order.model.rms.OrderCreateParams;
import com.yeestor.work_order.repository.*;
import com.yeestor.work_order.repository.analysis.FailAnalysisRepository;
import com.yeestor.work_order.repository.review.ReviewResultRepository;
import com.yeestor.work_order.service.device.RMSDeviceService;
import com.yeestor.work_order.service.job.ImportOrderJob;
import com.yeestor.work_order.service.job.JobService;
import com.yeestor.work_order.service.plan.TerminalService;
import com.yeestor.work_order.service.zentao.ZenTaoService;
import com.yeestor.work_order.utils.*;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.utils.TimeUtils;
import com.yeestor.zentao.model.resp.BugInfoResp;
import com.yeestor.zentao.model.resp.build.ZtBuildBugResp;
import com.yeestor.zentao.model.resp.build.ZtBuildResp;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.quartz.DateBuilder;
import org.quartz.JobDetail;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import reactor.util.function.Tuple2;

import javax.transaction.Transactional;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yeestor.work_order.entity.WorkOrderEntity.Status.*;
import static com.yeestor.work_order.utils.Const.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderService {

    private final Environment environment;
    private final WorkOrderRepository workOrderRepository;
    private final OrderFlashRepository orderFlashRepository;
    private final OrderPlanRepository planRepository;
    private final OrderDetailRepository orderDetailRepository;

    private final FailAnalysisRepository failAnalysisRepository;
    private final ReviewResultRepository reviewResultRepository;

    private final OrderBugRepository orderBugRepository;
    private final RedisTemplate<String, String> redisTemplate;

    private final FlashService flashService;
    private final PlanService planService;
    private final DeviceService deviceService;
    private final DingTalkUtils dingTalkUtils;

    private final NotificationService notificationService;
    private final RMSApis rmsApis;

    private final JobService jobService;

    private final UserFeignClient userFeignClient;

    private final ZenTaoService zenTaoService;
    private final PlanDeviceRepository planDeviceRepository;
    private final RMSDeviceService rmsDeviceService;

    @Setter(onMethod_ = { @Autowired,@Lazy} )
    private TerminalService terminalService;

    public OrderInfoModel fetchOrderInfoModel(String ciId, boolean isHistory) {

        RestTemplate restTemplate = new RestTemplate();
        String url = "/api/workOrder/buildVersion/" + (isHistory ? "newExport" : "export") + "/" + ciId;
        log.info("url:{}", url);


        OrderInfoModel infoModel;
        if (Arrays.asList(environment.getActiveProfiles()).contains("prod")) {
            infoModel = restTemplate.getForObject("http://ebuildin.yeestor.com:8789" + url, OrderInfoModel.class);
        } else {
            //http://172.22.30.141:8789/api/mptool/workOrder/newExport/7
            infoModel = restTemplate.getForObject("http://172.18.11.28:8789" + url, OrderInfoModel.class);
        }
        if (infoModel != null) {
            infoModel.setCiID(ciId);
        }
        return infoModel;
    }

    /**
     * 工单导入.
     *
     * @param params 导入的参数
     * @param body   请求的参数， 暂时使用hashmap，后期考虑使用Model
     */
    public void importOrder(
            Map<String, Object> params,
            Map<String, Object> body
    ) {
        HashMap<String, Object> datas = new HashMap<>();
        datas.put("params", params);
        datas.put("body", body);

        log.info("importOrder params : {} -- body:{}", params, body);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            redisTemplate.opsForList().rightPush("order", objectMapper.writeValueAsString(datas));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        String ciId = String.valueOf(Optional.ofNullable(params.get("ciId")).orElse(Optional.of(body.get("ciId")).get()));
        String testTaskLink = Optional.ofNullable(params.get("testTaskLink")).orElse(Optional.ofNullable(body.get("testTaskLink")).orElse("")).toString();
        String ztBuildId = Optional.ofNullable(params.get("buildId")).orElse(Optional.ofNullable(body.get("buildId")).orElse("")).toString();   // 获取禅道版本id信息
        boolean isHistory = Boolean.parseBoolean(Optional.ofNullable(params.get("isHistory")).orElse(Optional.ofNullable(body.get("isHistory")).orElse("false")).toString());

        OrderInfoModel infoModel = fetchOrderInfoModel(ciId, isHistory);

        if (infoModel == null) {
            log.error("ciId : {} -- infoModel is null", ciId);
            notificationService.sendErrorNotification("导入工单失败,获取到的版本数据为空,请检查版本:" + ciId + "的数据！");
            //TODO 通知管理员或者运维人员来关注这个测试单。
            throw new DataNotFoundException("CI Not found data- Id:" + ciId + "!");
        }
        String mpFilePath = infoModel.getMpFilePath();
        if (mpFilePath == null || mpFilePath.isEmpty()) {
            throw new ErrorImportOrderException("导入工单失败,mpFilePath数据为空,请检查版本:" + ciId + "的数据！");
        }
        infoModel.checkParams();

        // 创建一个异步任务来处理工单的导入
        String jobName = ImportOrderJob.buildJodName(ciId);
        JobDetail jobDetail = ImportOrderJob.buildJobDetail(jobName, ciId, testTaskLink, ztBuildId, isHistory);

        jobService.startJob(
                jobDetail,
                jobName,
                ImportOrderJob.GROUP_NAME,
                DateBuilder.futureDate(5, DateBuilder.IntervalUnit.SECOND)
        );

    }

    /**
     * 导入工单的处理方法
     * @param testTaskLink 测试单ID
     * @param ztBuildId  禅道版本id
     * @param infoModel  版本信息
     */
    public void importOrder(
            String testTaskLink,
            String ztBuildId,
            OrderInfoModel infoModel
    ) {
        String prefix = getOrderPrefix();

        // 获取禅道的project 信息, 以及获取禅道的对应的版本id
        String buildId = ztBuildId;
        String projectId = null;
        OrderInfoModel.ParamsModel paramsModel = infoModel.formatParams();

        // 如果禅道版本为空，则通过版本号名称去禅道匹配检索
        if (ztBuildId.isEmpty()) {
            String fullVersion = paramsModel.getFullVersion(infoModel.getMpFilePath());
            log.info("fullVersion: {}", fullVersion);
            String ztExecutionId = paramsModel.getExecutionId();
            // FIXME: 如果在新数据中存在 executionId 为空的情况, 并且从projectId中保存的的确是ProjectId ,那么这个地方就会出现问题
            if (ztExecutionId == null && paramsModel.getProjectId() != null) {
                ztExecutionId = paramsModel.getProjectId();
            }

            if (ztExecutionId != null && ztExecutionId.matches("\\d+")) {
                List<ZtBuildResp> builds = zenTaoService.fetchBuildsByExecutionId(ztExecutionId);

                ZtBuildResp buildInfo = builds.stream().filter(info -> info.getName().equals(fullVersion)).findFirst().orElse(null);
                if (buildInfo != null) {
                    log.info("buildInfo: {}", buildInfo);
                    buildId = String.valueOf(buildInfo.getId());
                    projectId = String.valueOf(buildInfo.getProject());
                    log.info("executionId: {}, projectId: {}, buildId: {}", ztExecutionId, projectId, buildId);
                }
            } else {
                log.warn("工单信息中的禅道执行(迭代)ID为空，或者不是数字，不进行禅道的同步-- ciId:{}", infoModel.getCiID());
            }
        }


        String buildPerson = dingTalkUtils.getPersonName(infoModel.getUserId());
        List<Tuple2<WorkOrderEntity, OrderDetailEntity>> entityList = infoModel.buildEntityList(testTaskLink, prefix);

        for (Tuple2<WorkOrderEntity, OrderDetailEntity> tuple2 : entityList) {
            WorkOrderEntity workOrderEntity = tuple2.getT1();
            OrderDetailEntity orderDetailEntity = tuple2.getT2();
            workOrderEntity.setBuildPerson(buildPerson);
            orderDetailEntity.setZentaoBuildId(buildId);
            orderDetailEntity.setZentaoProject(projectId);
            int count = countOrderByProduct(workOrderEntity.getProduct(), workOrderEntity.getSubProduct());
            workOrderEntity.setNo(String.format("%s%04d", workOrderEntity.getNo(), count + 1));

//            String fullVersion = workOrderEntity.getFullVersion();
//            String[] versions = fullVersion.split("\\(");
//            String version = versions[0].trim();
//            boolean isRelease = workOrderEntity.getVersionType().equals("Release");
//            boolean isGE = PRODUCT_GE.equals(workOrderEntity.getProduct());
//
//            List<WorkOrderEntity> alphaOrderEntityList = workOrderRepository.findByProductAndSubProductAndChipAndFlashAndVersionTypeAndFullVersionContaining(
//                    workOrderEntity.getProduct(),
//                    workOrderEntity.getSubProduct(),
//                    workOrderEntity.getChip(),
//                    workOrderEntity.getFlash(),
//                    "Alpha",
//                    version
//            );
//            if (isGE && alphaOrderEntityList.size() > 1) {
//                throw new ErrorImportOrderException("导入工单失败, 找到多个Alpha版本的工单！");
//            }

            // 如果查询到的工单信息只有一个alpha版本时，并且当前导入版本是release版本
//            if (isGE && isRelease && alphaOrderEntityList.size() == 1) {
//                WorkOrderEntity alphaOrderEntity = alphaOrderEntityList.get(0);
//                List<OrderFlashEntity> flashEntityList = flashService.fetchFlashListByOrderId(alphaOrderEntity.getId());
//                // 遍历alpha版本中的flash批次，如果在测试中，则更新复测信息，并发送工作通知
//                for (OrderFlashEntity flashEntity : flashEntityList) {
//                    if (flashEntity.getStatus().ordinal() <= OrderFlashEntity.Status.IN_PROGRESS.ordinal()) {
//                        flashEntity.setRetestAt(System.currentTimeMillis());
//                        flashEntity.setRetestBy(infoModel.getUserId());
//                        flashEntity.setRetestPerson(buildPerson);
//                        flashEntity.setHasTestConvert(true);
//                        flashEntity.setMarkVersion("AlphaToRelease");
//                        notificationService.updateFlashInteractiveMsg("版本转换，Flash复测中", workOrderEntity, flashEntity);
//                        orderFlashRepository.save(flashEntity);
//                    }
//                }
//                alphaOrderEntity.setConvertToolPath(workOrderEntity.getMpToolPath());
//                alphaOrderEntity.setVersionType("AlphaToRelease");
//                alphaOrderEntity.setConvertAt(System.currentTimeMillis());
//                alphaOrderEntity.setConvertBy(infoModel.getUserId());
//                alphaOrderEntity.setConvertPerson(buildPerson);
//                workOrderRepository.save(alphaOrderEntity);
//                // 更新工单信息
//                notificationService.updateOrderNotification("Alpha版本转为Release版本", workOrderEntity);
//                return;
//            }

            // 如果不是alpha转release版本，按照正常流程构建版本
            createWorkOrder(workOrderEntity, orderDetailEntity);
        }
    }

    /**
     * 创建工单
     * 对于每一个flash，都会创建一个工单。（虽然觉得不太合理，但是先这样吧 。）
     *
     * @param orderEntity  工单实体
     * @param detailEntity 工单详情实体
     */
    @Transactional(value = Transactional.TxType.SUPPORTS)
    public WorkOrderEntity createWorkOrder(WorkOrderEntity orderEntity, OrderDetailEntity detailEntity) {

        if (workOrderRepository.existsByNo(orderEntity.getNo())) {
            log.error("NO : {} -- workOrder is exists", orderEntity.getNo());
            // 已经有了的工单，暂时不处理。
            throw new DataNotFoundException("no:" + orderEntity.getNo() + " is exists!");
        }
        WorkOrderEntity result = workOrderRepository.save(orderEntity);
        LogUtils.setOrderTracePoint(result.getId(), "创建工单");

        detailEntity.setOrderId(result.getId());
        orderDetailRepository.save(detailEntity);

        String outTrackId = notificationService.sendOrderNotification("生成了新工单", orderEntity);
        result.setOutTrackId(outTrackId);

        if (PRODUCT_GE.equalsIgnoreCase((result.getProduct()))) {
            //  Alpha 下的Plan 列表。
            if (OrderInfoModel.VERSION_TYPE_ALPHA.equalsIgnoreCase(result.getVersionType())) {
                List<String> planList = Arrays.asList(detailEntity.getPlanList().split(";"));
                planService.assignAlphaPlan(result, planList);
            } else if (OrderInfoModel.VERSION_TYPE_RELEASE.equalsIgnoreCase(result.getVersionType())) {
                // 只在通用产品线下对release版本的工单进行预分配plan
                planService.preAssignPlan(result);
            }
        }

        WorkOrderEntity temp = workOrderRepository.save(result);
        LogUtils.clearTracePoint();
        return temp;
    }


    /**
     * 工单列表查询。
     *
     * @param product      产品线
     * @param subProduct   子产品
     * @param chip         主控
     * @param status       工单状态
     *                     <ol>
     *                         <li>待测试：传参status=0，对应原有的status=0、1、2、3；</li>
     *                         <li>测试中：传参status=1，对应原有的status=4，可能会有偏差；</li>
     *                         <li>测试完成：传参status=2，对应原有的status=5，可能会有偏差；</li>
     *                         <li>review：传参status=3，对应原有的status=6，可能会有偏差；</li>
     *                         <li>工单完成：传参status=4，对应原有的status=7；</li>
     *                         <li>撤销：传参status=5，对应原有的status=9；</li>
     *                      </ol
     * @param page         当前页，从1开始。
     * @param size         每页的数量。
     * @param direction    排序规则。
     * @param fieldName    排序字段。
     * @param fuzzyVersion 模糊查询版本字段 。
     * @param testUid      flash测试负责人
     * @param builderBy    版本构建人
     * @param flashName    flash名称
     * @return 每页的数据。
     */
    public Page<WorkOrderItemVO> fetchOrderList(
            String product,
            String subProduct,
            String chip,
            int status,
            int page,
            int size,
            String direction,
            String fieldName,
            String fuzzyVersion,
            String testUid,
            String builderBy,
            String flashName,
            Integer feature
    ) {
        HashMap<Integer, List<WorkOrderEntity.Status>> statusMap = new HashMap<>();
        statusMap.put(0, Arrays.asList(CREATED, CONFIRMED_FLASH));
        statusMap.put(1, Collections.singletonList(TESTING));
        statusMap.put(2, List.of(EVALUATING));
        statusMap.put(3, List.of(EVALUATING));
        statusMap.put(4, Collections.singletonList(COMPLETED));
        statusMap.put(5, Collections.singletonList(REVOKED));

        Sort sort = Sort.by("ascend".equals(direction) ? Sort.Direction.ASC : Sort.Direction.DESC, "priority", fieldName);
        // 如果 direction 为空，则不进行排序直接分页查询对应的数据
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        List<WorkOrderEntity.Status> statusList = statusMap.get(status);
        final List<WorkOrderEntity> top10Orders = new ArrayList<>();
        if (statusList.contains(CONFIRMED_FLASH) || statusList.contains(TESTING)) {
            top10Orders.addAll(findTop10OrderList(subProduct));
        }

        List<Integer> featureList = feature != null ? List.of(feature) : WorkOrderEntity.FEATURE_LIST;
        Page<WorkOrderEntity> pageList;
        if (!testUid.isEmpty() || status != 0 || !flashName.isEmpty()) {
            // 携带测试负责人查询工单
            List<Long> orderIdList = orderFlashRepository.findAllByFlashAndTestBy(testUid, flashName);
            pageList = workOrderRepository.findAllByProductAndSubProductAndChipContainingAndVersionContainingAndBuildByContainingAndStatusInAndIdInAndFeatureIn(
                    product,
                    subProduct,
                    chip,
                    fuzzyVersion,
                    builderBy,
                    statusMap.get(status),
                    orderIdList,
                    featureList,
                    pageable);
        } else {
            // 未进行测试负责人查询
            pageList = workOrderRepository.findAllByProductAndSubProductAndChipContainingAndVersionContainingAndBuildByContainingAndStatusInAndFeatureIn(
                    product,
                    subProduct,
                    chip,
                    fuzzyVersion,
                    builderBy,
                    statusMap.get(status),
                    featureList,
                    pageable);
        }
        log.info("pageList: {}", pageList);
        return pageList.map(e -> {
            AtomicInteger readyNum = new AtomicInteger();
            List<WorkOrderDetailVO.FlashInfoVO> flashInfoList = orderFlashRepository.findAllByOrderIdOrderByIdx(e.getId()).stream().map(item -> {
                WorkOrderDetailVO.FlashInfoVO flashInfo = WorkOrderDetailVO.FlashInfoVO.entityToVO(item);
                flashInfo.setReviewPass(reviewResultRepository.findFlashReviewResult(e.getId(), flashInfo.getFlash(), true));
                flashInfo.setQueueNum(planService.getQueuePlanCount(e.getId(), flashInfo.getFlash()));
                int planReady = 0;
                if (item.getStatus().ordinal() <= OrderFlashEntity.Status.IN_PROGRESS.ordinal()) {
                    planReady = planService.getReadyPlanCount(e.getId(), flashInfo.getFlash());
                }
                flashInfo.setReadyNum(planReady);
                flashInfo.setTestingNum(planService.getTestingPlanCount(e.getId(), flashInfo.getFlash()));
                flashInfo.setCompletedNum(planService.getCompletedPlanCount(e.getId(), flashInfo.getFlash()));
                readyNum.addAndGet(planReady);
                return flashInfo;
            }).collect(Collectors.toList());
            int indexInQueue = top10Orders.indexOf(e);
            WorkOrderItemVO vo = WorkOrderItemVO.convert(e, planRepository.countByOrderId(e.getId()));
            vo.setFlashInfoList(flashInfoList);
            // 判断 工单最少需要多少设备
            vo.setExpectNum(planService.fetchPlanDeviceCount(e.getId()));
            vo.setReadyNum(readyNum.get());
            vo.setOccupiedDeviceCount(deviceService.getOccupiedDeviceCount(e.getId()));
            // 获取报错的plan的数量
            vo.setErrorPlanCount(planService.getErrorPlanCount(e.getId()));
            if (indexInQueue != -1) {
                vo.setQueueIndex(indexInQueue + 1);
            }
            return vo;
        });
    }

    /**
     * 工单详情查询
     * 这里需要返回给前端的是更复杂的信息， 而不仅仅是工单信息。
     *
     * @param id 工单id
     * @return 工单的详情
     */
    public WorkOrderDetailVO fetchOrderInfo(long id) {
        WorkOrderEntity entity = workOrderRepository.findById(id).orElse(null);
        if (entity == null) {
            throw new DataNotFoundException(String.format("id为%d的工单不存在", id));
        }
        OrderDetailEntity detailEntity = orderDetailRepository.findById(id).orElse(null);
        assert detailEntity != null;
        List<OrderPlanEntity> planEntities = planRepository.findAllByOrderId(id);
        WorkOrderDetailVO workOrderDetailVO = WorkOrderDetailVO.convertToSimple(entity, detailEntity, planEntities);

        // 获取与改工单相关联的工单
        if (entity.getBuildId() != 0) {
            List<WorkOrderEntity> orderEntityList = workOrderRepository.findAllOrderByBuildId(entity.getBuildId(), id);
            workOrderDetailVO.setRelatedList(
                    orderEntityList.stream().map(WorkOrderDetailVO.OrderRelatedV0::entityToVO)
                            .collect(Collectors.toList())
            );
        }

        List<OrderBugEntity> orderBugList = orderBugRepository.findAllByOrderId(id);
        if (workOrderDetailVO.getBugLinks() != null) {
            workOrderDetailVO.getBugLinks().addAll(orderBugList.stream().map(OrderBugEntity::getBugLink).collect(Collectors.toList()));
        } else {
            workOrderDetailVO.setBugLinks(orderBugList.stream().map(OrderBugEntity::getBugLink).collect(Collectors.toList()));
        }
        workOrderDetailVO.setRequirement(detailEntity.getRequirement());

        // 如果此工单中已经包含了Flash 批次的信息的话，就需要额外的查询Flash 批次的信息。
        if (orderFlashRepository.countByOrderId(id) > 0) {
            List<OrderFlashEntity> flashEntities = orderFlashRepository.findAllByOrderIdOrderByIdx(id);
            List<WorkOrderDetailVO.FlashInfoVO> flashInfoList = flashEntities.stream()
                    .map(flashEntity -> {
                        WorkOrderDetailVO.FlashInfoVO flashInfoVO = WorkOrderDetailVO.FlashInfoVO.entityToVO(flashEntity);
                        if (flashEntity.getFailAssignTo() != null && flashEntity.getFailAssignTo().length() > 0) {
                            UserDTO user = userFeignClient.findUserByDingTalkId(flashEntity.getFailAssignTo()).getData();
                            flashInfoVO.setFailAssignPerson(user.getJobNumber());
                        }
                        return flashInfoVO;
                    }).collect(Collectors.toList());
            workOrderDetailVO.setFlashInfoList(flashInfoList);

            // 如果已经有过Fail分析相关的信息的话，需要查询出来。
            List<OrderFailAnalysisEntity> failAnalysisList = failAnalysisRepository.findAllByOrderId(id);
            workOrderDetailVO.setFailAnalysisList(
                    failAnalysisList.stream()
                            .map(WorkOrderDetailVO.FailAnalysisVO::entityToVO)
                            .collect(Collectors.toList())
            );

            String subProduct = entity.getSubProduct();
            List<PlanDetailVO> planInfoList = planEntities.stream()
                    .map(p -> {
                        OrderFlashEntity flashEntity = flashEntities.stream().filter(f -> f.getFlash().equals(p.getFlash())).findFirst().orElse(null);
                        String planType = Optional.ofNullable(p.getPlanType()).orElse("");
                        if (planType.equals("terminal")) {
                            return terminalService.fetchTerminalPlanInfo(p);
                        } else {
                            return planService.fetchPlanInfo(subProduct, p, flashEntity);
                        }
                    })
                    .collect(Collectors.toList());
            workOrderDetailVO.setPlanList(planInfoList);

        }
        return workOrderDetailVO;
    }

    /**
     * 创建Flash信息
     *
     * @param info         前端传递的Flash信息
     * @param orderEntity  工单实体
     * @param detailEntity 工单详情信息
     */
    @Transactional
    public void handleFlashData(
            FlashInfo info,
            WorkOrderEntity orderEntity,
            OrderDetailEntity detailEntity
    ) {

        OrderFlashEntity flashEntity = info.buildFlashEntity(orderEntity.getId());
        String orderFlashNo = orderEntity.getNo() + "_" + info.getFlash();
        if (OrderInfoModel.VERSION_TYPE_ALPHA.equalsIgnoreCase(orderEntity.getVersionType())) {
            orderFlashNo = orderEntity.getNo() + "_" + OrderInfoModel.VERSION_TYPE_ALPHA + "_" + info.getFlash();
        }

        flashEntity.setMarkVersion(orderEntity.getVersionType());       // 记录导入工单时，创建版本的版本类型
        // 设置Flash批次先后顺序
        flashEntity.setIdx(orderFlashRepository.countByOrderId(orderEntity.getId()));
        flashEntity.setOrderFlashNo(orderFlashNo);
        orderFlashRepository.save(flashEntity);
        planService.buildFlashPlanList(orderEntity, flashEntity, info.getPlanList());

        // 通过钉钉机器人发送消息卡片（放在构建Plan之后，防止Plan数量为空的情况出现）
        notificationService.sendFlashInteractiveMsg("你有Flash批次待测试", orderEntity, flashEntity);

        log.info("在RMS中创建对应工单： {}！", flashEntity.getOrderFlashNo());
        rmsApis.createOrder(
                OrderCreateParams.builder()
                        .key_value(flashEntity.getOrderFlashNo())
                        .chip(orderEntity.getChip())
                        .product_type(orderEntity.getSubProduct())
                        .priority(orderEntity.getPriority())
                        .version_type(orderEntity.getVersionType())
                        .fw_svn(detailEntity.getFwSvnPath())
                        .fw_ver(detailEntity.getFwSvnVersion())
                        .mptool_path(detailEntity.getMpFilePath())
                        .mptool_ver(detailEntity.getMpToolVersion())
                        .cap(flashEntity.getSize())
                        .flash_type(orderEntity.getFlash())
                        .driver_ver(detailEntity.getDriverVersion())
                        .version_log(detailEntity.getVersionLog())
                        .test_point(detailEntity.getTestPoint())
                        .mail_list(detailEntity.getMailList())
                        .builder(orderEntity.getBuildPerson())
                        .date_type("test")
                        .build()
        );
    }

    /**
     * 确认工单的Flash信息
     * <p>
     * 由主管填写工单的信息。
     */
    @Transactional
    public void confirmOrderFlashInfoV2(WorkOrderEntity orderEntity, OrderFlashConfirmV2Req confirmReq, OAuthUserDetail userDetail) {
        // 获取工单信息 , 理论上认为这个方法只有从Controller 中才可能会被调用，所以不需要考虑null的问题。
        log.debug("confirmOrderFlashInfoV2 params : {}", confirmReq);
        OrderDetailEntity detailEntity = orderDetailRepository.findById(orderEntity.getId()).orElse(null);
        assert detailEntity != null;

        // 将工单状态更新为确认状态。
        orderEntity.setUpdatedAt(System.currentTimeMillis());
        orderEntity.setConfirmBy(userDetail.getUid());
        orderEntity.setConfirmPerson(userDetail.getUsername());

        confirmReq.applyData(orderEntity, detailEntity);

        orderEntity.setConfirmAt(System.currentTimeMillis());
        orderEntity.setStatus(CONFIRMED_FLASH);

        // 保存工单
        workOrderRepository.save(orderEntity);
        planRepository.deleteAllByOrderId(orderEntity.getId());
        // 创建Flash相关的信息。Flash 样片以及数量。（后期考虑从mms 中获取）
        for (FlashInfo info : confirmReq.getFlashInfoList()) {
            handleFlashData(info, orderEntity, detailEntity);
        }
        notificationService.updateOrderNotification("工单待测试", orderEntity);

    }

    /**
     * 确认工单中的Plan的环境信息
     *
     * @param envConfirmReq 环境的信息。主要是测试机的信息。
     * @param orderEntity   工单信息。
     */
    @Transactional
    public List<OrderPlanEntity> confirmOrderEnvInfo(OrderEnvConfirmReq envConfirmReq, WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {

        if (flashEntity.getStatus() == OrderFlashEntity.Status.NEW) {
            log.info("更新Flash批次: {} 的状态至：{}", flashEntity.getFlash(), OrderFlashEntity.Status.WAITING_FOR_START);
            flashEntity.setStatus(OrderFlashEntity.Status.WAITING_FOR_START);
            flashEntity.setUpdatedAt(System.currentTimeMillis());
            orderFlashRepository.save(flashEntity);
        }

        List<OrderEnvConfirmReq.PlanDeviceInfo> deviceInfoList = envConfirmReq.getPlanDeviceInfoList();
        List<Long> planIdList = deviceInfoList.stream().map(OrderEnvConfirmReq.PlanDeviceInfo::getPlanId).collect(Collectors.toList());

//        // 将plan 对应的设备展开成 一个所有设备的列表。
//        List<String> newDeviceMacList = deviceInfoList.stream()
//                .map(OrderEnvConfirmReq.PlanDeviceInfo::getMacList)
//                .flatMap(Collection::stream)
//                .collect(Collectors.toList());
//
//        List<String> deviceMacList = planDeviceRepository.findAllByPlanIdIn(planIdList).stream().map(PlanDeviceEntity::getMac).collect(Collectors.toList());
//
//        // 不存在预分配中的设备，需要加锁的设备
//        List<String> needLockMac = newDeviceMacList.stream()
//                .filter(element -> !deviceMacList.contains(element))
//                .collect(Collectors.toList());
//        log.info("新增需要检测的设备地址有: {}", needLockMac);
//        for(String mac : needLockMac){
//            rmsDeviceService.checkInfoBeforeLock(mac);
//        }

        List<OrderPlanEntity> planList = planRepository.findAllById(planIdList);

        boolean hasNotInReadyPlan = planList.stream().anyMatch(plan -> plan.getStatus() != OrderPlanEntity.Status.READY);
        if(hasNotInReadyPlan){
            throw new IllegalArgumentException("参数中存在状态不为待确认环境的Plan");
        }

        if (planList.size() != deviceInfoList.size()) {
            throw new IllegalArgumentException("参数中存在无效的Plan");
        }

        // 更新plan
        return planService.confirmEnv(
                orderEntity,
                flashEntity,
                planList,
                deviceInfoList
        );
    }

    /**
     * 撤销工单
     *
     * @param orderId      需要撤销的工单
     * @param revokeReason 撤销的原因。
     */
    @Transactional
    public void revokeOrder(long orderId, String revokeReason) {
        // 获取工单信息
        WorkOrderEntity orderEntity = workOrderRepository.findById(orderId).orElse(null);
        assert orderEntity != null;
        // 获取当前登录用户的钉钉ID
        String userDingTalkID = DingTalkUtils.getCurrentUserDingTalkID();
        String revokePerson = DingTalkUtils.getCurrentUserName();


        // 释放掉所有的已经被占用的机器。
        // 获取已经分配好机器 的 Flash批次。
        orderFlashRepository.findAllByOrderId(orderId)
                .forEach(orderFlashEntity -> {
                    // 将其中plan 用到的设备释放掉
                    flashService.releaseDevicesByFlash("撤销Plan", orderEntity, orderFlashEntity);
                    orderFlashEntity.setStatus(OrderFlashEntity.Status.CANCELLED);
                    orderFlashEntity.setRevokeAt(System.currentTimeMillis());
                    orderFlashEntity.setRevokeBy(userDingTalkID);
                    orderFlashEntity.setRevokeReason(revokeReason);

                    // 关闭RMS中的工单。
                    rmsApis.closeOrder(orderFlashEntity.getOrderFlashNo(), revokeReason);
                });

        orderEntity.setUpdatedAt(System.currentTimeMillis());
        orderEntity.setRevokeAt(System.currentTimeMillis());
        // 工单撤销也认为整个工单已经结束。
        orderEntity.setEndAt(System.currentTimeMillis());
        orderEntity.setRevokeBy(userDingTalkID);
        orderEntity.setRevokePerson(revokePerson);
        orderEntity.setRevokeReason(revokeReason);
        orderEntity.setStatus(WorkOrderEntity.Status.REVOKED);
        workOrderRepository.save(orderEntity);

        // 检查是否包含测试单信息，如果存在，则需要同步更新测试单状态
        OrderDetailEntity orderDetailEntity = findOrderDetailOrElseThrow(orderId);
        String taskId = orderDetailEntity.getZentaoTestTask();
        if (taskId != null && !taskId.isEmpty()) {
            // 如果存在测试单信息，则需要关闭禅道中测试单
            HashMap<String, String> params = new HashMap<>();
            params.put("comment", "<div style='line-height: 24px; font-weight: 600;'>工单 " + orderEntity.getNo() + " 已被撤销，测试单关闭！</div>");
            params.put("status", "done");
            zenTaoService.updateTestTask(taskId, params);
        }

        // 更新工单消息卡片
        notificationService.updateOrderNotification("工单已撤销", orderEntity);

    }

    public void reTestOrder(long oldOrderId) {
        WorkOrderEntity oldOrder = workOrderRepository.findById(oldOrderId).orElse(null);
        assert oldOrder != null;
        // 清空状态
        WorkOrderEntity newOrder = oldOrder.cloneData();
        String oldNo = oldOrder.getNo();
        int count = countOrderByProduct(oldOrder.getProduct(), oldOrder.getSubProduct());
        String no = oldNo.substring(0, oldNo.length() - 6) + DateTime.now().toString("yy") + String.format("%04d", count + 1);
        newOrder.setNo(no);
        newOrder.setBuildBy(DingTalkUtils.getCurrentUserDingTalkID());
        newOrder.setBuildPerson(DingTalkUtils.getCurrentUserName());

        OrderDetailEntity detailEntity = orderDetailRepository.findByOrderId(oldOrderId);
        // 将detailEntity拷贝到新的OrderDetailEntity中
        OrderDetailEntity newDetailEntity = new OrderDetailEntity();
        BeanUtils.copyProperties(detailEntity, newDetailEntity);

        createWorkOrder(newOrder, newDetailEntity);
    }

    /**
     * 更改工单的优先级。
     *
     * @param orderEntity 工单实体。
     * @param priority    更改后的优先级。
     */
    @Transactional
    public void changePriority(WorkOrderEntity orderEntity, int priority) {
        // 获取工单信息

        orderEntity.setUpdatedAt(System.currentTimeMillis());
        orderEntity.setPriority(priority);
        workOrderRepository.save(orderEntity);

    }


    public List<WorkOrderEntity> findTop10OrderList(String subProduct) {
        return workOrderRepository.fetchOrdersBySpecifyStatusPlanCount(
                subProduct,
                Arrays.asList(CONFIRMED_FLASH, TESTING),
                Arrays.asList(OrderPlanEntity.Status.QUEUE, OrderPlanEntity.Status.READY, OrderPlanEntity.Status.CONFIRMED),
                PageRequest.of(0, 15, Sort.by(Sort.Order.desc("priority"), Sort.Order.desc("createdAt")))
        ).getContent();

    }

    /**
     * 更新工单的状态至测试中,如果工单的状态已经超过了测试中的状态，则不会更新。
     *
     * @param orderEntity 工单实体。
     */
    @Transactional
    public void startOrder(WorkOrderEntity orderEntity) {

        // 只要有 工单有一个批次进入到 测试，则工单的状态变为 测试状态。
        if (orderEntity.getStatus().ordinal() < WorkOrderEntity.Status.TESTING.ordinal()) {

            log.info("更新工单状态至运行中,距离确认Flash时间为：{}", TimeUtils.getDistanceTime(orderEntity.getConfirmAt(), System.currentTimeMillis()));
            orderEntity.setUpdatedAt(System.currentTimeMillis());
            orderEntity.setStatus(WorkOrderEntity.Status.TESTING);
            orderEntity.setTestStartAt(System.currentTimeMillis());
            workOrderRepository.save(orderEntity);
            notificationService.updateOrderNotification("工单测试中", orderEntity);
            OrderDetailEntity detailEntity =  findOrderDetailOrElseThrow(orderEntity.getId());
            String taskId = detailEntity.getZentaoTestTask();
            if (taskId != null && !taskId.isEmpty()) {
                // 如果存在测试单信息，则需要将禅道中测试单改为运行状态
                HashMap<String, String> params = new HashMap<>();
                params.put("comment", "<div style='line-height: 24px; font-weight: 600;'>工单 " + orderEntity.getNo() + " 开始测试，测试单状态改为进行中！</div>");
                params.put("status", "doing");
                zenTaoService.updateTestTask(taskId, params);
            }

        }
    }

    /**
     * 更新 工单的状态至 等待Fail分析
     *
     * @param orderEntity 工单实体
     */
    @Transactional
    public void startEvaluateOrder(WorkOrderEntity orderEntity) {
        log.info("工单下的所有Flash批次都已经完成测试阶段，更新工单状态至评估中");

        orderEntity.setUpdatedAt(System.currentTimeMillis());
        orderEntity.setStatus(EVALUATING);
        orderEntity.setTestEndAt(System.currentTimeMillis());
        workOrderRepository.save(orderEntity);

        // 检查是否包含测试单信息，如果存在，则需要同步更新测试单状态
        OrderDetailEntity orderDetailEntity = findOrderDetailOrElseThrow(orderEntity.getId());
        String taskId = orderDetailEntity.getZentaoTestTask();
        if (taskId != null && !taskId.isEmpty()) {
            // 如果存在测试单信息，则需要关闭禅道中测试单
            HashMap<String, String> params = new HashMap<>();
            params.put("comment", "<div style='line-height: 24px; font-weight: 600;'>工单 " + orderEntity.getNo() + " 已测试完成，关闭测试单！</div>");
            params.put("status", "done");
            zenTaoService.updateTestTask(taskId, params);
        }

        // 如果是消费级eMMC产品，测试结束时需要更新禅道
        if("eMMC".equals(orderEntity.getSubProduct())){
            log.info("order no {} update zt bug!", orderEntity.getNo());
            updateOrderZenTao(orderEntity);
        }

        // 更新工单消息卡片
        notificationService.updateOrderNotification("工单评估中", orderEntity);
    }

    /**
     * 更新 工单的状态至 COMPLETED
     *
     * @param orderEntity 工单实体
     */
    @Transactional
    public void completeReview(
            WorkOrderEntity orderEntity
    ) {
        orderEntity.setUpdatedAt(System.currentTimeMillis());
        orderEntity.setEndAt(System.currentTimeMillis());
        orderEntity.setStatus(COMPLETED);
        log.info("工单下的所有Flash批次都已经完成Review,更新工单状态至Review完成.");
        workOrderRepository.save(orderEntity);
        // 更新工单消息卡片
        notificationService.updateOrderNotification("工单测试完成", orderEntity);
    }

    public String getOrderPrefix() {

        String prefix = "WO_";
        if (Arrays.asList(environment.getActiveProfiles()).contains("prod")) {
            prefix = "";
        } else if (Arrays.asList(environment.getActiveProfiles()).contains("alpha")) {
            prefix = "W0_";
        }
        return prefix;
    }


    /**
     * 根据工单ID获取工单实体, 如果不存在则抛出DataNotFoundException 。
     *
     * @param orderId 工单ID
     * @return 工单实体
     * @throws DataNotFoundException 如果工单不存在则抛出该异常
     */
    public WorkOrderEntity findOrderOrElseThrow(long orderId) {
        return workOrderRepository.findById(orderId).orElseThrow(() -> new DataNotFoundException("id为" + orderId + "的工单不存在！"));
    }

    /**
     * 根据工单号获取工单实体, 如果不存在则抛出DataNotFoundException 。
     *
     * @param orderNo 工单号
     * @return 工单实体
     * @throws DataNotFoundException 如果工单不存在则抛出该异常
     */
    public WorkOrderEntity findOrderOrElseThrow(String orderNo) {
        return workOrderRepository.findByNo(orderNo).orElseThrow(() -> new DataNotFoundException("no为" + orderNo + "的工单不存在！"));
    }

    public OrderDetailEntity findOrderDetailOrElseThrow(long orderId) {
        return orderDetailRepository.findById(orderId).orElseThrow(() -> new DataNotFoundException("id为" + orderId + "的工单详情不存在！"));
    }

    /**
     * 根据状态列表获取工单列表
     * @param subProduct 子产品
     * @param chip 主控
     * @param feature 工单类型
     * @param statusList 状态列表
     * @return
     */
    public List<WorkOrderEntity> findOrderBySubProductAndChipAndFeatureNotAndStatusList(String subProduct, String chip, int feature, List<WorkOrderEntity.Status> statusList) {
        return workOrderRepository.findAllBySubProductAndChipAndFeatureNotAndStatusIn(subProduct, chip, feature, statusList);
    }

    /**
     * 取消工单
     *
     * @param orderId      需要取消的工单
     * @param cancelReason 取消的原因。
     */
    @Transactional
    public void cancelOrder(long orderId, String cancelReason) {
        // 获取工单信息
        WorkOrderEntity orderEntity = workOrderRepository.findById(orderId).orElse(null);
        assert orderEntity != null;
        // 获取当前登录用户的钉钉ID
        String userDingTalkID = DingTalkUtils.getCurrentUserDingTalkID();
        String person = DingTalkUtils.getCurrentUserName();


        // 释放掉所有的已经被占用的机器。
        // 获取已经分配好机器 的 Flash批次。
        orderFlashRepository.findAllByOrderId(orderId)
                .forEach(orderFlashEntity -> {
                    // 调用Flash取消测试的方法
                    flashService.cancelFlash(orderEntity, orderFlashEntity, cancelReason);
                });

        orderEntity.setUpdatedAt(System.currentTimeMillis());
        // 工单取消也认为整个工单的plan测试已经结束。
        orderEntity.setTestEndAt(System.currentTimeMillis());
        orderEntity.setCancelBy(userDingTalkID);
        orderEntity.setCancelPerson(person);
        orderEntity.setCancelReason(cancelReason);
        orderEntity.setCancelAt(System.currentTimeMillis());

        orderEntity.setStatus(WorkOrderEntity.Status.EVALUATING);
        workOrderRepository.save(orderEntity);

        // 检查是否包含测试单信息，如果存在，则需要同步更新测试单状态
        OrderDetailEntity orderDetailEntity = findOrderDetailOrElseThrow(orderId);
        String taskId = orderDetailEntity.getZentaoTestTask();
        if (taskId != null && !taskId.isEmpty()) {
            // 如果存在测试单信息，则需要关闭禅道中测试单
            HashMap<String, String> params = new HashMap<>();
            params.put("comment", "<div style='line-height: 24px; font-weight: 600;'>工单 " + orderEntity.getNo() + " 已被取消，关闭测试单！</div>");
            params.put("status", "done");
            zenTaoService.updateTestTask(taskId, params);
        }
    }

    /**
     * 将orderEntity中的数据保存到数据库中。
     *
     * @param orderEntity 需要保存的工单实体
     */
    public void save(WorkOrderEntity orderEntity) {
        orderEntity.setUpdatedAt(System.currentTimeMillis());
        workOrderRepository.save(orderEntity);
    }

    /**
     * FAE导入工单，调用ebuild 中接口进行打包构建。
     *
     * @param ciId     构建版本 ciId
     * @param faeModel 构建需要的参数信息
     */
    public void createHistoryOrder(Long ciId, FaeModel faeModel, OAuthUserDetail userDetail) {
        faeModel.setBuilder(userDetail.getUid());
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();

        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpRequest = new HttpEntity(faeModel, headers);

        String url = "/api/workOrder/fae/" + ciId;
        if (Arrays.asList(environment.getActiveProfiles()).contains("prod")) {
            restTemplate.postForEntity("http://ebuildin.yeestor.com:8789" + url, httpRequest, String.class);
        } else {
            restTemplate.postForEntity("http://172.18.11.245:8789" + url, httpRequest, String.class);
        }
    }


    private int countOrderByProduct(String product, String subProduct) {
        return workOrderRepository.countByProductAndSubProductAndCreatedAtAfter(
                product,
                subProduct,
                DateTime.now().withDayOfYear(1).withTimeAtStartOfDay().getMillis()
        );

    }

    /**
     * 获取指定产品线和产品下的Flash批次信息
     *
     * @param product    产品
     * @param subProduct 产品线
     * @return 批次信息
     */
    public List<String> findAllFlashByProductAndSubProduct(String product, String subProduct) {
        return workOrderRepository.findAllFlashByProductAndBySubProduct(product, subProduct);
    }

    /**
     * 获取指定产品线和产品下的所有构建人
     *
     * @param product    产品
     * @param subProduct 产品线
     * @return 构建人信息
     */
    public List<HashMap<String, String>> findAllBuilderByProductAndSubProduct(String product, String subProduct) {
        List<WorkOrderEntity> orderEntityList = workOrderRepository.findAllByProductAndBySubProduct(product, subProduct);
        List<HashMap<String, String>> buildList = new ArrayList<>();
        List<String> userIdList = new ArrayList<>();
        orderEntityList.forEach(orderEntity -> {
            if (!userIdList.contains(orderEntity.getBuildBy())) {
                HashMap<String, String> builder = new HashMap<>();
                builder.put("userName", orderEntity.getBuildPerson());
                builder.put("userId", orderEntity.getBuildBy());
                buildList.add(builder);
                userIdList.add(orderEntity.getBuildBy());
            }
        });
        return buildList;
    }

    /**
     * 获取指定产品线和产品下的所有主控信息
     *
     * @param product    产品
     * @param subProduct 产品线
     * @return 主控列表
     */
    public List<String> findChipNameByProductAndSubProduct(String product, String subProduct) {
        List<String> chipList = workOrderRepository.findChipNameByProductAndBySubProduct(product, subProduct);
        return chipList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 更新禅道bug信息
     * @param orderEntity 工单实体类
     */
    public void updateOrderZenTao(WorkOrderEntity orderEntity) {
        OrderDetailEntity detailEntity = findOrderDetailOrElseThrow(orderEntity.getId());
        String buildId = detailEntity.getZentaoBuildId();
        if (buildId != null && !buildId.isEmpty()) {
            ZtBuildBugResp ztBuildResp = zenTaoService.fetchBuildInfoById(buildId);
            log.info("updateOrderZenTao ztBuildResp: {}", ztBuildResp);
            List<OrderInfoModel.BugLink> bugList = new ArrayList<>();
            // 版本测试产生的bug数量
            int generatedSize = ztBuildResp.getGeneratedBugs().size();
            int svNum = 0;
            int fvNum = 0;
            for (BugInfoResp bugInfo : ztBuildResp.getGeneratedBugs().values()) {
                if (bugInfo.getOs().equals("SV")) {
                    svNum++;
                }
                if (bugInfo.getOs().equals("FV")) {
                    fvNum++;
                }
            }

            // 此版本关联的bug
            for (BugInfoResp bugInfoResp : ztBuildResp.getBugs()) {
                OrderInfoModel.BugLink bug = new OrderInfoModel.BugLink();
                bug.setBugId(bugInfoResp.getId());
                bug.setSolved(null);
                bug.setTime(System.currentTimeMillis());
                bugList.add(bug);
            }
            String json = detailEntity.getCiJson();

            if (json != null) {
                ObjectMapper mapper = new ObjectMapper();
                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                try {
                    OrderInfoModel.ParamsModel model = mapper.readValue(json, OrderInfoModel.ParamsModel.class);
                    model.setBugLink(bugList.toArray(new OrderInfoModel.BugLink[0]));
                    detailEntity.setCiJson(mapper.writeValueAsString(model));
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }
            detailEntity.setGeneratedBugNum(generatedSize);
            detailEntity.setSvBugNum(svNum);
            detailEntity.setFvBugNum(fvNum);
            orderDetailRepository.save(detailEntity);
        }

    }

}
