package com.yeestor.work_order.service.review;

import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.entity.analysis.OrderFailAnalysisEntity;
import com.yeestor.work_order.entity.review.ReviewInfoEntity;
import com.yeestor.work_order.entity.review.ReviewItemEntity;
import com.yeestor.work_order.entity.review.ReviewResultEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.req.ReviewFinishParams;
import com.yeestor.work_order.model.http.req.ReviewStartRequestParams;
import com.yeestor.work_order.model.http.req.SaveFailAnalysisParams;
import com.yeestor.work_order.model.http.resp.review.ReviewInfo;
import com.yeestor.work_order.model.http.resp.review.ReviewVO;
import com.yeestor.work_order.model.http.resp.review.ReviewResult;
import com.yeestor.work_order.repository.analysis.FailAnalysisRepository;
import com.yeestor.work_order.repository.OrderBugRepository;
import com.yeestor.work_order.repository.review.ReviewInfoRepository;
import com.yeestor.work_order.repository.review.ReviewItemRepository;
import com.yeestor.work_order.repository.review.ReviewResultRepository;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.order.FlashService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReviewService {

    private final Environment environment;
    private final ReviewInfoRepository reviewInfoRepository ;

    private final ReviewResultRepository reviewResultRepository;

    private final ReviewItemRepository reviewItemRepository;

    private final ZenTaoCommentService zenTaoCommentService;

    private final FlashService flashService;

    private final FailAnalysisRepository failAnalysisRepository;

    private final NotificationService notificationService;

    private final OrderBugRepository orderBugRepository;

    public ReviewResultEntity findReviewInfoByOrderIdAndFlash(long orderId, String flashName){
        return reviewResultRepository.findFirstByOrderIdAndFlash(orderId, flashName)
                .orElseThrow(()-> new DataNotFoundException("id为" + orderId + "的工单不存在review结果 信息！"));
    }

    public ReviewInfoEntity findReviewMeetingByOrderIdAndFlash(long orderId, String flashName){
        return reviewInfoRepository.findFirstByOrderIdAndFlash(orderId, flashName).orElse(new ReviewInfoEntity());
    }

    /**
     * 查询fail分析记录信息
     * @param orderId 工单id
     * @param flashName flash批次
     * @return 记录信息
     */
    public OrderFailAnalysisEntity findAnalysisRecord(long orderId, String flashName){
       return failAnalysisRepository.findFirstByOrderIdAndFlash(orderId, flashName)
                .orElseThrow(()-> new DataNotFoundException("未找到fail分析记录！"));
    }

    /**
     * 获取review check信息表
     * @param orderId 工单id
     * @param flashName flash批次
     * @return
     */
    public List<ReviewItemEntity> findReviewItem(long orderId, String flashName){
        return reviewItemRepository.findAllByOrderIdAndFlash(orderId, flashName);
    }

    public ReviewVO fetchInfo(long orderId, String flash){

        ReviewVO info = new ReviewVO();
        ReviewInfoEntity reviewInfoEntity = findReviewMeetingByOrderIdAndFlash(orderId, flash);
        if(reviewInfoEntity.getId() != 0){
            info.setMeetingInfo(ReviewInfo.entityToVO(reviewInfoEntity));
        }

        List<ReviewResult> list = reviewResultRepository.findAllByOrderIdAndFlashOrderByIdDesc(orderId, flash).stream().map(reviewResultEntity->{
            List<ReviewItemEntity> reviewItem = reviewItemRepository.findAllByResultIdAndFlash(reviewResultEntity.getId(), flash);
            return ReviewResult.entityToVo(reviewResultEntity, reviewItem);
        }).collect(Collectors.toList());

        info.setMeetingResult(list);
        return info;
    }

    /**
     * 增加review会议人员
     * @param orderId 工单ID
     * @param flash flash名称
     * @param personList 人员列表
     */
    public void changePersonList(long orderId, String flash, String personList){
        ReviewInfoEntity reviewInfoEntity = reviewInfoRepository.findFirstByOrderIdAndFlash(orderId, flash)
                .orElseThrow(()-> new DataNotFoundException("id为"+orderId+"的工单不存在review 信息！"));
        reviewInfoEntity.setPersonList(personList);
        reviewInfoRepository.save(reviewInfoEntity);
    }

    /**
     * 完成fail分析， 包括跳过和发起fail分析
     * @param orderDetailEntity 工单构建信息
     * @param orderEntity 工单数据
     * @param flashEntity flashEntity
     * @param params 输入内容
     * @param userDetail 执行人
     * @param operate 操作，跳过或发起fail分析
     */
    public void finishFail(OrderDetailEntity orderDetailEntity, WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, SaveFailAnalysisParams params, OAuthUserDetail userDetail, String operate){
        // 判断用户是否是指定的研发人员，只有指定的研发人员才能完成该工单的fail 分析。
        OrderFailAnalysisEntity failAnalysisEntity = params.toEntity();
        failAnalysisEntity.setOrderId(orderEntity.getId());
        failAnalysisEntity.setFlash(flashEntity.getFlash());
        failAnalysisEntity.setCreatedBy(userDetail.getUid());
        failAnalysisEntity.setCreatedPerson(userDetail.getUsername());
        failAnalysisRepository.save(failAnalysisEntity) ;

        flashService.completeFailAnalysis(flashEntity);
        log.info("用户{}选择了{}, 参数为: {}",userDetail.getUsername(), operate ,params);

        // 给测试负责人发送通知。
        notificationService.sendFailAnalysisFinishNotification(orderEntity, flashEntity);

        /*
        * 不管此处是跳过fail分析或者完成Fail分析都需要对相应的测试单或者bug进行填写
        **/
        // 只在正式环境中填写禅道
        if(Arrays.asList(environment.getActiveProfiles()).contains("prod") && orderEntity.getFeature() != WorkOrderEntity.FEATURE_VERIFY_FLASH){
            List<OrderBugEntity> bindList = orderBugRepository.findAllByOrderIdAndFlash(orderDetailEntity.getOrderId(), flashEntity.getFlash());
            zenTaoCommentService.saveZenTaoFailComment(orderDetailEntity, orderEntity,flashEntity, failAnalysisEntity, bindList);
        }
    }

    /**
     * 发起review方法
     * @param orderEntity orderEntity
     * @param flashEntity flashEntity
     * @param params 请求参数
     * @param userDetail 执行人
     */
    public void startReview(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, ReviewStartRequestParams params, OAuthUserDetail userDetail){
        // 发起review。
        // 创建ReviewInfoEntity ,用于保存发起Review时的相关信息.
        ReviewInfoEntity reviewInfoEntity = params.toInfoEntity();
        reviewInfoEntity.setOrderId(orderEntity.getId());
        reviewInfoEntity.setFlash(flashEntity.getFlash());
        reviewInfoEntity.setCreatedBy(userDetail.getUid());
        reviewInfoEntity.setCreatedPerson(userDetail.getUsername());
        reviewInfoRepository.save(reviewInfoEntity);
        flashService.startReview(orderEntity, flashEntity);
    }

    /**
     * 不发起 review 直接跳过
     * @param orderDetailEntity 工单构建信息
     * @param orderEntity orderEntity
     * @param flashEntity flashEntity
     * @param params 请求参数
     * @param userDetail 执行人
     */
    public void skipReview(OrderDetailEntity orderDetailEntity, WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, ReviewStartRequestParams params, OAuthUserDetail userDetail){
        // 创建ReviewResultEntity ,用于保存跳过Review时的相关信息.
        ReviewResultEntity reviewResultEntity = params.toResultEntity();
        reviewResultEntity.setOrderId(orderEntity.getId());
        reviewResultEntity.setFlash(flashEntity.getFlash());
        reviewResultEntity.setCreatedBy(userDetail.getUid());
        reviewResultEntity.setVersionType(orderEntity.getVersionType());        // 此处新增review时的工单版本信息，alpha或者release版本
        reviewResultEntity.setCreatedPerson(userDetail.getUsername());
        reviewResultRepository.save(reviewResultEntity);
        // 不管是发起review还是跳过review, 这个对Flash批次的处理都是一样的,不同的是在ReviewResultEntity上.
        flashService.completeReview(orderEntity, flashEntity, reviewResultEntity.getResult());
        // 禅道填写评论
        if(Arrays.asList(environment.getActiveProfiles()).contains("prod") && orderEntity.getFeature() != WorkOrderEntity.FEATURE_VERIFY_FLASH){
            List<OrderBugEntity> bindList = orderBugRepository.findAllByOrderIdAndFlash(orderEntity.getId(), flashEntity.getFlash());
            zenTaoCommentService.saveSkipReviewComment(orderDetailEntity, orderEntity, flashEntity.getFlash(), reviewResultEntity, bindList);
        }
    }

    /**
     * 完成review
     * @param orderDetailEntity 工单构建信息
     * @param orderEntity orderEntity
     * @param flashEntity flashEntity
     * @param params 请求参数
     * @param userDetail 执行人
     */
    public void finishReview(OrderDetailEntity orderDetailEntity, WorkOrderEntity orderEntity, OrderFlashEntity flashEntity, ReviewFinishParams params, OAuthUserDetail userDetail){
        long orderId = orderEntity.getId();
        String flash = flashEntity.getFlash();

        // 创建ReviewResultEntity ,用于保存完成Review时的相关信息.
        ReviewResultEntity reviewResultEntity = params.toEntity();
        reviewResultEntity.setOrderId(orderId);
        reviewResultEntity.setFlash(flash);
        reviewResultEntity.setCreatedBy(userDetail.getUid());
        reviewResultEntity.setCreatedPerson(userDetail.getUsername());
        reviewResultEntity.setVersionType(flashEntity.getMarkVersion());        // 此处新增review时的工单版本信息，alpha或者release版本
        ReviewResultEntity result = reviewResultRepository.save(reviewResultEntity);

        // 创建 Review对应的Item信息.
        List<ReviewItemEntity> reviewItemList = params.getCheckList().stream()
                .map(i -> {
                    ReviewItemEntity item = i.toEntity();
                    item.setOrderId(orderId);
                    item.setFlash(flash);
                    item.setResultId(result.getId());
                    return item;
                }).collect(Collectors.toList());
        reviewItemRepository.saveAll(reviewItemList);

        // 如果当前不是review状态，则不更新Flash批次状态
        if(flashEntity.getStatus() == OrderFlashEntity.Status.REVIEW_STARTED){
            flashService.completeReview(orderEntity, flashEntity, reviewResultEntity.getResult());
        }

        // 禅道填写评论
        if(Arrays.asList(environment.getActiveProfiles()).contains("prod") && orderEntity.getFeature() != WorkOrderEntity.FEATURE_VERIFY_FLASH){
            List<OrderBugEntity> bindList = orderBugRepository.findAllByOrderIdAndFlash(orderId, flash);
            zenTaoCommentService.saveReviewComment(orderDetailEntity, orderEntity, flashEntity, reviewResultEntity, reviewItemList, bindList);
        }
    }

}
