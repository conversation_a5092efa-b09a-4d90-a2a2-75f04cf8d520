package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "tall-info-testNo", description = "通过测试工单号获取工单批次容量信息、CE数")
public class OrderInfo {
    @ApiModelProperty(value = "工单号")
    private String no;

    @ApiModelProperty(value = "版本类型")
    private String verType;

    @ApiModelProperty(value = "构建人")
    private String builder;

    @ApiModelProperty(value = "批次信息")
    private List<FlashModelInfo> noLst;

    @Data
    public static class FlashModelInfo {
        @ApiModelProperty(value = "eRepost 测试单号")
        private String testNo;

        @ApiModelProperty(value = "ce 数")
        private String ceCnt;

        @ApiModelProperty(value = "容量")
        private String cap;
    }
}
