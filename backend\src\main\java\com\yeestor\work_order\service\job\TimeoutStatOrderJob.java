package com.yeestor.work_order.service.job;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.order.OrderStatInfoEntity;
import com.yeestor.work_order.entity.review.ReviewResultEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.repository.order.OrderStatInfoRepository;
import com.yeestor.work_order.repository.review.ReviewResultRepository;
import com.yeestor.work_order.service.SendEmailService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.utils.PathUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.quartz.JobBuilder.newJob;


/**
 * 每周定时统计当前工单测试信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TimeoutStatOrderJob implements Job {
    public static final String GROUP_NAME = "TimeoutStatOrderJob";

    private final OrderService orderService;
    private final OrderFlashRepository flashRepository;
    private final ReviewResultRepository reviewResultRepository;
    private final WorkOrderRepository workOrderRepository;
    private final SendEmailService sendEmailService;
    private final OrderStatInfoRepository orderStatInfoRepository;
    private final PathUtils pathUtils;

    // 尚未开始测试的工单状态
    public static final List<WorkOrderEntity.Status> BEFORE_TEST_STATUS = List.of(WorkOrderEntity.Status.CREATED, WorkOrderEntity.Status.CONFIRMED_FLASH);

    // 已经结束的工单状态
    public static final List<WorkOrderEntity.Status> FINISH_STATUS = List.of(WorkOrderEntity.Status.COMPLETED, WorkOrderEntity.Status.REVOKED);

    public static JobDetail buildJobDetail() {
        return newJob(TimeoutStatOrderJob.class)
                .withIdentity(GROUP_NAME, GROUP_NAME)
                .build();
    }

    /**
     * 获取工单列表id
     *
     * @param orderEntityList 工单列表
     * @return ids
     */
    public List<Long> fetchOrderIds(List<WorkOrderEntity> orderEntityList) {
        return orderEntityList.stream().map(WorkOrderEntity::getId).collect(Collectors.toList());
    }

    /**
     * 获取当前时间年月日
     *
     * @param timestamp 时间戳
     * @return yyyy/MM/dd
     */
    public String formatTime(long timestamp) {
        // 将时间戳转换为 Instant
        Instant instant = Instant.ofEpochSecond(timestamp / 1000);

        // 根据系统默认时区将 Instant 转换为 LocalDate
        LocalDate localDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

        // 格式化日期
        return localDate.format(formatter);
    }

    public String fetchVersionName(String subProduct, String fullVersion) {
        switch (subProduct) {
            case "SD":
                Pattern pattern = Pattern.compile("([^\\s\\(]+)_(\\d{8})");
                Matcher matcher = pattern.matcher(fullVersion);
                if (matcher.find()) {
                    return matcher.group(1) + "_" + matcher.group(2);
                }
                throw new DataNotFoundException("版本信息异常，请检查！");
            default:
                return fullVersion;
        }
    }

    /**
     * 检查所有在测试中、fail分析、review以及结束的Flash批次中的review信息【包括中期评审】
     *
     * @param flashList flash批次
     * @param passNum   数量集合，第一个数是review数量总和，第二个是review通过的数量
     * @return 版本对应批次信息
     */
    public HashMap<String, List<FlashModel>> fetchReviewPassFlash(List<OrderFlashEntity> flashList, int[] passNum) {
        HashMap<String, List<FlashModel>> versionMap = new HashMap<>();

        for (OrderFlashEntity flashEntity : flashList) {
            Optional<ReviewResultEntity> optional = reviewResultRepository.findFirstByOrderIdAndFlashOrderByIdDesc(flashEntity.getOrderId(), flashEntity.getFlash());
            optional.ifPresent(review -> {
                log.info("review: {}", review);
                // 发起评审算一次评审、未发起评审但是结果通过也算一次
                if (review.getType() == 0 || (review.getResult() && review.getType() == 1)) {
                    passNum[0] = passNum[0] + 1;    // 评审次数
                }
                if (review.getResult()) {
                    passNum[1] = passNum[1] + 1;        // 评审通过数量
                    WorkOrderEntity workOrderEntity = orderService.findOrderOrElseThrow(flashEntity.getOrderId());
                    String product = workOrderEntity.getProduct();
                    String subProduct = workOrderEntity.getSubProduct();
                    long id = workOrderEntity.getId();
                    String flashName = flashEntity.getFlash();
                    FlashModel flashModel = new FlashModel();
                    flashModel.setFlashName(flashName);
                    flashModel.setCompleted(flashEntity.getStatus() == OrderFlashEntity.Status.COMPLETED);
                    if (review.getRemark() != null) {
                        flashModel.setRemark(review.getRemark());
                    }
                    if (review.getConclusion() != null) {
                        flashModel.setRemark(review.getConclusion());
                    }

                    flashModel.setStatusName(flashEntity.getStatus().getDesc());
                    flashModel.setUrl(pathUtils.getFlashPageUrl(product, subProduct, id, flashEntity.getFlash()));

                    String version = fetchVersionName(subProduct, workOrderEntity.getFullVersion());
                    List<FlashModel> modelList = new ArrayList<>();

                    if (versionMap.containsKey(version)) {
                        modelList = versionMap.get(version);
                    }
                    modelList.add(flashModel);
                    versionMap.put(version, modelList);
                }
            });
        }
        return versionMap;
    }

    /**
     * 根据当前版本下批次信息，组合生成模板
     *
     * @param versionMap 版本批次信息
     * @param chipIndex 主控下标
     * @return HTML模板
     */
    public String fetchPassListModel(HashMap<String, List<FlashModel>> versionMap, int chipIndex) {
        StringBuilder chipText = new StringBuilder();
        int index = 1;
        for (Map.Entry<String, List<FlashModel>> entry : versionMap.entrySet()) {
            String key = entry.getKey();
            List<FlashModel> modelList = entry.getValue();

            StringBuilder versionText = new StringBuilder();
            for (FlashModel flashModel : modelList) {
                String template = String.format(
                        "<div style='line-height: 22px; font-size: 14px'>Flash批次: %s</div>" +
                                "%s" +
                                "<div style='line-height: 22px; font-size: 14px; margin-bottom: 12px'>评审记录: %s</div>",
                        "<a target='_blank' href='" + flashModel.getUrl() + "'>" + flashModel.getFlashName() + "</a>",
                        flashModel.isCompleted() ? "" : "<div style='line-height: 22px; font-size: 14px'>Flash状态: " + flashModel.getStatusName() + "</div>",
                        flashModel.getRemark()
                );
                versionText.append(template);
            }

            String versionTemplate = String.format(
                    "<div>%s.%s、版本信息: %s </div>" +
                            "<div style='padding: 8px 24px 0 24px'>%s</div>",
                    chipIndex, index, key,
                    versionText.toString()
            );
            chipText.append(versionTemplate);
            index++;
        }

        return chipText.toString();
    }

    /**
     * 通过工单id查询Flash批次信息
     *
     * @param orderIds 工单id
     * @return 批次信息
     */
    public List<OrderFlashEntity> findFlashByOrderIds(List<Long> orderIds) {
        return flashRepository.findByOrderIdIn(orderIds);
    }

    /**
     * 获取Flash批次中Alpha版本和release版本的数量
     * @param flashEntityList flash实体类列表
     * @param versionNum 版本分布情况
     */
    public void findFlashVersion(List<OrderFlashEntity> flashEntityList, int[] versionNum) {
        for (OrderFlashEntity flashEntity : flashEntityList) {
            if (flashEntity.getOrderFlashNo().toLowerCase().contains("alpha")) {
                versionNum[0] = versionNum[0] + 1;
            } else {
                versionNum[1] = versionNum[1] + 1;
            }
        }
    }

    public String getVersionText(int[] versionNum) {
        if (versionNum[0] == 0 && versionNum[1] == 0) {
            return "";
        }

        if (versionNum[0] == 0) {
            return "（" + versionNum[1] + "个Release）";
        }
        if (versionNum[1] == 0) {
            return "（" + versionNum[0] + "个Alpha）";
        }
        return "（" + versionNum[0] + "个Alpha+" + versionNum[1] + "个Release）";
    }

    public void runJob(long triggerTime) {
        List<OrderStatInfoEntity> statList = orderStatInfoRepository.findAll();
        for (OrderStatInfoEntity statInfo : statList) {

            // 此前五天时间，因为发邮件的时间是周五下午六点 【这个时间以设定的为准，追溯到多少小时前】
            long startTime = triggerTime - 1000 * 60 * 60 * statInfo.getHours();
            String product = statInfo.getProduct();
            String subProduct = statInfo.getSubProduct();

            String title = statInfo.getTitle() + "(" + formatTime(startTime) + "-" + formatTime(triggerTime) + ")";
            List<String> userList = Arrays.asList(statInfo.getCcMail().split(";"));
            String toMail = statInfo.getToEmail();
            log.info("product: {} subProduct: {}", product, subProduct);
            List<String> chipList = orderService.findChipNameByProductAndSubProduct(product, subProduct);

            int index = 1;
            StringBuilder text = new StringBuilder("<div>Dear  all：</div><p style='padding: 0 24px'>以下为日期 "
                    + formatTime(startTime) + " - " + formatTime(triggerTime) + " "
                    + statInfo.getDescription()
                    + "</p><div style='padding: 0 24px'>");

            for (String chip : chipList) {
                int[] passNum = {0, 0};
                log.info("chip: {}", chip);
                List<WorkOrderEntity> beforeTestOrderList = orderService.findOrderBySubProductAndChipAndFeatureNotAndStatusList(subProduct, chip, 32, BEFORE_TEST_STATUS);
                List<WorkOrderEntity> testingOrderList = orderService.findOrderBySubProductAndChipAndFeatureNotAndStatusList(subProduct, chip, 32, List.of(WorkOrderEntity.Status.TESTING));
                List<WorkOrderEntity> evaluatingOrderList = orderService.findOrderBySubProductAndChipAndFeatureNotAndStatusList(subProduct, chip, 32, List.of(WorkOrderEntity.Status.EVALUATING));
                List<WorkOrderEntity> finishOrderList = workOrderRepository.findBySubProductAndChipAndFeatureNotAndStatusInAndEndAt(subProduct, chip, 32, FINISH_STATUS, startTime);
                log.info("testingOrderList: {}", testingOrderList.stream().map(WorkOrderEntity::getId).collect(Collectors.toList()));
                log.info("evaluatingOrderList: {}", evaluatingOrderList.stream().map(WorkOrderEntity::getId).collect(Collectors.toList()));
                log.info("finishOrderList: {}", finishOrderList.stream().map(WorkOrderEntity::getId).collect(Collectors.toList()));

                List<WorkOrderEntity> orderList = new ArrayList<>();
                orderList.addAll(beforeTestOrderList);
                orderList.addAll(testingOrderList);
                orderList.addAll(evaluatingOrderList);
                orderList.addAll(finishOrderList);
                List<String> versionList = orderList.stream().map(order -> fetchVersionName(order.getSubProduct(), order.getFullVersion())).distinct().collect(Collectors.toList());
                log.info("versionList:{}", versionList);

                // 测试中的批次
                List<OrderFlashEntity> waitingFlashList = findFlashByOrderIds(fetchOrderIds(beforeTestOrderList));

                // 测试中的批次
                List<OrderFlashEntity> testingFlashList = findFlashByOrderIds(fetchOrderIds(testingOrderList));
                int[] testingVersionNum = {0, 0};
                findFlashVersion(testingFlashList, testingVersionNum);
                String testingText = getVersionText(testingVersionNum);

                // 测试完成的批次数量
                List<OrderFlashEntity> testEndFlashList = findFlashByOrderIds(fetchOrderIds(evaluatingOrderList));
                int[] testEndVersionNum = {0, 0};
                findFlashVersion(testEndFlashList, testEndVersionNum);
                String testEndText = getVersionText(testEndVersionNum);

                // 已结束的Flash批次
                List<OrderFlashEntity> endFlashList = findFlashByOrderIds(fetchOrderIds(finishOrderList));
                int[] endVersionNum = {0, 0};
                findFlashVersion(endFlashList, endVersionNum);
                String endText = getVersionText(endVersionNum);

                int waitingNum = waitingFlashList.size();
                int testingNum = testingFlashList.size();
                int testEndNum = testEndFlashList.size();
                int endNum = endFlashList.size();
                List<OrderFlashEntity> mergedList = Stream.concat(testingFlashList.stream(), testEndFlashList.stream()).collect(Collectors.toList());
                mergedList.addAll(endFlashList);

                HashMap<String, List<FlashModel>> versionMap = fetchReviewPassFlash(mergedList, passNum);
                log.info("versionMap: {} passNum: {}", versionMap, passNum);
                String chipText = fetchPassListModel(versionMap, index);

                String template = String.format(
                        "<div style='line-height: 42px; font-size: 20px; font-weight: 600; margin-bottom: 5px;'>%s 项目</div> " +
                                "<div style='margin-bottom: 4px; font-weight: 600; line-height: 24px'>共接收%s个版本【%s个测试工单，其中%s个工单测试中%s，%s个工单评估中%s，%s个工单测试完成%s】</div>" +
                                "<div style='margin: 6px 0 12px 0; line-height: 24px'>评审工单%s个，通过评审工单%s个%s" +
                                "<div style='padding: 0 16px'>%s</div>",
                        index + "、" + chip,
                        versionList.size(), waitingNum + testingNum + testEndNum + endNum, testingNum, testingText,  testEndNum, testEndText, endNum, endText,
                        passNum[0], passNum[1], passNum[1] > 0 ? "，以下是" + chip + "项目本周版本结论 </div>" : "</div>",
                        "<div style='font-size: 14px; margin: 0 0 32px 0'>" + chipText + "</div>"
                );

                text.append(template);
                index++;
            }
            text.append("</div>");
            log.info("text: {}", text.toString());
            sendEmailService.sendHtmlMail(title, text.toString(), true, toMail, userList);
        }
    }

    @Override
    public void execute(JobExecutionContext context) {
        runJob(System.currentTimeMillis());
    }
}


@Data
class FlashModel {
    public String flashName;
    public String url;
    public boolean isCompleted;
    public String statusName;
    public String remark;
}
