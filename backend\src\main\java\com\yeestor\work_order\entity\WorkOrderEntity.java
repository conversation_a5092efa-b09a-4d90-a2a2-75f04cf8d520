package com.yeestor.work_order.entity;


import com.yeestor.entity.BaseEntity;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity(name = "WorkOrder")
@Table(name = "wo_order")
public class WorkOrderEntity  extends BaseEntity {

    /**
     * 工单的状态 ,虽然使用文本的形式存储，但是顺序也同样重要。
     * 如果需要新增状态，不能都放在最后面添加，否则会导致状态的顺序不正确。
     */
    public enum Status {

        /**
         * 工单刚刚创建，等待测试主管确认信息。
         */
        CREATED,

        /**
         * 测试主管已经确认了Flash信息，目前正在排队中。
         */
        CONFIRMED_FLASH,


        /**
         * 工单已经开始测试。
         */
        TESTING,

        /**
         * 工单评估中。
         */
        EVALUATING,

        /**
         * 测试完成。
         */
        COMPLETED ,

        /**
         * 工单已经被取消。
         */
        REVOKED;

        public String getDisplayName(){
            switch (this){
                case CREATED:
                    return "创建中";
                case CONFIRMED_FLASH:
                    return "待测试";
                case TESTING:
                    return "测试中";
                case EVALUATING:
                    return "评估中";
                case COMPLETED:
                    return "已完成";
                case REVOKED:
                    return "已取消";
                default:
                    return "未知";
            }
        }

    }

    /**
     * 工单的特性,通过位运算来判断是否具有某个特性。
     */
    public static final int FEATURE_VERIFY_FLASH = 1;

    /**
     * FAE 工单
     */
    public static final int FEATURE_FAE = 2;

    /**
     * 内部测试工单
     */
    public static final int FEATURE_INNER_TEST = 4;

    /**
     * MPTOOL LLF 工单。
     */
    public static final int FEATURE_LLF = 8 ;

    /**
     * Dev 版本
     */
    public static final int FEATURE_DEV = 16;

    /**
     * 竞品测试，目前仅SD支持
     */
    public static final int FEATURE_RIVAL = 32;

    public static final List<Integer> FEATURE_LIST = List.of(0, FEATURE_VERIFY_FLASH, FEATURE_FAE, FEATURE_INNER_TEST, FEATURE_LLF, FEATURE_DEV, FEATURE_RIVAL);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "parent_id")
    private Long parentId ;

    /**
     * 工单号。工单号也应该是唯一值。
     */
    @Column(name = "no",unique = true)
    private String no ;

    /**
     * CI 版本构建的历史ID。
     */
    @Column(name = "build_id")
    private long buildId ;

    /**
     * 产品线 ,与CI中的产品线不一致。
     * 目前只有通用产品线 GE
     */
    @Column(name = "product",length = 32)
    private String product ;

    /**
     * 产品。目前有 U2,U3,SD
     */
    @Column(name = "sub_product",length = 32)
    private String subProduct ;

    /**
     * 工单对应的Flash。 Flash ID，和Flash批次中没有强关联。
     */
    @Column(name = "flash")
    private String flash ;

    /**
     * 主控。
     */
    @Column(name = "chip",length = 100)
    private String chip ;

    @ColumnDefault("0")
    @Column(name = "feature")
    private int feature ;

    /**
     * 版本号
     */
    @Column(name = "version")
    private String version ;

    /**
     * 自动生成的版本号。
     */
    @Column(name = "full_version")
    private String fullVersion ;

    /**
     * 版本类型。 alpha， release， alphaToRelease
     */
    @Column(name = "version_type")
    private String versionType ;

    /**
     * 优先级
     */
    @Column(name = "priority")
    private int priority ;

    /**
     * 工单状态信息，主要用于查询数据。
     * @see Status
     */
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private Status status ;

    @Column(name="rms_group")
    private String rmsGroup ;

    /**
     * 版本构建人， 发起人 ,钉钉ID
     */
    @Column(name = "build_by")
    private String buildBy ;


    /**
     * 版本构建人， 发起人 , 名称（英文名）
     */
    @Column(name = "build_p")
    private String buildPerson ;

    /**
     * 信息确认人员， 钉钉ID
     */
    @Column(name = "confirm_by")
    private String confirmBy ;

    /**
     * 信息确认人员， 名称（英文名）
     */
    @Column(name = "confirm_p")
    private String confirmPerson ;

    /**
     * 撤销工单的人员， 钉钉ID
     */
    @Column(name = "revoke_by")
    private String revokeBy ;


    /**
     * 撤销工单的人员， 名称（英文名）
     */
    @Column(name = "revoke_p")
    private String revokePerson ;


    /**
     * 撤销工单的原因、
     */
    @Column(name = "revoke_reason")
    private String revokeReason ;

    /**
     * 版本构建的时间、
     */
    @Column(name = "start_at")
    private Long buildStartAt ;

    /**
     * 版本构建完成的时间、
     */
    @Column(name = "build_at")
    private Long buildEndAt ;

    /**
     * 信息确认完成时间, 也可以认为是配置完成时间。
     * 由测试主管配置样片数量，以及可能手动调整plan信息。发生在进入排队之前
     */
    @Column(name = "confirm_at")
    private Long confirmAt;


    /**
     * 开始测试时间。 所有批次都开始测试的时间
     */
    @Column(name = "test_start_at")
    private Long testStartAt ;

    /**
     * 测试结束时间。 所有批次都结束测试的时间
     */
    @Column(name = "test_end_at")
    private Long testEndAt ;

    /**
     * 整个工单结束的时间
     */
    @Column(name = "end_at")
    private Long endAt ;

    /**
     * 工单撤销时间。
     */
    @Column(name = "revoke_at")
    private Long revokeAt ;

    /**
     * 与 OrderDetailEntity 中mpToolPath 不同，这个是Jenkins打包后存放的路径
     */
    @Column(name ="mptool_path")
    private String mpToolPath;

    @Column(name = "mars_path")
    private String marsPath ;

    @Column(name = "plan_path")
    private String planPath ;

    @Column(name = "scrcpy_path")
    private String scrcpyPath;

    @Column(name = "scrcpy_plan")
    private String scrcpyPlan;

    @Column(name = "out_track_id")
    private String outTrackId ;

    /**
     * 取消工单的人员， 钉钉ID
     */
    @Column(name = "cancel_by")
    private String cancelBy ;


    /**
     * 取消工单的人员， 名称（英文名）
     */
    @Column(name = "cancel_p")
    private String cancelPerson ;


    /**
     * 取消工单的原因、
     */
    @Column(name = "cancel_reason")
    private String cancelReason ;

    /**
     * 取消工单的时间、
     */
    @Column(name = "cancel_at")
    private Long cancelAt ;

    /**
     * 版本转换时间
     */
    @Column(name = "convert_at")
    private Long convertAt;

    /**
     * 版本转换人员钉钉id
     */
    @Column(name = "convert_by")
    private String convertBy;

    /**
     * 版本转换人员的名称
     */
    @Column(name = "convert_p")
    private String convertPerson;

    /**
     * 转化后的版本路径
     */
    @Column(name = "convert_tool_path")
    private String convertToolPath;

    /**
     * review的结果，通过或者不通过。 review的详情需要另外保存。
     */
    @Column(name = "review_result")
    private Boolean reviewResult ;
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        if(!(o instanceof WorkOrderEntity)){
            return false;
        }
        WorkOrderEntity that = (WorkOrderEntity) o;
        return id != 0 && Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    public WorkOrderEntity cloneData() {
        WorkOrderEntity newOrder = new WorkOrderEntity();
        newOrder.parentId = id ;
        newOrder.buildId = buildId ;
        newOrder.product = product ;
        newOrder.subProduct = subProduct ;
        newOrder.flash = flash ;
        newOrder.chip = chip ;
        newOrder.version = version ;
        newOrder.fullVersion = fullVersion ;
        newOrder.versionType = versionType ;
        newOrder.priority = priority ;
        newOrder.status = Status.CREATED ;
        newOrder.buildBy = buildBy ;
        newOrder.buildPerson = buildPerson ;
        newOrder.buildStartAt = buildStartAt ;
        newOrder.buildEndAt = buildEndAt ;

        newOrder.mpToolPath = mpToolPath ;
        newOrder.marsPath = marsPath ;
        newOrder.planPath = planPath ;

        return newOrder ;
    }
    public String getRmsGroup() {
        return Optional.ofNullable(rmsGroup).orElse("0");
    }

    /**
     * 清除当前工单记录的状态信息
     */
    public void clearOrderStatus() {
        this.endAt = null;
        this.testEndAt = null;
        this.reviewResult = null;
    }
}
