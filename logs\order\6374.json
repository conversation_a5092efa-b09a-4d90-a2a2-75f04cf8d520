{"@timestamp":"2025-07-23T15:13:35.602+08:00","@version":"1","message":"execute:PCIe_D8-BB-C1-9D-AA-01_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113578 mac:D8-BB-C1-9D-AA-01","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa7973ba38ec990e","spanId":"aa7973ba38ec990e","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T15:13:35.608+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0711[D8-BB-C1-9D-AA-01]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa7973ba38ec990e","spanId":"aa7973ba38ec990e","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T15:13:35.608+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0711]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa7973ba38ec990e","spanId":"aa7973ba38ec990e","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T15:13:35.608+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[D8-BB-C1-9D-AA-01])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa7973ba38ec990e","spanId":"aa7973ba38ec990e","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T15:13:35.608+08:00","@version":"1","message":"[156d8bc0] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa7973ba38ec990e","spanId":"aa7973ba38ec990e","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T15:13:35.745+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[D8-BB-C1-9D-AA-01]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=*************, mac=D8-BB-C1-9D-AA-01, pc_no=MB0711, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa7973ba38ec990e","spanId":"aa7973ba38ec990e","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T15:13:35.745+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"aa7973ba38ec990e","spanId":"aa7973ba38ec990e","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-23T15:18:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=61)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.624+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.651+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.686+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.701+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.716+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.716+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.716+08:00","@version":"1","message":"执行预分配前共有61颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.717+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.717+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.717+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.717+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.717+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.717+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"735309ab0f6c5c1a","spanId":"735309ab0f6c5c1a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:26:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=61)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.613+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.634+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.652+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.667+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.682+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.682+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.682+08:00","@version":"1","message":"执行预分配前共有61颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.683+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.683+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.683+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.683+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.683+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:02.683+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6bab207dffaa4f2f","spanId":"6bab207dffaa4f2f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.626+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:54:02.635+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=59)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.635+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.655+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.671+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.685+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.716+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.716+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.717+08:00","@version":"1","message":"执行预分配前共有59颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.717+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.717+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.717+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.718+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.718+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:54:02.718+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"21c519df7f1fe432","spanId":"21c519df7f1fe432","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.611+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:58:02.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=59)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.613+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.633+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.651+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.668+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.683+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.683+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.683+08:00","@version":"1","message":"执行预分配前共有59颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.683+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.684+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.684+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.684+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.684+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:58:02.684+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"581e3553570b8267","spanId":"581e3553570b8267","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.611+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:02:02.613+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=59)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.613+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.632+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.665+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.679+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.68+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.68+08:00","@version":"1","message":"执行预分配前共有59颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.68+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.68+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.68+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.681+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.681+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:02.681+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d245b7cfee620bf5","spanId":"d245b7cfee620bf5","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.622+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:06:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=59)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.624+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.645+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.669+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.691+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.705+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.705+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.706+08:00","@version":"1","message":"执行预分配前共有59颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.706+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.706+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.706+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.706+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.706+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:06:02.706+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6055a9779e6b6c50","spanId":"6055a9779e6b6c50","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.617+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:10:02.619+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=59)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.619+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.639+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.655+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.672+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.687+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.687+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.687+08:00","@version":"1","message":"执行预分配前共有59颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.687+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.687+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.688+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.688+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.688+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:10:02.688+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bfcd03b67b5f6e26","spanId":"bfcd03b67b5f6e26","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:03.07+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:03.318+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=59)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.073+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.109+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.432+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.631+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113668, orderId=6374, name=Plan32, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.648+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.685+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:04.702+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113660, orderId=6374, name=Plan27, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.238+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113661, orderId=6374, name=Plan29, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.254+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113662, orderId=6374, name=Plan30, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.27+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113663, orderId=6374, name=Plan67, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.286+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.302+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.318+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.334+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.335+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113668, orderId=6374, name=Plan32, status=QUEUE, priority=70), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113660, orderId=6374, name=Plan27, status=QUEUE, priority=50), OrderPlanEntity(id=113661, orderId=6374, name=Plan29, status=QUEUE, priority=50), OrderPlanEntity(id=113662, orderId=6374, name=Plan30, status=QUEUE, priority=50), OrderPlanEntity(id=113663, orderId=6374, name=Plan67, status=QUEUE, priority=50), OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.335+08:00","@version":"1","message":"执行预分配前共有59颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.336+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.336+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.338+08:00","@version":"1","message":" getValidPlanList sumList: [2, 4, 6, 8, 10, 12, 14, 16, 18] leftFlashNum:59 index:9 plans:[OrderPlanEntity(id=113668, orderId=6374, name=Plan32, status=QUEUE, priority=70), OrderPlanEntity(id=113660, orderId=6374, name=Plan27, status=QUEUE, priority=50), OrderPlanEntity(id=113661, orderId=6374, name=Plan29, status=QUEUE, priority=50), OrderPlanEntity(id=113662, orderId=6374, name=Plan30, status=QUEUE, priority=50), OrderPlanEntity(id=113663, orderId=6374, name=Plan67, status=QUEUE, priority=50), OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:18:05.339+08:00","@version":"1","message":"[预分配]分配给测试人[Sloane.li]的Plan有 [Plan32, Plan27, Plan29, Plan30, Plan67], 一共使用了 10 颗样片, 还剩下样片 49 颗","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.34+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.34+08:00","@version":"1","message":"assignList: [OrderPlanEntity(id=113668, orderId=6374, name=Plan32, status=QUEUE, priority=70), OrderPlanEntity(id=113660, orderId=6374, name=Plan27, status=QUEUE, priority=50), OrderPlanEntity(id=113661, orderId=6374, name=Plan29, status=QUEUE, priority=50), OrderPlanEntity(id=113662, orderId=6374, name=Plan30, status=QUEUE, priority=50), OrderPlanEntity(id=113663, orderId=6374, name=Plan67, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.341+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 5 plans: [OrderPlanEntity(id=113668, orderId=6374, name=Plan32, status=QUEUE, priority=70), OrderPlanEntity(id=113660, orderId=6374, name=Plan27, status=QUEUE, priority=50), OrderPlanEntity(id=113661, orderId=6374, name=Plan29, status=QUEUE, priority=50), OrderPlanEntity(id=113662, orderId=6374, name=Plan30, status=QUEUE, priority=50), OrderPlanEntity(id=113663, orderId=6374, name=Plan67, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.341+08:00","@version":"1","message":"updatePlanStatusToQueue: [OrderPlanEntity(id=113668, orderId=6374, name=Plan32, status=QUEUE, priority=70), OrderPlanEntity(id=113660, orderId=6374, name=Plan27, status=QUEUE, priority=50), OrderPlanEntity(id=113661, orderId=6374, name=Plan29, status=QUEUE, priority=50), OrderPlanEntity(id=113662, orderId=6374, name=Plan30, status=QUEUE, priority=50), OrderPlanEntity(id=113663, orderId=6374, name=Plan67, status=QUEUE, priority=50)] !","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.758+08:00","@version":"1","message":"[5a096020] HTTP GET http://ereport.yeestor.com/wo/device/list?p=PCIe","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:05.965+08:00","@version":"1","message":"getAllDeviceList with PCIe got data DeviceListResp(code=0, data=[{ SS-PC-MB0521,172.18.21.22,18-C0-4D-A6-72-5E,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0784,172.18.30.120,04-7C-16-00-2A-55,[{性能=1}] }, { SS-PC-MB0534,172.18.20.106,18-C0-4D-A5-7F-8B,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0734,172.18.30.26,D8-5E-D3-73-DC-C6,[{系统掉电=1}] }, { SS-PC-MB0753,172.18.30.2,50-EB-F6-9C-B0-19,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0375,172.18.20.141,00-E0-70-C4-2D-27,[{MARS单盘=1}] }, { SS-PC-MB0478,172.18.20.146,A8-5E-45-9E-D5-73,[{MARS单盘=1}] }, { SS-PC-MB0545,172.18.20.147,F0-2F-74-2E-69-26,[{MARS单盘=1}] }, { SS-PC-MB0540,172.18.20.154,F0-2F-74-2E-71-E0,[{MARS单盘=1}] }, { SS-PC-MB0536,172.18.20.155,18-C0-4D-A6-71-D5,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0544,172.18.21.113,F0-2F-74-2E-66-5B,[{MARS单盘=1}] }, { SS-PC-MB0535,172.18.20.226,18-C0-4D-A6-72-D8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0741,172.18.30.101,D8-BB-C1-D1-E2-DE,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0769,172.18.30.105,18-C0-4D-A9-A4-6D,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0532,172.18.20.44,18-C0-4D-A6-71-A3,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0539,172.18.20.48,18-C0-4D-A6-72-11,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0554,172.18.20.50,F0-2F-74-2E-74-33,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0378,172.18.20.149,A8-A1-59-27-3E-92,[{系统掉电=1}] }, { SS-PC-MB0513,172.18.40.174,A4-0C-66-02-AE-83,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0471,172.18.41.123,B4-2E-99-FD-D5-21,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0469,172.18.41.181,B4-2E-99-FD-CE-0E,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0470,172.18.40.22,B4-2E-99-FD-D5-30,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0384,172.18.21.187,A8-A1-59-26-0A-92,[{系统掉电=1}] }, { SS-PC-MB0476,172.18.20.28,A8-5E-45-9E-D9-10,[{MARS单盘=1}] }, { SS-PC-MB0406,172.18.21.160,F0-2F-74-8B-94-DC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0514,172.18.41.40,A4-0C-66-07-03-D0,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0376,172.18.21.210,00-E0-70-C4-2C-84,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0377,172.18.20.189,00-E0-70-C4-2B-4D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0541,172.18.21.86,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0424,172.18.20.197,F0-2F-74-34-0C-BC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0372,172.18.20.205,00-E0-70-C4-2A-EF,[{MARS单盘=1}] }, { SS-PC-MB0371,172.18.20.208,00-E0-70-C4-2B-A9,[{MARS单盘=1}] }, { SS-PC-MB0533,172.18.21.39,18-C0-4D-A6-71-53,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0386,172.18.21.42,A8-A1-59-26-10-8B,[{系统掉电=1}] }, { SS-PC-MB0543,172.18.21.56,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }, { SS-PC-MB0537,172.18.20.113,18-C0-4D-A6-74-26,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0515,172.18.41.120,A4-0C-66-07-03-01,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0512,172.18.41.148,A4-0C-66-07-01-DE,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0390,172.18.21.244,18-C0-4D-6C-68-0B,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0383,172.18.21.27,A8-A1-59-26-87-0E,[{系统掉电=1}] }, { SS-PC-MB0538,172.18.20.60,18-C0-4D-A6-72-BB,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0425,172.18.20.135,F0-2F-74-33-FD-E0,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0520,172.18.20.192,18-C0-4D-A7-7E-79,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0392,172.18.21.193,18-C0-4D-6C-68-0A,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0754,172.18.30.66,50-EB-F6-9C-AD-F2,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0756,172.18.30.70,50-EB-F6-9C-AF-DB,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0787,172.18.30.71,D8-5E-D3-73-DD-39,[{系统掉电=1}] }, { SS-PC-MB0752,172.18.30.8,50-EB-F6-9C-B0-03,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0755,172.18.30.81,50-EB-F6-9C-AE-4E,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0709,172.18.31.172,D8-BB-C1-9D-AA-10,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0711,*************,D8-BB-C1-9D-AA-01,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0712,172.18.31.50,D8-BB-C1-9D-A9-D7,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0713,172.18.31.174,D8-BB-C1-9D-AA-53,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0714,172.18.30.243,D8-BB-C1-9D-A9-D9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0715,172.18.31.24,D8-BB-C1-9D-A9-E9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0716,172.18.31.243,D8-BB-C1-9D-A9-FA,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0717,172.18.31.9,D8-BB-C1-9D-AA-51,[{低温=4}] }, { SS-PC-MB0719,172.18.31.76,D8-BB-C1-9D-A9-7A,[{低温=4}] }, { SS-PC-MB0720,172.18.31.47,D8-BB-C1-9D-AA-30,[{低温=4}] }, { SS-PC-MB0775,*************,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0789,172.18.30.175,3C-EC-EF-B0-62-43,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0806,172.18.12.233,3C-EC-EF-B0-8A-75,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0794,172.18.31.102,3C-EC-EF-B0-8A-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0795,172.18.31.101,3C-EC-EF-B0-89-23,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0796,172.18.30.188,3C-EC-EF-B0-8A-83,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0802,172.18.31.41,3C-EC-EF-B0-8A-93,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0763,172.18.30.79,2C-F0-5D-CC-E7-DB,[{DOS=1}] }, { SS-PC-MB0764,172.18.30.92,2C-F0-5D-CC-E7-C8,[{DOS=1}] }, { SS-PC-MB0751,172.18.30.80,FC-34-97-C2-12-10,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0759,172.18.31.110,2C-F0-5D-CC-E7-D2,[{DOS=1}] }, { SS-PC-MB0783,172.18.30.13,04-7C-16-00-2A-66,[{性能=1}] }, { SS-PC-MB0799,172.18.31.43,3C-EC-EF-B0-84-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0804,172.18.30.236,3C-EC-EF-B0-8A-9D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0788,172.18.31.223,3C-EC-EF-B0-84-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0782,172.18.30.179,18-C0-4D-A9-A8-D6,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0761,172.18.30.113,2C-F0-5D-CC-E7-CB,[{DOS=1}] }, { SS-PC-MB0731,172.18.30.21,D8-5E-D3-73-DC-7C,[{系统掉电=1}] }, { SS-PC-MB0739,172.18.30.6,D8-BB-C1-D1-E2-DC,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0740,172.18.30.1,D8-BB-C1-D1-D1-C3,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0735,172.18.30.28,D8-BB-C1-D7-23-28,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0797,172.18.30.174,3C-EC-EF-B0-84-7F,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0385,172.18.20.229,A8-A1-59-27-C9-DB,[{系统掉电=1}] }, { SS-PC-MB0786,172.18.30.4,D8-5E-D3-73-DD-47,[{系统掉电=1}] }, { SS-PC-MB0732,172.18.30.111,D8-5E-D3-73-D0-27,[{系统掉电=1}] }, { SS-PC-MB0758,172.18.30.115,50-EB-F6-9C-AF-A0,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0555,172.18.20.114,F0-2F-74-2E-66-7D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0737,172.18.30.24,D8-BB-C1-D1-D1-C7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0738,172.18.30.104,D8-BB-C1-D1-D1-CD,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0798,172.18.31.235,3C-EC-EF-B0-8A-7D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0718,172.18.30.117,D8-BB-C1-9D-AD-44,[{低温=4}] }, { SS-PC-MB0803,172.18.31.74,3C-EC-EF-B0-8A-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0730,172.18.30.12,D8-5E-D3-73-DD-84,[{系统掉电=1}] }, { SS-PC-MB0733,172.18.30.97,D8-5E-D3-73-E0-F2,[{系统掉电=1}] }, { SS-PC-MB0724,172.18.30.158,D8-BB-C1-9D-AA-03,[{低温=4}] }, { SS-PC-MB0785,172.18.30.3,D8-5E-D3-73-DD-48,[{系统掉电=1}] }, { SS-PC-MB0774,************,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0772,************,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0773,172.18.30.14,18-C0-4D-AA-06-FF,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0771,************,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0776,172.18.30.67,18-C0-4D-A9-A2-B6,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0780,172.18.30.39,18-C0-4D-A9-A4-93,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0742,172.18.30.107,D8-BB-C1-D1-E2-D7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0736,172.18.30.46,D8-BB-C1-D1-E2-CA,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0370,172.18.21.240,00-E0-70-C4-2B-A3,[{MARS单盘=1}] }, { SS-PC-MB0380,172.18.20.193,A8-A1-59-26-8B-34,[{系统掉电=1}] }, { SS-PC-MB0807,172.18.31.152,3C-EC-EF-B0-89-57,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0728,172.18.31.241,3C-EC-EF-B0-2F-C7,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00010,172.18.31.57,04-7C-16-B9-36-FD,[{低温=4}] }, { PCIE-PC-MB00003,172.18.31.196,04-7C-16-B9-36-E9,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00015,172.18.31.135,04-7C-16-B9-3D-62,[{低温=4}] }, { PCIE-PC-MB00012,172.18.31.191,04-7C-16-B9-36-F9,[{低温=4}] }, { SS-PC-MB0805,172.18.31.20,3C-EC-EF-B5-09-37,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00034,172.18.30.177,08-BF-B8-32-90-B4,[{性能=1}] }, { PCIE-PC-MB00018,*************,3C-EC-EF-B0-B4-CD,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00020,172.18.30.238,3C-EC-EF-B0-B4-D9,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00019,*************,3C-EC-EF-B0-B4-D1,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00023,172.18.30.194,3C-EC-EF-B0-B4-D7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00021,172.18.30.138,3C-EC-EF-B0-B8-B7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00016,172.18.31.6,04-7C-16-B9-36-E8,[{低温=4}] }, { PCIE-PC-MB00025,172.18.30.131,3C-EC-EF-B0-B2-33,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00035,172.18.31.213,08-BF-B8-39-E9-AB,[{性能=1}] }, { PCIE-PC-MB00030,*************,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00031,172.18.12.35,3C-EC-EF-B0-B2-D3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00013,172.18.31.29,04-7C-16-B9-3D-E1,[{低温=4}] }, { PCIE-PC-MB00029,************,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0468,172.18.40.197,B4-2E-99-FD-D5-3A,[{品质测试_高温=4}, {高温=1}] }, { PCIE-PC-MB00014,172.18.31.69,04-7C-16-B9-36-EB,[{低温=4}] }, { SS-PC-MB0766,\f仚w\u0002,2C-F0-5D-CC-E7-CA,[{DOS=1}] }, { SS-PC-MB0762,172.18.30.112,2C-F0-5D-CC-E7-CD,[{DOS=1}] }, { SS-PC-MB0760,172.18.30.96,2C-F0-5D-CC-E8-1F,[{DOS=1}] }, { PCIE-PC-MB00041,172.18.31.89,7C-C2-55-7D-6A-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00042,172.18.30.178,7C-C2-55-7D-6C-D1,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00043,172.18.31.13,7C-C2-55-7D-74-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00045,172.18.30.135,7C-C2-55-7D-6D-4D,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00047,************8,7C-C2-55-7D-71-0B,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00046,172.18.30.130,7C-C2-55-7D-6D-CD,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00044,172.18.31.8,7C-C2-55-7D-69-B9,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00032,*************,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00054,172.18.30.121,10-7C-61-0A-72-01,[{性能=1}] }, { PCIE-PC-MB00055,172.18.31.158,C8-7F-54-C5-F2-1F,[{性能=1}] }, { PCIE-PC-MB00036,*************,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }], msg=测试机信息获取成功！, workPcLst=[{ SS-PC-MB0407,172.18.20.29,F0-2F-74-8B-94-FD,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }])","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:06.569+08:00","@version":"1","message":"fetchAllRunnableDevices orderId: 6374 flash: SSV7_4X1_256G-4X4_1T_256GB available device: [{ SS-PC-MB0521,172.18.21.22,18-C0-4D-A6-72-5E,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0784,172.18.30.120,04-7C-16-00-2A-55,[{性能=1}] }, { SS-PC-MB0534,172.18.20.106,18-C0-4D-A5-7F-8B,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0734,172.18.30.26,D8-5E-D3-73-DC-C6,[{系统掉电=1}] }, { SS-PC-MB0753,172.18.30.2,50-EB-F6-9C-B0-19,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0375,172.18.20.141,00-E0-70-C4-2D-27,[{MARS单盘=1}] }, { SS-PC-MB0478,172.18.20.146,A8-5E-45-9E-D5-73,[{MARS单盘=1}] }, { SS-PC-MB0545,172.18.20.147,F0-2F-74-2E-69-26,[{MARS单盘=1}] }, { SS-PC-MB0540,172.18.20.154,F0-2F-74-2E-71-E0,[{MARS单盘=1}] }, { SS-PC-MB0536,172.18.20.155,18-C0-4D-A6-71-D5,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0544,172.18.21.113,F0-2F-74-2E-66-5B,[{MARS单盘=1}] }, { SS-PC-MB0535,172.18.20.226,18-C0-4D-A6-72-D8,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0741,172.18.30.101,D8-BB-C1-D1-E2-DE,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0769,172.18.30.105,18-C0-4D-A9-A4-6D,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0532,172.18.20.44,18-C0-4D-A6-71-A3,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0539,172.18.20.48,18-C0-4D-A6-72-11,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0554,172.18.20.50,F0-2F-74-2E-74-33,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0378,172.18.20.149,A8-A1-59-27-3E-92,[{系统掉电=1}] }, { SS-PC-MB0513,172.18.40.174,A4-0C-66-02-AE-83,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0471,172.18.41.123,B4-2E-99-FD-D5-21,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0469,172.18.41.181,B4-2E-99-FD-CE-0E,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0470,172.18.40.22,B4-2E-99-FD-D5-30,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0384,172.18.21.187,A8-A1-59-26-0A-92,[{系统掉电=1}] }, { SS-PC-MB0476,172.18.20.28,A8-5E-45-9E-D9-10,[{MARS单盘=1}] }, { SS-PC-MB0406,172.18.21.160,F0-2F-74-8B-94-DC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0514,172.18.41.40,A4-0C-66-07-03-D0,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0376,172.18.21.210,00-E0-70-C4-2C-84,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0377,172.18.20.189,00-E0-70-C4-2B-4D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0541,172.18.21.86,F0-2F-74-2E-6B-4D,[{MARS单盘=1}] }, { SS-PC-MB0424,172.18.20.197,F0-2F-74-34-0C-BC,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0372,172.18.20.205,00-E0-70-C4-2A-EF,[{MARS单盘=1}] }, { SS-PC-MB0371,172.18.20.208,00-E0-70-C4-2B-A9,[{MARS单盘=1}] }, { SS-PC-MB0533,172.18.21.39,18-C0-4D-A6-71-53,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0386,172.18.21.42,A8-A1-59-26-10-8B,[{系统掉电=1}] }, { SS-PC-MB0543,172.18.21.56,F0-2F-74-2E-6B-5C,[{MARS单盘=1}] }, { SS-PC-MB0537,172.18.20.113,18-C0-4D-A6-74-26,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0515,172.18.41.120,A4-0C-66-07-03-01,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0512,172.18.41.148,A4-0C-66-07-01-DE,[{品质测试_高温=4}, {高温=1}] }, { SS-PC-MB0390,172.18.21.244,18-C0-4D-6C-68-0B,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0383,172.18.21.27,A8-A1-59-26-87-0E,[{系统掉电=1}] }, { SS-PC-MB0538,172.18.20.60,18-C0-4D-A6-72-BB,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0425,172.18.20.135,F0-2F-74-33-FD-E0,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0520,172.18.20.192,18-C0-4D-A7-7E-79,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0392,172.18.21.193,18-C0-4D-6C-68-0A,[{MARS单盘=1}, {品质测试_常温=1}] }, { SS-PC-MB0754,172.18.30.66,50-EB-F6-9C-AD-F2,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0756,172.18.30.70,50-EB-F6-9C-AF-DB,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0787,172.18.30.71,D8-5E-D3-73-DD-39,[{系统掉电=1}] }, { SS-PC-MB0752,172.18.30.8,50-EB-F6-9C-B0-03,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0755,172.18.30.81,50-EB-F6-9C-AE-4E,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0709,172.18.31.172,D8-BB-C1-9D-AA-10,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0711,*************,D8-BB-C1-9D-AA-01,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0712,172.18.31.50,D8-BB-C1-9D-A9-D7,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0713,172.18.31.174,D8-BB-C1-9D-AA-53,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0714,172.18.30.243,D8-BB-C1-9D-A9-D9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0715,172.18.31.24,D8-BB-C1-9D-A9-E9,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0716,172.18.31.243,D8-BB-C1-9D-A9-FA,[{高温=4}, {品质测试_高温=1}] }, { SS-PC-MB0717,172.18.31.9,D8-BB-C1-9D-AA-51,[{低温=4}] }, { SS-PC-MB0719,172.18.31.76,D8-BB-C1-9D-A9-7A,[{低温=4}] }, { SS-PC-MB0720,172.18.31.47,D8-BB-C1-9D-AA-30,[{低温=4}] }, { SS-PC-MB0775,*************,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0789,172.18.30.175,3C-EC-EF-B0-62-43,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0806,172.18.12.233,3C-EC-EF-B0-8A-75,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0794,172.18.31.102,3C-EC-EF-B0-8A-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0795,172.18.31.101,3C-EC-EF-B0-89-23,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0796,172.18.30.188,3C-EC-EF-B0-8A-83,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0802,172.18.31.41,3C-EC-EF-B0-8A-93,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0763,172.18.30.79,2C-F0-5D-CC-E7-DB,[{DOS=1}] }, { SS-PC-MB0764,172.18.30.92,2C-F0-5D-CC-E7-C8,[{DOS=1}] }, { SS-PC-MB0751,172.18.30.80,FC-34-97-C2-12-10,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0759,172.18.31.110,2C-F0-5D-CC-E7-D2,[{DOS=1}] }, { SS-PC-MB0783,172.18.30.13,04-7C-16-00-2A-66,[{性能=1}] }, { SS-PC-MB0799,172.18.31.43,3C-EC-EF-B0-84-99,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0804,172.18.30.236,3C-EC-EF-B0-8A-9D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0788,172.18.31.223,3C-EC-EF-B0-84-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0782,172.18.30.179,18-C0-4D-A9-A8-D6,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0761,172.18.30.113,2C-F0-5D-CC-E7-CB,[{DOS=1}] }, { SS-PC-MB0731,172.18.30.21,D8-5E-D3-73-DC-7C,[{系统掉电=1}] }, { SS-PC-MB0739,172.18.30.6,D8-BB-C1-D1-E2-DC,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0740,172.18.30.1,D8-BB-C1-D1-D1-C3,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0735,172.18.30.28,D8-BB-C1-D7-23-28,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0797,172.18.30.174,3C-EC-EF-B0-84-7F,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0385,172.18.20.229,A8-A1-59-27-C9-DB,[{系统掉电=1}] }, { SS-PC-MB0786,172.18.30.4,D8-5E-D3-73-DD-47,[{系统掉电=1}] }, { SS-PC-MB0732,172.18.30.111,D8-5E-D3-73-D0-27,[{系统掉电=1}] }, { SS-PC-MB0758,172.18.30.115,50-EB-F6-9C-AF-A0,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0555,172.18.20.114,F0-2F-74-2E-66-7D,[{MARS单盘=1}, {MARS多盘=2}, {品质测试_常温=1}] }, { SS-PC-MB0737,172.18.30.24,D8-BB-C1-D1-D1-C7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0738,172.18.30.104,D8-BB-C1-D1-D1-CD,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0798,172.18.31.235,3C-EC-EF-B0-8A-7D,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0718,172.18.30.117,D8-BB-C1-9D-AD-44,[{低温=4}] }, { SS-PC-MB0803,172.18.31.74,3C-EC-EF-B0-8A-81,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0730,172.18.30.12,D8-5E-D3-73-DD-84,[{系统掉电=1}] }, { SS-PC-MB0733,172.18.30.97,D8-5E-D3-73-E0-F2,[{系统掉电=1}] }, { SS-PC-MB0724,172.18.30.158,D8-BB-C1-9D-AA-03,[{低温=4}] }, { SS-PC-MB0785,172.18.30.3,D8-5E-D3-73-DD-48,[{系统掉电=1}] }, { SS-PC-MB0774,************,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0772,************,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0773,172.18.30.14,18-C0-4D-AA-06-FF,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0771,************,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0776,172.18.30.67,18-C0-4D-A9-A2-B6,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0780,172.18.30.39,18-C0-4D-A9-A4-93,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0742,172.18.30.107,D8-BB-C1-D1-E2-D7,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0736,172.18.30.46,D8-BB-C1-D1-E2-CA,[{Reboot=1}, {Sleeper=1}] }, { SS-PC-MB0370,172.18.21.240,00-E0-70-C4-2B-A3,[{MARS单盘=1}] }, { SS-PC-MB0380,172.18.20.193,A8-A1-59-26-8B-34,[{系统掉电=1}] }, { SS-PC-MB0807,172.18.31.152,3C-EC-EF-B0-89-57,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0728,172.18.31.241,3C-EC-EF-B0-2F-C7,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00010,172.18.31.57,04-7C-16-B9-36-FD,[{低温=4}] }, { PCIE-PC-MB00003,172.18.31.196,04-7C-16-B9-36-E9,[{高温=4}, {品质测试_高温=1}] }, { PCIE-PC-MB00015,172.18.31.135,04-7C-16-B9-3D-62,[{低温=4}] }, { PCIE-PC-MB00012,172.18.31.191,04-7C-16-B9-36-F9,[{低温=4}] }, { SS-PC-MB0805,172.18.31.20,3C-EC-EF-B5-09-37,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00034,172.18.30.177,08-BF-B8-32-90-B4,[{性能=1}] }, { PCIE-PC-MB00018,*************,3C-EC-EF-B0-B4-CD,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00020,172.18.30.238,3C-EC-EF-B0-B4-D9,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00019,*************,3C-EC-EF-B0-B4-D1,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00023,172.18.30.194,3C-EC-EF-B0-B4-D7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00021,172.18.30.138,3C-EC-EF-B0-B8-B7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00016,172.18.31.6,04-7C-16-B9-36-E8,[{低温=4}] }, { PCIE-PC-MB00025,172.18.30.131,3C-EC-EF-B0-B2-33,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00035,172.18.31.213,08-BF-B8-39-E9-AB,[{性能=1}] }, { PCIE-PC-MB00030,*************,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00031,172.18.12.35,3C-EC-EF-B0-B2-D3,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00013,172.18.31.29,04-7C-16-B9-3D-E1,[{低温=4}] }, { PCIE-PC-MB00029,************,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { SS-PC-MB0468,172.18.40.197,B4-2E-99-FD-D5-3A,[{品质测试_高温=4}, {高温=1}] }, { PCIE-PC-MB00014,172.18.31.69,04-7C-16-B9-36-EB,[{低温=4}] }, { SS-PC-MB0766,\f仚w\u0002,2C-F0-5D-CC-E7-CA,[{DOS=1}] }, { SS-PC-MB0762,172.18.30.112,2C-F0-5D-CC-E7-CD,[{DOS=1}] }, { SS-PC-MB0760,172.18.30.96,2C-F0-5D-CC-E8-1F,[{DOS=1}] }, { PCIE-PC-MB00041,172.18.31.89,7C-C2-55-7D-6A-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00042,172.18.30.178,7C-C2-55-7D-6C-D1,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00043,172.18.31.13,7C-C2-55-7D-74-89,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00045,172.18.30.135,7C-C2-55-7D-6D-4D,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00047,************8,7C-C2-55-7D-71-0B,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00046,172.18.30.130,7C-C2-55-7D-6D-CD,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00044,172.18.31.8,7C-C2-55-7D-69-B9,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00032,*************,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00054,172.18.30.121,10-7C-61-0A-72-01,[{性能=1}] }, { PCIE-PC-MB00055,172.18.31.158,C8-7F-54-C5-F2-1F,[{性能=1}] }, { PCIE-PC-MB00036,*************,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:06.575+08:00","@version":"1","message":"Devices already occupied before Plan allocation: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:06.576+08:00","@version":"1","message":"[6374] Plan32 need Num: 2 to test. SSV7_4X1_256G-4X4_1T_256GB left num:59 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:06.576+08:00","@version":"1","message":"subProduct PCIe flashName SSV7_4X1_256G-4X4_1T_256GB plan [Plan32] attrs 5V掉电 belongTo Sloane.li is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:06.585+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [SS-PC-MB0769, SS-PC-MB0771, SS-PC-MB0772, SS-PC-MB0773, SS-PC-MB0774, SS-PC-MB0775, SS-PC-MB0776, SS-PC-MB0780, SS-PC-MB0788, SS-PC-MB0789, SS-PC-MB0794, SS-PC-MB0795, SS-PC-MB0796, SS-PC-MB0797, SS-PC-MB0798, SS-PC-MB0799, SS-PC-MB0802, SS-PC-MB0803, SS-PC-MB0804, SS-PC-MB0805, SS-PC-MB0806, SS-PC-MB0807, SS-PC-MB0728, SS-PC-MB0782, PCIE-PC-MB00018, PCIE-PC-MB00029, PCIE-PC-MB00030, PCIE-PC-MB00032, PCIE-PC-MB00036, PCIE-PC-MB00019, PCIE-PC-MB00047, PCIE-PC-MB00041, PCIE-PC-MB00042, PCIE-PC-MB00043, PCIE-PC-MB00044, PCIE-PC-MB00045, PCIE-PC-MB00020, PCIE-PC-MB00046, PCIE-PC-MB00021, PCIE-PC-MB00023, PCIE-PC-MB00025, PCIE-PC-MB00031]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:06.585+08:00","@version":"1","message":"PCIe Plan32不是测试所有样片的Plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:06.588+08:00","@version":"1","message":"sortedEntries: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.383+08:00","@version":"1","message":"Plan32已存在使用机柜，在机柜PCIE_1下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.384+08:00","@version":"1","message":"Plan32已存在使用机柜，在机柜PCIE_8下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.384+08:00","@version":"1","message":"Plan32已存在使用机柜，在机柜PCIE_7下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.384+08:00","@version":"1","message":"rackList: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.384+08:00","@version":"1","message":"devices: [PCIE_1_03, PCIE_1_05, PCIE_1_06, PCIE_1_07, PCIE_1_09, PCIE_1_10, PCIE_1_11, PCIE_1_15]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.385+08:00","@version":"1","message":"for index: 0 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.385+08:00","@version":"1","message":"for index: 1 size: 2 sum: 2 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.385+08:00","@version":"1","message":"wait testNum: 2 assign sum: 2 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.386+08:00","@version":"1","message":"Plan32已分配的机柜范围内：在机柜PCIE_1下找到空闲的可使用设备：[PCIE_1_05, PCIE_1_06]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.386+08:00","@version":"1","message":"[6374] plan:Plan32 use 2 pc: [{ SS-PC-MB0771,************,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0772,************,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.387+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ SS-PC-MB0771,************,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0772,************,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.387+08:00","@version":"1","message":"[6374] Plan27 need Num: 2 to test. SSV7_4X1_256G-4X4_1T_256GB left num:57 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.387+08:00","@version":"1","message":"subProduct PCIe flashName SSV7_4X1_256G-4X4_1T_256GB plan [Plan27] attrs 5V掉电 belongTo Sloane.li is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.391+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [SS-PC-MB0769, SS-PC-MB0773, SS-PC-MB0774, SS-PC-MB0775, SS-PC-MB0776, SS-PC-MB0780, SS-PC-MB0788, SS-PC-MB0789, SS-PC-MB0794, SS-PC-MB0795, SS-PC-MB0796, SS-PC-MB0797, SS-PC-MB0798, SS-PC-MB0799, SS-PC-MB0802, SS-PC-MB0803, SS-PC-MB0804, SS-PC-MB0805, SS-PC-MB0806, SS-PC-MB0807, SS-PC-MB0728, SS-PC-MB0782, PCIE-PC-MB00018, PCIE-PC-MB00029, PCIE-PC-MB00030, PCIE-PC-MB00032, PCIE-PC-MB00036, PCIE-PC-MB00019, PCIE-PC-MB00047, PCIE-PC-MB00041, PCIE-PC-MB00042, PCIE-PC-MB00043, PCIE-PC-MB00044, PCIE-PC-MB00045, PCIE-PC-MB00020, PCIE-PC-MB00046, PCIE-PC-MB00021, PCIE-PC-MB00023, PCIE-PC-MB00025, PCIE-PC-MB00031]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.391+08:00","@version":"1","message":"PCIe Plan27不是测试所有样片的Plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.394+08:00","@version":"1","message":"sortedEntries: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.964+08:00","@version":"1","message":"Plan27已存在使用机柜，在机柜PCIE_1下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.965+08:00","@version":"1","message":"Plan27已存在使用机柜，在机柜PCIE_8下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.965+08:00","@version":"1","message":"Plan27已存在使用机柜，在机柜PCIE_7下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.966+08:00","@version":"1","message":"rackList: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.966+08:00","@version":"1","message":"devices: [PCIE_1_03, PCIE_1_07, PCIE_1_09, PCIE_1_10, PCIE_1_11, PCIE_1_15]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.966+08:00","@version":"1","message":"for index: 0 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.966+08:00","@version":"1","message":"for index: 1 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.966+08:00","@version":"1","message":"for index: 2 size: 2 sum: 2 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.967+08:00","@version":"1","message":"wait testNum: 2 assign sum: 2 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.967+08:00","@version":"1","message":"Plan27已分配的机柜范围内：在机柜PCIE_1下找到空闲的可使用设备：[PCIE_1_09, PCIE_1_10]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.967+08:00","@version":"1","message":"[6374] plan:Plan27 use 2 pc: [{ SS-PC-MB0774,************,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0775,*************,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.967+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ SS-PC-MB0771,************,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0772,************,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0774,************,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0775,*************,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.967+08:00","@version":"1","message":"[6374] Plan29 need Num: 2 to test. SSV7_4X1_256G-4X4_1T_256GB left num:55 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:07.968+08:00","@version":"1","message":"subProduct PCIe flashName SSV7_4X1_256G-4X4_1T_256GB plan [Plan29] attrs 5V掉电 belongTo Sloane.li is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:08.026+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [SS-PC-MB0769, SS-PC-MB0773, SS-PC-MB0776, SS-PC-MB0780, SS-PC-MB0788, SS-PC-MB0789, SS-PC-MB0794, SS-PC-MB0795, SS-PC-MB0796, SS-PC-MB0797, SS-PC-MB0798, SS-PC-MB0799, SS-PC-MB0802, SS-PC-MB0803, SS-PC-MB0804, SS-PC-MB0805, SS-PC-MB0806, SS-PC-MB0807, SS-PC-MB0728, SS-PC-MB0782, PCIE-PC-MB00018, PCIE-PC-MB00029, PCIE-PC-MB00030, PCIE-PC-MB00032, PCIE-PC-MB00036, PCIE-PC-MB00019, PCIE-PC-MB00047, PCIE-PC-MB00041, PCIE-PC-MB00042, PCIE-PC-MB00043, PCIE-PC-MB00044, PCIE-PC-MB00045, PCIE-PC-MB00020, PCIE-PC-MB00046, PCIE-PC-MB00021, PCIE-PC-MB00023, PCIE-PC-MB00025, PCIE-PC-MB00031]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:08.026+08:00","@version":"1","message":"PCIe Plan29不是测试所有样片的Plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:08.032+08:00","@version":"1","message":"sortedEntries: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.223+08:00","@version":"1","message":"Plan29已存在使用机柜，在机柜PCIE_1下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.223+08:00","@version":"1","message":"Plan29已存在使用机柜，在机柜PCIE_8下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.223+08:00","@version":"1","message":"Plan29已存在使用机柜，在机柜PCIE_7下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.223+08:00","@version":"1","message":"rackList: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.224+08:00","@version":"1","message":"devices: [PCIE_1_03, PCIE_1_07, PCIE_1_11, PCIE_1_15]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.224+08:00","@version":"1","message":"for index: 0 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.224+08:00","@version":"1","message":"for index: 1 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.224+08:00","@version":"1","message":"for index: 2 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.224+08:00","@version":"1","message":"for index: 3 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.224+08:00","@version":"1","message":"devices: [PCIE_8_1, PCIE_8_13, PCIE_8_14, PCIE_8_17, PCIE_8_18, PCIE_8_2, PCIE_8_22, PCIE_8_25, PCIE_8_26, PCIE_8_27, PCIE_8_28, PCIE_8_29, PCIE_8_3, PCIE_8_30, PCIE_8_4, PCIE_8_6, PCIE_8_9]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.225+08:00","@version":"1","message":"for index: 0 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.225+08:00","@version":"1","message":"for index: 1 size: 2 sum: 2 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.225+08:00","@version":"1","message":"wait testNum: 2 assign sum: 2 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.225+08:00","@version":"1","message":"Plan29已分配的机柜范围内：在机柜PCIE_8下找到空闲的可使用设备：[PCIE_8_13, PCIE_8_14]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.225+08:00","@version":"1","message":"[6374] plan:Plan29 use 2 pc: [{ PCIE-PC-MB00029,************,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00030,*************,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.226+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ SS-PC-MB0771,************,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0772,************,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0774,************,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0775,*************,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { PCIE-PC-MB00029,************,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00030,*************,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.226+08:00","@version":"1","message":"[6374] Plan30 need Num: 2 to test. SSV7_4X1_256G-4X4_1T_256GB left num:53 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.226+08:00","@version":"1","message":"subProduct PCIe flashName SSV7_4X1_256G-4X4_1T_256GB plan [Plan30] attrs 5V掉电 belongTo Sloane.li is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.23+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [SS-PC-MB0769, SS-PC-MB0773, SS-PC-MB0776, SS-PC-MB0780, SS-PC-MB0788, SS-PC-MB0789, SS-PC-MB0794, SS-PC-MB0795, SS-PC-MB0796, SS-PC-MB0797, SS-PC-MB0798, SS-PC-MB0799, SS-PC-MB0802, SS-PC-MB0803, SS-PC-MB0804, SS-PC-MB0805, SS-PC-MB0806, SS-PC-MB0807, SS-PC-MB0728, SS-PC-MB0782, PCIE-PC-MB00018, PCIE-PC-MB00032, PCIE-PC-MB00036, PCIE-PC-MB00019, PCIE-PC-MB00047, PCIE-PC-MB00041, PCIE-PC-MB00042, PCIE-PC-MB00043, PCIE-PC-MB00044, PCIE-PC-MB00045, PCIE-PC-MB00020, PCIE-PC-MB00046, PCIE-PC-MB00021, PCIE-PC-MB00023, PCIE-PC-MB00025, PCIE-PC-MB00031]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.231+08:00","@version":"1","message":"PCIe Plan30不是测试所有样片的Plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.234+08:00","@version":"1","message":"sortedEntries: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.375+08:00","@version":"1","message":"Plan30已存在使用机柜，在机柜PCIE_1下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.376+08:00","@version":"1","message":"Plan30已存在使用机柜，在机柜PCIE_8下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.376+08:00","@version":"1","message":"Plan30已存在使用机柜，在机柜PCIE_7下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.376+08:00","@version":"1","message":"rackList: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.376+08:00","@version":"1","message":"devices: [PCIE_1_03, PCIE_1_07, PCIE_1_11, PCIE_1_15]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.377+08:00","@version":"1","message":"for index: 0 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.377+08:00","@version":"1","message":"for index: 1 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.377+08:00","@version":"1","message":"for index: 2 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.377+08:00","@version":"1","message":"for index: 3 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.377+08:00","@version":"1","message":"devices: [PCIE_8_1, PCIE_8_17, PCIE_8_18, PCIE_8_2, PCIE_8_22, PCIE_8_25, PCIE_8_26, PCIE_8_27, PCIE_8_28, PCIE_8_29, PCIE_8_3, PCIE_8_30, PCIE_8_4, PCIE_8_6, PCIE_8_9]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.378+08:00","@version":"1","message":"for index: 0 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.378+08:00","@version":"1","message":"for index: 1 size: 2 sum: 2 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.378+08:00","@version":"1","message":"wait testNum: 2 assign sum: 2 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.378+08:00","@version":"1","message":"Plan30已分配的机柜范围内：在机柜PCIE_8下找到空闲的可使用设备：[PCIE_8_17, PCIE_8_18]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.378+08:00","@version":"1","message":"[6374] plan:Plan30 use 2 pc: [{ PCIE-PC-MB00032,*************,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00036,*************,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.378+08:00","@version":"1","message":"Devices already occupied before Plan allocation: [{ SS-PC-MB0771,************,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0772,************,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0774,************,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0775,*************,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { PCIE-PC-MB00029,************,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00030,*************,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00032,*************,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00036,*************,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.379+08:00","@version":"1","message":"[6374] Plan67 need Num: 2 to test. SSV7_4X1_256G-4X4_1T_256GB left num:51 ","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.379+08:00","@version":"1","message":"subProduct PCIe flashName SSV7_4X1_256G-4X4_1T_256GB plan [Plan67] attrs 5V掉电 belongTo Sloane.li is ready for allocation","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.384+08:00","@version":"1","message":"fetchPlanRunnableDevices runnableDeviceList: [SS-PC-MB0769, SS-PC-MB0773, SS-PC-MB0776, SS-PC-MB0780, SS-PC-MB0788, SS-PC-MB0789, SS-PC-MB0794, SS-PC-MB0795, SS-PC-MB0796, SS-PC-MB0797, SS-PC-MB0798, SS-PC-MB0799, SS-PC-MB0802, SS-PC-MB0803, SS-PC-MB0804, SS-PC-MB0805, SS-PC-MB0806, SS-PC-MB0807, SS-PC-MB0728, SS-PC-MB0782, PCIE-PC-MB00018, PCIE-PC-MB00019, PCIE-PC-MB00047, PCIE-PC-MB00041, PCIE-PC-MB00042, PCIE-PC-MB00043, PCIE-PC-MB00044, PCIE-PC-MB00045, PCIE-PC-MB00020, PCIE-PC-MB00046, PCIE-PC-MB00021, PCIE-PC-MB00023, PCIE-PC-MB00025, PCIE-PC-MB00031]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.392+08:00","@version":"1","message":"PCIe Plan67不是测试所有样片的Plan","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.394+08:00","@version":"1","message":"sortedEntries: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.406+08:00","@version":"1","message":"Plan67已存在使用机柜，在机柜PCIE_1下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.411+08:00","@version":"1","message":"Plan67已存在使用机柜，在机柜PCIE_8下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.412+08:00","@version":"1","message":"Plan67已存在使用机柜，在机柜PCIE_7下寻找连号的设备","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.412+08:00","@version":"1","message":"rackList: [PCIE_1=7, PCIE_8=4, PCIE_7=1]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.412+08:00","@version":"1","message":"devices: [PCIE_1_03, PCIE_1_07, PCIE_1_11, PCIE_1_15]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.412+08:00","@version":"1","message":"for index: 0 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.412+08:00","@version":"1","message":"for index: 1 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.412+08:00","@version":"1","message":"for index: 2 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.413+08:00","@version":"1","message":"for index: 3 size: 1 sum: 1 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.413+08:00","@version":"1","message":"devices: [PCIE_8_1, PCIE_8_2, PCIE_8_22, PCIE_8_25, PCIE_8_26, PCIE_8_27, PCIE_8_28, PCIE_8_29, PCIE_8_3, PCIE_8_30, PCIE_8_4, PCIE_8_6, PCIE_8_9]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.413+08:00","@version":"1","message":"for index: 0 size: 2 sum: 2 testNum: 2","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.414+08:00","@version":"1","message":"wait testNum: 2 assign sum: 2 ","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.414+08:00","@version":"1","message":"Plan67已分配的机柜范围内：在机柜PCIE_8下找到空闲的可使用设备：[PCIE_8_1, PCIE_8_2]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.414+08:00","@version":"1","message":"[6374] plan:Plan67 use 2 pc: [{ PCIE-PC-MB00018,*************,3C-EC-EF-B0-B4-CD,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00019,*************,3C-EC-EF-B0-B4-D1,[{5V掉电热插拔=1}, {5V掉电=1}] }]","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.415+08:00","@version":"1","message":"assignDevices [6374] - find 5 run devices:  {Plan29=[{ PCIE-PC-MB00029,************,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00030,*************,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }], Plan30=[{ PCIE-PC-MB00032,*************,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00036,*************,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }], Plan67=[{ PCIE-PC-MB00018,*************,3C-EC-EF-B0-B4-CD,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00019,*************,3C-EC-EF-B0-B4-D1,[{5V掉电热插拔=1}, {5V掉电=1}] }], Plan32=[{ SS-PC-MB0771,************,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0772,************,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }], Plan27=[{ SS-PC-MB0774,************,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0775,*************,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }]}","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.418+08:00","@version":"1","message":"工单[6374] flash SSV7_4X1_256G-4X4_1T_256GB , 参与此次Plan预分配的plan共有 [Plan32, Plan27, Plan29, Plan30, Plan67] ","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.471+08:00","@version":"1","message":" add 2 devices :[{ PCIE-PC-MB00029,************,3C-EC-EF-B0-B4-CB,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00030,*************,3C-EC-EF-B0-B4-E7,[{5V掉电热插拔=1}, {5V掉电=1}] }]  to plan:Plan29","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.497+08:00","@version":"1","message":"plan:Plan29 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 2","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.642+08:00","@version":"1","message":"[YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB] - Plan29 holdDevices  :[PlanDeviceEntity(id=277119, orderId=6374, planId=113661, planName=Plan29, ip=************, mac=3C-EC-EF-B0-B4-CB, no=PCIE-PC-MB00029, position=PCIE_8_13, score=200, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277120, orderId=6374, planId=113661, planName=Plan29, ip=*************, mac=3C-EC-EF-B0-B4-E7, no=PCIE-PC-MB00030, position=PCIE_8_14, score=200, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.802+08:00","@version":"1","message":"lock device:3C-EC-EF-B0-B4-CB to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan29 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.845+08:00","@version":"1","message":"lock device:3C-EC-EF-B0-B4-E7 to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan29 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:09.859+08:00","@version":"1","message":"[779dc17d] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:10.066+08:00","@version":"1","message":"lockDevice with ipList:[************, *************] - macList:[3C-EC-EF-B0-B4-CB, 3C-EC-EF-B0-B4-E7],no:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:10.072+08:00","@version":"1","message":"自动分配PCIE-PC-MB00029(************),PCIE-PC-MB00030(*************)给SSV7_4X1_256G-4X4_1T_256GB下的Plan29","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:10.072+08:00","@version":"1","message":"add 2 devices to plan: Plan29 ,expect num: 2, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:10.073+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6374, flash:SSV7_4X1_256G-4X4_1T_256GB, planEntity:OrderPlanEntity(id=113661, orderId=6374, name=Plan29, status=QUEUE, priority=50)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:10.105+08:00","@version":"1","message":"测试单: YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB plan: Plan29已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.767+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.785+08:00","@version":"1","message":" add 2 devices :[{ PCIE-PC-MB00032,*************,3C-EC-EF-B0-B4-DF,[{5V掉电=1}, {5V掉电热插拔=1}] }, { PCIE-PC-MB00036,*************,7C-C2-55-7D-74-E9,[{5V掉电=1}, {5V掉电热插拔=1}] }]  to plan:Plan30","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.807+08:00","@version":"1","message":"plan:Plan30 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 2","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.816+08:00","@version":"1","message":"[YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB] - Plan30 holdDevices  :[PlanDeviceEntity(id=277121, orderId=6374, planId=113662, planName=Plan30, ip=*************, mac=3C-EC-EF-B0-B4-DF, no=PCIE-PC-MB00032, position=PCIE_8_17, score=200, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277122, orderId=6374, planId=113662, planName=Plan30, ip=*************, mac=7C-C2-55-7D-74-E9, no=PCIE-PC-MB00036, position=PCIE_8_18, score=200, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.819+08:00","@version":"1","message":"lock device:3C-EC-EF-B0-B4-DF to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan30 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.827+08:00","@version":"1","message":"lock device:7C-C2-55-7D-74-E9 to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan30 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.834+08:00","@version":"1","message":"[7fc6074e] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.984+08:00","@version":"1","message":"lockDevice with ipList:[*************, *************] - macList:[3C-EC-EF-B0-B4-DF, 7C-C2-55-7D-74-E9],no:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.984+08:00","@version":"1","message":"自动分配PCIE-PC-MB00032(*************),PCIE-PC-MB00036(*************)给SSV7_4X1_256G-4X4_1T_256GB下的Plan30","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.984+08:00","@version":"1","message":"add 2 devices to plan: Plan30 ,expect num: 2, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.984+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6374, flash:SSV7_4X1_256G-4X4_1T_256GB, planEntity:OrderPlanEntity(id=113662, orderId=6374, name=Plan30, status=QUEUE, priority=50)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:11.987+08:00","@version":"1","message":"测试单: YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB plan: Plan30已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.192+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.209+08:00","@version":"1","message":" add 2 devices :[{ PCIE-PC-MB00018,*************,3C-EC-EF-B0-B4-CD,[{5V掉电热插拔=1}, {5V掉电=1}] }, { PCIE-PC-MB00019,*************,3C-EC-EF-B0-B4-D1,[{5V掉电热插拔=1}, {5V掉电=1}] }]  to plan:Plan67","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.234+08:00","@version":"1","message":"plan:Plan67 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 2","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.241+08:00","@version":"1","message":"[YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB] - Plan67 holdDevices  :[PlanDeviceEntity(id=277123, orderId=6374, planId=113663, planName=Plan67, ip=*************, mac=3C-EC-EF-B0-B4-CD, no=PCIE-PC-MB00018, position=PCIE_8_1, score=200, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277124, orderId=6374, planId=113663, planName=Plan67, ip=*************, mac=3C-EC-EF-B0-B4-D1, no=PCIE-PC-MB00019, position=PCIE_8_2, score=200, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.245+08:00","@version":"1","message":"lock device:3C-EC-EF-B0-B4-CD to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan67 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.255+08:00","@version":"1","message":"lock device:3C-EC-EF-B0-B4-D1 to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan67 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.261+08:00","@version":"1","message":"[5e23ba3b] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.405+08:00","@version":"1","message":"lockDevice with ipList:[*************, *************] - macList:[3C-EC-EF-B0-B4-CD, 3C-EC-EF-B0-B4-D1],no:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.405+08:00","@version":"1","message":"自动分配PCIE-PC-MB00018(*************),PCIE-PC-MB00019(*************)给SSV7_4X1_256G-4X4_1T_256GB下的Plan67","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.405+08:00","@version":"1","message":"add 2 devices to plan: Plan67 ,expect num: 2, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.405+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6374, flash:SSV7_4X1_256G-4X4_1T_256GB, planEntity:OrderPlanEntity(id=113663, orderId=6374, name=Plan67, status=QUEUE, priority=50)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.408+08:00","@version":"1","message":"测试单: YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB plan: Plan67已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.802+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.817+08:00","@version":"1","message":" add 2 devices :[{ SS-PC-MB0771,************,18-C0-4D-A9-A9-59,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }, { SS-PC-MB0772,************,18-C0-4D-A9-A9-A7,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }]  to plan:Plan32","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.84+08:00","@version":"1","message":"plan:Plan32 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 2","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.846+08:00","@version":"1","message":"[YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB] - Plan32 holdDevices  :[PlanDeviceEntity(id=277125, orderId=6374, planId=113668, planName=Plan32, ip=************, mac=18-C0-4D-A9-A9-59, no=SS-PC-MB0771, position=PCIE_1_05, score=300, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277126, orderId=6374, planId=113668, planName=Plan32, ip=************, mac=18-C0-4D-A9-A9-A7, no=SS-PC-MB0772, position=PCIE_1_06, score=300, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.851+08:00","@version":"1","message":"lock device:18-C0-4D-A9-A9-59 to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan32 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.861+08:00","@version":"1","message":"lock device:18-C0-4D-A9-A9-A7 to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan32 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:12.868+08:00","@version":"1","message":"[1f9ffea1] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.015+08:00","@version":"1","message":"lockDevice with ipList:[************, ************] - macList:[18-C0-4D-A9-A9-59, 18-C0-4D-A9-A9-A7],no:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.016+08:00","@version":"1","message":"自动分配SS-PC-MB0771(************),SS-PC-MB0772(************)给SSV7_4X1_256G-4X4_1T_256GB下的Plan32","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.016+08:00","@version":"1","message":"add 2 devices to plan: Plan32 ,expect num: 2, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.016+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6374, flash:SSV7_4X1_256G-4X4_1T_256GB, planEntity:OrderPlanEntity(id=113668, orderId=6374, name=Plan32, status=QUEUE, priority=70)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.02+08:00","@version":"1","message":"测试单: YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB plan: Plan32已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.228+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.244+08:00","@version":"1","message":" add 2 devices :[{ SS-PC-MB0774,************,18-C0-4D-A9-A4-40,[{5V掉电=1}, {5V掉电热插拔=1}] }, { SS-PC-MB0775,*************,18-C0-4D-A9-A2-D5,[{5V掉电=1}, {5V掉电热插拔=1}, {MARS单盘=1}] }]  to plan:Plan27","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.267+08:00","@version":"1","message":"plan:Plan27 assign info update to ActualDeviceNum: 2, ExceptedSampleNum: 2","logger_name":"com.yeestor.work_order.service.plan.PlanAssignService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.274+08:00","@version":"1","message":"[YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB] - Plan27 holdDevices  :[PlanDeviceEntity(id=277127, orderId=6374, planId=113660, planName=Plan27, ip=************, mac=18-C0-4D-A9-A4-40, no=SS-PC-MB0774, position=PCIE_1_09, score=200, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null), PlanDeviceEntity(id=277128, orderId=6374, planId=113660, planName=Plan27, ip=*************, mac=18-C0-4D-A9-A2-D5, no=SS-PC-MB0775, position=PCIE_1_10, score=300, owner=, testNum=1, actualNum=null, version=0, status=OCCUPIED, failReason=null, releaseAt=null, releaseBy=null, releasePerson=null, startAt=null, endAt=null, confirmAt=null, terminateAt=null, terminateBy=null, terminatePerson=null, addedBy=null, addedPerson=null, operator=null)] with status: OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.276+08:00","@version":"1","message":"lock device:18-C0-4D-A9-A4-40 to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan27 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.29+08:00","@version":"1","message":"lock device:18-C0-4D-A9-A2-D5 to orderNo:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB planName:Plan27 devStatus:OCCUPIED","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.296+08:00","@version":"1","message":"[21df0a69] HTTP POST http://ereport.yeestor.com/wo/device/lock","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.437+08:00","@version":"1","message":"lockDevice with ipList:[************, *************] - macList:[18-C0-4D-A9-A4-40, 18-C0-4D-A9-A2-D5],no:YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB got data: HandleResp(code=0, data=, msg=加锁成功！, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.437+08:00","@version":"1","message":"自动分配SS-PC-MB0774(************),SS-PC-MB0775(*************)给SSV7_4X1_256G-4X4_1T_256GB下的Plan27","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.437+08:00","@version":"1","message":"add 2 devices to plan: Plan27 ,expect num: 2, left num: 0","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.438+08:00","@version":"1","message":"updatePlanStatusToReady orderId:6374, flash:SSV7_4X1_256G-4X4_1T_256GB, planEntity:OrderPlanEntity(id=113660, orderId=6374, name=Plan27, status=QUEUE, priority=50)","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.441+08:00","@version":"1","message":"测试单: YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB plan: Plan27已分配完成","logger_name":"com.yeestor.work_order.service.NotificationService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.646+08:00","@version":"1","message":"Reading to [com.yeestor.model.http.HandleResp<java.lang.String>]","logger_name":"org.springframework.web.client.HttpMessageConverterExtractor","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.647+08:00","@version":"1","message":"此次分配flash批次 SSV7_4X1_256G-4X4_1T_256GB 下的Plan共消耗 10 样片","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.652+08:00","@version":"1","message":"更新Flash:SSV7_4X1_256G-4X4_1T_256GB的样片数量从59变更为49","logger_name":"com.yeestor.work_order.service.order.FlashService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:18:13.713+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35838f289124b80f","spanId":"35838f289124b80f","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:22:02.612+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=49)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.612+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.63+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.65+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.678+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.693+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.71+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.724+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.738+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.738+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.739+08:00","@version":"1","message":"执行预分配前共有49颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.739+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.74+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.74+08:00","@version":"1","message":" getValidPlanList sumList: [2, 4, 6, 8] leftFlashNum:49 index:4 plans:[OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:22:02.74+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.74+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.74+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:22:02.74+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44be42e34e5f34bc","spanId":"44be42e34e5f34bc","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:26:02.614+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=49)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.615+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.678+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.701+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.723+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.739+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.754+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.772+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.788+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.805+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.805+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.806+08:00","@version":"1","message":"执行预分配前共有49颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.806+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.806+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.806+08:00","@version":"1","message":" getValidPlanList sumList: [2, 4, 6, 8] leftFlashNum:49 index:4 plans:[OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:26:02.807+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.807+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.807+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:02.807+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8773c0a99eeffa88","spanId":"8773c0a99eeffa88","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.625+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=49)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.625+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.644+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.658+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.673+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.687+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.708+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.722+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.736+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.752+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.752+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.753+08:00","@version":"1","message":"执行预分配前共有49颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.753+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.754+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.754+08:00","@version":"1","message":" getValidPlanList sumList: [2, 4, 6, 8] leftFlashNum:49 index:4 plans:[OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.754+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.755+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.755+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.755+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"450b63ac2470746c","spanId":"450b63ac2470746c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:34:02.614+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=49)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.615+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.656+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.671+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.685+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.699+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.714+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.728+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.745+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.745+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.745+08:00","@version":"1","message":"执行预分配前共有49颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.746+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.746+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.746+08:00","@version":"1","message":" getValidPlanList sumList: [2, 4, 6, 8] leftFlashNum:49 index:4 plans:[OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:34:02.746+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.746+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.747+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:34:02.747+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ac400201f4b034ec","spanId":"ac400201f4b034ec","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.617+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:38:02.618+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=49)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.618+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.638+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.652+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.667+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.683+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.699+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.715+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.73+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.749+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.75+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.751+08:00","@version":"1","message":"执行预分配前共有49颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.751+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.751+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.751+08:00","@version":"1","message":" getValidPlanList sumList: [2, 4, 6, 8] leftFlashNum:49 index:4 plans:[OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:38:02.752+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.752+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.752+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:02.752+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a246835e404cd649","spanId":"a246835e404cd649","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.63+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:02.631+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=49)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.632+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.657+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.682+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.698+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.712+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.736+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.751+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.765+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.78+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.78+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.78+08:00","@version":"1","message":"执行预分配前共有49颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.78+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.781+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.781+08:00","@version":"1","message":" getValidPlanList sumList: [2, 4, 6, 8] leftFlashNum:49 index:4 plans:[OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:42:02.781+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.781+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.781+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:42:02.781+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"66d3f99f4c90c9d3","spanId":"66d3f99f4c90c9d3","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.611+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:50:02.611+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=49)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.612+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.634+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.649+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.664+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.678+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.693+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.706+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.721+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.737+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.737+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.737+08:00","@version":"1","message":"执行预分配前共有49颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.737+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.738+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.738+08:00","@version":"1","message":" getValidPlanList sumList: [2, 4, 6, 8] leftFlashNum:49 index:4 plans:[OrderPlanEntity(id=113664, orderId=6374, name=Plan68, status=QUEUE, priority=50), OrderPlanEntity(id=113665, orderId=6374, name=Plan69, status=QUEUE, priority=50), OrderPlanEntity(id=113666, orderId=6374, name=Plan72, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:50:02.738+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.738+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.738+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:02.738+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13816e3b3df2194e","spanId":"13816e3b3df2194e","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.733+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.744+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=47)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.744+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.775+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.798+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.814+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.83+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.853+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.853+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.853+08:00","@version":"1","message":"执行预分配前共有47颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.854+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.854+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.855+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:47 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.856+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.856+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.856+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.856+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9c33e433f2181ca6","spanId":"9c33e433f2181ca6","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.705+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:10:02.707+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=47)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.708+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.731+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.749+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.766+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.781+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.799+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.799+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.8+08:00","@version":"1","message":"执行预分配前共有47颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.801+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.802+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.802+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:47 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:10:02.803+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.803+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.804+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:10:02.804+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"baea4d31ffd768ce","spanId":"baea4d31ffd768ce","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.767+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.769+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=47)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.769+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.787+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.802+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.817+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.831+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.847+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.847+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.847+08:00","@version":"1","message":"执行预分配前共有47颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.847+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.847+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.848+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:47 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.848+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.848+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.848+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.848+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c754eb247a973945","spanId":"c754eb247a973945","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.678+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:22:02.679+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=47)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.68+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.699+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.714+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.728+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.748+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.764+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.764+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.764+08:00","@version":"1","message":"执行预分配前共有47颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.764+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.765+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.765+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:47 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:22:02.765+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.765+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.766+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:22:02.766+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8eeeedfc8920031c","spanId":"8eeeedfc8920031c","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.775+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.783+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=49)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.783+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.807+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.823+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.839+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.856+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.873+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.874+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.874+08:00","@version":"1","message":"执行预分配前共有49颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.874+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.874+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.875+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:49 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.875+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.875+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.875+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.875+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d6fc6b89f06d23b","spanId":"8d6fc6b89f06d23b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:32:33.914+08:00","@version":"1","message":"execute:PCIe_18-C0-4D-A9-A8-17_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113583 mac:18-C0-4D-A9-A8-17","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3bab038a413c20ed","spanId":"3bab038a413c20ed","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T14:32:33.917+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0768[18-C0-4D-A9-A8-17]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3bab038a413c20ed","spanId":"3bab038a413c20ed","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T14:32:33.918+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0768]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3bab038a413c20ed","spanId":"3bab038a413c20ed","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T14:32:33.918+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[18-C0-4D-A9-A8-17])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3bab038a413c20ed","spanId":"3bab038a413c20ed","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T14:32:33.919+08:00","@version":"1","message":"[5ea986bb] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3bab038a413c20ed","spanId":"3bab038a413c20ed","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T14:32:34.049+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[18-C0-4D-A9-A8-17]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=18-C0-4D-A9-A8-17, pc_no=MB0768, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3bab038a413c20ed","spanId":"3bab038a413c20ed","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T14:32:34.049+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3bab038a413c20ed","spanId":"3bab038a413c20ed","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T15:49:24.925+08:00","@version":"1","message":"execute:PCIe_18-C0-4D-6C-66-B8_deviceShutdown  map key:[subProduct, orderId, planId, userName, title, mac, flash] subProduct:PCIe userName:eSee.system title:电脑自动释放后电脑关机 planId:113651 mac:18-C0-4D-6C-66-B8","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"24a21d5933b99a3d","spanId":"24a21d5933b99a3d","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T15:49:25.088+08:00","@version":"1","message":"倒计时结束，即将释放设备SS-PC-MB0397[18-C0-4D-6C-66-B8]","logger_name":"com.yeestor.work_order.service.job.TimedShutdownDeviceJob","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"24a21d5933b99a3d","spanId":"24a21d5933b99a3d","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T15:49:25.11+08:00","@version":"1","message":"需要关机的设备编号有: [SS-PC-MB0397]","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"24a21d5933b99a3d","spanId":"24a21d5933b99a3d","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T15:49:25.111+08:00","@version":"1","message":"即将关闭下列电脑设备: DeviceControlParams(username=eSee.system, product=PCIe, macList=[18-C0-4D-6C-66-B8])","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"24a21d5933b99a3d","spanId":"24a21d5933b99a3d","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T15:49:25.208+08:00","@version":"1","message":"[15817bbd] HTTP POST http://ereport.yeestor.com/wo/device/shutdown","logger_name":"org.springframework.web.reactive.function.client.ExchangeFunctions","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"24a21d5933b99a3d","spanId":"24a21d5933b99a3d","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T15:49:25.696+08:00","@version":"1","message":"shutdownDevice with DeviceControlParams(username=eSee.system, product=PCIe, macList=[18-C0-4D-6C-66-B8]) got data:HandleResp(code=0, data=DeviceControlResp(successLst=[DeviceControlResp.DeviceControlResult(ip=************, mac=18-C0-4D-6C-66-B8, pc_no=MB0397, reason=)], failLst=[]), msg=, total=null, page=null)","logger_name":"com.yeestor.work_order.utils.RMSApis","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"24a21d5933b99a3d","spanId":"24a21d5933b99a3d","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T15:49:25.698+08:00","@version":"1","message":"关机失败的电脑设备: []","logger_name":"com.yeestor.work_order.service.device.RMSDeviceService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"24a21d5933b99a3d","spanId":"24a21d5933b99a3d","no":"6374","traceType":"TimedShutdownDeviceJob","flash":"SSV7_4X1_256G-4X4_1T_256GB"}
{"@timestamp":"2025-07-24T15:50:03.016+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:03.029+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=53)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.03+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.066+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.082+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.099+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.116+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.133+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.134+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.134+08:00","@version":"1","message":"执行预分配前共有53颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.135+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.135+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.137+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:53 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:03.137+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.138+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.138+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:03.138+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c27e481b83a0113d","spanId":"c27e481b83a0113d","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.794+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.795+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=53)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.796+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.823+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.84+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.866+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.889+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.91+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.91+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.911+08:00","@version":"1","message":"执行预分配前共有53颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.912+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.912+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.913+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:53 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.913+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.913+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.913+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.914+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"312e1ec3b653672b","spanId":"312e1ec3b653672b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.79+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:02.802+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=53)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.802+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.824+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.841+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.863+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.88+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.896+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.896+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.896+08:00","@version":"1","message":"执行预分配前共有53颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.897+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.897+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.899+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:53 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:02.916+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.916+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.917+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:02.917+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"11c69ad0041a5440","spanId":"11c69ad0041a5440","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.761+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.767+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=53)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.767+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.799+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.816+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.839+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.867+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.894+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.894+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.894+08:00","@version":"1","message":"执行预分配前共有53颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.894+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.895+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.895+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:53 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.895+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.895+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.896+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.896+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acc0e143c50f479b","spanId":"acc0e143c50f479b","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.691+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:10:02.692+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=53)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.692+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.71+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.724+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.739+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.754+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.768+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.768+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.768+08:00","@version":"1","message":"执行预分配前共有53颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.768+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.768+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.768+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:53 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:10:02.769+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.769+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.769+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:10:02.769+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"682c1ffc6e4d9e63","spanId":"682c1ffc6e4d9e63","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.726+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.727+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=53)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.727+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.751+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.771+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.795+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.827+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.842+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.842+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.842+08:00","@version":"1","message":"执行预分配前共有53颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.843+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.843+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.843+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:53 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.843+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.844+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.845+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.845+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9b718c6aef1a7308","spanId":"9b718c6aef1a7308","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.693+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:02.694+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=53)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.694+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.713+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.728+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.743+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.76+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.776+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.776+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.776+08:00","@version":"1","message":"执行预分配前共有53颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.776+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.777+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.777+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:53 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:02.777+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.777+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.777+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:02.777+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2bb8925d7675929a","spanId":"2bb8925d7675929a","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.397+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.423+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6608, orderId=6374, flash=SSV7_4X1_256G-4X4_1T_256GB, orderFlashNo=YS9205##MP33B00102#00102#SSV7########250145_SSV7_4X1_256G-4X4_1T_256GB, num=100, leftNum=53)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.439+08:00","@version":"1","message":"SSD assign subProduct: PCIe ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.485+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.513+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.548+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.575+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.602+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.606+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113590, orderId=6374, name=Plan15, status=QUEUE, priority=75), OrderPlanEntity(id=113591, orderId=6374, name=Plan16, status=QUEUE, priority=75), OrderPlanEntity(id=113598, orderId=6374, name=Plan33, status=QUEUE, priority=70), OrderPlanEntity(id=113595, orderId=6374, name=Plan26, status=QUEUE, priority=50), OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.606+08:00","@version":"1","message":"执行预分配前共有53颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.609+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.609+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.609+08:00","@version":"1","message":" getValidPlanList sumList: [2] leftFlashNum:53 index:1 plans:[OrderPlanEntity(id=113667, orderId=6374, name=Plan74, status=QUEUE, priority=50)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.61+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.61+08:00","@version":"1","message":"[6374] - [SSV7_4X1_256G-4X4_1T_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.611+08:00","@version":"1","message":"[PCIe] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f8a773914d6bee1","spanId":"2f8a773914d6bee1","context":"QueueService","no":"6374","traceType":"分配设备"}
