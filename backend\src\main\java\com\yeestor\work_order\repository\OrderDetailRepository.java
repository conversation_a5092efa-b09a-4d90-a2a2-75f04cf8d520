package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.OrderDetailEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface OrderDetailRepository extends JpaRepository<OrderDetailEntity, Long> {

    OrderDetailEntity findByOrderId(long oldOrderId);

    @Query("select d from WorkOrder o, OrderDetail d where o.product = :product and o.subProduct = :subProduct and o.feature = :feature and o.id = d.orderId")
    List<OrderDetailEntity> findAllOrderDetailByProductAndBySubProduct(String product, String subProduct, int feature);
}
