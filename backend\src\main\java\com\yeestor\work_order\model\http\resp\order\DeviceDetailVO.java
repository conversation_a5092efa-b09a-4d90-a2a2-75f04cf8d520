package com.yeestor.work_order.model.http.resp.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.model.rms.AttrModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "详细 device 信息")
public class DeviceDetailVO {
    @ApiModelProperty("PC 的编号")
    private String pcNo ;

    @ApiModelProperty("测试机的IP地址")
    private String ip ;

    @ApiModelProperty("测试机的mac地址")
    private String mac ;

    @ApiModelProperty("测试机的位置")
    private String position ;

    @ApiModelProperty("理论上应该给测试机分配的样片数量")
    private int testNum ;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty("实际上已经给测试机分配的样片数量")
    private Integer actualNum ;

    @ApiModelProperty("测试机的分数；分数越高，表示机器越稀有，越不容易被抢占")
    private int score ;

    @ApiModelProperty("样片测试结果信息")
    private List<DeviceSamplesVO> samplesNoteList;

    @ApiModelProperty("设备的状态")
    private PlanDeviceEntity.Status status ;

    @ApiModelProperty("测试机所包含的属性列表")
    private List<AttrModel> attrModelList ;

    @ApiModelProperty("测试失败的原因")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String failReason ;


    @ApiModelProperty("设备释放的时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long releaseAt ;

    @ApiModelProperty("释放设备的人")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String releasePerson ;

    @ApiModelProperty("停止设备的时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long terminateAt ;

    @ApiModelProperty("停止设备的人")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String terminatePerson ;


    public void setInfo(PlanDeviceEntity planDeviceEntity) {
        this.pcNo = planDeviceEntity.getNo() ;
        this.ip = planDeviceEntity.getIp() ;
        this.mac = planDeviceEntity.getMac();
        this.position = planDeviceEntity.getPosition() ;
        this.score = planDeviceEntity.getScore() ;
        this.status = planDeviceEntity.getStatus() ;
        this.failReason = planDeviceEntity.getFailReason() ;
        this.testNum = planDeviceEntity.getTestNum();
        this.releaseAt = planDeviceEntity.getReleaseAt();
        this.releasePerson = planDeviceEntity.getReleasePerson();
        this.actualNum = planDeviceEntity.getActualNum();
        this.terminateAt = planDeviceEntity.getTerminateAt();
        this.terminatePerson = planDeviceEntity.getTerminatePerson();
    }
}
