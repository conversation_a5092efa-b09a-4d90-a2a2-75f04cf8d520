package com.yeestor.work_order.controller.order;

import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.entity.order.OrderReasonEntity;
import com.yeestor.work_order.service.order.OrderReasonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/order/reason")
@Api(tags = {"OrderReason"}, value = "获取原因信息")
public class OrderReasonController {

    private final OrderReasonService orderReasonService;

    @GetMapping("/{belong}/{type}")
    @ApiOperation(value = "获取工单相关原因信息", notes = "获取工单相关原因信息")
    public HandleResp<List<OrderReasonEntity>> fetchReasonList(
            @ApiParam(value= "所属类型") @PathVariable("belong") String belong,
            @ApiParam(value= "类型") @PathVariable("type") String type
    ){
        return HandleResp.ok(orderReasonService.findReasonByTypeAndBelong(belong, type),"获取原因成功！");
    }
}
