package com.yeestor.work_order.model.http.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
@ApiModel(value="AddTempPlanReq",description = "添加临时Plan的参数")
public class AddTempPlanReq {
    /**
     * "desc": "描述",
     * "priority": "分数,数值",
     * "product": "产品线",
     * "subProduct": "产品",
     * "orderId": "操作时的工单ID，只做记录",
     * "testNum": "预期测试样片数量"
     */

    @Size(min = 1,max = 20,message = "标题长度为1~20")
    @ApiModelProperty("plan标题")
    private String title;

    @NotBlank(message = "Plan描述不能为空")
    @ApiModelProperty("plan 描述")
    private String desc ;

    @ApiModelProperty("分数")
    private int priority ;

    @ApiModelProperty("产品线")
    private String product ;

    @ApiModelProperty("产品")
    private String subProduct ;

    @ApiModelProperty("测试负责人")
    private Person owner ;

    @Min(value = 1, message = "无效的工单ID")
    @ApiModelProperty("操作时的工单ID")
    private long orderId ;

    @ApiModelProperty("预期测试样片数量")
    private int testNum ;



}
