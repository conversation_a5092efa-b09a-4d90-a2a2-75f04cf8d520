package com.yeestor.work_order.service.plan;

import com.yeestor.work_order.entity.device.DeviceTestHistoryEntity;
import com.yeestor.work_order.model.rms.AttrModel;
import com.yeestor.work_order.repository.device.DeviceTestHistoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TestHistoryService {
    private final DeviceTestHistoryRepository deviceTestHistoryRepository;

    public List<DeviceTestHistoryEntity> findDeviceTestHistoryByPlan(String subProduct, String planName, String belongTo) {
//        log.info("subProduct: {} planName: {} belongTo: {}", workOrderEntity.getSubProduct(), planEntity.getName(), planEntity.getBelongTo());
        return deviceTestHistoryRepository.findBySubProductAndPlanNameAndUserIdOrderByPositionAscNoAsc(subProduct, planName, belongTo);
    }

    /**
     * 获取连号的历史空闲设备（连号设备）
     *
     * @param freeTestList 空闲的Plan历史测试设备
     * @param testNum plan测试数量
     * @param attrs plan测试属性
     * @return 满足测试的mac地址
     */
    public List<String> findMostUsedAdjacentDeviceInHistory(List<DeviceTestHistoryEntity> freeTestList, int testNum, List<String> attrs) {
        if (freeTestList.size() == 0) {
            return new ArrayList<>();
        }
        // 先获取连号的设备情况
        return getHistoryMostUsedAdjacentDevice(freeTestList, attrs, testNum);
    }

    /**
     * 获取非连号的历史空闲设备
     * @param freeTestList 空闲的Plan历史测试设备
     * @param testNum plan测试数量
     * @param attrs plan测试属性
     * @return 满足测试的mac地址
     */
    // todo 此处后面要考虑非连号时尽量分配一个机柜的设备
    public List<String> findMostUsedDeviceInHistory(List<DeviceTestHistoryEntity> freeTestList, int testNum, List<String> attrs) {
        if (freeTestList.size() == 0) {
            return new ArrayList<>();
        }
        log.info("找不到满足条件的历史使用设备，并且设备满足连号，正在考虑使用非连号设备！");
        return getHistoryMostUsedDevice(freeTestList, attrs, testNum);
    }

    /**
     * 随机获取一个机柜下使用频率最高的一个设备组合
     * @param devices 一个机柜下常使用的设备
     * @param attrs 测试属性
     * @param testNum 测试数量
     * @return 测试设备组合
     */
    public List<String> getHistoryMostUsedDevice(List<DeviceTestHistoryEntity> devices, List<String> attrs, int testNum) {
        // 按照机柜位置进行分组
        Map<String, List<DeviceTestHistoryEntity>> groupedDevices = new HashMap<>();
        for (DeviceTestHistoryEntity device : devices) {
            String cabinet = getCabinetPosition(device.getPosition());
            if (!groupedDevices.containsKey(cabinet)) {
                groupedDevices.put(cabinet, new ArrayList<>());
            }
            groupedDevices.get(cabinet).add(device);
        }

        List<DeviceTestHistoryEntity> mostUsedDevices = new ArrayList<>();
        int maxUse = 0;
        for (List<DeviceTestHistoryEntity> cabinetDevices : groupedDevices.values()) {
            // 获取一个机柜下最常用的一个组合（不考虑连号）
            List<DeviceTestHistoryEntity> rackMostUsed = getMostUseDeviceGroup(cabinetDevices, testNum, attrs);
            int sum = rackMostUsed.stream()
                    .mapToInt(DeviceTestHistoryEntity::getUseNum)
                    .sum();
            if (sum > maxUse) {
                maxUse = sum;
                mostUsedDevices.clear();
                mostUsedDevices.addAll(rackMostUsed);
            }
        }
        return mostUsedDevices.stream().map(DeviceTestHistoryEntity::getMac).collect(Collectors.toList());
    }

    /**
     * 获取当前设备下最常用的设备信息，不考虑连号问题
     * @param devices  设备信息
     * @param testNum 测试数量
     * @param attrs 测试属性
     * @return 常用组合
     */
    public List<DeviceTestHistoryEntity> getMostUseDeviceGroup(List<DeviceTestHistoryEntity> devices, int testNum, List<String> attrs) {
        devices.sort(Comparator.comparingInt(DeviceTestHistoryEntity::getUseNum).reversed());
        List<DeviceTestHistoryEntity> consecutiveSubsequences = new ArrayList<>();
        int sum = 0;
        for (DeviceTestHistoryEntity device : devices) {
            sum += findMinNum(attrs, device.getAttrModelList());
            consecutiveSubsequences.add(device);
            if (sum > testNum) {
                break;
            }
        }
        return consecutiveSubsequences;
    }

    /**
     * 一个Plan下所有空闲的历史测试设备的最常使用的连号组合
     *
     * @param devices 所有空闲设备
     * @param attrs 需要分配的Plan属性
     * @param testNum 需要的设备数量
     * @return 最常用的设备列表
     */
    public List<String> getHistoryMostUsedAdjacentDevice(List<DeviceTestHistoryEntity> devices, List<String> attrs, int testNum) {
        // 按照机柜位置进行分组
        Map<String, List<DeviceTestHistoryEntity>> groupedDevices = new HashMap<>();
        for (DeviceTestHistoryEntity device : devices) {
            String cabinet = getCabinetPosition(device.getPosition());
            if (!groupedDevices.containsKey(cabinet)) {
                groupedDevices.put(cabinet, new ArrayList<>());
            }
            groupedDevices.get(cabinet).add(device);
        }

        List<DeviceTestHistoryEntity> mostUsedDevices = new ArrayList<>();
        int maxUse = 0;
        for (List<DeviceTestHistoryEntity> cabinetDevices : groupedDevices.values()) {
            List<DeviceTestHistoryEntity> rackMostUsed = getOneRackMostUsedDevices(cabinetDevices, testNum, attrs);
//            log.info("rackMostUsed: {}", rackMostUsed.stream().map(DeviceTestHistoryEntity::getPosition).collect(Collectors.toList()));
            int sum = rackMostUsed.stream()
                    .mapToInt(DeviceTestHistoryEntity::getUseNum)
                    .sum();
            if (sum > maxUse) {
                maxUse = sum;
                mostUsedDevices.clear();
                mostUsedDevices.addAll(rackMostUsed);
            }
        }
//        log.info("mostUsedDevices: {}", mostUsedDevices.stream().map(DeviceTestHistoryEntity::getPosition).collect(Collectors.toList()));
        return mostUsedDevices.stream().map(DeviceTestHistoryEntity::getMac).collect(Collectors.toList());
    }

    /**
     * 找出一个机柜满足连续N个机器的组合形式
     *
     * @param devices 当前机柜的所有空闲设备
     * @param testNum 需要N个连续的设备
     * @param attrs 需要分配的Plan属性
     * @return 返回二维列表
     */
    public List<DeviceTestHistoryEntity> getOneRackMostUsedDevices(List<DeviceTestHistoryEntity> devices, int testNum, List<String> attrs) {
        List<List<DeviceTestHistoryEntity>> groupedDevices = getConsecutiveSubsequences(devices, testNum, attrs);
        int maxSum = 0;
        List<DeviceTestHistoryEntity> result = new ArrayList<>();
        for (List<DeviceTestHistoryEntity> group : groupedDevices) {
            int sum = group.stream()
                    .mapToInt(DeviceTestHistoryEntity::getUseNum)
                    .sum();
            if (sum > maxSum) {
                maxSum = sum;
                result.clear();
                result.addAll(group);
            }
        }
        return result;
    }

    /**
     * 处于空闲且时历史使用过的设备，获取满足连续numPositions的定位数组
     * 例如[ [SSD5_4_20, SSD5_4_19], [SSD5_4_21,SSD5_4_22] ]
     *
     * @param devices 空闲数组
     * @param testNum 连续个数
     * @param attrs 需要分配的Plan属性
     * @return 二位列表
     */
    private static List<List<DeviceTestHistoryEntity>> getConsecutiveSubsequences(List<DeviceTestHistoryEntity> devices, int testNum, List<String> attrs) {
        devices.sort(Comparator.comparingInt(DeviceTestHistoryEntity::getNumber).reversed());
//        log.info("getConsecutiveSubsequences devices: {}", devices.stream().map(DeviceTestHistoryEntity::getPosition).collect(Collectors.toList()));
        List<List<DeviceTestHistoryEntity>> consecutiveSubsequences = new ArrayList<>();
        for (int i = 0; i < devices.size(); i++) {
            boolean isConsecutive = true;
            int size = 0;
            int sum = 0;
            for (int j = 0; j < devices.size() - i - 1; j++) {      // 此处只需要遍历到数组的倒数第二个即可
                if (devices.get(i + j).getNumber() - devices.get(i + j + 1).getNumber() != 1) {
                    isConsecutive = false;
                    break;
                } else {
                    size++;
                    sum += findMinNum(attrs, devices.get(i + j).getAttrModelList());
                    if (i + j + 1 == devices.size()) {
                        size++;
                    }
                    if (sum >= testNum) {
                        break;
                    }
                }
            }

            if (isConsecutive) {
                List<DeviceTestHistoryEntity> subsequence = new ArrayList<>();
                for (int j = 0; j < size; j++) {
                    subsequence.add(devices.get(i + j));
                }
                consecutiveSubsequences.add(subsequence);
            }
        }
//        log.info("consecutiveSubsequences: {}", consecutiveSubsequences);
        return consecutiveSubsequences;
    }

    /**
     * 从位置字符串中获取机柜信息
     *
     * @param position 机柜位置 PCIE_5_06机柜为PCIE_5
     * @return 机柜编号
     */
    private static String getCabinetPosition(String position) {
        int firstUnderscoreIndex = position.indexOf('_');
        int secondUnderscoreIndex = position.indexOf('_', firstUnderscoreIndex + 1);
        if (secondUnderscoreIndex != -1) {
            return position.substring(0, secondUnderscoreIndex);
        }
        return position;
    }

    /**
     * 检查该设备满足属性信息的最大测试数量
     *
     * @param attributes    属性列表
     * @param attributeList 属性测试信息表
     * @return 满足测试的最大数量
     */
    public static int findMinNum(List<String> attributes, List<AttrModel> attributeList) {
        int minNum = Integer.MAX_VALUE;
        if (attributeList.size() == 0) {
            return 0;
        }

        for (String attribute : attributes) {
            for (AttrModel attr : attributeList) {
                if (attr.getAttrName().equals(attribute) && attr.getNum() < minNum) {
                    minNum = attr.getNum();
                }
            }
        }

        return minNum;
    }
}
