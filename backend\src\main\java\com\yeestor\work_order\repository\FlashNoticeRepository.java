package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.FlashNoticeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FlashNoticeRepository extends JpaRepository<FlashNoticeEntity, Long> {
    @Query("select f.outTrackId from FlashNotice f where f.orderId = :orderId and f.flash = :flashName")
    List<String> findUUIDByOrderIdAndFlash(long orderId, String flashName);
}
