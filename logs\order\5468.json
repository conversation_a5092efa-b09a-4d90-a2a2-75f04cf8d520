{"@timestamp":"2025-07-23T15:14:22.07+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b718ccb531b1b75","spanId":"2b718ccb531b1b75","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:14:22.079+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b718ccb531b1b75","spanId":"2b718ccb531b1b75","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:22.083+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b718ccb531b1b75","spanId":"2b718ccb531b1b75","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:22.083+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b718ccb531b1b75","spanId":"2b718ccb531b1b75","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:14:22.083+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2b718ccb531b1b75","spanId":"2b718ccb531b1b75","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:22.023+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db1c9fee75d8d033","spanId":"db1c9fee75d8d033","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:26:22.024+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db1c9fee75d8d033","spanId":"db1c9fee75d8d033","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:22.027+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db1c9fee75d8d033","spanId":"db1c9fee75d8d033","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:22.027+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db1c9fee75d8d033","spanId":"db1c9fee75d8d033","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:26:22.027+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"db1c9fee75d8d033","spanId":"db1c9fee75d8d033","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:22.181+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f20f7ec7d2bfb079","spanId":"f20f7ec7d2bfb079","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:50:22.191+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f20f7ec7d2bfb079","spanId":"f20f7ec7d2bfb079","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:22.195+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f20f7ec7d2bfb079","spanId":"f20f7ec7d2bfb079","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:22.195+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f20f7ec7d2bfb079","spanId":"f20f7ec7d2bfb079","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:50:22.195+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f20f7ec7d2bfb079","spanId":"f20f7ec7d2bfb079","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:53:22.252+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921657b4b3d7bb3","spanId":"c921657b4b3d7bb3","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:53:22.254+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921657b4b3d7bb3","spanId":"c921657b4b3d7bb3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:53:22.258+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921657b4b3d7bb3","spanId":"c921657b4b3d7bb3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:53:22.258+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921657b4b3d7bb3","spanId":"c921657b4b3d7bb3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:53:22.258+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c921657b4b3d7bb3","spanId":"c921657b4b3d7bb3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:59:22.107+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b113a2ff54d7e5a8","spanId":"b113a2ff54d7e5a8","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:59:22.109+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b113a2ff54d7e5a8","spanId":"b113a2ff54d7e5a8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:59:22.113+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b113a2ff54d7e5a8","spanId":"b113a2ff54d7e5a8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:59:22.113+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b113a2ff54d7e5a8","spanId":"b113a2ff54d7e5a8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:59:22.113+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b113a2ff54d7e5a8","spanId":"b113a2ff54d7e5a8","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:22.039+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6704d076b7e79cd2","spanId":"6704d076b7e79cd2","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:02:22.04+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6704d076b7e79cd2","spanId":"6704d076b7e79cd2","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:22.043+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6704d076b7e79cd2","spanId":"6704d076b7e79cd2","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:22.043+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6704d076b7e79cd2","spanId":"6704d076b7e79cd2","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:02:22.043+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6704d076b7e79cd2","spanId":"6704d076b7e79cd2","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:05:22.061+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be251268ea90f288","spanId":"be251268ea90f288","no":"5468","traceType":"分配设备","context":"QueueService","flash":"CY-1583-8T2F-A_8GB","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:05:22.063+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be251268ea90f288","spanId":"be251268ea90f288","no":"5468","traceType":"分配设备","context":"QueueService","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:05:22.065+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be251268ea90f288","spanId":"be251268ea90f288","no":"5468","traceType":"分配设备","context":"QueueService","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:05:22.065+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be251268ea90f288","spanId":"be251268ea90f288","no":"5468","traceType":"分配设备","context":"QueueService","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:05:22.065+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be251268ea90f288","spanId":"be251268ea90f288","no":"5468","traceType":"分配设备","context":"QueueService","flash":"CY-1583-8T2F-A_8GB"}
{"@timestamp":"2025-07-23T16:11:22.116+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef820836586105b7","spanId":"ef820836586105b7","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:11:22.121+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef820836586105b7","spanId":"ef820836586105b7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:11:22.125+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef820836586105b7","spanId":"ef820836586105b7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:11:22.125+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef820836586105b7","spanId":"ef820836586105b7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:11:22.125+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef820836586105b7","spanId":"ef820836586105b7","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:17:24.752+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f36c9e128b7ba8d6","spanId":"f36c9e128b7ba8d6","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:17:24.766+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f36c9e128b7ba8d6","spanId":"f36c9e128b7ba8d6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:17:24.773+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f36c9e128b7ba8d6","spanId":"f36c9e128b7ba8d6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:17:24.774+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f36c9e128b7ba8d6","spanId":"f36c9e128b7ba8d6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:17:24.774+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f36c9e128b7ba8d6","spanId":"f36c9e128b7ba8d6","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:20:22.052+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daff3d97716fcb45","spanId":"daff3d97716fcb45","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:20:22.055+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daff3d97716fcb45","spanId":"daff3d97716fcb45","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:20:22.057+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daff3d97716fcb45","spanId":"daff3d97716fcb45","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:20:22.058+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daff3d97716fcb45","spanId":"daff3d97716fcb45","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:20:22.058+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"daff3d97716fcb45","spanId":"daff3d97716fcb45","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:23:22.061+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6cba4f1cafe4ced1","spanId":"6cba4f1cafe4ced1","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:23:22.063+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6cba4f1cafe4ced1","spanId":"6cba4f1cafe4ced1","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:23:22.065+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6cba4f1cafe4ced1","spanId":"6cba4f1cafe4ced1","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:23:22.065+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6cba4f1cafe4ced1","spanId":"6cba4f1cafe4ced1","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:23:22.065+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6cba4f1cafe4ced1","spanId":"6cba4f1cafe4ced1","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:22.193+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"97104bbf95bdb612","spanId":"97104bbf95bdb612","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:26:22.195+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"97104bbf95bdb612","spanId":"97104bbf95bdb612","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:22.199+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"97104bbf95bdb612","spanId":"97104bbf95bdb612","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:22.199+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"97104bbf95bdb612","spanId":"97104bbf95bdb612","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:26:22.199+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"97104bbf95bdb612","spanId":"97104bbf95bdb612","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:29:22.057+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bf8468fab779b868","spanId":"bf8468fab779b868","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:29:22.059+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bf8468fab779b868","spanId":"bf8468fab779b868","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:29:22.061+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bf8468fab779b868","spanId":"bf8468fab779b868","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:29:22.061+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bf8468fab779b868","spanId":"bf8468fab779b868","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:29:22.061+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bf8468fab779b868","spanId":"bf8468fab779b868","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:35:22.161+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"815963f0c69177bf","spanId":"815963f0c69177bf","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:35:22.163+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"815963f0c69177bf","spanId":"815963f0c69177bf","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:35:22.166+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"815963f0c69177bf","spanId":"815963f0c69177bf","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:35:22.166+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"815963f0c69177bf","spanId":"815963f0c69177bf","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:35:22.166+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"815963f0c69177bf","spanId":"815963f0c69177bf","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:22.139+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1d161d119c1c8af","spanId":"c1d161d119c1c8af","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:38:22.14+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1d161d119c1c8af","spanId":"c1d161d119c1c8af","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:22.146+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1d161d119c1c8af","spanId":"c1d161d119c1c8af","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:22.146+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1d161d119c1c8af","spanId":"c1d161d119c1c8af","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:38:22.146+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1d161d119c1c8af","spanId":"c1d161d119c1c8af","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:41:22.034+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0a0a90ca277a4f","spanId":"cd0a0a90ca277a4f","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:41:22.035+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0a0a90ca277a4f","spanId":"cd0a0a90ca277a4f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:41:22.037+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0a0a90ca277a4f","spanId":"cd0a0a90ca277a4f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:41:22.037+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0a0a90ca277a4f","spanId":"cd0a0a90ca277a4f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:41:22.037+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cd0a0a90ca277a4f","spanId":"cd0a0a90ca277a4f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:44:21.982+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef80885354001b0e","spanId":"ef80885354001b0e","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:44:21.984+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef80885354001b0e","spanId":"ef80885354001b0e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:44:21.986+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef80885354001b0e","spanId":"ef80885354001b0e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:44:21.987+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef80885354001b0e","spanId":"ef80885354001b0e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:44:21.987+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ef80885354001b0e","spanId":"ef80885354001b0e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:47:21.99+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d822e641a729a00","spanId":"6d822e641a729a00","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:47:21.992+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d822e641a729a00","spanId":"6d822e641a729a00","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:47:21.994+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d822e641a729a00","spanId":"6d822e641a729a00","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:47:21.994+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d822e641a729a00","spanId":"6d822e641a729a00","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:47:21.994+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6d822e641a729a00","spanId":"6d822e641a729a00","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:22.187+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acfd0683251e738b","spanId":"acfd0683251e738b","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:50:22.188+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acfd0683251e738b","spanId":"acfd0683251e738b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:22.19+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acfd0683251e738b","spanId":"acfd0683251e738b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:22.19+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acfd0683251e738b","spanId":"acfd0683251e738b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:50:22.191+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"acfd0683251e738b","spanId":"acfd0683251e738b","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:21.566+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18ff91929b0dfffa","spanId":"18ff91929b0dfffa","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:21.58+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18ff91929b0dfffa","spanId":"18ff91929b0dfffa","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:21.586+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18ff91929b0dfffa","spanId":"18ff91929b0dfffa","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:21.586+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18ff91929b0dfffa","spanId":"18ff91929b0dfffa","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:21.586+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18ff91929b0dfffa","spanId":"18ff91929b0dfffa","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:08:22.246+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5eff7bd1f440d74","spanId":"b5eff7bd1f440d74","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:08:22.248+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5eff7bd1f440d74","spanId":"b5eff7bd1f440d74","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:08:22.252+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5eff7bd1f440d74","spanId":"b5eff7bd1f440d74","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:08:22.253+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5eff7bd1f440d74","spanId":"b5eff7bd1f440d74","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:08:22.253+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b5eff7bd1f440d74","spanId":"b5eff7bd1f440d74","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:11:21.459+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74c28e383755575f","spanId":"74c28e383755575f","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:11:21.461+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74c28e383755575f","spanId":"74c28e383755575f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:11:21.464+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74c28e383755575f","spanId":"74c28e383755575f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:11:21.465+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74c28e383755575f","spanId":"74c28e383755575f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:11:21.465+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74c28e383755575f","spanId":"74c28e383755575f","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:21.492+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ecbb165080af483d","spanId":"ecbb165080af483d","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:21.494+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ecbb165080af483d","spanId":"ecbb165080af483d","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:21.497+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ecbb165080af483d","spanId":"ecbb165080af483d","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:21.498+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ecbb165080af483d","spanId":"ecbb165080af483d","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:21.498+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ecbb165080af483d","spanId":"ecbb165080af483d","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:17:21.653+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"190a46612f1d493e","spanId":"190a46612f1d493e","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:17:21.655+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"190a46612f1d493e","spanId":"190a46612f1d493e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:17:21.66+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"190a46612f1d493e","spanId":"190a46612f1d493e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:17:21.661+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"190a46612f1d493e","spanId":"190a46612f1d493e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:17:21.661+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"190a46612f1d493e","spanId":"190a46612f1d493e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:23:21.559+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3cbbea80e19caba3","spanId":"3cbbea80e19caba3","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:23:21.56+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3cbbea80e19caba3","spanId":"3cbbea80e19caba3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:23:21.564+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3cbbea80e19caba3","spanId":"3cbbea80e19caba3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:23:21.564+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3cbbea80e19caba3","spanId":"3cbbea80e19caba3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:23:21.565+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3cbbea80e19caba3","spanId":"3cbbea80e19caba3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:21.534+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"534ff7c731760626","spanId":"534ff7c731760626","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:21.536+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"534ff7c731760626","spanId":"534ff7c731760626","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:21.539+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"534ff7c731760626","spanId":"534ff7c731760626","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:21.539+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"534ff7c731760626","spanId":"534ff7c731760626","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:21.539+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"534ff7c731760626","spanId":"534ff7c731760626","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:29:21.573+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35f00b23ab70d72a","spanId":"35f00b23ab70d72a","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:29:21.575+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35f00b23ab70d72a","spanId":"35f00b23ab70d72a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:29:21.578+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35f00b23ab70d72a","spanId":"35f00b23ab70d72a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:29:21.578+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35f00b23ab70d72a","spanId":"35f00b23ab70d72a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:29:21.578+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"35f00b23ab70d72a","spanId":"35f00b23ab70d72a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:35:21.486+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee6a654df3a1769e","spanId":"ee6a654df3a1769e","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:35:21.488+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee6a654df3a1769e","spanId":"ee6a654df3a1769e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:35:21.491+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee6a654df3a1769e","spanId":"ee6a654df3a1769e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:35:21.491+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee6a654df3a1769e","spanId":"ee6a654df3a1769e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:35:21.491+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ee6a654df3a1769e","spanId":"ee6a654df3a1769e","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:21.561+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"439f50b92fb884f9","spanId":"439f50b92fb884f9","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:50:21.578+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"439f50b92fb884f9","spanId":"439f50b92fb884f9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:21.585+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"439f50b92fb884f9","spanId":"439f50b92fb884f9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:21.586+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"439f50b92fb884f9","spanId":"439f50b92fb884f9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:50:21.586+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"439f50b92fb884f9","spanId":"439f50b92fb884f9","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:21.549+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f3bf4e5fd5f0e74c","spanId":"f3bf4e5fd5f0e74c","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:02:21.571+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f3bf4e5fd5f0e74c","spanId":"f3bf4e5fd5f0e74c","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:21.581+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f3bf4e5fd5f0e74c","spanId":"f3bf4e5fd5f0e74c","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:21.581+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f3bf4e5fd5f0e74c","spanId":"f3bf4e5fd5f0e74c","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:02:21.581+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f3bf4e5fd5f0e74c","spanId":"f3bf4e5fd5f0e74c","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:05:21.487+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"458ae42cbc366036","spanId":"458ae42cbc366036","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:05:21.489+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"458ae42cbc366036","spanId":"458ae42cbc366036","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:05:21.493+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"458ae42cbc366036","spanId":"458ae42cbc366036","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:05:21.493+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"458ae42cbc366036","spanId":"458ae42cbc366036","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:05:21.494+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"458ae42cbc366036","spanId":"458ae42cbc366036","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:08:21.576+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"437ae4793a4dad56","spanId":"437ae4793a4dad56","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:08:21.582+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"437ae4793a4dad56","spanId":"437ae4793a4dad56","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:08:21.586+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"437ae4793a4dad56","spanId":"437ae4793a4dad56","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:08:21.586+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"437ae4793a4dad56","spanId":"437ae4793a4dad56","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:08:21.587+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"437ae4793a4dad56","spanId":"437ae4793a4dad56","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:11:21.449+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f9444ccfc9d6939","spanId":"2f9444ccfc9d6939","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:11:21.45+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f9444ccfc9d6939","spanId":"2f9444ccfc9d6939","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:11:21.454+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f9444ccfc9d6939","spanId":"2f9444ccfc9d6939","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:11:21.454+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f9444ccfc9d6939","spanId":"2f9444ccfc9d6939","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:11:21.454+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2f9444ccfc9d6939","spanId":"2f9444ccfc9d6939","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:21.442+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4b13b1d084087b3","spanId":"b4b13b1d084087b3","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:21.443+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4b13b1d084087b3","spanId":"b4b13b1d084087b3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:21.446+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4b13b1d084087b3","spanId":"b4b13b1d084087b3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:21.446+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4b13b1d084087b3","spanId":"b4b13b1d084087b3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:21.446+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b4b13b1d084087b3","spanId":"b4b13b1d084087b3","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:17:21.491+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c164b895e38b779","spanId":"5c164b895e38b779","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:17:21.493+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c164b895e38b779","spanId":"5c164b895e38b779","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:17:21.496+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c164b895e38b779","spanId":"5c164b895e38b779","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:17:21.496+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c164b895e38b779","spanId":"5c164b895e38b779","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:17:21.496+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5c164b895e38b779","spanId":"5c164b895e38b779","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:21.437+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc8ad7f8e3d5de7a","spanId":"fc8ad7f8e3d5de7a","context":"QueueService","no":"5468","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:26:21.438+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=5653, orderId=5468, flash=X4_9060_PSLC_256GB, orderFlashNo=YS9205##MP33B00020#00020#X4_6090#####250000_X4_9060_PSLC_256GB, num=100, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc8ad7f8e3d5de7a","spanId":"fc8ad7f8e3d5de7a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:21.441+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc8ad7f8e3d5de7a","spanId":"fc8ad7f8e3d5de7a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:21.441+08:00","@version":"1","message":"[5468] - [X4_9060_PSLC_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc8ad7f8e3d5de7a","spanId":"fc8ad7f8e3d5de7a","context":"QueueService","no":"5468","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:26:21.441+08:00","@version":"1","message":"[IND_SATA] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc8ad7f8e3d5de7a","spanId":"fc8ad7f8e3d5de7a","context":"QueueService","no":"5468","traceType":"分配设备"}
