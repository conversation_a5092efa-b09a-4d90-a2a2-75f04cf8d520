// @ts-nocheck
import React from 'react';
import initialState from 'F:/Git/eSee/frontend/src/.umi/plugin-initial-state/models/initialState';
import model0 from "F:/Git/eSee/frontend/src/models/import/fae";
import model1 from "F:/Git/eSee/frontend/src/models/role/index";
import model2 from "F:/Git/eSee/frontend/src/models/workorder/product/flash";
import model3 from "F:/Git/eSee/frontend/src/models/workorder/product/flashConfirm";
import model4 from "F:/Git/eSee/frontend/src/models/workorder/product/flashReview";
import model5 from "F:/Git/eSee/frontend/src/models/workorder/product/index";
import model6 from "F:/Git/eSee/frontend/src/models/workorder/product/mobileFlash";
// @ts-ignore
import Dispatcher from './helpers/dispatcher';
// @ts-ignore
import Executor from './helpers/executor';
// @ts-ignore
import { UmiContext } from './helpers/constant';

export const models = { '@@initialState': initialState, 'import.fae': model0, 'role.index': model1, 'workorder.product.flash': model2, 'workorder.product.flashConfirm': model3, 'workorder.product.flashReview': model4, 'workorder.product.index': model5, 'workorder.product.mobileFlash': model6 };

export type Model<T extends keyof typeof models> = {
  [key in keyof typeof models]: ReturnType<typeof models[T]>;
};

export type Models<T extends keyof typeof models> = Model<T>[T]

const dispatcher = new Dispatcher!();
const Exe = Executor!;

export default ({ children }: { children: React.ReactNode }) => {

  return (
    <UmiContext.Provider value={dispatcher}>
      {
        Object.entries(models).map(pair => (
          <Exe key={pair[0]} namespace={pair[0]} hook={pair[1] as any} onUpdate={(val: any) => {
            const [ns] = pair as [keyof typeof models, any];
            dispatcher.data[ns] = val;
            dispatcher.update(ns);
          }} />
        ))
      }
      {children}
    </UmiContext.Provider>
  )
}
