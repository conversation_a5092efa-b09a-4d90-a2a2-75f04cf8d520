
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wo-dev-deployment
  namespace: yeestor
  labels:
    app: wo-dev-deployment
spec:
  replicas: 1
  template:
    metadata:
      name: wo-dev
      labels:
        app: wo-dev
    spec:
      containers:
        - name: wo-dev
          image: gateway.yeestor.com:5000/workorder:v0.0912.01
          imagePullPolicy: Always
          lifecycle:
            preStop:
              exec:
                command: [ "sh", "-c", "sleep 10" ]
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: dev
            - name: EUREKA_SERVER_ADDRESS
              value: http://eureka.yeestor.svc.cluster.local:8788/eureka
            - name: ADMIN_SERVER_URL
              value: http://eureka.yeestor.svc.cluster.local:8788/admin
            - name: EUREKA_HOSTNAME
              value: wo-dev
            - name: TZ
              value: Asia/Shanghai
      restartPolicy: Always

  selector:
    matchLabels:
      app: wo-dev
