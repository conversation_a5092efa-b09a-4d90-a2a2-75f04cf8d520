package com.yeestor.work_order.repository.review;

import com.yeestor.work_order.entity.review.ReviewItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ReviewItemRepository extends JpaRepository<ReviewItemEntity, Long> {
    List<ReviewItemEntity> findAllByResultId(long resultId);

    List<ReviewItemEntity> findAllByResultIdAndFlash(long resultId, String flash);

    List<ReviewItemEntity> findAllByOrderIdAndFlash(long orderId, String flash);
}
