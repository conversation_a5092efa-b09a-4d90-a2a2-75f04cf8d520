package com.yeestor.work_order.entity;

import com.yeestor.entity.BaseEntity;
import com.yeestor.work_order.model.rms.PlanModel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity(name = "TempPlan")
@Table(name="wo_temp_plan")
public class TempPlanEntity extends BaseEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "order_id")
    private long orderId ;

    /**
     * plan 标题
     */
    @Column(name = "title")
    private String title ;

    /**
     * plan 名称 , 如: plan1 .
     * 理论上 同一个批次的Flash下的plan 名称不会冲突
     */
    @Column(name = "name")
    private String name ;

    /**
     * Plan的描述
     */
    @Column(name = "feature", length = 512)
    private String feature ;
    /**
     * 优先级
     */
    @Column(name = "priority")
    private int priority ;

    /**
     * 测试类型。 手动或者自动测试。
     */
    @Column(name = "type")
    private int type ;

    /**
     * 所属产品线
     */
    @Column(name = "product")
    private String product ;

    /**
     * 所属产品
     */
    @Column(name = "sub_product")
    private String subProduct ;

    /**
     * 预期测试的样片的数量.
     */
    @Column(name = "test_num")
    private Integer testNum ;

    /**
     * 版本类型
     */
    @Column(name = "version_type")
    private String versionType ;

    /**
     * 添加人员信息
     */
    @Column(name = "added_by")
    private String addedBy;

    @Column(name = "added_p")
    private String addedPerson ;

    @Column(name = "owner_id")
    private String ownerId ;

    @Column(name = "owner_name")
    private String ownerName ;

    /**
     * plan 涉及到的脚本名称, 以分号为分隔符。
     *
     */
    @Column(name = "scripts")
    private String scripts;

    public PlanModel convert2PlanModel(){
        return PlanModel.builder()
                .title(title)
                .name(name)
                .feature(feature)
                .attrs(new ArrayList<>())
                .type(type)
                .testNum(Optional.ofNullable(testNum).orElse(0))
                .product(product)
                .subProduct(subProduct)
                .priority(priority)
                .versionType(versionType)
                .tempPlanId(id)
                .parent(new ArrayList<>())
                .belongToPerson(ownerName)
                .belongTo(ownerId)
                .build();
    }

    public OrderPlanEntity convert2PlanEntity(String planType, OrderPlanEntity.Phase phase, String flashName, List<String> attrList){
        OrderPlanEntity entity = new OrderPlanEntity();
        entity.setCreatedAt(System.currentTimeMillis());
        entity.setName(name);
        entity.setFeature(feature);
        entity.setPhase(phase);
        entity.setType(1);
        entity.setAttrs(String.join(";", new ArrayList<>()));
        entity.setTestNum(0);
        entity.setPriority(0);
        entity.setNeedReport(false);
        entity.setBelongTo(ownerId);
        entity.setBelongToPerson(ownerName);
        entity.setTestAll(false);
        entity.setOrderId(orderId);
        entity.setFlash(flashName);
        entity.setReadyAt(System.currentTimeMillis());
        entity.setConfirmedAt(System.currentTimeMillis());
        entity.setConfirmedPerson("System");
        entity.setStatus(OrderPlanEntity.Status.CONFIRMED);
        entity.setPlanType(planType);
        entity.setScripts(scripts);
        entity.setAttrs(String.join(";", Optional.ofNullable(attrList).orElse(new ArrayList<>())));
        return entity;
    }


}
