package com.yeestor.work_order.repository;

import com.yeestor.work_order.entity.OrderFlashEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface OrderFlashRepository extends JpaRepository<OrderFlashEntity, Long> {
    List<OrderFlashEntity> findAllByOrderId(long orderId);

    @Query("select f from OrderFlash f where f.orderId = :orderId and (f.disabled = false  or f.disabled is null)")
    List<OrderFlashEntity> findAllByOrderIdAndNotDisabled(long orderId);


    Optional<OrderFlashEntity> findByOrderIdAndFlash(long orderId, String flash);

    List<OrderFlashEntity> findByOrderIdIn(List<Long> orderIds);

    int countByOrderId(long orderId);

    List<OrderFlashEntity> findAllByOrderIdOrderByIdx(long orderId);


    @Query("select count(a) from OrderFlash a where a.orderId = :orderId and a.status in :statusList  ")
    int countByOrderIdAndStatusIn(long orderId, List<OrderFlashEntity.Status> statusList);

    boolean existsByOrderIdAndFlash(long id, String flash);

    Optional<OrderFlashEntity> findByOrderFlashNo(String orderFlashNo);

    boolean existsByOrderFlashNo(String orderNo);

    @Query("select DISTINCT f.orderId from OrderFlash f where f.testBy like %:testBy% and f.flash like %:flash%")
    List<Long> findAllByFlashAndTestBy(String testBy, String flash);

    @Query("select f.orderFlashNo from OrderFlash f where f.orderFlashNo like %:orderNo% and f.size = :size")
    List<String> findFlashNoByOrderNoAndSize(String orderNo, String size);
}
