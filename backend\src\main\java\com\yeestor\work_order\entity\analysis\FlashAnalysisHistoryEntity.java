package com.yeestor.work_order.entity.analysis;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;

import javax.persistence.*;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity(name = "FlashAnalysisHistory")
@Table(name = "wo_flash_analysis_history")
public class FlashAnalysisHistoryEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "created_at")
    private Long createdAt;

    /**
     * 工单id
     */
    @Column(name = "order_id")
    private long orderId;

    /**
     * 评审时的工单版本
     * 如果alpha转成release之后再评审，则版本记录为release
     */
    @Column(name = "version_type")
    private String versionType;

    /**
     * Flash批次
     */
    @Column(name = "flash")
    private String flash;

    /**
     * 详细信息，使用Json 来存储。
     */
    @Lob
    @Column(name = "detail_info")
    private String detailInfo;

    /**
     * 将fail分析内容转换成json 存储到历史中
     *
     * @param analysisEntity 分析内容数据
     * @return 历史实体类
     */
    @SneakyThrows
    public FlashAnalysisHistoryEntity analysisToHistory(OrderFailAnalysisEntity analysisEntity) {
        ObjectMapper mapper = new ObjectMapper();
        FlashAnalysisHistoryEntity historyEntity = new FlashAnalysisHistoryEntity();
        historyEntity.orderId = analysisEntity.getOrderId();
        historyEntity.flash = analysisEntity.getFlash();
        historyEntity.createdAt = System.currentTimeMillis();
        historyEntity.detailInfo = mapper.writeValueAsString(analysisEntity);
        return historyEntity;
    }
}
