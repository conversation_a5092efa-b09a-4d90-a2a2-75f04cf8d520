package com.yeestor.work_order.service.product;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.rms.DeviceModel;
import com.yeestor.work_order.model.rms.PlanStatusChangeParams;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.service.product.impl.DefaultProductService;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class ProductContext {

    private final Map<String, ProductService> productServiceMap = new HashMap<>();

    private final DefaultProductService defaultProductService;

    @Setter(onMethod = @__({@Autowired, @Lazy}))
    private WorkOrderRepository orderRepository ;

    public ProductService getProductService(String product) {
        String key = product.toLowerCase();
        return productServiceMap.getOrDefault(key, defaultProductService);
    }

    public void registerProductService(String product, ProductService productService) {
        String key = product.toLowerCase();
        productServiceMap.put(key, productService);
    }


    public void onPlanStatusChange(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            PlanStatusChangeParams params
    ) {
        getProductService(orderEntity.getSubProduct()).onPlanStatusChange(orderEntity,flashEntity, planEntity, params);
    }

    public void assignDevices(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {
        getProductService(orderEntity.getSubProduct()).assignDevices(orderEntity, flashEntity);
    }

    public String getOrderSubProduct(long orderId) {
        return orderRepository.findSubProductById(orderId).orElse(null);
    }

    public Comparator<DeviceModel> deviceComparator(long orderId) {
        String subProduct = getOrderSubProduct(orderId);
        if (subProduct == null) {
            return defaultProductService.deviceComparator();
        }
        return getProductService(subProduct).deviceComparator();
    }

    public List<DeviceModel> handleTestAllPlan(OrderPlanEntity planEntity, List<DeviceModel> runnableDevices) {
        String subProduct = getOrderSubProduct(planEntity.getOrderId());
        return getProductService(subProduct).handleTestAllPlan(planEntity.getOrderId(), planEntity, runnableDevices);
    }

}
