package com.yeestor.work_order.entity.config;

import com.yeestor.work_order.model.http.req.Person;
import com.yeestor.work_order.utils.converter.PersonListConvert;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@Entity(name = "TimeoutConfig")
@Table(name = "wo_timeout_config")
public class TimeoutConfigEntity {

    public enum Type {
        ORDER,
        FLASH,
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "product", length = 50)
    private String product ;

    @Column(name = "sub_product", length = 100)
    private String subProduct ;


    @Column(name = "type", length = 100)
    @Enumerated(EnumType.STRING)
    private Type type ;

    @Column(name = "phase", length = 100)
    private String phase ;

    /**
     * 超时时间, 单位为小时
     */
    private int timeout ; // -1 为不超时

    @Column(name="listeners", columnDefinition = "json")
    @Convert(converter = PersonListConvert.class)
    private List<Person> listeners ;

}
