package com.yeestor.work_order.service.order;

import com.yeestor.admin.api.UserFeignClient;
import com.yeestor.admin.model.UserDTO;
import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.OrderBugEntity;
import com.yeestor.work_order.entity.OrderDetailEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.exception.ErrorImportOrderException;
import com.yeestor.work_order.model.ci.FaeModel;
import com.yeestor.work_order.model.ci.OrderInfoModel;
import com.yeestor.work_order.model.http.resp.order.BugInfo;
import com.yeestor.work_order.repository.OrderBugRepository;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.service.zentao.ZenTaoService;
import com.yeestor.work_order.utils.DingTalkUtils;
import com.yeestor.work_order.utils.TextUtils;
import com.yeestor.zentao.model.ZtResolvedBug;
import com.yeestor.zentao.model.resp.build.ZtBuildBugResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderImportService {
    private final Environment environment;
    private final UserFeignClient userFeignClient;
    private final WorkOrderRepository workOrderRepository;
    private final OrderService orderService;
    private final OrderBugRepository bugRepository;
    private final DingTalkUtils dingTalkUtils;
    private final ZenTaoService zenTaoService;


    public String getOrderPrefix() {
        String prefix = "WO_";
        if (Arrays.asList(environment.getActiveProfiles()).contains("prod")) {
            prefix = "";
        } else if (Arrays.asList(environment.getActiveProfiles()).contains("alpha")) {
            prefix = "W0_";
        }
        return prefix;
    }

    /**
     * 给对应的工单保存bug信息
     *
     * @param bugList 禅道bug
     * @param orderId 需要绑定工单id
     */
    public void createBugInfo(List<BugInfo> bugList, long orderId) {
        for (BugInfo bugInfo : bugList) {
            OrderBugEntity bugEntity = bugInfo.convertOrder(orderId);
            bugRepository.save(bugEntity);
        }
    }

    /**
     * 获取当年此产品导入的工单数量
     *
     * @param product    产品
     * @param subProduct 产品线
     * @return 导入数量
     */
    private int countOrderByProduct(String product, String subProduct) {
        return workOrderRepository.countByProductAndSubProductAndCreatedAtAfter(
                product,
                subProduct,
                DateTime.now().withDayOfYear(1).withTimeAtStartOfDay().getMillis()
        );

    }

    /**
     * 从禅道导入工单
     *
     * @param product      产品线
     * @param subProduct   产品名称
     * @param testTaskId   在禅道中的测试单id
     * @param flash        flash名称
     * @param desc         描述
     * @param mailTo       通知人（构建人）
     * @param buildResp         禅道版本信息
     * @param orderFeature 工单测试类型
     * @return 工单信息
     */
    public WorkOrderEntity importOrderFromZenTao(
            String product,
            String subProduct,
            String testTaskId,
            String flash,
            String desc,
            String mailTo,
            ZtBuildBugResp buildResp,
            Integer orderFeature
    ) {
        ZtBuildBugResp.BuildResp info = buildResp.getBuild();
        String buildDesc = info.getDesc();
        String builder = Optional.ofNullable(info.getBuilder())
                .orElse(DingTalkUtils.getCurrentUserName()); // 否则，使用默认的用户名

        String date = info.getDate();
        String filePath = info.getFilePath();
        String ztProductName = info.getProductName();
        String fullVersion = info.getName();
        log.info("ztProductName: {}", ztProductName);
        // 如果导入的是工业级SATA，则从filePath中获取版本信息
        if (ztProductName.contains("SATA") && !"SATA".equalsIgnoreCase(ztProductName)) {
            if (!filePath.isEmpty()) {
                String[] splitFiles = filePath.split("\\\\");
                fullVersion = splitFiles[splitFiles.length - 1];
            }
        }
        log.info("fullVersion: {}", fullVersion);
        String prefix = getOrderPrefix();
        int buildId = info.getId();

        String productId = String.valueOf(info.getProduct());
        String projectId = String.valueOf(info.getProject());
        String executionId = String.valueOf(info.getExecution());

        String executionName = info.getExecutionName();

        UserDTO userDTO = fetchZenTaoBuilder(builder);

        WorkOrderEntity entity = new WorkOrderEntity();
        entity.setBuildBy(userDTO.getDUserId());
        entity.setBuildPerson(userDTO.getUsername());

        if (orderFeature != null) {
            entity.setFeature(orderFeature);
            entity.setBuildBy(DingTalkUtils.getCurrentUserDingTalkID());
            entity.setBuildPerson(DingTalkUtils.getCurrentUserName());
        }

        entity.setCreatedAt(System.currentTimeMillis());
        entity.setStatus(WorkOrderEntity.Status.CREATED);

        DateTime dateTime = DateTime.parse(date, DateTimeFormat.forPattern("yyyy-MM-dd"));

        entity.setBuildStartAt(dateTime.getMillis());
        entity.setBuildEndAt(dateTime.getMillis());
        String version = Optional.of(fullVersion).map(TextUtils::parseMpToolVersion).orElse(fullVersion);
        String fwVersion = fullVersion.split("_")[2];      // fw 版本号

        int count = countOrderByProduct(product, subProduct);

        String chip = executionName;
        log.info("executionName: {}", executionName);
        // FIXME 对PCIe版本做特殊标记，后续支持eBuild导入后，考虑是否需要移除一部分代码
        if ("pcie".equalsIgnoreCase(subProduct)) {
            if (ztProductName.contains("9205") || fullVersion.contains("9205")) {
                chip = "9205";
            } else if (executionName.contains("9203") || fullVersion.contains("9203")) {
                chip = "9203";
            } else {
                throw new ErrorImportOrderException("PCIe导入禅道版本时无法获取到相关主控!");
            }
        } else if ("IND_SD".equalsIgnoreCase(subProduct)) {
            // IND_SD产品类型特殊处理：从fullVersion末尾括号中提取chip值
            if (fullVersion.matches(".*\\(.+\\)$")) {
                // 提取括号中的内容
                int startIndex = fullVersion.lastIndexOf('(') + 1;
                int endIndex = fullVersion.lastIndexOf(')');
                chip = fullVersion.substring(startIndex, endIndex);
            } else {
                throw new ErrorImportOrderException("IND_SD产品导入禅道版本时fullVersion格式不正确，应以(xx)结尾");
            }
        } else {
            //判断 executionName 是否只包含字母和数字, 如果不是, 则从name 中提取 以_分隔的第一个字符串
            if (!executionName.matches("[a-zA-Z0-9]+")) {
                chip = info.getName().split("_")[0];
            }
        }

        entity.setNo(
                TextUtils.buildOrderNo(
                        prefix,
                        chip.toUpperCase(),
                        "MPTOOL",
                        version,
                        flash,
                        count,
                        fwVersion,
                        subProduct
                )
        );

        entity.setVersion(version);

        String fullVersionLowerCase = fullVersion.toLowerCase();
        String versionType = fullVersionLowerCase.contains(OrderInfoModel.VERSION_TYPE_RELEASE.toLowerCase()) ? OrderInfoModel.VERSION_TYPE_RELEASE : OrderInfoModel.VERSION_TYPE_ALPHA;
        entity.setVersionType(versionType);
        entity.setFullVersion(fullVersion);

        entity.setPriority(0);
        entity.setProduct(product);
        entity.setSubProduct(subProduct);
        entity.setChip(chip);
        entity.setMpToolPath(filePath);
        entity.setFlash(flash);

        OrderDetailEntity detailEntity = new OrderDetailEntity();

        detailEntity.setVersionLog(buildDesc);
        detailEntity.setMpFilePath(filePath);
        detailEntity.setMpToolVersion(version);
        detailEntity.setMailList(mailTo);

        // 禅道 相关信息
        detailEntity.setZentaoProduct(productId);
        detailEntity.setZentaoProject(projectId);
        detailEntity.setZentaoExecutionId(executionId);
        detailEntity.setZentaoTestTask(testTaskId);
        detailEntity.setZentaoBuildId(String.valueOf(buildId));

        detailEntity.setRequirement(desc);

        detailEntity.setPlanList("");
        return orderService.createWorkOrder(entity, detailEntity);
    }

    /**
     * 调用eBuild 中接口进行打包构建。[通过 ci 打包构建后导入工单]
     *
     * @param ciId     构建版本 ciId
     * @param faeModel 构建需要的参数信息
     */
    public void importOrderFromCI(Long ciId, FaeModel faeModel, OAuthUserDetail userDetail) {
        faeModel.setBuilder(userDetail.getUid());
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();

        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpRequest = new HttpEntity(faeModel, headers);

        String url = "/api/workOrder/fae/" + ciId;
        if (Arrays.asList(environment.getActiveProfiles()).contains("prod")) {
            restTemplate.postForEntity("http://ebuildin.yeestor.com:8789" + url, httpRequest, String.class);
        } else {
            restTemplate.postForEntity("http://*************:8789" + url, httpRequest, String.class);
        }
    }

    /**
     * 导入Dev版本，此功能暂时只有eMMC在使用
     *
     * @param product     产品
     * @param subProduct  子产品
     * @param chip        主控
     * @param flash       flash
     * @param mpVersion   量产工具版本
     * @param fwVersion   固件版本
     * @param buildBy     构建人
     * @param description 描述
     * @return 工单实体
     */
    public WorkOrderEntity importOrderFromDev(
            String product,
            String subProduct,
            String chip,
            String flash,
            String mpVersion,
            String fwVersion,
            String buildBy,
            String description
    ) {
        String prefix = getOrderPrefix();
        WorkOrderEntity entity = new WorkOrderEntity();
        entity.setFeature(WorkOrderEntity.FEATURE_DEV);
        entity.setProduct(product);
        entity.setSubProduct(subProduct);
        entity.setChip(chip);
        entity.setFlash(flash);
        entity.setBuildBy(buildBy);
        entity.setBuildPerson(dingTalkUtils.getPersonName(buildBy));

        String fullVersion = "MPTool_" + chip + "_" + flash + "_" + mpVersion + "_" + fwVersion;
        entity.setVersionType(OrderInfoModel.VERSION_TYPE_ALPHA);
        entity.setFullVersion(fullVersion);
        entity.setVersion(mpVersion);

        entity.setBuildStartAt(System.currentTimeMillis());
        entity.setBuildEndAt(System.currentTimeMillis());
        entity.setCreatedAt(System.currentTimeMillis());
        entity.setStatus(WorkOrderEntity.Status.CREATED);

        int count = countOrderByProduct(product, subProduct);
        entity.setNo(
                TextUtils.buildOrderNo(
                        prefix,
                        chip.toUpperCase(),
                        "MPTOOL",
                        mpVersion,
                        flash,
                        null,
                        fwVersion,
                        subProduct
                )
        );
        entity.setNo(String.format("%s%04d", entity.getNo(), count + 1));

        OrderDetailEntity detailEntity = new OrderDetailEntity();
        detailEntity.setRequirement(description);

        detailEntity.setMpToolVersion(fullVersion);
        detailEntity.setPlanList("");
        return orderService.createWorkOrder(entity, detailEntity);
    }

    /**
     * 导入竞品版本工单，当前仅仅SD在使用
     * @param product 产品线
     * @param subProduct 产品
     * @param chip 主控
     * @param flash flash型号
     * @param buildBy 构建人
     * @param mpFilePath 竞品版本包路径
     * @param description 测试描述信息
     * @param ztProductId 禅道产品
     * @param level 测试优先级
     * @param description 版本备注
     * @param bugList 禅道绑定bug
     * @return order实体类
     */
    public WorkOrderEntity importOrderFromRival(
            String product,
            String subProduct,
            String chip,
            String flash,
            String buildBy,
            String mpFilePath,
            String ztProductId,
            int level,
            String description,
            List<BugInfo> bugList
    ) {
        String prefix = getOrderPrefix();
        WorkOrderEntity entity = new WorkOrderEntity();
        entity.setFeature(WorkOrderEntity.FEATURE_RIVAL);
        entity.setProduct(product);
        entity.setSubProduct(subProduct);
        entity.setChip(chip);
        entity.setFlash(flash);
        entity.setBuildBy(buildBy);
        entity.setBuildPerson(dingTalkUtils.getPersonName(buildBy));
        entity.setVersionType(OrderInfoModel.VERSION_TYPE_RELEASE);

        String fullVersion = getFullVersion(mpFilePath);
        entity.setFullVersion(fullVersion);
        entity.setVersion("");

        entity.setBuildStartAt(System.currentTimeMillis());
        entity.setBuildEndAt(System.currentTimeMillis());
        entity.setCreatedAt(System.currentTimeMillis());
        entity.setStatus(WorkOrderEntity.Status.CREATED);
        entity.setPriority(level);

        int count = countOrderByProduct(product, subProduct);
        entity.setNo(
                TextUtils.buildOrderNo(
                        prefix,
                        chip.toUpperCase(),
                        "MPTOOL",
                        "###.###",
                        flash,
                        null,
                        "######",
                        subProduct
                )
        );
        entity.setNo(String.format("%s%04d", entity.getNo(), count + 1));

        OrderDetailEntity detailEntity = new OrderDetailEntity();
        detailEntity.setRequirement(description);

        detailEntity.setMpToolVersion(fullVersion);
        detailEntity.setMpFilePath(mpFilePath);
        detailEntity.setPlanList("");
        detailEntity.setZentaoProduct(ztProductId);

        if (Arrays.asList(environment.getActiveProfiles()).contains("prod")) {
            // 在禅道bug中填写版本信息
            for (BugInfo bugInfo : bugList) {
                String comment = fetchRivalHtmlModel(mpFilePath, buildBy, description);
                ZtResolvedBug ztResolvedBug = new ZtResolvedBug();
                ztResolvedBug.setComment(comment);
                zenTaoService.updateBugInfo(bugInfo.getId(), ztResolvedBug);
            }
        }

        return orderService.createWorkOrder(entity, detailEntity);
    }

    /**
     * 从禅道导入PCIe工单
     *
     * @param product      产品线
     * @param subProduct   产品名称
     * @param buildTye  0代表消费级pcie，1代表工业级pcie
     * @param flash        flash名称
     * @param desc         描述
     * @param mailTo       通知人（构建人）
     * @param buildResp         导入填写信息
     * @param orderFeature 工单测试类型
     * @return 工单信息
     */
    public WorkOrderEntity importPCIeOrderFromZenTao(
            String product,
            String subProduct,
            int buildTye,
            String flash,
            String desc,
            String mailTo,
            ZtBuildBugResp buildResp,
            Integer orderFeature
    ) {
        ZtBuildBugResp.BuildResp info = buildResp.getBuild();
        String buildDesc = info.getDesc();
        String builder = Optional.ofNullable(info.getBuilder())
                .orElse(DingTalkUtils.getCurrentUserName()); // 否则，使用默认的用户名

        String date = info.getDate();
        String filePath = info.getFilePath();
        String ztProductName = info.getProductName();
        String fullVersion = info.getName();
        String prefix = getOrderPrefix();
        int buildId = info.getId();

        String productId = String.valueOf(info.getProduct());
        String projectId = String.valueOf(info.getProject());
        String executionId = String.valueOf(info.getExecution());

        String executionName = info.getExecutionName();

        UserDTO userDTO = fetchZenTaoBuilder(builder);

        WorkOrderEntity entity = new WorkOrderEntity();
        entity.setBuildBy(userDTO.getDUserId());
        entity.setBuildPerson(userDTO.getUsername());

        if (orderFeature != null) {
            entity.setFeature(orderFeature);
            entity.setBuildBy(DingTalkUtils.getCurrentUserDingTalkID());
            entity.setBuildPerson(DingTalkUtils.getCurrentUserName());
        }

        entity.setCreatedAt(System.currentTimeMillis());
        entity.setStatus(WorkOrderEntity.Status.CREATED);

        DateTime dateTime = DateTime.parse(date, DateTimeFormat.forPattern("yyyy-MM-dd"));

        entity.setBuildStartAt(dateTime.getMillis());
        entity.setBuildEndAt(dateTime.getMillis());
        String version = Optional.of(fullVersion).map(TextUtils::parseMpToolVersion).orElse(fullVersion);
        String fwVersion = "";      // fw 版本号
        if (buildTye == 0) {        // 消费级PCIe
            fwVersion = fullVersion.split("_")[2];
        } else {        // 工业级PCIe
            Pattern pattern = Pattern.compile("[a-zA-Z0-9]{8}");    // 正则表达式模式
            Matcher matcher = pattern.matcher(fullVersion.replace("MPTool",""));         // 创建Matcher对象
            if (matcher.find()) {  // 查找匹配项
                fwVersion = matcher.group();
            } else {
                throw new ErrorImportOrderException("导入工单失败,该工业级禅道版本与预期不符,请检查版本是否符合命名规范:" + fullVersion + "！");
            }
        }

        int count = countOrderByProduct(product, subProduct);

        String chip = null;
        // FIXME 对PCIe版本做特殊标记，后续支持eBuild导入后，考虑是否需要移除一部分代码
        if (ztProductName.contains("9205") || fullVersion.contains("9205")) {
            chip = "9205";
        } else if (executionName.contains("9203") || fullVersion.contains("9203")) {
            chip = "9203";
        } else {
            throw new ErrorImportOrderException("PCIe导入禅道版本时无法获取到相关主控!");
        }
        log.info("importPCIeOrderFromZenTao version: {} fwVersion: {}", version, fwVersion);

        entity.setNo(
                TextUtils.buildOrderNo(
                        prefix,
                        chip.toUpperCase(),
                        "MPTOOL",
                        version,
                        flash,
                        count,
                        fwVersion,
                        subProduct
                )
        );
        entity.setVersion(version);

        String fullVersionLowerCase = fullVersion.toLowerCase();
        String versionType = fullVersionLowerCase.contains(OrderInfoModel.VERSION_TYPE_RELEASE.toLowerCase()) ? OrderInfoModel.VERSION_TYPE_RELEASE : OrderInfoModel.VERSION_TYPE_ALPHA;
        entity.setVersionType(versionType);
        entity.setFullVersion(fullVersion);

        entity.setPriority(0);
        entity.setProduct(product);
        entity.setSubProduct(subProduct);
        entity.setChip(executionName);
        entity.setMpToolPath(filePath);
        entity.setFlash(flash);

        OrderDetailEntity detailEntity = new OrderDetailEntity();

        detailEntity.setVersionLog(buildDesc);
        detailEntity.setMpFilePath(filePath);
        detailEntity.setMpToolVersion(version);
        detailEntity.setMailList(mailTo);

        // 禅道 相关信息
        detailEntity.setZentaoProduct(productId);
        detailEntity.setZentaoProject(projectId);
        detailEntity.setZentaoExecutionId(executionId);
        detailEntity.setZentaoBuildId(String.valueOf(buildId));

        detailEntity.setRequirement(desc);

        detailEntity.setPlanList("");
        return orderService.createWorkOrder(entity, detailEntity);
    }

    /**
     * 工业级通过禅道版本导入工单
     * @param product 产品线
     * @param subProduct 产品
     * @param flash flash型号
     * @param desc 测试描述
     * @param buildResp 禅道版本信息
     * @param orderFeature 工单类型
     * @return 工单信息
     */
    public WorkOrderEntity importINDEMMCOrderFromZenTao(
            String product,
            String subProduct,
            String flash,
            String desc,
            ZtBuildBugResp buildResp,
            Integer orderFeature
    ) {
        ZtBuildBugResp.BuildResp info = buildResp.getBuild();
        String buildDesc = info.getDesc();
        String builder = Optional.ofNullable(info.getBuilder())
                .orElse(DingTalkUtils.getCurrentUserName()); // 否则，使用默认的用户名
        String date = info.getDate();
        String filePath = info.getFilePath();
        String fullVersion = info.getName();
        String prefix = getOrderPrefix();
        int buildId = info.getId();
        log.info("fullVersion: {}", fullVersion);

        String productId = String.valueOf(info.getProduct());
        String projectId = String.valueOf(info.getProject());
        String executionId = String.valueOf(info.getExecution());
        String executionName = info.getExecutionName();
        UserDTO userDTO = fetchZenTaoBuilder(builder);

        WorkOrderEntity entity = new WorkOrderEntity();
        entity.setBuildBy(userDTO.getDUserId());
        entity.setBuildPerson(userDTO.getUsername());
        entity.setFeature(orderFeature);

        entity.setCreatedAt(System.currentTimeMillis());
        entity.setStatus(WorkOrderEntity.Status.CREATED);
        DateTime dateTime = DateTime.parse(date, DateTimeFormat.forPattern("yyyy-MM-dd"));

        entity.setBuildStartAt(dateTime.getMillis());
        entity.setBuildEndAt(dateTime.getMillis());
        String version = getINDeMMCVersion(fullVersion);
        String fwVersion = matchSixDigits(fullVersion);

        int count = countOrderByProduct(product, subProduct);

        String chip = Arrays.stream(fullVersion.split("_")).findFirst()
                .orElseThrow(() -> new ErrorImportOrderException("PCIe导入禅道版本时无法获取到相关主控!"));

        log.info("importPCIeOrderFromZenTao version: {} fwVersion: {}", version, fwVersion);

        entity.setNo(
                TextUtils.buildOrderNo(
                        prefix,
                        chip.toUpperCase(),
                        "MPTOOL",
                        version,
                        flash,
                        count,
                        fwVersion,
                        subProduct
                )
        );
        entity.setVersion(version);

        String fullVersionLowerCase = fullVersion.toLowerCase();
        String versionType = fullVersionLowerCase.contains(OrderInfoModel.VERSION_TYPE_RELEASE.toLowerCase()) ? OrderInfoModel.VERSION_TYPE_RELEASE : OrderInfoModel.VERSION_TYPE_ALPHA;
        entity.setVersionType(versionType);
        entity.setFullVersion(fullVersion);

        entity.setPriority(0);
        entity.setProduct(product);
        entity.setSubProduct(subProduct);
        entity.setChip(executionName);
        entity.setMpToolPath(filePath);
        entity.setFlash(flash);

        OrderDetailEntity detailEntity = new OrderDetailEntity();

        detailEntity.setVersionLog(buildDesc);
        detailEntity.setMpFilePath(filePath);
        detailEntity.setMpToolVersion(version);
        detailEntity.setMailList("");

        // 禅道 相关信息
        detailEntity.setZentaoProduct(productId);
        detailEntity.setZentaoProject(projectId);
        detailEntity.setZentaoExecutionId(executionId);
        detailEntity.setZentaoBuildId(String.valueOf(buildId));

        detailEntity.setRequirement(desc);

        detailEntity.setPlanList("");
        return orderService.createWorkOrder(entity, detailEntity);
    }

    /**
     * 获取工业级禅道版本的版本号
     * @param fullVersion 版本名称
     * @return 版本号
     */
    public String getINDeMMCVersion(String fullVersion){
        String regex = "\\d{1,2}\\.\\d{2}\\.\\d{2}\\.\\d{2}";     // 定义正则表达式，匹配类似 11.01.04.80 的格式
        Pattern pattern = Pattern.compile(regex);       // 编译正则表达式
        Matcher matcher = pattern.matcher(fullVersion);     // 创建匹配器
        return Optional.ofNullable(matcher.find() ? matcher.group() : null)  // fw 版本号
                .orElseThrow(() -> new ErrorImportOrderException("获取版本号失败,该禅道版本号与预期不符,请检查版本是否符合命名规范:" + fullVersion + "！" ));
    }

    /**
     * 使用正则表达式匹配输入字符串中的所有6位数字。
     *
     * @param input 输入字符串
     */
    public String matchSixDigits(String input) {
        String regex = "\\d{6}";
        Pattern pattern = Pattern.compile(regex);       // 编译正则表达式
        Matcher matcher = pattern.matcher(input);     // 创建匹配器

        return Optional.ofNullable(matcher.find() ? matcher.group() : null)  // fw 版本号
                .orElseThrow(() -> new ErrorImportOrderException("获取fw号失败,该禅道版本号与预期不符,请检查版本是否符合命名规范:" + input + "！" ));
    }

    /**
     * 获取禅道版本构建人员信息
     * @param builder 构建人
     * @return 人员信息
     */
    public UserDTO fetchZenTaoBuilder(String builder){
        HandleResp<List<UserDTO>> matchUsers = userFeignClient.findUsersByUsername(builder);
        if (!matchUsers.isSuccess() || matchUsers.getData().isEmpty()) {
            throw new DataNotFoundException("找不到对应的用户");
        }
        List<UserDTO> users = matchUsers.getData();
        UserDTO userDTO = users.get(0);
        log.info("userDTO: {}", userDTO);
        if (users.size() > 1) {
            if (builder.contains(".")) {
                throw new DataNotFoundException("用户名称:" + builder + "不唯一, 请及时修改!");
            }
            log.info("builder: {}", builder);
            userDTO = users.stream().filter(user -> user.getUsername().toLowerCase().contains(builder + "."))
                    .findFirst().orElseThrow(
                            () -> new DataNotFoundException("用户名称:" + builder + ". 没有找到对应的用户, 请及时修改!")
                    );
        }
        return userDTO;
    }

    /**
     * 获取产品线信息
     * @param subProduct 产品
     * @return
     */
    public String getProductBySubProduct(String subProduct){
        switch (subProduct){
            case "SD":
            case "U3":
            case "U2":
                return "GE";
            case "eMMC":
            case "UFS":
                return "EM";
            case "SATA":
            case "PCIe":
                return "SSD";
            case "IND_EMMC":
            case "IND_SD":
            case "IND_SATA":
            case "IND_PCIE":
                return "IND";
            default:
                return "unknown";
        }
    }

    private String getFullVersion(String mpFilePath){
        if (!"".equals(mpFilePath)) {
            String[] strArray = mpFilePath.split("\\\\");
            String filename = strArray[strArray.length - 1];
            return filename.substring(0, filename.lastIndexOf("."));
        }
        return "";
    }

    /**
     * 获取竞品测试的禅道评论
     *
     * @param mpFilePath  版本路径
     * @param buildBy     构建人
     * @param description 版本描述
     * @return html模板
     */
    public String fetchRivalHtmlModel(String mpFilePath, String buildBy, String description) {
        String buildPerson = dingTalkUtils.getPersonName(buildBy);
        String fullVersion = getFullVersion(mpFilePath);
        String template = String.format(
                "<div><div style='line-height: 32px; font-size: 18px;font-weight: 600; margin-bottom: 8px;'>竞品验证测试！ </div>" +
                        "<div style='line-height: 22px; font-size: 14px; margin-bottom: 3px'>版本名称: %s</div>" +
                        "<div style='line-height: 22px; font-size: 14px; margin-bottom: 3px'>量产工具存放路径: %s</div>" +
                        "<div style='line-height: 22px; font-size: 14px; margin-bottom: 3px'>构建人: %s</div>" +
                        "<div style='line-height: 22px; font-size: 14px; margin-bottom: 3px'>版本描述: <div style='padding: 2px 20px'>%s</div></div></div>",
                fullVersion,
                mpFilePath,
                buildPerson,
                description
        );
        return template;
    }

}
