package com.yeestor.work_order.model.rms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("电脑返回测试机状态格式说明")
public class DeviceStatusCheckResp {
    @ApiModel(value = "DeviceCheckResult", description = "测试机信息。")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DeviceCheckResult {
        private String ip;
        private String mac;
        private String pc_no;
        private String status;
    }

    @ApiModelProperty("设备状态列表")
    public List<DeviceCheckResult> statusList;
}
