package com.yeestor.work_order.model.http.resp.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.plan.PlanAssignInfoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Data
@ApiModel(value = "详细 plan 信息")
public class PlanDetailVO {
    @ApiModelProperty(value = "Plan的标题,一般为临时Plan")
    private String title;

    @ApiModelProperty(value = "Plan的名称")
    private String name;

    @ApiModelProperty(value = "Plan的功能")
    private String feature;

    @ApiModelProperty(value = "Plan的测试数量，为0的时候表示由Flash的批次决定。")
    private int testNum;

    @ApiModelProperty(value = "Plan的id")
    private long id;

    @ApiModelProperty(value = "Plan的类型， 0 是自动，1 是手动")
    @JsonInclude
    private int type ;

    @ApiModelProperty(value = "Plan的分数, 分数越高，表示约紧急")
    private int priority;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "Plan的阶段")
    private OrderPlanEntity.Phase phase;

    @ApiModelProperty(value = "Plan 所属的Flash批次")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String flash;

    @ApiModelProperty(value = "属性列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> attrs;

    @ApiModelProperty(value = "预选择的设备编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<DeviceDetailVO> devices;

    @ApiModelProperty(value = "预选择的平台信息")
    private List<PlatformDetailVO> platforms;

    @ApiModelProperty(value = "Plan的状态")
    private OrderPlanEntity.Status status;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "Plan 创建时间")
    private Long createdAt ;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "Plan 的资源就绪时间")
    private Long readyAt ;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "plan 的环境确认时间。")
    private Long confirmedAt ;

    /**
     * plan 的环境确认人, 钉钉ID
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "plan 的环境确认人")
    private String confirmedPerson  ;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "plan 的开始测试时间")
    private Long startAt ;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "开始此Plan的用户")
    private String startPerson ;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "此Plan对应的负责人,名称")
    private String ownerName ;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "plan 的真正结束测试时间")
    private Long endAt ;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "Plan 自动结束的时间。如果一直在重测的话，这个时间会一直更新。")
    private Long autoEndAt ;

    /**
     * 终止plan的时间。
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "人为终止plan的时间。")
    private Long terminateAt ;

    /**
     * 终止plan的人员 名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "终止plan的人员 名称")
    private String terminatePerson ;

    /**
     *Plan是否被禁用
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "Plan是否被禁用")
    private Boolean disabled ;

    @ApiModelProperty(value = "Plan是否需要上传报告")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean needReport ;

    @ApiModelProperty(value = "指定的Plan的操作人")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String belongToPerson ;

    @ApiModelProperty(value = "Plan的预期完成时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long expectedEndTime ;

    @ApiModelProperty(value = "手动Plan测试结果")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String result ;

    @ApiModelProperty(value = "手动Plan测试结果的描述")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String reason ;

    @ApiModelProperty(value = "plan 排队信息。")
    private AssignPlan assignInfo;

    @Data
    public static class AssignPlan {
        private PlanAssignInfoEntity.Status status;

        /**
         * 需要分配样片的数量
         */
        private int needSampleNum;

        /**
         * 已经分配样片的数量
         */
        private int actualSampleNum;

        /**
         * 需要设备的数量
         */
        private int needDeviceNum;

        /**
         * 实际已经分配的设备的数量
         */
        private int actualDeviceNum;

        /**
         * 开始自动分配的时间
         */
        private Long startQueueTime;
        /**
         * 结束自动分配的时间, 手动停止时, 也会记录结束时间.
         */
        private Long endQueueTime;

        public static AssignPlan toAssign(PlanAssignInfoEntity entity){
            AssignPlan assignInfo = new AssignPlan();
            assignInfo.setStatus(entity.getStatus());
            assignInfo.setNeedDeviceNum(entity.getNeedDeviceNum());
            assignInfo.setActualDeviceNum(entity.getActualDeviceNum());
            assignInfo.setNeedSampleNum(entity.getNeedSampleNum());
            assignInfo.setActualSampleNum(entity.getActualSampleNum());
            assignInfo.setStartQueueTime(entity.getStartQueueTime());
            assignInfo.setEndQueueTime(entity.getEndQueueTime());
            return assignInfo;
        }
    }

    @ApiModelProperty(value = "脚本列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> scripts;

    @ApiModelProperty(value = "Plan的类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String planType;


    /**
     * 将PlanEntity转换为PlanDetailInfo
     */
    public static PlanDetailVO of(OrderPlanEntity planEntity) {
        PlanDetailVO planDetailInfo = new PlanDetailVO();
        planDetailInfo.setId(planEntity.getId());
        planDetailInfo.setName(planEntity.getName());
        planDetailInfo.setFeature(planEntity.getFeature());
        planDetailInfo.setTestNum(planEntity.getTestNum());
        planDetailInfo.setFlash(planEntity.getFlash());
        planDetailInfo.setType(planEntity.getType());
        planDetailInfo.setPhase(planEntity.getPhase());
        planDetailInfo.setStatus(planEntity.getStatus());
        planDetailInfo.setAttrs(Arrays.asList(planEntity.getAttrs().split(";")));
        planDetailInfo.setPriority(planEntity.getPriority());
        planDetailInfo.setCreatedAt(planEntity.getCreatedAt());
        planDetailInfo.setReadyAt(planEntity.getReadyAt());
        planDetailInfo.setConfirmedAt(planEntity.getConfirmedAt());
        planDetailInfo.setConfirmedPerson(planEntity.getConfirmedPerson());
        planDetailInfo.setStartAt(planEntity.getStartAt());
        planDetailInfo.setStartPerson(planEntity.getStartPerson());
        planDetailInfo.setEndAt(planEntity.getEndAt());
        planDetailInfo.setAutoEndAt(planEntity.getAutoEndAt());
        planDetailInfo.setTerminateAt(planEntity.getTerminateAt());
        planDetailInfo.setTerminatePerson(planEntity.getTerminatePerson());
        planDetailInfo.setDisabled(planEntity.getDisabled());
        planDetailInfo.setNeedReport(planEntity.getNeedReport());
        planDetailInfo.setBelongToPerson(planEntity.getBelongToPerson());
        planDetailInfo.setExpectedEndTime(planEntity.getExpectedEndTime());
        Boolean isPass = planEntity.getEndStatus() == 1;
        Boolean isFail = planEntity.getEndStatus() == 2;
        if(isPass || isFail) {
            planDetailInfo.setResult(isPass ? "Pass" : "Fail");
            planDetailInfo.setReason(planEntity.getEndReason());
        }

        planDetailInfo.setPlanType(planEntity.getPlanType());
        planDetailInfo.setScripts(planEntity.getScripts() != null
                ? Arrays.asList(planEntity.getScripts().split(";"))
                : Collections.emptyList());
        return planDetailInfo;
    }
}
