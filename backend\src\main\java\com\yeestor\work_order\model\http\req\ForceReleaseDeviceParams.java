package com.yeestor.work_order.model.http.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@ApiModel(value = "ForceReleaseDeviceParams", description = "强制释放电脑的参数")
public class ForceReleaseDeviceParams {

    @NotNull(message = "工单ID")
    @ApiModelProperty("工单Id")
    private Long orderId;

    @NotBlank(message = "flash 批次不能为空")
    @ApiModelProperty("flash 批次")
    private String flash;

    @NotBlank(message = "设备的编号不能为空")
    @ApiModelProperty("设备的编号")
    private String pcNo ;

    @Pattern(regexp="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}" +
            "(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$",message="IP地址格式不正确")
    @ApiModelProperty("设备的IP")
    private String ip ;

     @NotBlank(message = "设备的MAC地址不能为空")
    @ApiModelProperty("设备的MAC地址")
    private String mac ;

    @ApiModelProperty("Plan的名称")
    private String plan ;
}
