package com.yeestor.work_order.entity;

import com.yeestor.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString
@Entity(name = "DeviceSample")
@Table(name="wo_device_sample")
public class DeviceSampleEntity extends BaseEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;


    @Column(name = "order_id")
    private Long orderId;

    @Column(name = "plan_id")
    private Long planId ;

    @Column(name = "device_id")
    private Long deviceId ;

    /**
     * 样片编号
     */
    @Column(name = "no")
    private String no ;

    /**
     * 样片对应的端口号。
     */
    @Column(name = "port")
    private Integer port ;


    /**
     * 对应位置所在的路径。
     */
    @Column(name = "path")
    private String path ;


    /**
     * 样片所处的设备ip
     */
    @Column(name = "device_ip")
    private String deviceIp ;

    @Column(name = "plan_name")
    private String planName ;

    @Column(name = "order_flash_no")
    private String orderFlashNo ;

    /**
     * 标准pe值
     */
    @Column(name = "all_p_e")
    private Integer allPE;

    /**
     * 实际已使用的pe值
     */
    @Column(name = "use_p_e")
    private Integer usePE;

    /**
     * 预测剩余pe值
     */
    @Column(name = "residual_p_e")
    private Integer residualPE;

    @Column(name = "is_success")
    private Boolean success ;

}
