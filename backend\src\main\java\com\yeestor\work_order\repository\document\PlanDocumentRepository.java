package com.yeestor.work_order.repository.document;

import com.yeestor.work_order.entity.document.PlanDocumentEntity;
import com.yeestor.work_order.model.http.resp.order.PlanDocumentItemVO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface PlanDocumentRepository extends JpaRepository<PlanDocumentEntity,Long> {
    boolean existsByPlanId(long planId);

    @Query("select new com.yeestor.work_order.model.http.resp.order.PlanDocumentItemVO(p, d) from PlanDocument p, Document d " +
            "where p.planId = :planId and p.documentId = d.id and p.isRetest = 0")
    List<PlanDocumentItemVO> findAllByPlanId(long planId) ;

    @Query("select new com.yeestor.work_order.model.http.resp.order.PlanDocumentItemVO(p, d) from PlanDocument p, Document d " +
            "where p.orderId = :orderId and p.documentId = d.id and p.isRetest = 0")
    List<PlanDocumentItemVO>  findAllByOrderId(long orderId);
}
