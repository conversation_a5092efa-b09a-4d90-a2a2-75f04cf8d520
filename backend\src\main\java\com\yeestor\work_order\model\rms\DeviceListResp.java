package com.yeestor.work_order.model.rms;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yeestor.model.http.ResultCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceListResp {
    private int code ;
    private List<DeviceModel> data ;
    private String msg ;
    private List<DeviceModel> workPcLst ;


    public static DeviceListResp failed(String msg) {
        return new DeviceListResp(ResultCode.FAILED.getCode(), null , msg, null);
    }


    public boolean isSuccess(){
        return Objects.equals(ResultCode.SUCCESS.getCode(), code) ;
    }
}
