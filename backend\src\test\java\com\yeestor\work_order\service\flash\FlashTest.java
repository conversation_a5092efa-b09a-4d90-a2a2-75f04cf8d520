package com.yeestor.work_order.service.flash;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.http.resp.order.WorkOrderDetailVO;
import com.yeestor.work_order.service.flow.FlowService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.plan.PlanService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

@Slf4j
@SpringBootTest
@ActiveProfiles("test-read-prod-db")
public class FlashTest {
    @Autowired
    FlashService flashService ;
    @Autowired
    OrderService orderService ;

    @Autowired
    PlanService planService;

    @Autowired
    FlowService flowService ;

    public FlashTest() {
    }

    @Test
    void checkFlashStatus(){
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(1950L);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(1950L, "JC-1583-AHGJ0B-B_64GB");
//        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(38355L);
        log.info("orderEntity: {}",orderEntity.getNo());
        log.info("flashEntity: {}",flashEntity.getFlash());
        flashService.checkWaitMerge(orderEntity, flashEntity);
//        planService.checkDeviceReleaseStatus(orderEntity, flashEntity, planEntity);

//        WorkOrderDetailVO detailVO = orderService.fetchOrderInfo(1445L);

//        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(1445L);
//        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(1445L, "SSV6_4x1_256G_2P_256GB");
//        Map<String, Object> data = flowService.fetchFlashFlow(orderEntity,flashEntity) ;

    }
}
