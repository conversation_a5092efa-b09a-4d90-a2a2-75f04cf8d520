package com.yeestor.work_order.utils.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.model.rms.SampleInfo;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

@Slf4j
@Converter
public class SampleListConvert implements AttributeConverter<List<SampleInfo>, String> {

    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public String convertToDatabaseColumn(List<SampleInfo> meta) {
        try {
            return mapper.writeValueAsString(meta);
        } catch (JsonProcessingException ex) {
            log.warn("convertToDatabaseColumn error", ex);
            return null;
        }
    }

    @Override
    public List<SampleInfo> convertToEntityAttribute(String dbData) {
        try {
            return mapper.readValue(dbData, new TypeReference<>() {
            });
        } catch (IOException ex) {
            log.warn("convertToEntityAttribute error", ex);
            return Collections.emptyList();
        }
    }
}
