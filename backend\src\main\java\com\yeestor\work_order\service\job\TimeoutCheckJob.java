package com.yeestor.work_order.service.job;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.entity.config.TimeoutConfigEntity;
import com.yeestor.work_order.model.http.req.Person;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.repository.TimeoutConfigRepository;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.flow.FlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static org.quartz.JobBuilder.newJob;

/**
 * 超时检测任务.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TimeoutCheckJob implements Job {
    public static final String GROUP_NAME = "TimeoutCheckJob";
    private final WorkOrderRepository orderRepository;
    private final OrderFlashRepository orderFlashRepository;
    private final TimeoutConfigRepository timeoutCOnfigRepository;
    private final FlowService flowService;
    private final NotificationService notificationService;


    public static JobDetail buildJobDetail(){
        return newJob(TimeoutCheckJob.class)
                .withIdentity(GROUP_NAME, GROUP_NAME)
                .build() ;
    }
    @Override
    public void execute(JobExecutionContext context) {

        List<TimeoutConfigEntity> configList = timeoutCOnfigRepository.findAll();
        log.info("TimeoutCheckJob start: {}", configList);
        if (configList.isEmpty()) {
            return;
        }

        // 获取当前还没有结束的工单.
        List<WorkOrderEntity> notFinishedOrders = orderRepository.findAllByStatusNotIn(List.of(
                WorkOrderEntity.Status.COMPLETED, WorkOrderEntity.Status.REVOKED
        ));

        for (WorkOrderEntity order : notFinishedOrders) {

            WorkOrderEntity.Status orderStatus = order.getStatus();
            configList.stream()
                    .filter(e -> e.getType() == TimeoutConfigEntity.Type.ORDER
                            && orderStatus.name().equalsIgnoreCase(e.getPhase())
                            && e.getProduct().equalsIgnoreCase(order.getProduct())
                            && e.getSubProduct().equalsIgnoreCase(order.getSubProduct())
                            && e.getTimeout() > 0
                    )
                    .findFirst()
                    .ifPresent(config -> {
                        // 检测是否超时, 如果超时
                        long startTime = flowService.getOrderCurrentStartTime(order);
                        long now = System.currentTimeMillis();
                        long hours = (now - startTime) / 1000 / 60 / 60;
                        if (hours < config.getTimeout()) {
                            return;
                        }
                        List<String> users = config.getListeners().stream().map(Person::getId).collect(Collectors.toList());
                        notificationService.sendTimeoutNotification(
                                order,
                                users,
                                null,
                                orderStatus.getDisplayName(),
                                config.getTimeout(),
                                hours
                        );

                    });

            List<OrderFlashEntity> flashList = orderFlashRepository.findAllByOrderId(order.getId());

            for (OrderFlashEntity flashEntity : flashList) {
                OrderFlashEntity.Status flashStatus = flashEntity.getStatus();
                configList.stream()
                        .filter(e -> e.getType() == TimeoutConfigEntity.Type.ORDER
                                && flashStatus.name().equalsIgnoreCase(e.getPhase())
                                && e.getProduct().equalsIgnoreCase(order.getProduct())
                                && e.getSubProduct().equalsIgnoreCase(order.getSubProduct())
                                && e.getTimeout() > 0
                        )
                        .findFirst()
                        .ifPresent(config -> {
                            long now = System.currentTimeMillis();
                            long startTime = flowService.getFlashCurrentStartTime(flashEntity);
                            long hours = (now - startTime) / 1000 / 60 / 60;
                            if (hours < config.getTimeout()) {
                                return;
                            }
                            List<String> users = config.getListeners().stream().map(Person::getId).collect(Collectors.toList());
                            notificationService.sendTimeoutNotification(
                                    order,
                                    users,
                                    flashEntity.getFlash(),
                                    orderStatus.getDisplayName(),
                                    config.getTimeout(),
                                    hours
                            );

                        });
            }

        }

    }

}
