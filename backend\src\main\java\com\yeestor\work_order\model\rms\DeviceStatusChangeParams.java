package com.yeestor.work_order.model.rms;

import com.yeestor.work_order.entity.DeviceSampleEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DeviceStatusChangeParams",description = "设备状态变更参数")
public class DeviceStatusChangeParams {

    @NotBlank(message = "工单号不能为空")
    @ApiModelProperty(value = "RMS中的工单号", required = true)
    private String orderNo ;

    @NotEmpty(message = "设备列表不能为空")
    @ApiModelProperty(value = "设备列表", required = true)
    private List<DeviceInfo> deviceInfoLst ;


    public static DeviceSampleEntity convertToDeviceSampleEntity(PlanDeviceEntity deviceEntity, String orderFlashNo ,String no){
        DeviceSampleEntity sampleEntity = new DeviceSampleEntity();
        sampleEntity.setDeviceIp(deviceEntity.getIp());
        sampleEntity.setNo(no);
        sampleEntity.setCreatedAt(System.currentTimeMillis());
        sampleEntity.setUpdatedAt(System.currentTimeMillis());
        sampleEntity.setOrderFlashNo(orderFlashNo);
        sampleEntity.setPlanName(deviceEntity.getPlanName());
        sampleEntity.setOrderId(deviceEntity.getOrderId());
        sampleEntity.setPlanId(deviceEntity.getPlanId());
        sampleEntity.setDeviceId(deviceEntity.getId());
        return sampleEntity;
    }

}
