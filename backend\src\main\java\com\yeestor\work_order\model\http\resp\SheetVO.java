package com.yeestor.work_order.model.http.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.extensions.XSSFCellBorder;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@Slf4j
@Data
@ApiModel(value = "SheetVO", description = "工作表视图对象")
public class SheetVO {

    @ApiModelProperty(value = "工作表名称")
    private String name;

    @ApiModelProperty(value = "工作表顺序")
    private int idx;

    @ApiModelProperty(value = "最大关注的列数")
    private int cols ;

    @ApiModel(value = "CellValue", description = "表格数据")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CellVO {
        @ApiModelProperty(value = "单元格值")
        private String value;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "列跨距,默认为0, 前端需要+1")
        private int colSpan;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "行跨距,默认为0, 前端需要+1")
        private int rowSpan;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "样式", required = true)
        private String styles;
    }

    @ApiModel(value = "RowValue", description = "行数据")
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RowVO {
        @ApiModelProperty(value = "单元格数据, String 为列， CellVO 为单元格")
        private HashMap<String, CellVO> cells;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "样式", required = true)
        private String styles;
    }


    @ApiModelProperty(value = "单元格数据")
    private List<RowVO> rowList;


    @SneakyThrows
    public static HashMap<String,String> cellStyleToCss(CellStyle cellStyle) {

        XSSFCellStyle style = (XSSFCellStyle) cellStyle;

        HashMap<String,String> styles = new HashMap<>();


        XSSFCellBorder.BorderSide[] borderSides = new XSSFCellBorder.BorderSide[] {
                XSSFCellBorder.BorderSide.BOTTOM,
                XSSFCellBorder.BorderSide.TOP,
                XSSFCellBorder.BorderSide.LEFT,
                XSSFCellBorder.BorderSide.RIGHT
        };

        for (XSSFCellBorder.BorderSide borderSide : borderSides) {
            styles.putAll(handleBorderStyle(borderSide,style));
        }

        // 前景色
        if( style.getFillPattern() == FillPatternType.SOLID_FOREGROUND) {
            Optional.ofNullable(style.getFillForegroundXSSFColor()).map(SheetVO::colorToHSLValue) .ifPresent(str -> styles.put("backgroundColor",str));
        }
        else {
            // 背景色
            Optional.ofNullable(style.getFillBackgroundXSSFColor()).map(SheetVO::colorToHSLValue) .ifPresent(str -> styles.put("backgroundColor",str));
        }

        // 字体大小
        short fontSize = style.getFont().getFontHeightInPoints();
        styles.put("fontSize",fontSize+"px");
        // 字体颜色
        Optional.ofNullable(style.getFont().getXSSFColor()).map(SheetVO::colorToHSLValue) .ifPresent(str -> styles.put("color",str));

        // 字体加粗
        boolean bold = style.getFont().getBold();
        styles.put("fontWeight",bold?"bold":"normal");
        // 字体倾斜
        boolean italic = style.getFont().getItalic();
        styles.put("fontStyle",italic?"italic":"normal");
//        // 字体下划线
//        boolean underline = style.getFont().getUnderline() != Font.U_NONE;
//        styles.put("textDecoration",underline?"underline":"none");
        return styles;
    }

    private static HashMap<String,String> handleBorderStyle(XSSFCellBorder.BorderSide side, XSSFCellStyle style) {
        HashMap<String,String> styles = new HashMap<>();
        // 边框样式

        XSSFColor borderColor = style.getBorderColor(side);
        BorderStyle borderStyle = getBorderStyle(style, side);
        if (borderStyle == null) {
            return new HashMap<>() ;
        }

        String borderPrefix = getBorderPrefix(side);

        String borderWidthStr = "thin" ;
        String borderStyleStr = borderStyleToCssValue(borderStyle);

        switch (borderStyle) {
            case NONE:
                return new HashMap<>();
            case MEDIUM:
            case MEDIUM_DASHED:
            case MEDIUM_DASH_DOT:
            case MEDIUM_DASH_DOT_DOT:
                borderWidthStr = "medium";
                break;
            case DOTTED:
            case DASH_DOT:
            case SLANTED_DASH_DOT:
            case DOUBLE:
            case THIN:
                borderWidthStr = "thin";
                break;
            case THICK:
                borderWidthStr = "thick";
                break;
            case HAIR:
                borderWidthStr = "hair";
                break;
        }
        styles.put(borderPrefix+"Style", borderStyleStr);
        styles.put(borderPrefix+"Width", borderWidthStr);
        Optional.ofNullable(borderColor).map(SheetVO::colorToHSLValue).ifPresent(str -> styles.put(borderPrefix+"Color",str));
        return styles;
    }

    private static BorderStyle getBorderStyle(XSSFCellStyle style,XSSFCellBorder.BorderSide side) {
        switch (side) {
            case TOP:
                return style.getBorderTop();
            case BOTTOM:
                return style.getBorderBottom();
            case LEFT:
                return style.getBorderLeft();
            case RIGHT:
                return style.getBorderRight();
            default:
                return null;
        }
    }

    private static String getBorderPrefix(XSSFCellBorder.BorderSide side) {
        switch (side) {
            case TOP:
                return "borderTop";
            case BOTTOM:
                return "borderBottom";
            case LEFT:
                return "borderLeft";
            case RIGHT:
                return "borderRight";
            default:
                return null;
        }
    }

    private static String borderStyleToCssValue(BorderStyle style) {
        switch (style) {
            case NONE:
                return "none";
            case DASHED:
            case MEDIUM_DASHED:
                return "dashed";
            case DOTTED:
            case DASH_DOT:
            case MEDIUM_DASH_DOT:
            case MEDIUM_DASH_DOT_DOT:
            case SLANTED_DASH_DOT:
                return "dotted";
            case DOUBLE:
                return "double";
            case THIN:
            case MEDIUM:
            case THICK:
            case HAIR:
                return "solid";
        }
        return "solid";
    }

    public static String colorToHSLValue(XSSFColor color) {
        byte[] rgb = color.getRGB() ;
        if (rgb == null) {
            return null ;
        }
        double tint = color.getTint();
        return rgbAndTintToHSL(rgb,tint);
    }


    public static String rgbAndTintToHSL(byte[] rgb, double tint) {
        double[] hsl = rgbToHSL(rgb, tint);
        double h = hsl[0] * 255;
        double s = hsl[1];
        double l = hsl[2];

        if( tint == 0 ) {
            //返回rgb
            return String.format("rgb(%d,%d,%d)", rgb[0] & 0xFF, rgb[1] & 0xFF, rgb[2] & 0xFF);
        }

        return String.format("hsl(%d,%d%%,%d%%)", (int)h, (int)(s  * 100  ) , (int)(l * 100) );
    }


    public static double[] rgbToHSL(byte[] rgb, double tint) {
        int r = rgb[0] & 0xFF;
        int g = rgb[1] & 0xFF;
        int b = rgb[2] & 0xFF;
        double rd = r / 255.0;
        double gd = g / 255.0;
        double bd = b / 255.0 ;
        double max = Math.max(rd, Math.max(gd, bd));
        double min = Math.min(rd, Math.min(gd, bd));
        double h, s, l = (max + min) / 2.0;


        if (max == min) {
            h = s = 0; // achromatic
        }
        else {
            double d = max - min;
            s = d / (1 - Math.abs(2 * l - 1));
            if (max == rd) {
                h = (gd - bd) / d + (gd < bd ? 6.0 : 0);
            }
            else if (max == gd) {
                h = (bd - rd) / d + 2.0;
            }
            else {
                h = (rd - gd) / d + 4.0;
            }
            h /= 6.0;
        }

        l *= 255.0 ;
        /*
        If (tint < 0)
       Lum' = Lum * (1.0 + tint)

       For example: Lum = 200; tint = -0.5; Darken 50%
       Lum' = 200 * (0.5) => 100
       For example: Lum = 200; tint = -1.0; Darken 100% (make black)
       Lum' = 200 * (1.0-1.0) => 0
       If (tint > 0)
       Lum' = Lum * (1.0-tint) + (HLSMAX - HLSMAX * (1.0-tint))
       For example: Lum = 100; tint = 0.75; Lighten 75%

       Lum' = 100 * (1-.75) + (HLSMAX - HLSMAX*(1-.75))
       = 100 * .25 + (255 - 255 * .25)
       = 25 + (255 - 63) = 25 + 192 = 217
       For example: Lum = 100; tint = 1.0; Lighten 100% (make white)
       Lum' = 100 * (1-1) + (HLSMAX - HLSMAX*(1-1))
       = 100 * 0 + (255 - 255 * 0)
       = 0 + (255 - 0) = 255
         */
        if( tint < 0 ) {
            l = l * (1.0 + tint);
        }
        else if( tint > 0 ) {
            l = l * (1.0 - tint) + (255 - 255 * (1.0 - tint));
        }
        l /= 255.0 ;
        return new double[]{h, s, l};


    }

}
