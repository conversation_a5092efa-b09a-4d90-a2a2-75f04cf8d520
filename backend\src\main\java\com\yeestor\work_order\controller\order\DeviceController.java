package com.yeestor.work_order.controller.order;

import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.model.http.req.DeviceReleaseParams;
import com.yeestor.work_order.model.http.req.DeviceRestartParams;
import com.yeestor.work_order.model.http.req.ForceReleaseDeviceParams;
import com.yeestor.work_order.model.http.req.RetestParams;
import com.yeestor.work_order.model.http.req.order.OrderEnvConfirmReq;
import com.yeestor.work_order.model.http.resp.order.MachineDetailVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.model.rms.*;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.repository.PlanDeviceRepository;
import com.yeestor.work_order.service.device.RMSDeviceService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.role.RoleService;
import com.yeestor.work_order.utils.LogUtils;
import com.yeestor.work_order.utils.RMSApis;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/device")
@Api(value = "DeviceController", tags = {"设备接口"})
public class DeviceController {

    private final OrderPlanRepository orderPlanRepository;
    private final PlanDeviceRepository planDeviceRepository;

    private final OrderService orderService ;
    private final FlashService flashService ;
    private final PlanService planService;
    private final DeviceService deviceService;
    private final RMSDeviceService rmsDeviceService;
    private final RoleService roleService;
    private final RMSApis rmsApis;

    private final DataChangeListener dataChangeListener;

    @GetMapping("/count/{product}")
    @ApiOperation(value = "获取指定产品线下的设备分布", notes = "获取指定产品线下的设备分布")
    public HandleResp<DeviceCountInfoVO> fetchDeviceCount(
            @PathVariable(value = "product") String product,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        return HandleResp.ok(rmsDeviceService.fetchDeviceCount(product), "获取成功");
    }

    @PostMapping("/rms/release")
    @ApiOperation(value = "直接释放RMS的设备", notes = "直接释放RMS的设备")
    public HandleResp<String> releaseDevice(
            @RequestParam(value = "ip") String ip,
            @RequestParam(value = "mac") String mac,
            @RequestParam(value = "orderFlashNo") String orderFlashNo,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "释放RMS设备";
        OrderFlashEntity flashEntity = flashService.fetchFlashByFlashNoOrElseThrow(orderFlashNo);
        long orderId = flashEntity.getOrderId();
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        log.info("工单[{}]准备释放设备[{}]", orderEntity.getNo(), mac);
        roleService.checkPermission(userDetail, orderEntity.getSubProduct(), operate, Permission.RELEASE_LOCKED_DEVICE);

        int runningSize = deviceService.findDeviceByOrderIdAndMac(orderId, mac);
        if (runningSize > 0) {
            return HandleResp.failed("设备还在测试中！");
        }
        log.info("{} 准备手动释放RMS中的设备:{}", userDetail.getUsername(), mac);
        List<String> deviceIpList = List.of(ip);
        List<String> deviceMacList = List.of(mac);
        rmsApis.unlockDevice(deviceIpList, deviceMacList, orderFlashNo);

        return HandleResp.ok(mac, "设备释放成功!");
    }

    @Transactional
    @PostMapping("/force-release")
    @ApiOperation(value = "强制释放指定的单台设备,并更新plan的状态", notes = "强制释放指定的单台设备,并更新plan的状态,需要强制释放的权限")
    public HandleResp<String> forceReleaseDevice(
            @RequestBody @Valid ForceReleaseDeviceParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {

        String operate = "强制释放设备" ;
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(params.getOrderId(), params.getFlash()) ;
        LogUtils.setOrderAndFlashTracePoint(flashEntity.getOrderId(), flashEntity.getFlash(), operate);

        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(flashEntity.getOrderId()) ;

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(),operate, Permission.FORCE_RELEASE_DEVICE);
        log.info("{} 准备释放Plan:{}中的设备:{}", userDetail.getUsername(),params.getPlan(),params);

        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(orderEntity.getId(), flashEntity.getFlash(),
                params.getPlan());

        deviceService.forceReleaseDevice(orderEntity, flashEntity, planEntity, params);

        LogUtils.clearTracePoint();

        return HandleResp.ok(params.getMac(), "设备释放成功!");
    }

    @ApiOperation(value = "设备释放", notes = "释放指定工单下的指定plan的指定设备")
    @PostMapping("/release")
    public HandleResp<String> releaseDevice(
            @RequestBody @Valid DeviceReleaseParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "手动释放设备" ;
        LogUtils.setOrderTracePoint(params.getOrderId(), operate);
        log.debug("release device, params: {}", params);
        // 检查 params.getPlanDeviceList() 中是否有mac为空的情况

        if (params.getPlanDeviceList().stream().anyMatch(p -> p.getMacList() == null || p.getMacList().isEmpty())) {
            return HandleResp.failed("Plan设备列表中有设备为空");
        }
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(params.getOrderId()) ;

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(),operate, Permission.OPERATE_PLAN, Permission.START_TEST, Permission.FORCE_RELEASE_DEVICE);

        if (!orderPlanRepository.existsByIdIn(params.getPlanDeviceList().stream().map(OrderEnvConfirmReq.PlanDeviceInfo::getPlanId).collect(Collectors.toList()))) {
            return HandleResp.failed("plan不存在");
        }

        for (OrderEnvConfirmReq.PlanDeviceInfo planDeviceInfo : params.getPlanDeviceList()) {

            OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planDeviceInfo.getPlanId());

            // 获取 对应Flash 批次的实体
            OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());

            // 找出plan 下的所有设备
            List<PlanDeviceEntity> allPlanDeviceEntityList = planDeviceRepository.findAllByPlanId(planEntity.getId());

            List<String> allMacList = allPlanDeviceEntityList.stream().map(PlanDeviceEntity::getMac).collect(Collectors.toList());

            // 判断设备是否存在
            // 如果ip列表中的设备在 plan 下不存在，则返回错误
            for (String mac : planDeviceInfo.getMacList()) {
                if (!allMacList.contains(mac)) {
                    return HandleResp.failed("设备" + mac + "不存在" + planEntity.getName() + "中!!！");
                }
            }
            // 过滤出来需要操作的 PlanDeviceEntity
            List<PlanDeviceEntity> planDeviceEntityList = allPlanDeviceEntityList.stream().filter(planDeviceEntity -> planDeviceInfo.getMacList().contains(planDeviceEntity.getMac())).collect(Collectors.toList());

            // 判断设备是否已经满足释放条件
            for (PlanDeviceEntity planDeviceEntity : planDeviceEntityList) {
                if (planDeviceEntity.isNotFinished() && !planDeviceEntity.isOccupied()) {
                    return HandleResp.failed("设备" + planDeviceEntity.getMac() + "不满足释放条件！");
                }
            }

            log.info("即将释放{}下的设备：{}",planEntity.getName(), planDeviceEntityList.stream().map(d  -> String.format("%s(%s)",d.getNo(),d.getMac())).collect(Collectors.joining(",")));
            // 释放设备
            deviceService.releaseDevices(flashEntity, planEntity, planDeviceEntityList, userDetail.getUid(), userDetail.getUsername());

            dataChangeListener.onDataChange(DataChangeEvent.builder()
                    .type(DataChangeEvent.Type.DEVICE_RELEASED)
                    .orderId(params.getOrderId())
                    .flash(planEntity.getFlash())
                    .planId(planEntity.getId())
                    .build());
            // 如果设备都已经释放了，就更新 plan的状态
            planService.checkDeviceReleaseStatus(orderEntity,flashEntity, planEntity);
        }



        return HandleResp.ok(null, "释放成功");
    }

    @ApiOperation(value = "批量重测设备", notes = "批量重测工单下的设备,需要以plan id 来进行分组")
    @PostMapping("/order/{orderId}/re-test")
    public HandleResp<String> reTestDevices(
            @PathVariable("orderId") @ApiParam("工单id") @Min(value = 1, message = "无效的工单id") long orderId,
            @RequestBody @Valid RetestParams body,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "批量重测设备" ;
        LogUtils.setOrderAndFlashTracePoint(orderId, body.getOrderNo(), operate);
        log.debug("reTestDevices, params: {}", body);

        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);

        roleService.checkPermission(userDetail, orderEntity.getSubProduct(),operate, Permission.REPEAT_TEST);

        if (!Objects.equals(orderEntity.getNo(), body.getOrderNo())) {
            flashService.findFlashOrElseThrow(body.getOrderNo());
        }
        for (OrderEnvConfirmReq.PlanDeviceInfo planDeviceInfo :body.getPlanDeviceList()) {
            OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planDeviceInfo.getPlanId());
            long planId = planEntity.getId();
            if(planEntity.getStatus().ordinal() > OrderPlanEntity.Status.RUNNING.ordinal()){
                return HandleResp.failed(planEntity.getName() + "已完成或者取消的Plan 不能直接启动测试!");
            }
            if (planDeviceRepository.countByPlanIdAndMacIn(planId, planDeviceInfo.getMacList()) != planDeviceInfo.getMacList().size()) {
                return HandleResp.failed("设备不存在!");
            }
            // 检测当前设备是否存在被重复锁定问题
            for (String mac : planDeviceInfo.getMacList()) {
                rmsDeviceService.findLockInfoInTest(planId, mac);
            }
        }
        for (OrderEnvConfirmReq.PlanDeviceInfo planDeviceInfo : body.getPlanDeviceList()) {
            OrderPlanEntity p = planService.findPlanOrElseThrow(planDeviceInfo.getPlanId());
            OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(p.getOrderId(), p.getFlash());

            //如果Plan没有启动的话， 启动Plan
            planService.updatePlanStatusToRunning(
                    orderEntity,
                    flashEntity,
                    p,
                    userDetail
            );
            // 释放完成后，检查任务的状态
            planService.cancelPlanJob(flashEntity, p);
            // 启动设备测试
            deviceService.startTest(p, planDeviceInfo.getMacList());
        }
        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "重测成功,请留意测试结果!");
    }


    @PostMapping("/test/start")
    @ApiOperation(value = "启动测试", notes = "测试指定plan 下的指定设备")
    public HandleResp<String> testStart(
            @RequestParam(value = "planId") long planId,
            @RequestParam(value = "ip") String ip,
            @RequestParam(value = "mac") String mac,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {

        if (!planDeviceRepository.existsByPlanIdAndMac(planId, mac)) {
            return HandleResp.failed("设备不存在");
        }
        String operate = "启动测试" ;
        // 根据 planId 找到对应的 plan
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        LogUtils.setOrderAndFlashTracePoint(planEntity.getOrderId(), planEntity.getFlash(), operate);
        log.info("test start, plan: {}-{}, ip: {} -- mac: {}", planEntity.getName(), planId, ip,mac);
        if (planEntity.getStatus().ordinal() > OrderPlanEntity.Status.RUNNING.ordinal()) {
            return HandleResp.failed("已完成或者取消的Plan 不能直接启动测试");
        }
        // 检测当前设备是否存在被重复锁定问题，或者锁定异常问题
        rmsDeviceService.findLockInfoInTest(planId, mac);

        // 工单实体类
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId()) ;
        roleService.checkPermission(userDetail,orderEntity.getSubProduct(),operate, Permission.REPEAT_TEST);

        // 获取 对应Flash 批次的实体
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash()) ;

        //如果Plan没有启动的话， 启动Plan
        planService.updatePlanStatusToRunning(
                orderEntity,
                flashEntity,
                planEntity,
                userDetail
        );

        // 释放完成后，检查任务的状态
        log.info("用户:{} 测试设备:{}", userDetail.getUsername(), mac);
        planService.cancelPlanJob(flashEntity, planEntity);
        deviceService.startTest(
                planEntity,
                Collections.singletonList(mac)
        );
        LogUtils.clearTracePoint();

        return HandleResp.ok(null, "测试成功,请留意测试结果!");
    }


    @PostMapping("/test/{planId}/stop")
    @ApiOperation(value = "停止指定的电脑上的测试", notes = "停止指定的电脑上的测试")
    public HandleResp<String> stopDeviceTest(
            @ApiParam("PlanID") @PathVariable(value = "planId") @Min(value = 1, message = "PlanId 无效") long planId,
            @ApiParam("需要停止测试的ip") @RequestParam(value = "ipList") @NotEmpty(message = "设备列表为空") List<String> ipList,
            @ApiParam("需要停止测试的mac") @RequestParam(value = "macList") @NotEmpty(message = "设备列表为空") List<String> macList,
            @NotBlank(message = "设备停止测试原因不能为空") @RequestParam("reason") @ApiParam("设备停止测试的原因") String reason,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        log.info("stop test start, planId: {}, ipList: {} macList:{}", planId, ipList,macList);

        String operate = "停止测试" ;
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        LogUtils.setOrderTracePoint(planEntity.getOrderId(), operate);

        if (macList.isEmpty()) {
            return HandleResp.failed("macList不能为空");
        }
        if (planDeviceRepository.countByPlanIdAndMacIn(planId, macList) != macList.size()) {
            return HandleResp.failed("设备不存在");
        }

        // 工单实体类
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(), operate,Permission.OPERATE_PLAN,Permission.START_TEST);

        // 获取 对应Flash 批次的实体
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(planEntity.getOrderId(), planEntity.getFlash());

        deviceService.stopRunningDevice(orderEntity, flashEntity, planEntity, macList, reason);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.DEVICE_STATUS_CHANGED)
                        .orderId(planEntity.getOrderId())
                        .flash(planEntity.getFlash())
                        .planId(planId)
                .build());

        LogUtils.clearTracePoint();
        return HandleResp.ok(null, "停止成功");
    }

    @SneakyThrows
    @PostMapping("/test/reselect")
    @ApiOperation(value = "重新选择环境", notes = "重新测试指定plan 下的指定设备， 可以变更为其他的设备，只有当设备已经测试完成，才能重新选择环境")
    public HandleResp<String> restartDevice(
            @RequestBody @Valid DeviceRestartParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ) {
        String operate = "重新选择环境" ;
        LogUtils.setOrderTracePoint(params.getOrderId(), operate);
        log.debug("restart device, params: {}", params);

        if (params.getOldMac().equals(params.getNewMac())) {
            return HandleResp.failed("新旧设备不能相同");
        }
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(params.getOrderId());

        roleService.checkPermission(userDetail,orderEntity.getSubProduct(), operate,Permission.CONFIRM_ENVIRONMENT);

        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(params.getPlanId());

        // 获取对应的设备
        PlanDeviceEntity planDeviceEntity = planDeviceRepository.findByPlanIdAndMac(params.getPlanId(), params.getOldMac());
        if (planDeviceEntity == null || planDeviceEntity.getStatus() == PlanDeviceEntity.Status.RUNNING) {
            // 如果之前的设备不存在,或者设备正在测试中, 则不能重新选择
            return HandleResp.failed("设备" + params.getOldMac() + "不满足重新选择条件！");
        }
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderEntity.getId(), planEntity.getFlash()) ;


        // 判断是否可以重新选择
        // 替换设备的时候, 不能选择已经被占用的设备
        List<DeviceModel> availableDevices = rmsDeviceService.getAvailableDevices(orderEntity.getSubProduct());
        DeviceModel newDevice = availableDevices.stream()
                .filter(deviceModel -> deviceModel.getMac().equals(params.getNewMac()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("设备" + params.getNewMac() + "已经被占用！"));


        // 如果新设备已经在设备列表中, 则不能通过替换设备的方式重新选择
        if (planDeviceRepository.existsByPlanIdAndMac(params.getPlanId(), params.getNewMac())) {
            return HandleResp.failed("设备" + params.getNewMac() + "已经在设备列表中！");
        }


        // 重新分配设备
        deviceService.reAllocDevice(orderEntity,flashEntity, planEntity, planDeviceEntity, newDevice);

        dataChangeListener.onDataChange(DataChangeEvent.builder()
                .type(DataChangeEvent.Type.DEVICE_ADDED)
                .orderId(planEntity.getOrderId())
                .flash(planEntity.getFlash())
                .planId(params.getPlanId())
                .build());

        LogUtils.clearTracePoint();

        return HandleResp.ok(null, "测试环境分配成功！");
    }

    /*
     * 获取 plan 下的某个设备需要重测所需的设备列表
     */
    @GetMapping("/list/paired")
    @ApiOperation(value = "获取匹配的设备列表", notes = "获取指定plan下的可以测试的设备列表")
    public HandleResp<List<DeviceModel>> fetchNeedDevices(
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail,
            @RequestParam(value = "planId") long planId
    ) {
        LogUtils.clearTracePoint();
        log.info("fetchNeedDevices, params: {}", planId);
        OrderPlanEntity planEntity = orderPlanRepository.findById(planId).orElseThrow(() -> new RuntimeException("id:" + planId + "的plan不存在"));

        // 获取工单实体类
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(planEntity.getOrderId());

        // 获取所有支持的设备
        List<DeviceModel> supportDevices = deviceService.findSupportDevicesByPlan(orderEntity, planEntity, false);
        log.info("device fetch supportDevices: {}", supportDevices);

        // 这些设备里面,有些设备是手动测试的.需要过滤掉
        List<PlanDeviceEntity> devices =  planDeviceRepository.fetchAllInManualPlanBySubProduct(
                orderEntity.getSubProduct(),
                List.of(OrderPlanEntity.Status.READY,OrderPlanEntity.Status.RUNNING),
                List.of(PlanDeviceEntity.Status.CONFIRMED,PlanDeviceEntity.Status.RUNNING)
        );
        // 如果是手动测试的, 则更新状态为已经 手动测试占用中
        devices.forEach(planDeviceEntity -> supportDevices.stream()
                .filter(deviceModel -> deviceModel.getMac().equals(planDeviceEntity.getMac()))
                .findFirst()
                .ifPresent(deviceModel -> deviceModel.setStatus("手动测试占用中")));

        // 获取已经分配了的设备,然后从里面找出合适的设备

        List<DeviceModel> occupiedDevices = deviceService.findSupportDevicesInOccupied(orderEntity ,planEntity);
        log.info("占用中的设备有: {}", occupiedDevices);
        occupiedDevices.forEach(deviceModel -> {
            deviceModel.setStatus("占用中");
            supportDevices.add(deviceModel);
        });

        if (supportDevices.isEmpty()) {
            return HandleResp.failed("没有支持的设备");
        }

        return HandleResp.ok(supportDevices, "获取成功");
    }

    @GetMapping("/machine/info")
    @ApiOperation(value = "机房设备获取测试信息", notes = "机房设备获取测试信息")
    public HandleResp<MachineDetailVO> fetchMachineInfo(@RequestParam("pcNo") @ApiParam("测试机") String pcNo) {
        PlanDeviceEntity planDeviceEntity = deviceService.findPlanDeviceByPcNo(pcNo);
        long planId = planDeviceEntity.getPlanId();
        List<PlanDeviceEntity> deviceList = deviceService.findDevicesByPlanId(planId);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        long orderId = planEntity.getOrderId();
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, planEntity.getFlash());
        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderDetailEntity orderDetailEntity = orderService.findOrderDetailOrElseThrow(orderId);
        return HandleResp.ok(MachineDetailVO.convertToMachineDetail(orderDetailEntity, orderEntity, flashEntity, planEntity, deviceList), "获取成功");
    }
}
