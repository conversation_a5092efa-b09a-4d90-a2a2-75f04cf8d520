
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wo-alpha-deployment
  namespace: yeestor
  labels:
    app: wo-alpha-deployment
spec:
  replicas: 1
  template:
    metadata:
      name: wo-alpha
      labels:
        app: wo-alpha
    spec:
      containers:
        - name: wo-alpha
          image: gateway.yeestor.com:5000/workorder:v0.0929.01
          imagePullPolicy: Always
          lifecycle:
            preStop:
              exec:
                command: [ "sh", "-c", "sleep 10" ]
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: alpha
            - name: EUREKA_SERVER_ADDRESS
              value: http://eureka.yeestor.svc.cluster.local:8788/eureka
            - name: ADMIN_SERVER_URL
              value: http://eureka.yeestor.svc.cluster.local:8788/admin
            - name: EUREKA_HOSTNAME
              value: wo-alpha
            - name: TZ
              value: Asia/Shanghai
      restartPolicy: Always
  selector:
    matchLabels:
      app: wo-alpha
