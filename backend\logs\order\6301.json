{"@timestamp":"2025-07-23T10:33:02.926+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:33:02.934+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.938+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.938+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:33:02.938+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c9ae01338d0c1593","spanId":"c9ae01338d0c1593","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.694+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:39:02.695+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.699+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.699+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:39:02.699+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4156068f0d773742","spanId":"4156068f0d773742","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.672+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:45:02.676+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.681+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.681+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:45:02.681+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca79aba365cf3665","spanId":"ca79aba365cf3665","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.688+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:48:02.689+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.693+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.693+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:48:02.693+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d0a7518282c9dc6a","spanId":"d0a7518282c9dc6a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.701+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:51:02.702+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.706+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.706+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:51:02.706+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b61240654624c38b","spanId":"b61240654624c38b","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.765+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:02.766+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.769+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.769+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.769+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4f9618b971a76506","spanId":"4f9618b971a76506","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.68+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:03:02.681+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.683+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.683+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:03:02.683+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"48d779ff5be07e22","spanId":"48d779ff5be07e22","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.689+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:09:02.69+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.692+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.692+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:09:02.692+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5e31ca0213c92a43","spanId":"5e31ca0213c92a43","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.675+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:12:02.676+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.678+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:12:02.678+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7566a74499be9c28","spanId":"7566a74499be9c28","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.671+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:15:02.671+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.673+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.673+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:15:02.673+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"403a8c5f3fe4f430","spanId":"403a8c5f3fe4f430","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.707+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:39:02.713+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.717+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.717+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:39:02.717+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973fdb936df78429","spanId":"973fdb936df78429","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.737+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:02.738+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.74+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.74+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.74+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00a2a82cfb645131","spanId":"00a2a82cfb645131","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.673+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:45:02.674+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.676+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.676+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:45:02.676+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"18f0d1201d968c00","spanId":"18f0d1201d968c00","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.701+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:51:02.702+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.704+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.704+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:51:02.704+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"973d71c988b7d137","spanId":"973d71c988b7d137","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.686+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:00:02.687+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.689+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.69+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:00:02.69+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e6bbe5f9b5770639","spanId":"e6bbe5f9b5770639","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.663+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:03:02.664+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.666+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:03:02.666+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bb2bfd6649e9895","spanId":"9bb2bfd6649e9895","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.682+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:15:02.683+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.685+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.685+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:15:02.685+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"86641994e0f497b5","spanId":"86641994e0f497b5","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.702+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:27:02.703+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.707+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.707+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:27:02.707+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"311d7e7967a92d9a","spanId":"311d7e7967a92d9a","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.701+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:02.702+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.704+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.704+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.704+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d1740e4bfe146bd9","spanId":"d1740e4bfe146bd9","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.681+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:36:02.682+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.684+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.684+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:36:02.684+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af46839894221f1d","spanId":"af46839894221f1d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.662+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:39:02.663+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.665+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:39:02.665+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0ef7e08a1aad976f","spanId":"0ef7e08a1aad976f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.704+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:02.705+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.707+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.707+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.707+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a106d94f31e5ca4","spanId":"1a106d94f31e5ca4","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.681+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:45:02.682+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.686+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.687+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:45:02.687+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1e6ae94c30111683","spanId":"1e6ae94c30111683","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.659+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:51:02.66+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.662+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.662+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:51:02.662+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"62023ff9964e5784","spanId":"62023ff9964e5784","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.662+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:00:02.663+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.665+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:00:02.665+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"95aadb163e1c9e9d","spanId":"95aadb163e1c9e9d","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.679+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:12:02.68+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.682+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.682+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:12:02.682+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"434dec1b2047558e","spanId":"434dec1b2047558e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.662+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:15:02.662+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.664+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.664+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:15:02.664+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"de62648ac05ca52f","spanId":"de62648ac05ca52f","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.68+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:02.681+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.683+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.683+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.683+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"53d96be1174d7e23","spanId":"53d96be1174d7e23","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.669+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:21:02.67+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.672+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.672+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:21:02.672+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d516cbd9f838fd27","spanId":"d516cbd9f838fd27","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.668+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:27:02.669+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.671+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.671+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:27:02.671+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9957eb7af273a9e3","spanId":"9957eb7af273a9e3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.681+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:02.682+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.684+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.684+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.684+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c021bdfa31903cc1","spanId":"c021bdfa31903cc1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.683+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:33:02.684+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.686+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.686+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:33:02.686+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68ff5dc454fc73e3","spanId":"68ff5dc454fc73e3","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.669+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:36:02.67+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.672+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.672+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:36:02.672+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"088477336f984048","spanId":"088477336f984048","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.677+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:39:02.678+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.68+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.68+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:39:02.68+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"44b6c53308f808c1","spanId":"44b6c53308f808c1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.691+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:02.692+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.694+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.694+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.694+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ca052c71a18ab0af","spanId":"ca052c71a18ab0af","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.688+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:45:02.688+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.69+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.69+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:45:02.69+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bc9152275f7ab033","spanId":"bc9152275f7ab033","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.672+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:48:02.673+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.675+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.675+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:48:02.675+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0fc18d367305c053","spanId":"0fc18d367305c053","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.683+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:51:02.683+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.685+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.685+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:51:02.685+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2601f02188e18869","spanId":"2601f02188e18869","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.661+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:00:02.662+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.664+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.664+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:00:02.664+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0575a9143a87d2c1","spanId":"0575a9143a87d2c1","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.684+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:03:02.685+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.688+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.688+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:03:02.688+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bbc257d393e564c6","spanId":"bbc257d393e564c6","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.68+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:02.681+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.683+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.683+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.683+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"800a4cc353f41ed4","spanId":"800a4cc353f41ed4","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.67+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:09:02.671+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.673+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.673+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:09:02.673+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8d91c0301fa3152e","spanId":"8d91c0301fa3152e","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.676+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6301","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:15:02.676+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6532, orderId=6301, flash=BYT-6285ENAB-0T26-J_64GB, orderFlashNo=YS6285##MP#########16695#0T26-DDR_TLC250387_BYT-6285ENAB-0T26-J_64GB, num=104, leftNum=100)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.68+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.68+08:00","@version":"1","message":"[6301] - [BYT-6285ENAB-0T26-J_64GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6301","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:15:02.68+08:00","@version":"1","message":"[SD] end check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0737ae6500485832","spanId":"0737ae6500485832","context":"QueueService","no":"6301","traceType":"分配设备"}
