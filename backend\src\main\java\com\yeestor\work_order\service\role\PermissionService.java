package com.yeestor.work_order.service.role;

import com.yeestor.work_order.entity.role.RolePermissionEntity;
import com.yeestor.work_order.entity.role.RoleUserEntity;
import com.yeestor.work_order.repository.role.RolePermissionRepository;
import com.yeestor.work_order.repository.role.RoleUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionService {

    private final RoleUserRepository roleUserRepository;
    private final RolePermissionRepository rolePermissionRepository ;



    /**
     * 获取用户的权限列表。
     */
    public void getUserPermissions() {
        log.info("获取用户的权限列表。");
        // FIXME: 获取当前登录用户的钉钉ID
        String userDingTalkID = "0653290119972081";
        List<RoleUserEntity> roleUserEntityList = roleUserRepository.findAllByUserId(userDingTalkID);
        List<Long> roleUserIdList = roleUserEntityList.stream().map(RoleUserEntity::getRoleId).collect(java.util.stream.Collectors.toList());
        List<RolePermissionEntity> permissionEntityList = rolePermissionRepository.findAllByRoleIdIn(roleUserIdList);

        log.info("获取用户的权限列表：{}", permissionEntityList);

        // 通过产品线来分组权限列表。
        Map<String, List<String>> productPermissionMap = permissionEntityList.stream()
                .collect(Collectors.groupingBy(
                        RolePermissionEntity::getProduct,
                        Collectors.mapping(RolePermissionEntity::getPermission, Collectors.toList())
                        )
                );
//        Map<PermissionGroup, List<Permission>> listHashMap = Arrays.stream(Permission.values())
//                .filter(p ->
//                        Optional.of(p).map(Permission::getGroup).isPresent()
//                )
//                .collect(Collectors.groupingBy(p -> Optional.of(p).map(Permission::getGroup).get()));


    }

}
