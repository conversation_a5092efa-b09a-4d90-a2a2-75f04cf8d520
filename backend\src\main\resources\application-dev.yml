server:
  port: 8793

management:
  endpoints:
    web:
      exposure:
        include: '*'

spring:
  application:
    name: system-workorder-dev
    title: 工单系统-开发
  cloud:
    client:
      ip-address: ***********
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  main:
    allow-bean-definition-overriding: true
  boot:
    admin:
      client:
        url: ${ADMIN_SERVER_URL:http://gateway.yeestor.com:8788/admin}
        instance:
          prefer-ip: true
  datasource:
    url: ************************************************************************************
    username: root
    password: Reliable123!

  jpa:
    hibernate:
      ddl-auto: 'update'
    show-sql: false
    open-in-view: false
  redis:
    database: 1
    host: ***********
    port: 6379
    timeout: 1000

  data:
    redis:
      repositories:
        enabled: false

  quartz:
    enable: true
    auto-startup: false
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    properties:
      org:
        quartz:
          scheduler:
            instanceId: AUTO
          jobStore:
            isClustered: true
  ### 邮箱信息配置，使用的事ci公共邮箱
  mail:
    host: smtp.mxhichina.com
    username: <EMAIL>
    password: 4DvKl1K0hyqTBEAR
    port: 465
    properties:
      from: <EMAIL>
      mail:
        smtp:
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory

eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_SERVER_ADDRESS:http://gateway.yeestor.com:8788/eureka/}
      fetch-registry: true
      register-with-eureka: true
      lease-renewal-interval-in-seconds: 10

  instance:
    prefer-ip-address: true
    ip-address: ***********
    instance-id: ${spring.application.name}:${DOCKER_IP:${spring.cloud.client.ip-address}}:${DOCKER_PORT:${server.port}}
    lease-renewal-interval-in-seconds: 10

order:
  prefix: order/dev

logging:
  level:
    com.yeestor.work_order: TRACE
    org.hibernate.SQL: TRACE
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.jdbc.core.JdbcTemplate: DEBUG
    org.springframework.jdbc.core.StatementCreatorUtils: TRACE
    org.springframework.web: INFO
    org.springframework.web.HttpLogging: DEBUG
    org.springframework.security: DEBUG
    org.springframework.security.oauth2: DEBUG
