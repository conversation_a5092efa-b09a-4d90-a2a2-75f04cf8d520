package com.yeestor.work_order.repository.document;

import com.yeestor.work_order.entity.document.DocumentEntity;
import com.yeestor.work_order.entity.document.FlashDocumentEntity;
import com.yeestor.work_order.model.http.resp.order.FlashDocumentItemVO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FlashDocumentRepository extends JpaRepository<FlashDocumentEntity,Long> {

    @Query("select d from FlashDocument fd, Document d " +
            "where fd.documentId = d.id and fd.flashId = :flashId and  fd.type = :type ")
    List<DocumentEntity> findFlashErrorDocument(long flashId,String type);

    @Query("select d from FlashDocument fd, Document d " +
            "where fd.documentId = d.id and fd.flashId = :flashId and  fd.type = :type and fd.source = :source order by fd.id desc")
    List<DocumentEntity> findFlashErrorDocument(long flashId,String type, String source);

    void deleteAllByOrderIdAndFlashAndSource(long id, String flash, String rms);

    @Query("select new com.yeestor.work_order.model.http.resp.order.FlashDocumentItemVO(f, d) from FlashDocument f, Document d " +
            "where f.orderId = :orderId and f.documentId = d.id")
    List<FlashDocumentItemVO> findAllByOrderId(long orderId);

    @Query(
            "select d from FlashDocument fd, Document d " +
                    "where fd.documentId = d.id " +
                    "and fd.flash = :flash and fd.orderId = :orderId " +
                    "and fd.type = 'report' and fd.source = 'LOCAL' " +
                    "order by d.createdAt desc ")
    List<DocumentEntity> findLastUploadReport(long orderId, String flash, Pageable pageable);

    void deleteByIdAndOrderIdAndFlash(long id, long orderId, String flash);

    int countByOrderIdAndFlashAndTypeAndSource(long orderId, String flash, String type, String source);


}
