{"@timestamp":"2025-07-23T10:34:02.686+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:34:02.695+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.696+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.7+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.7+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.7+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.701+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.701+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.701+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.701+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.701+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.705+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.705+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.705+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.705+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:34:02.705+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2a5920a3e38523ca","spanId":"2a5920a3e38523ca","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.663+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:38:02.665+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.666+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.669+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.67+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.67+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.67+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.67+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.67+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.67+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.67+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.673+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.673+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.674+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.674+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:38:02.674+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"00288cf352e974e6","spanId":"00288cf352e974e6","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.707+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:42:02.709+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.709+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.711+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.711+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.712+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.712+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.712+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.712+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.712+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.712+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.715+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.715+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.715+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.715+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:42:02.715+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f629cbd2aef31cd5","spanId":"f629cbd2aef31cd5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.677+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:46:02.678+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.678+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.682+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.682+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.682+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.682+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.682+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.682+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.683+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.683+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.685+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.685+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.686+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.686+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:46:02.686+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af4bbc926b6d3a90","spanId":"af4bbc926b6d3a90","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.67+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:50:02.673+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.673+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.681+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.681+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.681+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.682+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.683+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.683+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.683+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.683+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.687+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.687+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.687+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.688+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:50:02.688+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"4868ed02ac2ffa6c","spanId":"4868ed02ac2ffa6c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.704+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:54:02.706+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.707+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.714+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.715+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.716+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.716+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.716+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.716+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.717+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.717+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.722+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.722+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.722+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.723+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:54:02.723+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bcdbab9f5a74398e","spanId":"bcdbab9f5a74398e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.663+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T10:58:02.664+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.664+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.667+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.668+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.668+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.668+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.668+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.668+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.668+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.668+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.671+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.671+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.671+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.671+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T10:58:02.671+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"47f3692caf068ac5","spanId":"47f3692caf068ac5","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.725+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:06:02.731+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.731+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.739+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.739+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.739+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.739+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.74+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.74+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.74+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.74+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.741+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.741+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.741+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.741+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:06:02.741+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc34c04f950a95aa","spanId":"fc34c04f950a95aa","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.671+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:10:02.672+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.672+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.674+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.674+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.674+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.675+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.675+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.675+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.675+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.675+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.676+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.676+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.677+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.677+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:10:02.677+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2ecf25e231a1c59e","spanId":"2ecf25e231a1c59e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.71+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:18:02.72+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.72+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.728+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.728+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.729+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.729+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.729+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.73+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.73+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.73+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.733+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.733+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.734+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.734+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:18:02.734+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"df1d8269a2ca826d","spanId":"df1d8269a2ca826d","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.806+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:38:02.813+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.813+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.817+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.817+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.817+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.817+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.833+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.833+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.833+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.833+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.836+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.836+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.836+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.836+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:38:02.836+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"fc7d61ecb4d99c44","spanId":"fc7d61ecb4d99c44","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.69+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:42:02.695+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.695+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.697+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.697+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.697+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.698+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.698+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.698+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.698+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.698+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.699+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.699+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.7+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.7+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:42:02.7+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f259e922f2da9982","spanId":"f259e922f2da9982","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.664+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:50:02.664+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.665+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.667+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.667+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.667+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.667+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.667+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.667+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.667+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.667+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.669+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.669+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.669+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.669+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:50:02.669+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"031c3d100b733b8a","spanId":"031c3d100b733b8a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.699+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:54:02.7+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.7+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.703+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.703+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.703+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.703+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.703+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.703+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.703+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.703+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.712+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.712+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.712+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.712+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:54:02.712+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b7821bab0ee9d575","spanId":"b7821bab0ee9d575","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.658+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T11:58:02.659+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.66+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.662+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.662+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.662+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.662+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.662+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.662+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.662+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.662+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.664+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.664+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.664+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.664+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T11:58:02.664+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3087328165e99412","spanId":"3087328165e99412","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.665+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:02:02.666+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.666+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.668+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.668+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.668+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.668+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.669+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.669+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.669+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.669+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.67+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.67+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.67+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.67+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:02:02.67+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"13117be857891905","spanId":"13117be857891905","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.693+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:06:02.693+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.693+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.695+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.695+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.696+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.696+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.696+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.696+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.696+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.696+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.697+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.697+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.697+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.697+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:06:02.697+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9105c7433ab2e40f","spanId":"9105c7433ab2e40f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.668+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:10:02.671+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.671+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.677+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.678+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.678+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.678+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.679+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.679+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.679+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.679+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.682+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.682+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.682+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.683+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:10:02.683+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b1569b7239a6cb82","spanId":"b1569b7239a6cb82","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.625+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:18:02.626+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.626+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.628+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.628+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.628+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.628+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.628+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.628+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.628+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.628+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.63+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.63+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.63+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.63+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:18:02.63+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"06d3687242c1bc16","spanId":"06d3687242c1bc16","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.606+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:26:02.607+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.607+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.609+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.609+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.609+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.609+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.609+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.609+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.609+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.609+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.611+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.611+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.611+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:26:02.611+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3e50238d719f0d68","spanId":"3e50238d719f0d68","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:30:02.612+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.612+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.614+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.614+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.614+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.615+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.615+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.615+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.616+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.616+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.616+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.616+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:30:02.616+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"83442f08f488e2cd","spanId":"83442f08f488e2cd","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:38:02.608+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.608+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.61+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.61+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.61+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.61+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.612+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.612+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.612+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.612+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:38:02.612+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"12393177c5d8a63e","spanId":"12393177c5d8a63e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.606+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:42:02.607+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.607+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.609+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.609+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.609+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.61+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.61+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.611+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.611+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.611+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:42:02.611+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"5baedb73e1a147c9","spanId":"5baedb73e1a147c9","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:46:02.608+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.608+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.611+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.611+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.612+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.612+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.612+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.614+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.614+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.614+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.614+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:46:02.614+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d4700eeb55ff730f","spanId":"d4700eeb55ff730f","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:54:02.612+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.612+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.615+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.615+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.615+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.615+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.615+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.617+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.617+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.617+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.617+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:54:02.617+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1a20e9a9fc5d0676","spanId":"1a20e9a9fc5d0676","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.608+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T12:58:02.609+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.609+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.611+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.611+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.611+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.611+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.611+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.613+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.613+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.613+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.613+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T12:58:02.613+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"74a6ba1800802e9a","spanId":"74a6ba1800802e9a","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:02:02.611+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.611+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.615+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.615+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.616+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.616+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.618+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.618+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.618+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:02:02.618+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"be0381252af9709c","spanId":"be0381252af9709c","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.617+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:06:02.618+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.618+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.62+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.62+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.62+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.62+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.62+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.62+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.62+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.62+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.622+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.622+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.622+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.622+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:06:02.622+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"9bc085be61235cc2","spanId":"9bc085be61235cc2","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.611+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:10:02.612+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.612+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.615+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.615+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.615+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.615+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.615+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.617+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.618+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.618+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:10:02.618+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8a12259b7e3efffc","spanId":"8a12259b7e3efffc","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.611+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:18:02.612+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.612+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.614+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.614+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.614+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.614+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.614+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.614+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.615+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.615+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.616+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.616+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.616+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.616+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:18:02.617+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8b3261cd2c818624","spanId":"8b3261cd2c818624","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:22:02.608+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.608+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.612+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.612+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.613+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.613+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.613+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.613+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.613+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.613+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.615+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.615+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.616+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:22:02.616+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"ad61a40ab2acd778","spanId":"ad61a40ab2acd778","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:26:02.608+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.608+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.61+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.61+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.611+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.611+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.611+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.612+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.613+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.613+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.613+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:26:02.613+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"82f57b0295211316","spanId":"82f57b0295211316","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:30:02.61+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.612+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.613+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.613+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.613+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.613+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.613+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.613+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.613+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.615+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.615+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.615+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:30:02.615+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"72c5c6a5e3d128bb","spanId":"72c5c6a5e3d128bb","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.61+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:34:02.614+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.614+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.618+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.618+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.618+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.618+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.618+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.621+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.621+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.621+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.621+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:34:02.621+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"49decf56811f9f62","spanId":"49decf56811f9f62","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:42:02.613+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.613+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.615+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.615+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.615+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.615+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.615+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.617+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.617+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.617+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.617+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:42:02.617+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a7cc2bb0a5b3bf32","spanId":"a7cc2bb0a5b3bf32","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.606+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:46:02.607+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.607+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.609+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.609+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.609+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.609+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.609+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.609+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.61+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.611+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.611+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.611+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.611+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:46:02.611+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"cbbd5d5e5f47d677","spanId":"cbbd5d5e5f47d677","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:54:02.613+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.613+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.616+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.616+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.616+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.616+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.616+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.616+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.616+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.618+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.618+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.618+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.618+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:54:02.618+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"68f793cfe72a5df0","spanId":"68f793cfe72a5df0","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T13:58:02.608+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.608+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.61+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.61+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.611+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.611+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.611+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.612+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.612+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.612+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.612+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T13:58:02.612+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c1eeeb175032d745","spanId":"c1eeeb175032d745","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.614+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:06:02.615+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.615+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.617+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.617+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.617+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.617+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.617+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.617+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.617+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.618+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.619+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.619+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.619+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.619+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:06:02.619+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"644be74889337b98","spanId":"644be74889337b98","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.615+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:18:02.616+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.618+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.618+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.619+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.619+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.619+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.619+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.619+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.619+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.62+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.62+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.62+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.62+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:18:02.621+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"886d8e617c57250e","spanId":"886d8e617c57250e","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.607+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T14:22:02.608+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6531, orderId=6300, flash=N38B_2x2_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_N38B_2x2_512GB, num=50, leftNum=34), OrderFlashEntity(id=6602, orderId=6300, flash=Sloane_test_512GB, orderFlashNo=YS9085##MP#########25016#N38B_QLC_1LU250191_Sloane_test_512GB, num=50, leftNum=30)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.608+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.61+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.61+08:00","@version":"1","message":"执行预分配前共有34颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.61+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.61+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.61+08:00","@version":"1","message":"[6300] - [N38B_2x2_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.612+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.612+08:00","@version":"1","message":"执行预分配前共有30颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.612+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.612+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
{"@timestamp":"2025-07-23T14:22:02.612+08:00","@version":"1","message":"[6300] - [Sloane_test_512GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"26d1c71d59383681","spanId":"26d1c71d59383681","context":"QueueService","no":"6300","traceType":"分配设备"}
