package com.yeestor.work_order.utils;

import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.model.rms.*;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.FileSystemResource;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;
import java.util.*;

@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class RMSApisTest {

    @Autowired
    RMSApis api ;

    @Test
    @DisplayName("获取Plan列表")
    void getPlanList() {
        List<PlanModel> models = api.getPlanList("GE","U2","release");
        Assertions.assertThat(models)
                .isNotNull()
                .hasSize(18);
    }

    @Test
    @DisplayName("获取设备列表")
    void getDeviceList() {
        List<DeviceModel> models = api.getDeviceList("U2");
        Assertions.assertThat(models).isNotNull();
    }


    @Test
    @DisplayName("上锁设备，并解锁")
    void lockAndUnlockDevice(){
        String deviceIp = "127.0.0.1";
        api.lockDevice(Collections.singletonList(deviceIp),List.of(),"test");
        api.unlockDevice(Collections.singletonList(deviceIp),List.of(),"test");
        Assertions.assertThat(deviceIp).isEqualTo("127.0.0.1");
    }

    @Test
    @DisplayName("启动测试")
    void startTestPlan(){
        PlanTestParams params = PlanTestParams.builder()
                .planName("test")
                .ipList(Collections.singletonList("127.0.0.1"))
                .bAutoLoc(false)
                .no("test")
                .group("")
                .product("SD")
                .marsPath("/home/<USER>/mars/")
                .mpPath("/home/<USER>/mp/")
                .planPath("/home/<USER>/plan/")
                .username("Test")
                .build();
        HandleResp<PlanTestRespModel> resp = api.startTestPlan(params);
        Assertions.assertThat(resp.getData()).isNotNull();
    }


    @Test
    @DisplayName("启动SATA的测试")
    void startTestPlan_SATA(){
        PlanTestParams params = PlanTestParams.builder()
                .planName("test")
                .ipList(Collections.singletonList("127.0.0.1"))
                .bAutoLoc(false)
                .no("test")
                .group("")
                .product("SATA")
                .marsPath("/home/<USER>/mars/")
                .mpPath("/home/<USER>/mp/")
                .planPath("/home/<USER>/plan/")
                .username("Test")
                .build();
        HandleResp<PlanTestRespModel> resp = api.startTestPlan(params);
        Assertions.assertThat(resp.getData()).isNotNull();
    }


    @Test
    @DisplayName("停止测试")
    void stopTestPlan(){
        StopTestParams params = StopTestParams.builder()
                .orderNo("test")
                .planDeviceList(Collections.singletonList(
                        StopTestParams.PlanDeviceIpInfo.builder()
                                .plan("test")
                                .ipList(Collections.singletonList("127.0.0.1"))
                                .build()
                        )
                )
                .count(3)

                .build();
        HandleResp<PlanTestRespModel> resp = api.stopTestPlan(params);
        Assertions.assertThat(resp.getData()).isNotNull();
    }


    @Test
    @DisplayName("打开工单，关闭工单")
    void createOrder_closeOrder(){
        OrderCreateParams params = OrderCreateParams.builder()
                .key_value("V2.0.0.13.572_7G6T-280B_HS-6285DA-3DV7-A_64GB")
                .chip("6285")
                .product_type("SD")
                .priority(1)
                .version_type("Release")
                .fw_svn("**********************:YS6285/fw/flycode/pa20.git")
                .fw_ver("")
                .mptool_path("\\\\************\\软件工具\\04外发工具版本\\MPTOOLS\\2022\\YS6285\\MPTool_V2.0.0.13.572_202207076285.7z")
                .mptool_ver("d4d3b492048179d73c27676cbed85ec2b6b5fb13")
                .cap("Unknown")
                .flash_type("7G6T-280B")
                .driver_ver("572")
                .version_log("---")
                .test_point("---")
                .mail_list("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>")
                .builder("clock.zhou")
                .date_type("test")
                .build() ;
        api.createOrder(params);
        api.closeOrder("test","test");
    }


    @Test
    @DisplayName("上传文件")
    void testUploadFile(){
        Map<String, String> resultMap = api.uploadReport(0,
                "WO_V6.10.00.39.360_1230_bugAlpha_3TA_EC1C983F84CB_1",
                "SD",
                "Plan1",
                "Bugs.Wan",
                Collections.singletonList(
                        new FileSystemResource(new File("./pom.xml"))
                ));
        Assertions.assertThat(resultMap)
                .isNotNull();

    }
    @Test
    @DisplayName("上传文件")
    void testUploadFile_type1(){
        Map<String, String> resultMap = api.uploadReport(1,
                "WO_V6.10.00.39.360_1230_bugAlpha_3TA_EC1C983F84CB_1",
                "SD",
                "Plan1",
                "Bugs.Wan",
                Collections.singletonList(
                        new FileSystemResource(new File("./pom.xml"))
                ));
        Assertions.assertThat(resultMap)
                .isNotNull();
    }


    @Test
    @DisplayName("合并报告")
    void testMergeOrderReport() {
        boolean result = api.mergeOrderReport("test",
                "SD"
               );
        Assertions.assertThat(result)
                .isTrue();
    }


    @Test
    @DisplayName("获取Plan合集列表")
    void testGetPlanGroups(){
        List<PlanGroupModel> groupModels = api.getPlanGroups("GE","SD","release") ;
        Assertions.assertThat(groupModels)
                .isNotNull()
                .map(PlanGroupModel::getPlans)
                .hasSizeGreaterThan(0);


    }

}