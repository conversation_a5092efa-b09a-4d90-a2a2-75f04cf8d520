{"@timestamp":"2025-07-23T15:09:03.103+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:09:03.113+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.121+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.122+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.131+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:09:03.131+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"93a9004390cb3903","spanId":"93a9004390cb3903","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.66+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:12:02.661+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.665+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.665+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.668+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:12:02.668+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"b2dbb5d186fbaa07","spanId":"b2dbb5d186fbaa07","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.658+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:15:02.66+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.662+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.663+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.666+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:15:02.666+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"366fbfbf0042d245","spanId":"366fbfbf0042d245","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.692+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:18:02.693+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.697+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.697+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.7+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:18:02.7+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"509723fe1cbe26be","spanId":"509723fe1cbe26be","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.671+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:24:02.673+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.677+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.678+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.681+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:24:02.682+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"45ba158818893726","spanId":"45ba158818893726","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.834+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:51:02.87+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.876+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.877+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.887+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:51:02.887+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"8e9f61f166545321","spanId":"8e9f61f166545321","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.654+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T15:57:02.657+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.663+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.664+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.667+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T15:57:02.668+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a6ac8ae2f84397cc","spanId":"a6ac8ae2f84397cc","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.078+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:00:05.08+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.085+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.085+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.09+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:00:05.09+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"537b65ab8888e7f5","spanId":"537b65ab8888e7f5","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.636+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:03:02.638+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.643+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:03:02.647+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"56d1f1f710b078ec","spanId":"56d1f1f710b078ec","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.638+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:09:02.64+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.643+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.643+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.647+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:09:02.647+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"1fc7041bec467e7c","spanId":"1fc7041bec467e7c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.624+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:12:02.625+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.628+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.628+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-6","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:12:02.631+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-6","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"a557c41ead88db36","spanId":"a557c41ead88db36","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:24:02.661+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6363","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:24:02.668+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6363","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.672+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6363","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.673+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6363","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.678+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6363","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:24:02.678+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"58abae32e22899b0","spanId":"58abae32e22899b0","no":"6363","traceType":"分配设备","context":"QueueService","flash":"YXW-6285ENAB-8T2M-A_32GB"}
{"@timestamp":"2025-07-23T16:30:02.609+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:30:02.611+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.617+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.617+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.621+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-8","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:30:02.621+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-8","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"64287836b940a138","spanId":"64287836b940a138","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.653+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:33:02.655+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.658+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.659+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.662+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-9","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:33:02.662+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-9","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"3590d8ce29f7498c","spanId":"3590d8ce29f7498c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.614+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:36:02.615+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.617+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.617+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.619+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-7","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:36:02.62+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-7","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e36d3cb3b850b11f","spanId":"e36d3cb3b850b11f","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.615+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:39:02.616+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.618+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.619+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.621+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:39:02.621+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2d6dc770d17ac9fd","spanId":"2d6dc770d17ac9fd","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.627+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:48:02.628+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.631+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.631+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.634+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:48:02.635+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"af1862c6e420aa3c","spanId":"af1862c6e420aa3c","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.612+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6363","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-23T16:51:02.613+08:00","@version":"1","message":"find 2 flash: [OrderFlashEntity(id=6604, orderId=6363, flash=YS-6297EN-WTSX2-A_128GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX2-A_128GB, num=100, leftNum=48), OrderFlashEntity(id=6605, orderId=6363, flash=YS-6297EN-WTSX4-A_256GB, orderFlashNo=YS6297##MP#########0071##WTS-9BC4_TLC250410_Alpha_YS-6297EN-WTSX4-A_256GB, num=120, leftNum=84)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.615+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.615+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX2-A_128GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.618+08:00","@version":"1","message":"fetchNeedTestPlanList planEntityList: []","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-10","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6363","traceType":"分配设备"}
{"@timestamp":"2025-07-23T16:51:02.618+08:00","@version":"1","message":"[6363] - [YS-6297EN-WTSX4-A_256GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-10","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"645e1c742183857e","spanId":"645e1c742183857e","context":"QueueService","no":"6363","traceType":"分配设备"}
