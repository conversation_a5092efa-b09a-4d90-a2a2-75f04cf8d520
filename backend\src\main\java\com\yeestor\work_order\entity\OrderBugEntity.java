package com.yeestor.work_order.entity;

import com.yeestor.entity.BaseEntity;
import com.yeestor.work_order.model.ci.OrderInfoModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString
@Entity(name = "OrderBug")
@Table(name = "wo_order_bug")
public class OrderBugEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    /**
     * 工单id 。这里不采用外键。
     */
    @Column(name = "order_id")
    private long orderId ;

    /**
     * bug 关联的flash 批次.
     */
    @Column(name = "flash")
    private String flash ;

    /**
     * bug 关联的Plan名称.
     */
    @Column(name = "plan")
    private String plan ;

    /**
     * bug关联的设备
     */
    @Column(name = "device" )
    private String device ;

    /**
     * bug关联的样片编号
     */
    @Column(name = "sample" )
    private String sample;


    /**
     * 禅道中bug id
     */
    @Column(name = "bug_id")
    private String bugId ;


    @Column(name = "title")
    private String title ;

    /**
     * 禅道中的优先级
     */
    @Column(name = "pri")
    private int pri ;

    /**
     * 禅道中的严重度
     */
    @Column(name = "severity")
    private int severity ;


    /**
     * 禅道中的bug 的状态
     */
    @Column(name = "status")
    private String status ;

    @Column(name = "created_by")
    private String createdBy ;

    @Column(name = "created_p")
    private String createdPerson ;

    @Transient
    public OrderInfoModel.BugLink getBugLink() {
        OrderInfoModel.BugLink link  = new OrderInfoModel.BugLink();
        link.setBugId(this.bugId);
        link.setTime(getCreatedAt());
        link.setSolved(this.status);
        return link;
    }
}
