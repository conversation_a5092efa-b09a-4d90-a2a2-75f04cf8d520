package com.yeestor.work_order.controller.role;

import com.yeestor.admin.api.UserFeignClient;
import com.yeestor.admin.model.UserDTO;
import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import com.yeestor.work_order.exception.DataNotFoundException;
import com.yeestor.work_order.model.http.req.RoleParams;
import com.yeestor.work_order.model.http.resp.RoleVO;
import com.yeestor.work_order.model.permission.Permission;
import com.yeestor.work_order.repository.role.RoleRepository;
import com.yeestor.work_order.service.role.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/api/role")
@Api(tags = {"Role"}, value = "用于处理工单系统内部的角色相关的接口")
public class RoleController {

    private final RoleService roleService;
    private final UserFeignClient userFeignClient ;
    private final RoleRepository roleRepository ;

    @PutMapping
    @ApiOperation(value = "创建角色", notes = "创建角色，但是目前没有用户相关的信息")
    public HandleResp<Long> createRole(
            @RequestBody RoleParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        // 判断用户是否有权限创建 角色了。
        roleService.checkPermission(userDetail,null , "创建角色" , Permission.CREATE_ROLE);
        roleService.createRole(params);
        return HandleResp.ok(-1L, "创建成功！");
    }



    @GetMapping("/list")
    @ApiOperation(value = "获取角色信息列表", notes = "${RoleController.listRole.notes}")
    public HandleResp<List<RoleVO>> listRole(
            @ApiParam("角色名称") @RequestParam(value = "name", required = false, defaultValue = "") String name,
            @ApiParam("角色描述") @RequestParam(value = "desc", required = false, defaultValue = "") String desc,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        // 判断用户是否有权限查询角色列表
        roleService.checkPermission(userDetail,null , "查询角色列表" , Permission.VIEW_ROLE_LIST);
        return HandleResp.ok(roleService.findAllRole(name, desc), "查询成功！");
    }


    @DeleteMapping("/{roleId}")
    @ApiOperation(value = "删除角色", notes = "删除角色，不会真正删除数据。而且执行此操作需要一定的权限。比如说测试主管")
    public HandleResp<String> deleteRole(
            @ApiParam("角色的ID") @PathVariable("roleId") long roleId,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        // 判断用户是否有权限删除角色
        roleService.checkPermission(userDetail,null , "删除角色" , Permission.DELETE_ROLE);
        if(!roleRepository.existsById(roleId)){
            throw new DataNotFoundException("角色不存在！");
        }

        roleService.deleteRole(roleId);
        return HandleResp.ok("","删除成功！") ;
    }


    @PostMapping("/{roleId}")
    @ApiOperation(value = "修改角色", notes = "修改角色的信息。")
    public HandleResp<String> updateRole(
            @PathVariable(name ="roleId") long roleId,
            @RequestBody RoleParams params,
            @ApiIgnore @AuthenticationPrincipal OAuthUserDetail userDetail
    ){
        // 判断用户是否有权限修改角色
        roleService.checkPermission(userDetail,null , "修改角色" , Permission.UPDATE_ROLE);
        if(!roleRepository.existsById(roleId)) {
            throw new DataNotFoundException("角色不存在！");
        }
        roleService.updateRole(params, roleId);
        return HandleResp.ok("null","更新成功！");
    }


    @GetMapping("/test/users")
    @ApiOperation(value = "获取指定产品线测试角色的人员列表", notes = "获取测试角色的人员列表")
    public HandleResp<List<UserDTO>> getTestRoleUsers(
            @ApiParam("产品线") @RequestParam(value = "p") String product
    ){
        List<String> useridList = roleService.getUserListByProductAndPermission(product, Permission.START_TEST);
        List<UserDTO> userDetailList = new ArrayList<>();
        // 从钉钉服务中查询用户信息
        useridList.forEach(uid-> {
            HandleResp<UserDTO> userDetailResp = userFeignClient.findUserByDingTalkId(uid);
            if(userDetailResp.isSuccess()){
                UserDTO userDetail = userDetailResp.getData();
                userDetailList.add(userDetail) ;
            }
        });
        return HandleResp.ok(userDetailList, "查询成功！");
    }


}
