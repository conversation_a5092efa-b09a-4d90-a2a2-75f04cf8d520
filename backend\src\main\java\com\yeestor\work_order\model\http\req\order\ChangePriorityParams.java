package com.yeestor.work_order.model.http.req.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 *
 * {@url https://gitlab.yeestor.com/sw100/web/wo/backend/-/issues/76}
 */
@Data
@ApiModel(value = "ChangePriorityParams", description = "修改优先级参数")
public class ChangePriorityParams {

    /**
     * 工单id
     */
    @Min(value = 1, message = "工单id不能小于1")
    @ApiModelProperty(value = "工单id", example = "1", required = true)
    private long orderId;

    /**
     * flash批次
     */
    @NotBlank(message = "flash 批次号不能为空")
    @ApiModelProperty(value = "flash批次", example = "3ta-001", required = true)
    private String flash;

    /**
     * 需要更改的Plan优先级列表。
     */
    @NotEmpty(message = "Plan的优先级列表 不能为空")
    @ApiModelProperty(value = "Plan的优先级列表", example = "")
    private List<PlanPriority> plans;

    @Data
    public static class PlanPriority {
        /**
         * 需要修改的批次的名称
         */
        @ApiModelProperty(value = "需要修改的批次的名称", example = "Plan22")
        private String planName;
        /**
         * 更改后的优先级。
         */
        @ApiModelProperty(value = "更改后的优先级。", example = "1")
        private int priority;
    }
}
