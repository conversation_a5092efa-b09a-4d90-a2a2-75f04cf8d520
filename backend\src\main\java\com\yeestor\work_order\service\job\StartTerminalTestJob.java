package com.yeestor.work_order.service.job;

import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.model.rms.TerminalModel;
import com.yeestor.work_order.model.rms.TerminalPlanTestParams;
import com.yeestor.work_order.model.rms.TerminalPlanTestRespModel;
import com.yeestor.work_order.model.rms.TerminalPlatformStatusResp;
import com.yeestor.work_order.repository.PlanPlatformRepository;
import com.yeestor.work_order.service.device.ScrcpyService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.OrderService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.plan.TerminalService;
import com.yeestor.work_order.utils.LogUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class StartTerminalTestJob implements Job {
    public static final String GROUP_NAME = "StartTerminalTestJob";
    private static final String ORDER_ID = "order_id";
    private static final String FLASH = "flash";
    private static final String PLAN_ID = "plan_id";
    private static final String USER_NAME = "user_name";
    private static final String PLATFORM_STR = "platforms_str";
    private static final String OPERATE = "启动终端Plan测试";

    private final PlanService planService;
    private final OrderService orderService;
    private final FlashService flashService;
    private final PlanPlatformRepository planPlatformRepository;
    private final TerminalService terminalService;
    private final ScrcpyService scrcpyService;
    private final DataChangeListener dataChangeListener;

    public static String buildJobName(long orderId, long planId) {
        return GROUP_NAME + "_" + orderId + "_" + planId + "_" + UUID.randomUUID();
    }

    public static JobDetail buildJobDetail(
            String jobName,
            long orderId,
            String flash,
            long planId,
            String userName,
            List<PlanPlatformEntity> platformEntityList
    ) {
        List<String> numberList = platformEntityList.stream()
                .map(t -> t.getMac() + ";" + t.getNumber())
                .collect(Collectors.toList());

        return JobBuilder.newJob(StartTerminalTestJob.class)
                .withIdentity(jobName, GROUP_NAME)
                .usingJobData(ORDER_ID, orderId)
                .usingJobData(FLASH, flash)
                .usingJobData(PLAN_ID, planId)
                .usingJobData(USER_NAME, userName)
                .usingJobData(PLATFORM_STR, String.join("#", numberList))
                .build();
    }

    @Override
    public void execute(JobExecutionContext context) {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        long orderId = dataMap.getLong(ORDER_ID);
        String flash = dataMap.getString(FLASH);
        long planId = dataMap.getLong(PLAN_ID);
        String username = dataMap.getString(USER_NAME);

        LogUtils.setOrderAndFlashTracePoint(orderId, flash, OPERATE);

        WorkOrderEntity orderEntity = orderService.findOrderOrElseThrow(orderId);
        OrderPlanEntity planEntity = planService.findPlanOrElseThrow(planId);
        OrderFlashEntity flashEntity = flashService.findFlashOrElseThrow(orderId, flash);
        List<PlanPlatformEntity> platformEntityList = planPlatformRepository.findAllByPlanId(planId);
        log.info("Flash:{}下的终端测试: {}", flash, planEntity.getName());

        String platformStr = dataMap.getString(PLATFORM_STR);
        List<String> macToNumbersList = Arrays.asList(platformStr.split("#"));

        Map<String, List<String>> macToNumbersMap = macToNumbersList.stream()
                .collect(Collectors.groupingBy(
                        s -> s.split(";")[0], // 根据MAC地址分组
                        Collectors.mapping(
                                s -> s.split(";")[1], // 提取number部分
                                Collectors.toList() // 收集到列表中
                        )
                ));

        List<TerminalModel> terminalList = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : macToNumbersMap.entrySet()) {
            String mac = entry.getKey();
            List<String> numberList = entry.getValue();
            TerminalModel model = new TerminalModel();
            model.setMac(mac);
            model.setNumberList(numberList);
            terminalList.add(model);
        }


        List<String> scriptList = planEntity.getScripts() != null
                ? Arrays.asList(planEntity.getScripts().split(";"))
                : Collections.emptyList();
        String scrcpyPlan = Optional.ofNullable(orderEntity.getScrcpyPlan()).orElse("");
        String scrcpyPath = Optional.ofNullable(orderEntity.getScrcpyPath()).orElse("");

        // 如果不存在Plan版本包信息,则此次执行失败
        if (StringUtils.isEmpty(scrcpyPlan)) {
            List<PlanPlatformEntity> savePlatforms = platformEntityList.stream()
                    .peek(p -> {
                        p.setEndAt(System.currentTimeMillis());
                        p.setStatus(PlanPlatformEntity.Status.FINISHED_FAILED);
                        p.setFailReason("Plan版本包不存在!");
                    })
                    .collect(Collectors.toList());
            planPlatformRepository.saveAll(savePlatforms);
            dataChangeListener.onDataChange(DataChangeEvent.builder()
                    .type(DataChangeEvent.Type.DEVICE_STATUS_CHANGED)
                    .orderId(orderEntity.getId())
                    .flash(planEntity.getFlash())
                    .planId(planEntity.getId())
                    .build());
            return;
        }
        // 使用 lastIndexOf 找到最后一个反斜杠的位置
        int lastPlanIndex = scrcpyPlan.lastIndexOf('\\');
        String planVersion =  lastPlanIndex != -1 ? scrcpyPlan.substring(lastPlanIndex + 1, scrcpyPlan.length() - 4) : null;
        TerminalPlanTestParams params = TerminalPlanTestParams.builder()
                .plan(planEntity.getName())
                .no(flashEntity.getOrderFlashNo())
                .planPath(planVersion)
                .toolPath(scrcpyPath)
                .username(username)
                .caseList(scriptList)
                .terminalList(terminalList)
                .build();

        TerminalPlanTestRespModel respModel = new TerminalPlanTestRespModel();
        respModel.setFailLst(new ArrayList<>());
        respModel.setSuccessLst(new ArrayList<>());
        // 启动测试
        HandleResp<TerminalPlatformStatusResp> testResp = scrcpyService.startTestPlatforms(params);

        if (testResp.isSuccess()) {
            log.info("终端Plan {} 启动测试成功, resp: {}", planEntity.getName(), testResp.getData());
            TerminalPlatformStatusResp model = testResp.getData();
//            List<TerminalPlatformStatusResp.PlatformStatusResult> successLst = model.getSuccessLst();
            List<TerminalPlatformStatusResp.PlatformStatusResult> failLst = Optional.ofNullable(model.getFailLst()).orElse(new ArrayList<>());

            List<PlanPlatformEntity> failPlatforms = platformEntityList.stream()
                    .filter(p -> failLst.stream().anyMatch(d -> d.getMac().equals(p.getMac()) && d.getNumber().equals(p.getNumber())))
                    .peek(p -> {
                        TerminalPlatformStatusResp.PlatformStatusResult result = failLst.stream()
                                .filter(d -> d.getMac().equals(p.getMac()) && d.getNumber().equals(p.getNumber()))
                                .findFirst().orElse(null);

                        p.setEndAt(System.currentTimeMillis());
                        p.setStatus(PlanPlatformEntity.Status.FINISHED_FAILED);
                        p.setFailReason(result.getReason());
                    })
                    .collect(Collectors.toList());

            // 没有失败的平台信息
            if (failPlatforms.isEmpty()) {
                return;
            }

            // 保存错误的执行信息
            planPlatformRepository.saveAll(failPlatforms);

            // 启动一个定时任务定时检查Plan下的平台设备信息回调
            terminalService.startCheckTerminalPlanStartStatusJob(
                    orderId,
                    flashEntity.getOrderFlashNo(),
                    planEntity,
                    terminalList
            );
        } else {
            log.warn("{}启动终端Plan测试失败，{}", planEntity.getName(), testResp.getMsg());
        }
        LogUtils.clearTracePoint();
    }

}
