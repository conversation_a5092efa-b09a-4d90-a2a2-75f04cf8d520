package com.yeestor.work_order.config;

import com.yeestor.admin.api.UserFeignClient;
import com.yeestor.admin.model.UserDTO;
import com.yeestor.model.http.HandleResp;
import com.yeestor.security.OAuthUserDetail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private UserFeignClient userFeignClient ;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        OAuthUserDetail detail = null;

        HandleResp<UserDTO> userResp = userFeignClient.findUserByUsername(username);
        if (userResp.isSuccess()) {
            UserDTO userDTO = userResp.getData();
            detail =  new OAuthUserDetail();

            detail.setId(userDTO.getUid());
            detail.setUid(userDTO.getDUserId());
            detail.setUsername(userDTO.getUsername());

            detail.setEnabled(true);
        }
        return detail;
    }
}
