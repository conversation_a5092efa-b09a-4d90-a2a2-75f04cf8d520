package com.yeestor.work_order.entity.role;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

/**
 * 用户角色关联表 。
 */
@Getter
@Setter
@ToString
@Entity(name = "RoleUser")
@Table(name = "wo_role_user")
public class RoleUserEntity {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id ;

    @Column(name = "role_id")
    private long roleId ;

    /**
     * 用户的钉钉id
     */
    @Column(name = "user_id")
    private String userId ;

    @Column(name = "created_at")
    private long createdAt = System.currentTimeMillis();

}
