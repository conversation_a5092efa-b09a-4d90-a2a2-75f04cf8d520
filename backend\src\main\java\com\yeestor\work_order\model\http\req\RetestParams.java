package com.yeestor.work_order.model.http.req;

import com.yeestor.work_order.model.http.req.order.OrderEnvConfirmReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "RetestParams", description = "重测参数")
public class RetestParams {


    @NotBlank(message = "工单号不能为空")
    @ApiModelProperty(value = "工单号",notes = "如果传递的是Flash批次 的工单号,则Plan Id 都需要为Flash批次的Plan Id; 如果传递的时工单的工单号,则Plan Id 都需要为工单的Plan Id")
    private String orderNo ;

    @NotEmpty(message = "设备列表为空")
    @ApiModelProperty(value= "Plan及需要重测的设备的信息", notes = "字典,key 为Plan ID, value为需要重测的设备IP列表",dataType = "Map<Long, List<String>",example = "{\"1\":[\"************\",\"************\"]}")
    private Map<Long, List<String>> planIpMap;

    @ApiModelProperty("Plan下的设备列表")
    @NotEmpty(message = "Plan设备列表为空")
    private List<OrderEnvConfirmReq.PlanDeviceInfo> planDeviceList  ;


}
