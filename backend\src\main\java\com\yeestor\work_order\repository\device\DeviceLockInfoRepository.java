package com.yeestor.work_order.repository.device;

import com.yeestor.work_order.entity.device.DeviceLockInfoEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

public interface DeviceLockInfoRepository extends JpaRepository<DeviceLockInfoEntity, Long> {

    List<DeviceLockInfoEntity> findAllBySubProductAndLockedIsTrue(String subProduct);

    List<DeviceLockInfoEntity> findByPlanIdAndMacAndLockedTrue(long planId, String mac);

    List<DeviceLockInfoEntity> findAllByMacAndLockedTrue(String mac);

    List<DeviceLockInfoEntity> findByMacInAndLockedTrue(List<String> macList);

    List<DeviceLockInfoEntity> findByPlanIdAndLockedTrue(long planId);



}
