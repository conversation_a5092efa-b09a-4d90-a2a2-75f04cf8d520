package com.yeestor.work_order.entity.order;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity(name = "OrderReason")
@Table(name = "wo_order_reason")
public class OrderReasonEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    /**
     * 属于工单或者批次
     * Order 工单相关
     * Flash 相关。
     */
    @Column(name = "belong_to")
    private String belongTo;

    /**
     * 原因类型，取消测试或撤销工单
     * CANCEL 取消
     * REVOKE 撤销
     */
    @Column(name = "type")
    private String type;

    /**
     * 描述
     */
    @Column(name = "description")
    private String description ;

}
