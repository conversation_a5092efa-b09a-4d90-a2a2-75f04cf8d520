package com.yeestor.work_order.service.order;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.http.req.order.OrderEnvConfirmReq;
import com.yeestor.work_order.model.http.resp.order.PlanDetailVO;
import com.yeestor.work_order.model.http.resp.order.WorkOrderDetailVO;
import com.yeestor.work_order.model.http.resp.order.WorkOrderItemVO;
import com.yeestor.work_order.model.rms.PlanModel;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.repository.WorkOrderRepository;
import com.yeestor.work_order.service.device.RMSDeviceService;
import com.yeestor.work_order.utils.RMSApis;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class OrderServiceTest {

    @Autowired
    OrderService orderService;
    @Autowired
    RMSApis rmsApis;

    @Autowired
    RMSDeviceService rmsDeviceService;

    @Autowired
    WorkOrderRepository workOrderRepository;

    @Autowired
    OrderPlanRepository orderPlanRepository;

    @Autowired
    OrderFlashRepository orderFlashRepository;

    @Autowired
    FlashService flashService;

    @Test
    void checkFlash(){
        orderService.findAllBuilderByProductAndSubProduct("GE", "SD");
    }

    @Test
    @Order(1)
    void orderListIsEmpty() {
        // 这个时候工单列表应该为空
        Page<WorkOrderItemVO> orderItemVOS = orderService.fetchOrderList(
                "GE",
                "U2",
                null,
                0,
                1,
                10,
                "descend",
                "createdAt",
                "",
                "",
                "",
                "",
                null
        );
        assertEquals(0, orderItemVOS.getTotalElements());
    }

    @Test
    @Order(2)
    void importOrder() {
        // 导入一个工单
        HashMap<String, Object> params = new HashMap<>();
        params.put("ciId", 98);
        orderService.importOrder(
                params,
                params
        );
        // 工单导入后，工单列表应该不为空
        Page<WorkOrderItemVO> orderItemVOS = orderService.fetchOrderList(
                "GE",
                "U2",
                null,
                0,
                1,
                10,
                "descend",
                "createdAt",
                "",
                "",
                "",
                "",
                null
        );
        assertEquals(1, orderItemVOS.getTotalElements());
    }

    @Test
    @Order(3)
    void firstOrderInfoPlanListNotEmpty() {
        // 第一个工单的Plan 列表应该不为空。
        WorkOrderDetailVO detailVO = orderService.fetchOrderInfo(1);
//        List<PlanModel> models = rmsApis.getPlanList(detailVO.getProduct(),detailVO.getSubProduct(), detailVO.getVersionType());
        assertNotNull(detailVO);
//        assertNotEquals(0, detailVO.getPlanList().size());
//        assertEquals(16, detailVO.getPlanList().size());

        List<PlanModel> models = Arrays.asList(
                PlanModel.builder()
                        .feature("POR")
                        .name("Plan4")
                        .product("GE")
                        .subProduct("SD")
                        .testNum(8)
                        .versionType("5")
                        .attrs(new ArrayList<>(Arrays.asList("TCPIP掉电", "Mars")))
                        .build(),
                PlanModel.builder()
                        .feature("SPOR")
                        .name("Plan5")
                        .product("GE")
                        .subProduct("SD")
                        .testNum(8)
                        .versionType("5")
                        .attrs(new ArrayList<>(Arrays.asList("TCPIP掉电", "Mars")))
                        .build()

        );


        // 应该包含所有的Plan
        Assertions.assertThat(detailVO.getPlanList()).extracting("name")
                .containsAll(
                        models.stream()
                                .map(PlanModel::getName)
                                .collect(Collectors.toList()
                                )
                );
        // 在未确认之前，所有的Plan的测试数量应该是一致的。
        Assertions.assertThat(detailVO.getPlanList())
                .allMatch(planSimpleInfo -> models.stream()
                        .filter(planModel -> planModel.getName().equals(planSimpleInfo.getName()))
                        .findFirst()
                        .map(m -> m.getTestNum() == planSimpleInfo.getTestNum())
                        .orElse(false)
                );

    }


    @Test
    @Order(5)
    void confirmOrderResult() {

        long orderId = 1L;
//        WorkOrderDetailVO detailVO = orderService.fetchOrderInfo(1);
        WorkOrderEntity orderEntity = workOrderRepository.findById(orderId).orElse(null);
        assertNotNull(orderEntity);
        assertEquals(WorkOrderEntity.Status.CONFIRMED_FLASH, orderEntity.getStatus()); // 检查状态是否为已确认

//        List<PlanModel> models = rmsApis.getPlanList(orderEntity.getProduct(),orderEntity.getSubProduct(), orderEntity.getVersionType());
//        log.info("models:{}", models);

        List<PlanModel> models = Arrays.asList(
                PlanModel.builder()
                        .feature("POR")
                        .name("Plan4")
                        .product("GE")
                        .subProduct("SD")
                        .testNum(8)
                        .versionType("5")
                        .attrs(new ArrayList<>(Arrays.asList("TCPIP掉电", "Mars")))
                        .build(),
                PlanModel.builder()
                        .feature("SPOR")
                        .name("Plan5")
                        .product("GE")
                        .subProduct("SD")
                        .testNum(8)
                        .versionType("5")
                        .attrs(new ArrayList<>(Arrays.asList("TCPIP掉电", "Mars")))
                        .build()

        );

        List<OrderFlashEntity> flashEntityList = orderFlashRepository.findAllByOrderId(orderId);
        assertNotNull(flashEntityList);
        assertEquals(2, flashEntityList.size()); // 检查flash数量是否正确


        List<OrderPlanEntity> orderPlanEntities = orderPlanRepository.findAllByOrderId(orderId);
        assertNotNull(orderPlanEntities);
        assertEquals(models.size() * flashEntityList.size(), orderPlanEntities.size()); // 检查plan数量是否正确

        // 检查每个plan的测试数量是否正确。
        Assertions.assertThat(orderPlanEntities)
                .allMatch(planEntity -> {

                    String planName = planEntity.getName();
                    String flash = planEntity.getFlash();
                    int testNum = planEntity.getTestNum();
                    // 找到对应的Flash
                    OrderFlashEntity flashEntity = flashEntityList.stream().filter(f -> f.getFlash().equals(flash)).findFirst().orElse(null);
                    assertNotNull(flashEntity);

                    if (models.stream()
                            .filter(m -> m.getName().equals(planName)) // 找到对应的plan
                            .filter(m -> m.getTestNum() != 0 && m.getTestNum() == testNum) // 测试数量是否正确
                            .count() == 1)
                        return true;
                    else return models.stream()
                            .filter(m -> m.getName().equals(planName)) // 找到对应的plan
                            .filter(m -> m.getTestNum() == 0 && flashEntity.getNum() == testNum) // 测试所有的情况下，和Flash批次数量是否一致
                            .count() == 1;
                });

    }


    @Test
    @Order(6)
    void testConfirmOrderEnvInfo() {
        rmsDeviceService.getAvailableDevices("SD") ;
        WorkOrderEntity entity = workOrderRepository.getById(1L);
        String userDingTalkID = "0653290119972081";
        OrderEnvConfirmReq req = new OrderEnvConfirmReq();
        req.setOrderId(1);
        req.setFlash("YS-SDTNQGBMA_08G-A");
        req.setPlanDeviceInfoList(
                Arrays.asList(
                        OrderEnvConfirmReq.PlanDeviceInfo.builder()
                                .planId(1)
                                .nodes(Arrays.asList("172.18.28.247", "172.18.29.16"))
                                .build(),
                        OrderEnvConfirmReq.PlanDeviceInfo.builder()
                                .planId(2)
                                .nodes(Arrays.asList("172.18.28.207", "172.18.28.158"))
                                .build()
                )
        );
        OrderFlashEntity orderFlashEntity = flashService.findFlashOrElseThrow(req.getOrderId(), req.getFlash());
        //
        orderService.confirmOrderEnvInfo(req, entity, orderFlashEntity);

        WorkOrderDetailVO detailVO = orderService.fetchOrderInfo(1);

        WorkOrderDetailVO.FlashInfoVO flashInfo = detailVO.getFlashInfoList().stream().filter(f -> f.getFlash().equals("YS-SDTNQGBMA_08G-A")).findFirst().orElse(null);

        assertNotNull(flashInfo);

        List<PlanDetailVO> detailInfoList = detailVO.getPlanList();


        Assertions.assertThat(detailInfoList)
                .filteredOn(planInfo -> planInfo.getFlash().equals("YS-SDTNQGBMA_08G-A"))
                .hasSize(2)
        ;
        log.info("detailVo:{}", detailVO);
    }


    @Test
    @Order(7)
    void revokeOrder(){
        long orderId = 1L ;
        String revokeReason = "test" ;
        orderService.revokeOrder(orderId, revokeReason);

    }

    @Test
    void testVersionRegex(){
        String regex = "^\\D*[\\s_]([\\w\\.]*)_.*$";
        regex= "^[^_]*[\\s_]([\\w\\.]*)_.*$";
        regex= "^[^_|\\s]*[\\s_]([\\w\\.]*)_.*$" ;
        regex= "^[^_|\\s]*[\\s_]([^_]*)_.*$" ;

        final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);

        List<String> fullVersionList = workOrderRepository.findAllFullVersion();
        for (   String fullVersion : fullVersionList) {

            final Matcher matcher = pattern.matcher(fullVersion);
            assertTrue(matcher.matches(), "fullVersion:" + fullVersion);
            log.info("group 1 :{} --- version: {}", matcher.group(1),fullVersion);
            assertNotNull(matcher.group(1));
            Assertions.assertThat(matcher.group(1))
                    .isNotBlank()
                    .matches("^.*\\d*.*")
            ;
        }
    }


}