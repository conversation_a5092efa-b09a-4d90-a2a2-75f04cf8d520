package com.yeestor.work_order.model.rms;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceDetailModel",description = "RMS中的设备详情")
public class DeviceDetailModel extends DeviceModel{


    @ApiModelProperty(value = "样片详细信息列表")
    private List<SampleInfo> sampleNoLst ;

}
