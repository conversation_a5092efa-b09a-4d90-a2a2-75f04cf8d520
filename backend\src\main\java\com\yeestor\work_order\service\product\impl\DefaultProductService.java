package com.yeestor.work_order.service.product.impl;


import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.entity.PlanDeviceEntity;
import com.yeestor.work_order.entity.WorkOrderEntity;
import com.yeestor.work_order.model.rms.DeviceModel;
import com.yeestor.work_order.model.rms.PlanStatusChangeParams;
import com.yeestor.work_order.repository.PlanDeviceRepository;
import com.yeestor.work_order.service.NotificationService;
import com.yeestor.work_order.service.device.QueueService;
import com.yeestor.work_order.service.device.RMSDeviceService;
import com.yeestor.work_order.service.job.AutoCompletePlanJob;
import com.yeestor.work_order.service.job.JobService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.change.DataChangeEvent;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.service.product.ProductService;
import com.yeestor.utils.TimeUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yeestor.work_order.utils.DingTalkUtils.SYSTEM_USER_ID;
import static com.yeestor.work_order.utils.DingTalkUtils.SYSTEM_USER_NAME;


@Slf4j
@Setter(onMethod = @__({@Autowired, @Lazy}))
@Component
public class DefaultProductService implements ProductService {


    protected DataChangeListener dataChangeListener;

    protected DeviceService deviceService;

    protected PlanService planService;

    protected PlanDeviceRepository planDeviceRepository;

    protected RMSDeviceService rmsDeviceService;

    protected NotificationService notificationService;

    protected JobService jobService;

    protected QueueService queueService ;

    protected FlashService flashService ;


    /**
     * plan 状态变更回调（样片状态变更回调）
     * @param orderEntity 工单实体
     * @param flashEntity Flash批次实体
     * @param planEntity Plan实体
     * @param params 回调参数
     */
    @Override
    public void onPlanStatusChange(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            OrderPlanEntity planEntity,
            PlanStatusChangeParams params
    ) {
        log.info("DefaultProductService.onPlanStatusChange params:{}",params);

        String status = params.getStatus();
        List<PlanStatusChangeParams.PlanDeviceInfo> deviceList = params.getDeviceList();

        dataChangeListener.onDataChange(
                DataChangeEvent.builder()
                        .type(DataChangeEvent.Type.DEVICE_STATUS_CHANGED)
                        .orderId(planEntity.getOrderId())
                        .flash(planEntity.getFlash())
                        .planId(planEntity.getId())
                        .build()
        );

        log.info("[RMS回调] 需要将Flash:{} 下的{}[{}]中的设备:{}状态变更为: {}",
                planEntity.getFlash(),
                planEntity.getName(),
                planEntity.getStatus(),
                deviceList.stream().map(d -> String.format("%s(%s)", d.getNo(), d.getIp()))
                        .collect(Collectors.joining(",")),
                status);

        List<PlanDeviceEntity> oldDeviceList = new ArrayList<>();
        List<PlanDeviceEntity> newDeviceList = new ArrayList<>();
        AtomicInteger canReleaseSampleNum = new AtomicInteger();

        // 获取当前Plan wo_plan_device表中存储的所有设备
        List<PlanDeviceEntity> allPlanDevices = planDeviceRepository.findAllByPlanId(planEntity.getId());

        PlanDeviceEntity.Status resultStatus = params.isSuccess() ? PlanDeviceEntity.Status.FINISHED_SUCCESS : PlanDeviceEntity.Status.FINISHED_FAILED;
        log.info("即将更新样片信息更新至: {}", resultStatus);

        // 可以根据 对应的设备列表，找到对应的工单以及plan
        deviceList.forEach(d ->
                // 找到 对应着的还在运行中的设备 。
                allPlanDevices.stream()
                        .filter(planDevice -> planDevice.isSameDevice(d))       // 判断当前plan的设备是不是与回调的一致
                        .filter(planDevice -> planDevice.getStatus() == PlanDeviceEntity.Status.RUNNING)        // 如果不是在运行中的设备需要排除
                        .findFirst()
                        .ifPresent(deviceEntity -> {
                            oldDeviceList.add(deviceEntity);    // 用于存储的设备历史记录
                            PlanDeviceEntity newDeviceEntity = deviceEntity.duplicate();

                            deviceService.updateSampleInfo(deviceEntity, d.getFailList());      // 检查当前设备下有多少样片测试失败，失败则更新记录

                            log.info("样片状态更新提示: Flash{}下的{}中的设备{}({}) 状态更新", planEntity.getFlash(), planEntity.getName(), d.getNo(), d.getMac());

                            newDeviceEntity.setUpdatedAt(System.currentTimeMillis());

                            newDeviceEntity.setEndAt(System.currentTimeMillis());
                            newDeviceEntity.setStatus(resultStatus);
                            newDeviceEntity.setFailReason(d.getMsg());

                            // 检查是否需要更新释放设备信息
                            peekDeviceOnPlanStatusChange(flashEntity, planEntity, newDeviceEntity) ;

                            canReleaseSampleNum.addAndGet(deviceEntity.getTestNum());
                            newDeviceList.add(newDeviceEntity);
                        })

        );
        if (params.isSuccess()) {
            // 当Plan成功时的设备处理.
            /*
             * 如果设备是成功的则直接释放设备
             */
            int releaseSampleNum = deviceHandlerOnPlanSuccess(orderEntity.getSubProduct(), params.getOrderNo(), planEntity, newDeviceList);
            deviceService.saveDeviceControlList(
                    orderEntity.getSubProduct(),
                    flashEntity,
                    "eSee.system",
                    "电脑自动释放后电脑关机",
                    false,
                    oldDeviceList
            );
            canReleaseSampleNum.addAndGet(-releaseSampleNum);
        }
        // 更新设备信息
        deviceService.saveDeviceList("RMS Plan状态更新", oldDeviceList, newDeviceList);

        /*
         * 回调时Plan运行中
         * 正常样片回调都会走这一步，样片回调时plan预期的状态一定是运行在
         * 设备释放：两小时后自动释放
         * 样片释放：跟随设备两小时后一起释放
         * */
        if(planEntity.getStatus() == OrderPlanEntity.Status.RUNNING){
            if (flashEntity.getStatus().greaterThan(OrderFlashEntity.Status.WAITING_FOR_MERGE)) {
                /*
                 * 如果flash状态的预期超过运行中，则表明当前Flash可能已经取消测试，或存在认为干扰
                 * 设备释放：直接释放设备
                 * 样片释放：跟随设备一起释放
                 */
                log.info("当前回调Plan：{}的Flash批次已经取消测试！", planEntity.getName());
                // 释放当前回调的设备
                rmsDeviceService.releaseDevices(orderEntity.getSubProduct(), flashEntity.getOrderFlashNo(), planEntity, newDeviceList);
                // 释放样片
                flashService.updateLeftSampleNum(flashEntity, canReleaseSampleNum.get());
                deviceService.saveDeviceControlList(
                        orderEntity.getSubProduct(),
                        flashEntity,
                        "eSee.system",
                        "电脑自动释放后电脑关机",
                        false,
                        newDeviceList
                );
            }
            else {
                /*
                 * 当预期Plan的状态为运行中
                 * 设备释放：最后一个结束测试的设备回调后两小时释放
                 * 样片释放：跟随设备
                 */

                // 如果所有的设备都是finished 的话，则更新plan 的状态为 finished。
                int countFinished = planDeviceRepository.countByPlanIdAndStatusIn(planEntity.getId(),
                        Arrays.asList(
                                PlanDeviceEntity.Status.FINISHED_SUCCESS,
                                PlanDeviceEntity.Status.FINISHED_FAILED,
                                PlanDeviceEntity.Status.CANCELED
                        )
                );
                int countAll = planDeviceRepository.countByPlanId(planEntity.getId());

                log.info("{} 下总共有{}台设备, 总计结束了{}台. ", planEntity.getName(), countAll, countFinished);

                // 如果有一个设备还没有结束，那么就不更新plan 的状态。
                if (countFinished != countAll || countAll == 0) {
                    return;
                }

                log.info("Flash:{} 下的{} 所有设备都已测试完成,发送测试结束通知!", planEntity.getFlash(), planEntity.getName());
                notificationService.sendPlanTestEndNotification(orderEntity, flashEntity, planEntity);
                // 更新Plan的AutoEndAt 时间.
                planService.updateAutoEndTime(planEntity.getId(), System.currentTimeMillis());

                // 查询Plan下所有的已完成释放的设备，如果所有的设备都是都已经释放，那么直接检测并变更Plan状态。
                int countRelease = planDeviceRepository.countByPlanIdAndReleaseAtNotNull(planEntity.getId());
                // 如果所有设备均已释放，则需要检查并更新Plan状态信息
                if (countRelease == countAll) {
                    planService.checkDeviceReleaseStatus(orderEntity, flashEntity, planEntity);
                } else {
                    // 否则存在失败设备，需要设置定时释放任务，定时去释放设备
                    autoCompletePlan(orderEntity, flashEntity, planEntity);
                }
            }
        }else if(
                planEntity.getStatus() == OrderPlanEntity.Status.COMPLETED
                || planEntity.getStatus() == OrderPlanEntity.Status.STOPPED
        ){
            String pcNo = deviceList.get(0).getNo();
            if (planEntity.getStatus() == OrderPlanEntity.Status.COMPLETED) {
                /*
                 * 回调时Plan测试完成
                 * 暂时没有想到哪个场景会触发这个情况，正常plan完成需要所有设备都测试完成才行
                 * 设备释放：直接释放所有回调设备
                 * 样片释放：直接释放
                 */
                log.info("异常信息: 当前回调的Plan：{}测试已完成, 回调信息与plan状态不同步!", planEntity.getName());
                notificationService.sendDeviceCallBackNotification("设备" + pcNo + "回调回调时，Plan已经结束！");
            } else {
                /*
                 * 回调时Plan取消测试
                 * 当plan取消后，plan状态变成STOPPED，同时需要注意当停止的是最后一个Plan时可能会导致最后一个plan的设备回调处理被忽略
                 * 设备释放：直接释放所有回调设备
                 * 样片释放：直接释放
                 */
                log.info("消息提示: 当前回调的Plan: {}已经被取消测试, 回调信息与plan状态不同步!", planEntity.getName());
//                notificationService.sendDeviceCallBackNotification("设备" + pcNo + "回调回调时，Plan已经取消！");
                deviceService.saveDeviceControlList(
                        orderEntity.getSubProduct(),
                        flashEntity,
                        "eSee.system",
                        "Plan停止测试后电脑关机",
                        false,
                        newDeviceList
                );
            }

            // 释放当前回调的设备
            rmsDeviceService.releaseDevices(orderEntity.getSubProduct(), flashEntity.getOrderFlashNo(), planEntity, newDeviceList);
            // 释放样片
            flashService.updateLeftSampleNum(flashEntity, canReleaseSampleNum.get());
            // 检查一下Flash状态是否正常，如果不正常则需要更新
            flashService.checkWaitMerge(orderEntity, flashEntity);
        }

    }

    @Override
    public void peekDeviceOnPlanStatusChange(OrderFlashEntity flashEntity, OrderPlanEntity planEntity, PlanDeviceEntity newDeviceEntity) {
        // 检测设备是否需要释放，如果是Flash取消或者Plan 停止了，应该更新数据库中的状态为释放。
        if (planEntity.getStatus() == OrderPlanEntity.Status.STOPPED
                || flashEntity.getStatus().greaterThan(OrderFlashEntity.Status.WAITING_FOR_MERGE)) {
            newDeviceEntity.setReleaseAt(System.currentTimeMillis());
            newDeviceEntity.setReleaseBy(planEntity.getTerminateBy());
            newDeviceEntity.setReleasePerson(planEntity.getTerminatePerson());
        }
    }

    @Override
    public void autoCompletePlan(WorkOrderEntity orderEntity,OrderFlashEntity flashEntity, OrderPlanEntity planEntity) {
        String jobName = flashEntity.getOrderFlashNo() + "_" + planEntity.getName();

        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("planId", planEntity.getId());
        dataMap.put("orderFlashNo", flashEntity.getFlash());
        dataMap.put("timestamp", System.currentTimeMillis());

        // 找到 2个小时后的最合适的工作时间。
        //调用定时任务更新plan的状态。 暂定为2小时，并且 需要在晚上（18：00-9：00）、节假日（全天）不自动释放
        // 在两个小时后通知，目前的设置是 周一至周五 9:00 - 18:00
        Date futureDate = TimeUtils.getReleaseTime(new Date(), 2);
        planService.updateExpectedEndTime(planEntity.getId(), futureDate.getTime());

        log.info("{} 将在 {} 自动释放设备。", planEntity.getName(), new DateTime(futureDate).toString("yyyy-MM-dd HH:mm"));

        jobService.startJobAtFutureDate(jobName, AutoCompletePlanJob.JOB_GROUP_NAME, dataMap, AutoCompletePlanJob.class, TimeUtils.getReleaseTime(new Date(), 2));

    }

    @Override
    public Comparator<DeviceModel> deviceComparator() {
        return Comparator.comparing(DeviceModel::getScore)
                .reversed()
                .thenComparing(DeviceModel::getPcNo);
    }

    @Override
    public void assignDevices(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {

        long orderId = orderEntity.getId() ;
        String flashName = flashEntity.getFlash();

        //获取 需要测试的plan 列表
        List<OrderPlanEntity> planEntityList = fetchNeedTestPlanList(orderEntity, flashEntity);
        log.debug("[{}] - [{}] find {} plans: {}",orderId, flashName ,planEntityList.size(),planEntityList);

        if(planEntityList.isEmpty()){
            return;
        }
        planService.updatePlanStatusToQueue(planEntityList);

        // key 为plan的名称， value 为device 列表
        Map<String, List<DeviceModel>> runDevices = fetchAllRunnableDevices(orderEntity, flashEntity, planEntityList);
        log.info("assignDevices [{}] - find {} run devices:  {}", orderId, runDevices.size(), runDevices);
        if(runDevices.size() > 0) {
            queueService.saveData(orderEntity, flashEntity, flashName, planEntityList, runDevices);
        }

    }

    @Override
    public List<OrderPlanEntity> fetchNeedTestPlanList(WorkOrderEntity orderEntity, OrderFlashEntity flashEntity) {
        return planService.fetchNeedTestPlanList(orderEntity.getSubProduct(),orderEntity.getId(),flashEntity.getFlash());
    }


    /**
     * 获取此次预分配中可以得到分配的Plan和plan分配的设备情况
     * @param orderEntity 工单实体
     * @param flashEntity flash批次
     * @param planEntities 需要匹配的plan 列表
     * @return key 为plan的名称， value 为device 列表 。
     */
    public Map<String,List<DeviceModel>> fetchAllRunnableDevices(
            WorkOrderEntity orderEntity,
            OrderFlashEntity flashEntity,
            List<OrderPlanEntity> planEntities
    ) {
        String subProduct = orderEntity.getSubProduct();
        // 此处UFS的产品暂时使用eMMC产品的电脑
        List<DeviceModel> deviceList = rmsDeviceService.getAvailableDevices("UFS".equals(subProduct) ? "eMMC" : subProduct);
        log.info("fetchAllRunnableDevices orderId: {} flash: {} available device: {}", orderEntity.getId(), flashEntity.getFlash(), deviceList);
        HashMap<String, List<DeviceModel>> devicesMap = new HashMap<>();
        List<DeviceModel> filterDevices = new ArrayList<>();
        final long orderId = orderEntity.getId();
        final AtomicInteger leftNum = new AtomicInteger(flashEntity.getLeftNum());

        // 判断是否有满足plan的设备。
        planEntities.forEach(planEntity -> {
            //  获取plan 中的 需要测试的数量和测试属性。
            int testNum = planEntity.getTestNum();
            log.info("Devices already occupied before Plan allocation: {}", filterDevices);
            log.info("[{}] {} need Num: {} to test. {} left num:{} ", orderId, planEntity.getName(), testNum, flashEntity.getFlash(), leftNum);

            if(testNum > leftNum.get()) {
                log.info("样片不足，无法进行分配!");
                // 如果测试数量大于剩余数量，则认为不能运行。
                return;
            }

            List<DeviceModel> planDevices = queueService.fetchPlanRunnableDevices(orderEntity, planEntity, filterDevices, deviceList);
            if(planDevices.isEmpty()) {
                return;
            }

            devicesMap.put(
                    planEntity.getName(),
                    planDevices // 区间为：[0, pcCount) 包含0 ，但是不包含 pcCount
            );
            // 过滤掉已经选择了的设备
            filterDevices.addAll(planDevices) ;
            leftNum.addAndGet(-testNum);
            log.info("[{}] plan:{} use {} pc: {}",orderId, planEntity.getName() , planDevices.size(), planDevices);
        });

        return devicesMap;
    }

    /**
     * 处理回调为成功状态的设备，直接释放
     * @param subProduct 产品线
     * @param orderFlashNo rms 工单号
     * @param planEntity Plan实体
     * @param devices 设备列表
     * @return 样片回调数量
     */
    @Override
    public int deviceHandlerOnPlanSuccess(String subProduct, String orderFlashNo, OrderPlanEntity planEntity, List<PlanDeviceEntity> devices) {
        // 通用产品线要求,按照测试结果,如果测试成功,则直接释放设备,如果测试失败,则按照之前的逻辑处理.
        // 如果是成功，则直接释放设备, 需要更新设备的释放信息.
        AtomicInteger canReleaseSampleNum = new AtomicInteger();

        devices.forEach(device -> {
            device.setReleaseAt(System.currentTimeMillis());
            device.setReleasePerson(SYSTEM_USER_NAME);
            device.setReleaseBy(SYSTEM_USER_ID);
            canReleaseSampleNum.addAndGet(device.getTestNum());
        });

        log.info("[RMS 回调] {} 下的 {} 测试成功，直接释放设备：{} - 开始更新Flash样片数量.", subProduct, planEntity.getName(),
                devices.stream()
                        .map(d -> String.format("%s(%s - %s)", d.getNo(), d.getIp(), d.getMac()))
                        .collect(Collectors.joining(",")));

        flashService.updateLeftSampleNum(planEntity.getOrderId(), planEntity.getFlash(), canReleaseSampleNum.get());
        rmsDeviceService.releaseDevices(
                subProduct,
                orderFlashNo,
                planEntity,
                devices
        );
        return canReleaseSampleNum.get();
    }

}
