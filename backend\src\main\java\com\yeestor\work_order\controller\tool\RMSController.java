package com.yeestor.work_order.controller.tool;

import cn.hutool.core.net.URLDecoder;
import com.yeestor.model.http.HandleResp;
import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.model.event.DeviceDiskEvent;
import com.yeestor.work_order.model.rms.*;
import com.yeestor.work_order.service.order.DocumentService;
import com.yeestor.work_order.service.order.FlashService;
import com.yeestor.work_order.service.order.change.DataChangeListener;
import com.yeestor.work_order.service.plan.PlanService;
import com.yeestor.work_order.repository.OrderFlashRepository;
import com.yeestor.work_order.service.plan.DeviceService;
import com.yeestor.work_order.utils.LogUtils;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags= {"RMS"}, value = "RMS中的API")
public class RMSController {

    private final DocumentService documentService ;
    private final PlanService planService ;
    private final OrderFlashRepository orderFlashRepository;
    private final FlashService flashService ;

    private final DeviceService deviceService ;

    private final Environment environment ;
    private final DataChangeListener dataChangeListener ;
    private final ApplicationEventPublisher eventPublisher;

    @GetMapping("/plan/list")
    @ApiOperation(value = "获取plan 列表 ", notes = "通过产品线 ,所属产品，以及版本类型来获取对应的plan 列表")
    public HandleResp<List<PlanModel>> listPlan(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @ApiParam(value = "产品线",allowableValues = "GE") @RequestParam(value = "p") String product,
            @ApiParam(value = "子产品", allowableValues = "U2,U3,SD") @RequestParam(value = "sp") String subProduct,
            @ApiParam(value = "版本类型",allowableValues = "alpha,release,alphaToRelease") @RequestParam(value = "v") String versionType

    ){
        log.info("getActiveProfiles: {}", Arrays.toString(environment.getActiveProfiles()));
        return HandleResp.ok(new ArrayList<>(), "查询成功！");
    }

    @GetMapping("/plan/group/list")
    @ApiOperation(value = "获取plan合集列表 ", notes = "通过产品线 ,所属产品，以及版本类型来获取对应的plan合集列表")
    public HandleResp<List<PlanGroupModel>> listPlanGroup(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @ApiParam(value = "产品线",allowableValues = "GE") @RequestParam(value = "p") String product,
            @ApiParam(value = "子产品", allowableValues = "U2,U3,SD") @RequestParam(value = "sp") String subProduct,
            @ApiParam(value = "版本类型",allowableValues = "alpha,release,alphaToRelease") @RequestParam(value = "v") String versionType
    ){
        return HandleResp.ok(new ArrayList<>(), "查询成功！");
    }



    @GetMapping("/device/list")
    @ApiOperation(value = "获取设备列表 ", notes = "通过产品线来获取对应产品线的设备列表")
    public HandleResp<List<DeviceModel>> listRole(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @ApiParam("产品线") @RequestParam(value = "p") String product
    ){
        return HandleResp.ok(new ArrayList<>(), "查询成功！");
    }

    @GetMapping("/device/detail")
    @ApiOperation(value = "获取指定设备的详情", notes = "通过设备的IP或者Mac地址来获取指定设备的详情，包括样片的编号信息等,IP和Mac地址二选一")
    public HandleResp<DeviceDetailModel> queryDeviceDetail(
            @ApiParam("ip") @RequestParam(value = "ip",required = false) String ip,
            @ApiParam("mac") @RequestParam(value = "mac", required= false) String mac
    ) {
        return HandleResp.ok(null, "设备详情查询成功！");
    }


    @PostMapping("/device/lock")
    @ApiOperation(value = "锁定设备 ", notes = "通过设备IP以及工单号来锁定对应的设备。")
    public HandleResp<String> lockDevice(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @RequestBody OrderLockParams orderLockModel
    ){
        return HandleResp.ok("","锁定成功！");
    }

    @PostMapping("/device/unlock")
    @ApiOperation(value = "解锁设备 ", notes = "通过设备IP以及工单号来锁定对应的设备。")
    public HandleResp<String> unlockDevice(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @RequestBody OrderLockParams orderLockModel
    ){
        return HandleResp.ok("","解锁成功！");
    }

    @PostMapping("/plan/test")
    @ApiOperation(value = "开始测试Plan", notes = "将Plan以及传入的设备列表传递给RMS，然后RMS会开始测试。0表示全部成功, 1表示部分成功 , 2表示全部失败")
    public HandleResp<PlanTestRespModel> startTest(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @RequestBody PlanTestParams body
    ){
        return HandleResp.ok(null,"启动成功！");
    }

    @PostMapping("/plan/stop")
    @ApiOperation(value = "停止对应的Plan", notes = "将工单号，plan ，以及需要停止的电脑列表传递给RMS")
    public HandleResp<PlanTestRespModel> stopTest(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @RequestBody StopTestParams body
    ){
        return HandleResp.ok(null,"停止成功！");
    }


    @PostMapping(value = "/order/create",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @ApiOperation(value = "创建工单", notes = "使用从CI中获取的数据创建工单")
    public HandleResp<String> createOrder(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @RequestBody OrderCreateParams body
    ){
        return HandleResp.ok(null,"创建成功！");
    }

    @PostMapping(value = "/order/close")
    @ApiOperation(value = "关闭eReport中的测试单", notes = "关闭在RMS的工单，调用此接口的时机为：研发完成fail 分析，或者跳过测试fail 分析后, 工单被撤销")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "no", dataTypeClass = String.class, value = "RMS中的工单号", paramType = "body", required = true),
            @ApiImplicitParam(name = "reason", dataTypeClass = String.class, value = "关闭的原因", paramType = "body")
    })
    public HandleResp<String> closeOrder(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @ApiIgnore @RequestParam Map<String, Object> params
    ){
        return HandleResp.ok(null,"关闭成功！");
    }

    @PostMapping(value = "/order/open")
    @ApiOperation(value = "打开eReport中的测试单", notes = "重新打开在RMS的工单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "no", dataTypeClass = String.class, value = "RMS中的工单号", paramType = "body", required = true),
            @ApiImplicitParam(name = "reason", dataTypeClass = String.class, value = "打开的原因", paramType = "body")
    })
    public HandleResp<String> openRMSOrder(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @ApiIgnore @RequestParam Map<String, String> params
    ) {
        return HandleResp.ok(null, "打开成功！");
    }


    @PostMapping(value= "/report/upload")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", allowableValues = "0,1" , dataTypeClass = Integer.class, value = "报告类型，0表示手动报告类型，1表示整理报告类型", required = true ,paramType= "body"),
            @ApiImplicitParam(name = "no", dataTypeClass = String.class, value = "RMS中的工单号", paramType= "body",required = true),
            @ApiImplicitParam(name = "p", dataTypeClass = String.class, value = "产品", paramType= "body",required = true),
            @ApiImplicitParam(name = "plan", dataTypeClass = String.class, value = "Plan的名称，当type为0时必填。", paramType= "body",required = true),
            @ApiImplicitParam(name = "user", dataTypeClass = String.class, value = "上传人的名称", paramType= "body",required = true)
    })
    @ApiOperation(value = "上传报告文件", notes = "上传报告到RMS.报告类型可能有手动报告以及 整理后的完整报告！")
    public HandleResp<HashMap<String, String>> uploadReport(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @ApiIgnore @RequestParam Map<String, Object> params,
            @ApiParam("报告文件列表") @RequestParam("file") MultipartFile[] files
    ) {
        return HandleResp.ok(null, "上传成功!");
    }


    @ApiOperation(value = "合并工单的报告", notes = "合并工单的报告。")
    @GetMapping("/report/merge")
    public HandleResp<String> mergeOrderReport(
            @ApiParam(value = "服务器类型", allowableValues = "dev,prod") @RequestHeader("SERVER-TYPE") String serverType,
            @ApiParam("工单号") @RequestParam("no") String orderNo,
            @ApiParam("产品") @RequestParam("p") String p
    ){
        return HandleResp.ok(null,"报告合并成功！");
    }

    @PostMapping("/report/status")
    @ApiOperation(value = "报告状态变更回调", notes = "报告变更回调，由RMS来调用。")
    public HandleResp<String> reportStatusChangeCallback(
            @ApiParam(value = "工单号") @RequestHeader("ORDER-NO") String orderNo,
            @RequestBody @Valid ReportStatusChangeParams body
    ){
        log.debug("reportStatusChangeCallback body:{}" ,body);
        // 判断工单号是否存在
        if(!orderFlashRepository.existsByOrderFlashNo(body.getOrderNo())){
            return HandleResp.failed("找不到对应的工单号!");
        }
        documentService.updateDocumentStatus(
                body.getOrderNo(),
                body.getReportInfoList()
        ) ;

        return  HandleResp.ok(null,"报告合并成功！");
    }

    @PostMapping("/device/status")
    @ApiOperation(value = "设备测试状态变更回调", notes = "设备测试状态变更回调，由RMS来调用。")
    public HandleResp<String> deviceStatusChangeCallback(
            @ApiParam(value = "工单号") @RequestHeader("ORDER-NO") String orderNo,
            @RequestBody @Valid DeviceStatusChangeParams body
    ){
        log.info("deviceStatusChangeCallback body:{}",body) ;
        // 判断工单号是否存在
        OrderFlashEntity orderFlashEntity = flashService.findFlashOrElseThrow(body.getOrderNo());
        LogUtils.setOrderAndFlashTracePoint(orderFlashEntity.getOrderId(),orderFlashEntity.getFlash(), "样片信息回调");
        Map<Long,String> planIDAndNames = deviceService.updateDeviceStatus(
                orderFlashEntity,
            body.getDeviceInfoLst()
        );
        planIDAndNames.forEach((planId,planName) ->
                deviceService.updatePlanAssignInfoAndJob(orderFlashEntity, planId, planName));
        LogUtils.clearTracePoint();
        return  HandleResp.ok(null,"设备状态变更成功！");
    }


    @PostMapping("/plan/status")
    @ApiOperation(value = "Plan状态变更回调", notes = "Plan状态变更回调，由RMS来调用。")
    public HandleResp<String> planStatusChangeCallback(
            @ApiParam(value = "工单号") @RequestHeader("ORDER-NO") String orderNo,
            @RequestBody @Valid PlanStatusChangeParams body,
            @ApiIgnore HttpServletRequest request
            ){
        log.info("planStatusChangeCallback body:{} -- request.getRemoteAddr():{}",body,request.getRemoteAddr());

        planService.updatePlanDeviceStatus(
            body.getOrderNo(),
            body.getPlan(),
            body
        );

        return HandleResp.ok("","更新成功！");
    }

    @PostMapping("/device/disk/callback")
    @ApiOperation(value = "设备样片回调", notes = "设备样片回调，由RMS来调用。")
    public HandleResp<String> deviceDiskReadyCallback(
            @ApiParam(value = "工单号") @RequestHeader("ORDER-NO") String orderNo,
            @RequestBody @Valid DeviceInfo body
    ) {
        log.info("deviceDiskReadyCallback body:{}", body);
        String no = URLDecoder.decode(orderNo, StandardCharsets.UTF_8);
        eventPublisher.publishEvent(
                DeviceDiskEvent.builder()
                        .diskInfo(body)
                        .orderNo(no)
                        .build());
        return HandleResp.ok("", "更新成功！");
    }

    @GetMapping("/flash/model")
    @ApiOperation(value = "获取工单的所有批次容量、Ce数信息", notes = "通过测试单号获取批次信息")
    public HandleResp<OrderInfo> fetchTestFlashModel(
            @ApiParam("testNo") @RequestParam(value = "testNo") String testNo
    ) {
        log.info("fetchTestFlashModel orderFlashNo: {}", testNo);
        return HandleResp.ok(flashService.fetchTestFlashInfo(testNo), "设备详情查询成功！");
    }

    @PostMapping(value = "/device/shutdown")
    @ApiOperation(value = "电脑关机", notes = "电脑关机")
    public HandleResp<DeviceControlResp> closeDevice(@RequestBody @Valid DeviceControlParams body){
        return HandleResp.ok(null,"关闭成功！");
    }

    @PostMapping(value = "/device/startup")
    @ApiOperation(value = "电脑开机", notes = "电脑开机")
    public HandleResp<DeviceControlResp> openDevice(@RequestBody @Valid DeviceControlParams body) {
        return HandleResp.ok(null, "打开成功！");
    }

    @GetMapping("/query/device/status")
    @ApiOperation(value = "设备测试状态查询", notes = "设备测试状态查询")
    public HandleResp<DeviceStatusCheckResp> queryDeviceStatus(
            @RequestBody @Valid DeviceStatusCheckParams body
    ){
        return  HandleResp.ok(null,"查询设备开关机状态成功！");
    }

}
