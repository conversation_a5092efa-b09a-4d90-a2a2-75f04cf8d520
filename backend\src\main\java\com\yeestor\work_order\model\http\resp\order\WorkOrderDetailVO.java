package com.yeestor.work_order.model.http.resp.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeestor.work_order.entity.*;
import com.yeestor.work_order.entity.analysis.OrderFailAnalysisEntity;
import com.yeestor.work_order.model.ci.OrderInfoModel;
import com.yeestor.work_order.model.http.req.Person;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Slf4j
@ApiModel(value = "工单详情")
public class WorkOrderDetailVO {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "Flash 相关的信息、")
    public static class FlashInfoVO {

        @ApiModelProperty(value = "Flash批次")
        private String flash;

        @ApiModelProperty(value = "该Flash批次在工单中的先后顺序")
        private int idx;

        @ApiModelProperty(value = "该Flash批次的状态")
        private OrderFlashEntity.Status status;

        @ApiModelProperty(value = "该Flash批次的数量")
        private int num ;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "分配Flash批次的时间")
        private long createAt ;

        @ApiModelProperty(value = "Flash 批次的测试人员，目前只返回人员名称。")
        private String testPerson ;

        @ApiModelProperty(value = "Flash 批次的测试负责人钉钉ID。")
        private String testPersonId ;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "开始测试时间。")
        private Long testStartAt ;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "测试结束时间, 也是开始合并报告的时间")
        private Long testEndAt ;

        @ApiModelProperty(value = "剩余的可用Flash 的数量.")
        private int leftNum ;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "合并报告完成的时间")
        private Long mergeCompleteAt ;

        @ApiModelProperty(value = "Fail分析指派人")
        private String failAssignPerson;

        @ApiModelProperty(value = "启动fail 分析的人员名称")
        private String startFailPerson;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "发起Fail分析时间")
        private Long startFailAt;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty(value = "flash 批次结束的时间")
        private Long completeAt ;


        @JsonInclude(JsonInclude.Include.NON_NULL)
        @ApiModelProperty(value = "自动定位编号")
        private Boolean autoLoc ;

        @ApiModelProperty(value = "CE数")
        private String ceCount ;

        /**
         * Flash批次是否被禁用
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @ApiModelProperty(value = "Flash批次是否被禁用")
        private Boolean disabled ;

        private List<Person> testPersons ;

        @ApiModelProperty(value = "取消测试时间")
        private Long cancelAt ;

        @ApiModelProperty(value = "取消测试原因")
        private String cancelReason ;

        @ApiModelProperty(value = "取消测试人员")
        private String cancelPerson ;

        @ApiModelProperty(value = "是否通过review")
        private Boolean reviewPass ;

        @ApiModelProperty(value = "等待分配环境的Plan数量")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private int queueNum ;

        @ApiModelProperty(value = "环境已经准备好的Plan数量")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private int readyNum ;

        @ApiModelProperty(value = "正在测试中的Plan数量")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private int testingNum;

        @ApiModelProperty(value = "测试完成的Plan数量")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private int completedNum;

        @ApiModelProperty(value = "flash批次被标记的版本")
        private String markVersion;

        @ApiModelProperty(value = "复测时间")
        private Long retestAt;

        @ApiModelProperty(value = "复测人员钉钉id")
        private String retestBy;

        @ApiModelProperty(value = "复测人员的名称")
        private String retestPerson;

        @ApiModelProperty(value = "转release版本后是否复测")
        private Boolean hasTestConvert;

        public static FlashInfoVO entityToVO(OrderFlashEntity flashEntity) {
            ObjectMapper mapper = new ObjectMapper();
            FlashInfoVO flashInfo = new FlashInfoVO();
            flashInfo.setFlash(flashEntity.getFlash());
            flashInfo.setIdx(flashEntity.getIdx());
            flashInfo.setStatus(flashEntity.getStatus());
            flashInfo.setNum(flashEntity.getNum());
            flashInfo.setCreateAt(flashEntity.getCreatedAt());
            flashInfo.setTestPerson(flashEntity.getTestPerson());
            flashInfo.setTestPersonId(flashEntity.getTestBy());
            flashInfo.setTestStartAt(flashEntity.getTestStartAt());
            flashInfo.setTestEndAt(flashEntity.getTestEndAt());
            flashInfo.setLeftNum(flashEntity.getLeftNum());
            flashInfo.setMergeCompleteAt(flashEntity.getMergeCompleteAt());
            flashInfo.setCompleteAt(flashEntity.getCompleteAt());
            flashInfo.autoLoc = flashEntity.getAutoLoc();
            flashInfo.disabled = flashEntity.getDisabled() ;
            flashInfo.ceCount = flashEntity.getCeCount();
            String personStr = flashEntity.getTestRelatedPerson();

            flashInfo.cancelAt = flashEntity.getCancelAt() ;
            flashInfo.cancelReason = flashEntity.getCancelReason();
            flashInfo.cancelPerson = flashEntity.getCancelPerson() ;
            flashInfo.startFailAt = flashEntity.getStartFailAt();
            flashInfo.startFailPerson = flashEntity.getStartFailPerson();

            flashInfo.markVersion = flashEntity.getMarkVersion();
            flashInfo.retestAt = flashEntity.getRetestAt();
            flashInfo.retestBy = flashEntity.getRetestBy();
            flashInfo.retestPerson = flashEntity.getRetestPerson();
            flashInfo.hasTestConvert = flashEntity.getHasTestConvert();

            try {
                if(personStr != null){
                    flashInfo.testPersons = mapper.readValue(personStr, new TypeReference<List<Person>>() {
                    });
                }
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }

            return flashInfo;
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "Fail 分析的结果")
    public static class FailAnalysisVO {
        private String flash ;
        private int type ;
        private String result ;
        private long createdAt ;
        private String createdPerson ;

        public static FailAnalysisVO entityToVO(OrderFailAnalysisEntity failAnalysisEntity) {
            FailAnalysisVO failAnalysisVO = new FailAnalysisVO();
            failAnalysisVO.setFlash(failAnalysisEntity.getFlash());
            failAnalysisVO.setType(failAnalysisEntity.getType());
            failAnalysisVO.setResult(failAnalysisEntity.getResult());
            failAnalysisVO.setCreatedAt(failAnalysisEntity.getCreatedAt());
            failAnalysisVO.setCreatedPerson(failAnalysisEntity.getCreatedPerson());
            return failAnalysisVO;
        }
    }


    @ApiModelProperty(value = "工单id")
    private long id;

    @ApiModelProperty(value = "工单号")
    private String no;

    /**
     * 工单状态
     * @see WorkOrderEntity.Status
     */
    @ApiModelProperty(value = "工单状态, 详情见WorkOrderEntity.Status \n created(0),confirmed(1) ,ready(2),testing(3),waiting_for_report(4),waiting_for_review(5),completed(6) ,revoked(9)")
    private WorkOrderEntity.Status status;

    @ApiModelProperty(value = "产品线")
    private String product ;

    @ApiModelProperty(value = "产品")
    private String subProduct ;

    @ApiModelProperty(value = "工单对应的Flash")
    private String flash ;

    @ApiModelProperty(value = "工单对应的Flash")
    private String chip ;

    @ApiModelProperty(value = "版本")
    private String version ;

    @ApiModelProperty(value = "完整版本，由自动构建工具生成！")
    private String fullVersion;


    @ApiModelProperty(value = "版本类型",allowableValues ="Alpha, Release, alphaToRelease")
    private String versionType ;

    @ApiModelProperty(value = "优先级")
    private int priority ;

    @ApiModelProperty(value = "feature")
    private int feature ;

    @ApiModelProperty(value = "禅道产品id")
    private String zentaoProduct;

    @ApiModelProperty(value = "禅道项目")
    private String zentaoProject;

    @ApiModelProperty(value = "禅道测试单")
    private String zentaoTestTask ;

    @ApiModelProperty(value = "版本产生bug")
    private Integer generatedBugNum ;

    @ApiModelProperty(value = "版本测试验证产生的bug")
    private Integer svBugNum;

    @ApiModelProperty(value = "版本验证开发(QA)测试产生的bug")
    private Integer fvBugNum;

    @ApiModelProperty(value = "MpTool path")
    private String mpToolPath ;

    @ApiModelProperty(value = "MpFile path")
    private String mpFilePath ;

    @ApiModelProperty(value = "简单的Plan信息列表，只会在第一次确认信息的时候返回。")
    private List<PlanDetailVO> planList;

    @ApiModelProperty(value = "工单的Flash 批次.")
    private List<FlashInfoVO> flashInfoList;

    @ApiModelProperty(value = "Fail分析的结果")
    private List<FailAnalysisVO> failAnalysisList;

    @ApiModelProperty(value = "测试要求")
    private String requirement;

    @ApiModelProperty(value = "版本日志")
    private String versionLog ;

    @ApiModelProperty(value = "构建完成时间 ，实际上就是导入工单时的时间")
    private long buildEndAt ;

    @ApiModelProperty(value = "构建人")
    private String buildPerson ;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "工单的Flash确认时间。")
    private Long confirmAt;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "工单的Flash确认人员，目前只返回人员名称。")
    private String confirmPerson;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "开始测试时间")
    private Long testStartAt ;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "结束测试时间")
    private Long testEndAt ;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "撤销时间")
    private Long revokeAt;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "撤销人员")
    private String revokePerson ;

    @ApiModelProperty(value = "bug列表")
    private List<OrderInfoModel.BugLink>  bugLinks;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "整个工单结束的时间")
    private Long endAt ;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "mars 路径")
    private String marsPath ;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "mars_plan 路径")
    private String planPath ;

    @ApiModelProperty(value = "scrcpy 路径")
    private String scrcpyPath ;

    @ApiModelProperty(value = "scrcpy_plan 路径")
    private String scrcpyPlan ;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "rms 通知群组")
    private String rmsGroup ;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "撤销原因")
    private String revokeReason ;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "关联工单")
    private List<OrderRelatedV0> relatedList ;

    @ApiModelProperty(value = "alphaToRelease的版本路径")
    private String convertToolPath ;

    @ApiModelProperty(value = "alphaToRelease的时间")
    private Long convertAt;

    @ApiModelProperty(value = "alphaToRelease的人员名称")
    private String convertPerson;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "与当前工单相关的工单")
    public static class OrderRelatedV0 {
        private String no ;
        private long id ;

        public static OrderRelatedV0 entityToVO(WorkOrderEntity orderEntity) {
            OrderRelatedV0 relatedV0 = new OrderRelatedV0();
            relatedV0.setId(orderEntity.getId());
            relatedV0.setNo(orderEntity.getNo());
            return relatedV0;
        }
    }


    /**
     * 将工单实体{@link WorkOrderEntity} 转换成工单详情
     * @param workOrder 工单实体
     * @return 工单详情
     */
    public static WorkOrderDetailVO convertToSimple(WorkOrderEntity workOrder, OrderDetailEntity detailEntity, List<OrderPlanEntity> planEntities) {
        WorkOrderDetailVO workOrderDetailVO = new WorkOrderDetailVO();
        workOrderDetailVO.setId(workOrder.getId());
        workOrderDetailVO.setNo(workOrder.getNo());
        workOrderDetailVO.setFlash(workOrder.getFlash());
        workOrderDetailVO.setChip(workOrder.getChip());
        workOrderDetailVO.setStatus(workOrder.getStatus());
        workOrderDetailVO.setProduct(workOrder.getProduct());
        workOrderDetailVO.setSubProduct(workOrder.getSubProduct());
        workOrderDetailVO.setVersion(workOrder.getVersion());
        workOrderDetailVO.setFullVersion(workOrder.getFullVersion());
        workOrderDetailVO.setVersionType(workOrder.getVersionType());
        workOrderDetailVO.setPriority(workOrder.getPriority());
        workOrderDetailVO.setBuildEndAt(workOrder.getBuildEndAt());
        workOrderDetailVO.setBuildPerson(workOrder.getBuildPerson());
        workOrderDetailVO.setMpToolPath(workOrder.getMpToolPath());

        // 新增Mars信息、Mars路径、plan信息
        workOrderDetailVO.setMarsPath(workOrder.getMarsPath());
        workOrderDetailVO.setPlanPath(workOrder.getPlanPath());
        workOrderDetailVO.setRmsGroup(workOrder.getRmsGroup());
        workOrderDetailVO.setScrcpyPath(workOrder.getScrcpyPath());
        workOrderDetailVO.setScrcpyPlan(workOrder.getScrcpyPlan());
        workOrderDetailVO.setMpFilePath(detailEntity.getMpFilePath());

        // 撤销描述
        workOrderDetailVO.setRevokeReason(workOrder.getRevokeReason());

        workOrderDetailVO.setZentaoProduct(detailEntity.getZentaoProduct());
        workOrderDetailVO.setZentaoProject(detailEntity.getZentaoProject());
        workOrderDetailVO.setZentaoTestTask(detailEntity.getZentaoTestTask());
        workOrderDetailVO.setGeneratedBugNum(detailEntity.getGeneratedBugNum());
        workOrderDetailVO.setFvBugNum(detailEntity.getFvBugNum());
        workOrderDetailVO.setSvBugNum(detailEntity.getSvBugNum());
        workOrderDetailVO.setPlanList(
                planEntities.stream().map(PlanDetailVO::of).collect(Collectors.toList())
        );
        workOrderDetailVO.setConfirmPerson(workOrder.getConfirmPerson());
        workOrderDetailVO.setConfirmAt(workOrder.getConfirmAt());

        workOrderDetailVO.revokeAt = workOrder.getRevokeAt();
        workOrderDetailVO.revokePerson = workOrder.getRevokePerson();
        workOrderDetailVO.endAt = workOrder.getEndAt();
        workOrderDetailVO.testStartAt = workOrder.getTestStartAt() ;
        workOrderDetailVO.testEndAt = workOrder.getTestEndAt();
        workOrderDetailVO.requirement = detailEntity.getRequirement() ;
        workOrderDetailVO.versionLog = detailEntity.getVersionLog();
        workOrderDetailVO.convertToolPath = workOrder.getConvertToolPath();
        workOrderDetailVO.convertAt = workOrder.getConvertAt();
        workOrderDetailVO.confirmPerson = workOrder.getConvertPerson();

        workOrderDetailVO.setFeature(workOrder.getFeature());

        String json = detailEntity.getCiJson();

        if(json != null) {

            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            try {
                OrderInfoModel.ParamsModel model = mapper.readValue(json, OrderInfoModel.ParamsModel.class);
                log.info("model = {} -- json:{}", model,json);
                if(model != null && model.getBugLink() != null ){
                    workOrderDetailVO.bugLinks = new ArrayList<>(Arrays.asList(model.getBugLink()));
                }
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }

        return workOrderDetailVO;
    }
}
