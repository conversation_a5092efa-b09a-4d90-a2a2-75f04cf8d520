package com.yeestor.work_order.model.http.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "DeviceRestartParams", description = "设备重启参数")
public class DeviceRestartParams {

    @ApiModelProperty(value = "工单id", required = true)
    private long orderId;

    @ApiModelProperty(value = "plan id", required = true)
    private long planId ;

    @ApiModelProperty(value = "重测之前的设备ip", required = true)
    private String oldIp ;

    @ApiModelProperty(value = "重测之后的设备ip", required = true)
    private String newIp ;

    @ApiModelProperty(value = "重测之前的设备mac", required = true)
    private String oldMac ;

    @ApiModelProperty(value = "重测之后的设备mac", required = true)
    private String newMac ;
}
