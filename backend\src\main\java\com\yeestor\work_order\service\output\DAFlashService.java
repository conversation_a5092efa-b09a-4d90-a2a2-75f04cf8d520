package com.yeestor.work_order.service.output;

import com.yeestor.work_order.entity.OrderFlashEntity;
import com.yeestor.work_order.entity.OrderPlanEntity;
import com.yeestor.work_order.model.http.resp.output.DAFlashModel;
import com.yeestor.work_order.model.http.resp.output.DAFlashPlanModel;
import com.yeestor.work_order.model.http.resp.output.DAPieModel;
import com.yeestor.work_order.repository.OrderPlanRepository;
import com.yeestor.work_order.repository.WorkOrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DAFlashService {
    private final WorkOrderRepository workOrderRepository;
    private final OrderPlanRepository orderPlanRepository;

    /**
     * 获取Flash 统计页面中的Flash信息
     *
     * @param subProduct  子产品
     * @param chip        主控
     * @param fullVersion 版本号
     * @param flash       型号
     * @param afterAt     最早导入时间
     * @param beforeAt    最晚导入时间
     * @return 信息列表
     */
    public List<DAFlashModel> retrievalDAFlashList(
            String subProduct,
            String chip,
            String fullVersion,
            String flash,
            Long afterAt,
            Long beforeAt
    ) {
        return workOrderRepository.findOrderFlashBySubProductAndChipAndFlashAndTime(subProduct, chip, flash, fullVersion, afterAt, beforeAt);
    }

    /**
     * 获取Flash 统计页面中的Plan信息
     *
     * @param subProduct  子产品
     * @param chip        主控
     * @param fullVersion 版本号
     * @param flash       型号
     * @param afterAt     最早导入时间
     * @param beforeAt    最晚导入时间
     * @return 信息列表
     */
    public List<DAFlashPlanModel> retrievalDAFlashPlanList(
            String subProduct,
            String chip,
            String fullVersion,
            String flash,
            Long afterAt,
            Long beforeAt
    ){
        return orderPlanRepository.findOrderPlanBySubProductAndChipAndFlashAndTime(subProduct, chip, flash, fullVersion, afterAt, beforeAt);
    }

    /**
     * 获取Flash 统计页面中的Plan信息
     *
     * @param subProduct  子产品
     * @param chip        主控
     * @param fullVersion 版本号
     * @param flash       型号
     * @param afterAt     最早导入时间
     * @param beforeAt    最晚导入时间
     * @return 信息列表
     */
    public List<DAFlashPlanModel> retrievalDAPlanList(
            String subProduct,
            String chip,
            String fullVersion,
            String flash,
            Long afterAt,
            Long beforeAt
    ){
        return orderPlanRepository.findOrderPlanBySubProductAndChipAndFlashAndTime(subProduct, chip, flash, fullVersion, afterAt, beforeAt);
    }

    /**
     * 获取当前Flash批次下所有的容量分配情况
     * @param modelList flash批次信息
     * @return 容量信息分布情况
     */
    public List<DAPieModel> countFlashVolumeSpread(List<DAFlashModel> modelList) {
        List<String> volumeList = modelList.stream().map(DAFlashModel::getSize).collect(Collectors.toList());
        HashMap<String, Long> volumeMap = new HashMap<>();
        for (String size : volumeList) {
            if (size.isEmpty()) {
                continue;
            }
            volumeMap.put(size, volumeMap.getOrDefault(size, 0L) + 1);
        }
        List<DAPieModel> daList = new ArrayList<>();

        for (Map.Entry<String, Long> entry : volumeMap.entrySet()) {
            String key = entry.getKey();
            Long value = entry.getValue();
            DAPieModel da = new DAPieModel();
            da.setName(key);
            da.setValue(value);
            daList.add(da);
        }
        return daList;
    }

    /**
     * 获取当前Flash列表的散点图数据 【当前数据仅仅包含已完成的Flash批次。取消测试和未完成的均不在内】
     * @param modelList flash批次信息
     * @return 数据格式 【【测试时长(天)，容量，flash批次，测试负责人，页面url地址】】
     */
    public List<List<String>> findFlashTestTimeScatter(List<DAFlashModel> modelList) {
        List<DAFlashModel> list = modelList.stream()
                .filter(flash -> OrderFlashEntity.Status.COMPLETED == flash.getStatus() && flash.getCancelAt() == null)
                .collect(Collectors.toList());
        List<List<String>> chartData = new ArrayList<>();
        BigDecimal divisor = new BigDecimal(String.valueOf(24 * 60 * 60 * 1000));
        for (DAFlashModel da : list) {
            List<String> dataList = new ArrayList<>();      // 用于传递数据的信息列表
            long useTime = da.getCompleteAt() - da.getCreateAt();
            // 使用 BigDecimal 进行除法运算
            BigDecimal dividend = new BigDecimal(String.valueOf(useTime));

            // 设置小数点后一位，并使用四舍五入
            BigDecimal result = dividend.divide(divisor, 1, RoundingMode.HALF_UP);
            String url = fetchESeeFlashUrl(da);
            Collections.addAll(dataList, String.valueOf(result), da.getSize().replace("GB", ""), da.getFlash(), da.getTestPerson(), url);
            chartData.add(dataList);
        }
        log.info("chartData: {}", chartData);
        return chartData;
    }

    /**
     * 获取Flash页面中Plan的散点图数据 【仅仅包含测试完成的Plan】
     * @param planModelList plan列表
     * @return 数据格式 【【测试时长(小时)，plan名称，flash批次，flash容量，测试负责人，plan测试数量，页面url地址】】
     */
    public List<List<String>> findPlanTestTimeScatter(List<DAFlashPlanModel> planModelList){
        List<DAFlashPlanModel> modelList = planModelList.stream()
                .filter(plan -> OrderPlanEntity.Status.COMPLETED == plan.getStatus() && plan.getStartAt() != null && plan.getEndAt() != null)
                .collect(Collectors.toList());
        List<List<String>> chartData = new ArrayList<>();
        BigDecimal divisor = new BigDecimal(String.valueOf(60 * 60 * 1000));    // 单位为小时
        for (DAFlashPlanModel da: modelList){
            List<String> dataList = new ArrayList<>();      // 用于传递数据的信息列表
            long useTime = da.getEndAt() - da.getStartAt();
            // 使用 BigDecimal 进行除法运算
            BigDecimal dividend = new BigDecimal(String.valueOf(useTime));
            // 设置小数点后一位，并使用四舍五入
            BigDecimal result = dividend.divide(divisor, 1, RoundingMode.HALF_UP);

            String url = fetchESeePlanUrl(da);
            Collections.addAll(dataList, String.valueOf(result), da.getName(),  da.getFlash(), da.getSize(), da.getBelongToPerson(), String.valueOf(da.getTestNum()), url);
            chartData.add(dataList);
        }
        log.info("chartData: {}", chartData);
        return chartData;
    }
    /**
     * 获取工单的页面链接
     * @param da 批次信息
     * @return 页面访问地址
     */
    public String fetchESeeFlashUrl(DAFlashModel da) {
        return "http://gateway.yeestor.com:8512/work_order/" + da.getProduct() + "/" + da.getSubProduct() + "/" + da.getOrderId() + "?flash=" + da.getFlash();
    }

    public String fetchESeePlanUrl(DAFlashPlanModel da) {
        return "http://gateway.yeestor.com:8512/work_order/" + da.getSubProduct() + "/" + da.getOrderId() + "/" + da.getFlash() + "/" + da.getName() + "/" + da.getType() + "/detail";
    }
}
