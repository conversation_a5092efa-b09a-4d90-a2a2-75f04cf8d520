package com.yeestor.work_order.entity;

import com.yeestor.entity.BaseEntity;
import lombok.*;

import javax.persistence.*;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity(name = "PlatformCase")
@Table(name = "wo_platform_case")
public class PlatformCaseEntity extends BaseEntity {

    public enum Status {
        /**
         * 等待开始
         */
        WAITING,

        RUNNING,
        /**
         * 已结束 , 成功
         */
        FINISHED_SUCCESS,
        /**
         * 已结束 ， 失败
         */
        FINISHED_FAILED,

        /**
         * 已取消
         */
        CANCELED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "order_id")
    private long orderId;

    @Column(name = "plan_id")
    private Long planId;

    @Column(name = "platform_id")
    private Long platformId;

    @Column(name = "case_name")
    private String caseName;

    @Column(name = "case_step")
    private Integer caseStep;

    @Column(name = "platform_name")
    private String platformName;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private Status status;

    /**
     * 测试的开始时间
     */
    @Column(name = "start_at")
    private Long startAt;

    /**
     * 测试的结束时间
     */
    @Column(name = "end_at")
    private Long endAt;

    /**
     * 测试失败的原因。当测试失败时，这个字段会有值。
     */
    @Column(name = "fail_reason")
    private String failReason;

    @Transient
    public boolean isFailed() {
        return this.status == PlatformCaseEntity.Status.FINISHED_FAILED;
    }
}
