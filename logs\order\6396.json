{"@timestamp":"2025-07-24T14:02:02.635+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:02:02.654+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.658+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.72+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.803+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.819+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.933+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.95+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.952+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.952+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.959+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.966+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:02:02.966+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2fe9d97b11b4fab3","spanId":"2fe9d97b11b4fab3","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.614+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:06:02.616+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.64+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.659+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.683+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.715+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.744+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.744+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.745+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.745+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.745+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:06:02.745+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"6fdf4cbb0b9fc13b","spanId":"6fdf4cbb0b9fc13b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:14:02.616+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.616+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.641+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.659+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.678+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.693+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.708+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.708+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.708+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.709+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.709+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:14:02.709+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d8ab2aded30b801f","spanId":"d8ab2aded30b801f","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.619+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:18:02.621+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.622+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.66+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.69+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.71+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.749+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.768+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.768+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.768+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.768+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.768+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:18:02.769+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"0bec537659e4d784","spanId":"0bec537659e4d784","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.608+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:26:02.61+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.61+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.628+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.643+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.657+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.672+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.686+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.686+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.686+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.686+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.686+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-1","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:26:02.687+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-1","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"f13890fbd2b85ca8","spanId":"f13890fbd2b85ca8","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.621+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:30:02.623+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.623+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.652+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.67+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.707+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.749+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.78+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.78+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.78+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.781+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.781+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:30:02.781+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"2387dd54235f742d","spanId":"2387dd54235f742d","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.613+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T14:34:02.616+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.617+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.636+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.654+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.673+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.689+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.709+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.71+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.71+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.711+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.711+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-2","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T14:34:02.711+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-2","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"40a28c20548261db","spanId":"40a28c20548261db","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.765+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T15:54:02.786+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.786+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.832+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.861+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.885+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.904+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.927+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.927+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.928+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.928+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.929+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T15:54:02.931+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"d69e2a74d7f367a6","spanId":"d69e2a74d7f367a6","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.743+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:06:02.758+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.758+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.824+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.845+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.872+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.899+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.914+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.915+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.915+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.915+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.915+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-5","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:06:02.916+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-5","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"bdf475653dfa3775","spanId":"bdf475653dfa3775","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.684+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:14:02.685+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.685+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.702+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.72+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.735+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.749+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.766+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.767+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.767+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.768+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.768+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:14:02.768+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"dba9b7f84be6d62b","spanId":"dba9b7f84be6d62b","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.761+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:18:02.764+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.764+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.784+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.801+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.818+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.833+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.856+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.857+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.857+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.857+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.857+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:18:02.857+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"c4058b126fb93283","spanId":"c4058b126fb93283","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.682+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:22:02.684+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.685+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.702+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.718+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.732+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.746+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.765+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.766+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.766+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.766+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.766+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-4","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:22:02.766+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-4","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"7b38aab04b04b7f5","spanId":"7b38aab04b04b7f5","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.417+08:00","@version":"1","message":"start check order plan's devices ----------- ;","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备","tags":["QueueService"]}
{"@timestamp":"2025-07-24T16:30:03.441+08:00","@version":"1","message":"find 1 flash: [OrderFlashEntity(id=6632, orderId=6396, flash=29506_8T23_2Lun_1024GB, orderFlashNo=YS9082HCMPHCS1415C#19016#29506_8T23##250197_29506_8T23_2Lun_1024GB, num=20, leftNum=8)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.45+08:00","@version":"1","message":"SSD assign subProduct: SATA ","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.478+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.506+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.524+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.545+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.575+08:00","@version":"1","message":"Plan OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80) is in queue, status is WAITING","logger_name":"com.yeestor.work_order.service.plan.PlanService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.586+08:00","@version":"1","message":"fetchCanAssignPlanList planEntityList: [OrderPlanEntity(id=113764, orderId=6396, name=Plan501, status=QUEUE, priority=90), OrderPlanEntity(id=113765, orderId=6396, name=Plan502, status=QUEUE, priority=90), OrderPlanEntity(id=113772, orderId=6396, name=Plan509, status=QUEUE, priority=85), OrderPlanEntity(id=113768, orderId=6396, name=Plan505, status=QUEUE, priority=80), OrderPlanEntity(id=113770, orderId=6396, name=Plan507, status=QUEUE, priority=80)]","logger_name":"com.yeestor.work_order.service.device.QueueService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.587+08:00","@version":"1","message":"执行预分配前共有8颗样片","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.588+08:00","@version":"1","message":"此次Plan分配的可分配额度为0！","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.588+08:00","@version":"1","message":"assignList: []","logger_name":"com.yeestor.work_order.service.product.impl.SATAProductService","thread_name":"quartzScheduler_Worker-3","level":"INFO","level_value":20000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
{"@timestamp":"2025-07-24T16:30:03.588+08:00","@version":"1","message":"[6396] - [29506_8T23_2Lun_1024GB] find 0 plans: []","logger_name":"com.yeestor.work_order.service.product.impl.DefaultProductService","thread_name":"quartzScheduler_Worker-3","level":"DEBUG","level_value":10000,"springAppName":"system-workorder","orderPrefix":"order","traceId":"e7f5d7e73ddc8474","spanId":"e7f5d7e73ddc8474","context":"QueueService","no":"6396","traceType":"分配设备"}
