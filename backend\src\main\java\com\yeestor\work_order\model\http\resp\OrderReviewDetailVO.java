package com.yeestor.work_order.model.http.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;

@Data
@ApiModel(value = "OrderReviewDetailVO", description = "工单Review详情")
public class OrderReviewDetailVO {


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "OrderReviewDetailVO.Flash", description = "Flash批次的详情")
    public static class FlashReviewDetailVO {

        @ApiModelProperty("flash 批次")
        private String flash ;

        @ApiModelProperty("测试人员")
        private String testPerson ;



    }


    @ApiModelProperty("工单号")
    private String orderNo ;

    @ApiModelProperty("版本构建开始的时间")
    private long buildStartAt ;

    @ApiModelProperty("版本构建完成的时间")
    private long buildEndAt ;

    @ApiModelProperty("构建人")
    private String buildPerson ;

    @ApiModelProperty("开始测试时间")
    private long testStartAt ;

    @ApiModelProperty("测试结束时间")
    private long testEndAt ;

    @ApiModelProperty(value = "flash 批次对应的错误列表, key: flash批次, value: 工作表视图对象")
    private HashMap<String, SheetVO> summaryInfo = new HashMap<>() ;

}
