package com.yeestor.work_order.model.http.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "ShareInfoParams", description = "分享信息")
public class ShareInfoParams {

    @ApiModelProperty("会话id")
    @NotBlank(message = "会话id不能为空")
    private String cid;

    @ApiModelProperty("分享链接")
    @NotBlank(message = "分享链接不能为空")
    private String linkUrl ;

    @ApiModelProperty("描述信息")
    private String description;

}
