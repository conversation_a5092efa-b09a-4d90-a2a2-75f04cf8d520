package com.yeestor.work_order.utils;

import com.yeestor.dingtalk.api.DingTalkFeignClient;
import com.yeestor.dingtalk.model.DingTalkUserDetail;
import com.yeestor.model.http.HandleResp;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;


@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DingTalkUtilsTest {

    @Autowired
    DingTalkUtils dingTalkUtils;

    @MockBean
    DingTalkFeignClient dingTalkFeignClient;

    private static final String dUserId = "0653290119972081";

    @Test
    @DisplayName("正确的钉钉ID获取用户名称")
    void getPersonName_correctDUserId() {

        when(dingTalkFeignClient.fetchUserDetail(dUserId))
                .thenReturn(HandleResp.<DingTalkUserDetail>builder()
                        .code(0)
                        .msg("success")
                        .data(DingTalkUserDetail.builder()
                                .userid("0653290119972081")
                                .name("Bugs.Wan")
                                .jobNumber("Bugs.Wan")
                                .build())
                        .build());


        String name = dingTalkUtils.getPersonName(dUserId);
        assertEquals("Bugs.Wan", name);
    }
    @Test
    @DisplayName("未找到用户时，获取用户名称")
    void getPersonName_errorDUserId() {

        when(dingTalkFeignClient.fetchUserDetail(dUserId))
                .thenReturn(HandleResp.<DingTalkUserDetail>builder()
                        .code(1)
                        .msg("failed")
                        .data(null)
                        .build());


        String name = dingTalkUtils.getPersonName(dUserId);
        assertEquals("Bugs.Wan", name);
    }

    @Test
    @DisplayName("获取当前登录用户的钉钉ID")
    @WithUserDetails(value = "Bugs.Wan", userDetailsServiceBeanName = "userDetailsServiceImpl")
    void getCurrentUserDingTalkID() {
        assertEquals(dUserId, DingTalkUtils.getCurrentUserDingTalkID());
    }

    @Test
    @DisplayName("获取当前登录用户的英文名")
    @WithUserDetails(value = "Bugs.Wan", userDetailsServiceBeanName = "userDetailsServiceImpl")
    void getCurrentUserName() {
        String name = DingTalkUtils.getCurrentUserName();
        assertEquals("Bugs.Wan", name);
    }

    @Test
    @DisplayName("没有登录，获取用户名")
    void getUser_NotLogin() {
        IllegalStateException exception = assertThrows(IllegalStateException.class, DingTalkUtils::getCurrentUserName);
        assertEquals("用户未登录", exception.getMessage());
    }
}